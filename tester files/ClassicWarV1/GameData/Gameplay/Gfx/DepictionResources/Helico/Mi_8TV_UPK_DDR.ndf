export Modele_Mi_8TV_UPK_DDR is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/DDR/Helico/Mi_8TV/Mi_8TV_UPK.fbx"
)

export Modele_Mi_8TV_UPK_DDR_MID is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/DDR/Helico/Mi_8TV_MID/Mi_8TV_UPK_MID.fbx"
)

export Modele_Mi_8TV_UPK_DDR_LOW is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/DDR/Helico/Mi_8TV_LOW/Mi_8TV_UPK_LOW.fbx"
)

export ModelFile_Pod_UPK_t2 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/SOV/UPK_23_250/UPK_23_250_t2.fbx'

export Modele_Mi_8TV_UPK_DDR_Tourelle_02 is TResourceMesh
(
    Mesh=ModelFile_Pod_UPK_t2
)

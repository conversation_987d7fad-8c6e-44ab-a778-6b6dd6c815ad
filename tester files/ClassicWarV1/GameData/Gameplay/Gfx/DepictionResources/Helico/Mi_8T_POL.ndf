export Modele_Mi_8T_POL is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/DDR/Helico/Mi_17/Mi_17.fbx"
    Textures="GameData:/Assets/3D/Units/POL/Helico/Mi_17"
)

export Modele_Mi_8T_POL_MID is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/DDR/Helico/Mi_17_MID/Mi_17_MID.fbx"
    Textures="GameData:/Assets/3D/Units/POL/Helico/Mi_17_MID"
)

export Modele_Mi_8T_POL_LOW is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/DDR/Helico/Mi_17_LOW/Mi_17_LOW.fbx"
    Textures="GameData:/Assets/3D/Units/POL/Helico/Mi_17_LOW"
)

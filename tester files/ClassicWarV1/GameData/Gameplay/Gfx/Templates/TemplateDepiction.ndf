
private DisabledOperatorMid is [
    'weapon_effet_tag1_physic_fire_effect',
    'weapon_effet_tag2_physic_fire_effect',
    'weapon_effet_tag3_physic_fire_effect',
    'weapon_effet_tag4_physic_fire_effect',
    'airplane_parts',
    'ikchain1',
    'WaterRotors',
]

private DisabledOperator<PERSON>ow is DisabledOperatorMid + [
    'chassis',
    'cannon_hydraulic_recoil',
    'cannon_rocking_recoil',
    'GroundPuff',
    // 'rotary_cannon',
    'Tracks',
    'Wheels',
]

LOD_High is 'high'
LOD_Mid  is 'mid'
LOD_Low  is 'low'

// ****************************************************************************************************************************************
// *** Véhicules **************************************************************************************************************************
// ****************************************************************************************************************************************
template DepictionDescriptor_LOD_High
[
    MeshDescriptor : TResourceMesh,
    SelectorId = LOD_High
]
is TDepictionDescriptor
(
    SelectorId = [<SelectorId>]
    MeshDescriptor = <MeshDescriptor>
)
// ****************************************************************************************************************************************
template DepictionDescriptor_LOD_Mid
[
    MeshDescriptor : TResourceMesh,
    SelectorId = LOD_Mid
]
is TDepictionDescriptor
(
    SelectorId = [<SelectorId>]
    MeshDescriptor = <MeshDescriptor>
    DisabledOperators = DisabledOperatorMid
)
// ****************************************************************************************************************************************
template DepictionDescriptor_LOD_Low
[
    MeshDescriptor : TResourceMesh,
    SelectorId = LOD_Low,
    DisabledOperators = []
]
is TDepictionDescriptor
(
    SelectorId = [<SelectorId>]
    MeshDescriptor = <MeshDescriptor>
    DisabledOperators = DisabledOperatorLow + <DisabledOperators>
)
MaterialTagMech is 'Mechanical'
// ****************************************************************************************************************************************
// *** infantrie **************************************************************************************************************************
// ****************************************************************************************************************************************
AlternativeNone is TMeshlessDepictionDescriptor
(
    SelectorId = ['none']
    ReferenceMeshForSkeleton = $/GFX/DepictionResources/Rien
)

template DepictionDescriptor_LOD_InfantryHigh
[
    MeshDescriptor : TResourceMesh
]
is TDepictionDescriptor
(
    SelectorId = [LOD_High]
    MeshDescriptor = <MeshDescriptor>
)

template DepictionDescriptor_LOD_InfantryLow
[
    MeshDescriptor : TResourceMesh
]
is TDepictionDescriptor
(
    SelectorId = [LOD_Low]
    MeshDescriptor = <MeshDescriptor>
)

template TemplateAllSubWeaponDepiction
[
    Alternatives,
    Operators,
] is TSubDepiction
(
    Anchors = ['arme_1']
    Depiction = TDepictionTemplate
    (
        Selector = ~/SoldierDynamicWeaponSubDepictionSelector
        DepictionAlternatives = <Alternatives>
        Operators = <Operators>
    )
)

template TemplateAllSubBackpackWeaponDepiction
[
    Alternatives,
] is TSubDepiction
(
    Anchors = ['arme_2']
    Depiction = TDepictionTemplate
    (
        Selector = ~/SoldierDynamicBackpackWeaponSubDepictionSelector
        DepictionAlternatives = <Alternatives>
    )
)

template TemplateWeaponAccessorySubDepiction
[
    MeshDescriptor,
    EnabledValue,
] is TSubDepiction
(
    Anchors = ['bip01 spine1']
    Depiction = TDepictionTemplate
    (
        Selector = TInfantryWeaponAccessorySelector
        (
            NoneLimitInMeter = LodHighNoneLimit_SoldierWeapon
            CameraMoverManager = $/Camera/CameraMoverManager
            MeshAlternativeKeys = ['mesh_alternative','mesh_alternative_backpack']
            PrincipalDepiction = [LOD_High]
            EnabledValue = <EnabledValue>
        )
        DepictionAlternatives = [
          TDepictionDescriptor( SelectorId = [LOD_High] MeshDescriptor = <MeshDescriptor> ),
        ]
    )
)

TacticDepiction_01_Surrogates is [[LOD_High, '01']]
TacticDepiction_02_Surrogates is TacticDepiction_01_Surrogates + [[LOD_High, '02']]
TacticDepiction_03_Surrogates is TacticDepiction_02_Surrogates + [[LOD_High, '03']]
TacticDepiction_04_Surrogates is TacticDepiction_03_Surrogates + [[LOD_High, '04']]
TacticDepiction_05_Surrogates is TacticDepiction_04_Surrogates + [[LOD_High, '05']]
TacticDepiction_06_Surrogates is TacticDepiction_05_Surrogates + [[LOD_High, '06']]
TacticDepiction_07_Surrogates is TacticDepiction_06_Surrogates + [[LOD_High, '07']]
TacticDepiction_08_Surrogates is TacticDepiction_07_Surrogates + [[LOD_High, '08']]
TacticDepiction_09_Surrogates is TacticDepiction_08_Surrogates + [[LOD_High, '09']]
TacticDepiction_10_Surrogates is TacticDepiction_09_Surrogates + [[LOD_High, '10']]

template TemplateInfantrySelectorTactic
[
    UniqueCount,
    Surrogates
] is TInfantryLodSelector
(
    UniqueCount = <UniqueCount>
    HighSurrogates = <Surrogates>
    HighLowLimitInMeter = LodHighLowLimit_Infantry
    LowNoneLimitInMeter = LodLowNoneLimit_Infantry
    CameraMoverManager = $/Camera/CameraMoverManager
    OptionalLimit = $/GraphicOption/ModelQuality
)

template TemplateInfantryDepictionFactoryTactic
[
    Alternatives,
    SubDepictions = [],
    Operators,
    Selector
] is TDepictionTemplate
(
        Scaler = CommonInfantryScaler
        Selector = <Selector>
        DepictionAlternatives = <Alternatives>
        SubDepictions = <SubDepictions>
        Operators = [ DepictionOperator_Shader_Selection, DepictionOperator_Shader_Spotted, DepictionOperator_Shader_GhostTactic ] + <Operators>
)

template TemplateInfantryDepictionFactoryGhost
[
    Alternatives,
    Selector
] is TDepictionTemplate
(
    ResidentMaterialTags = [ MaterialTagGhost ]
    Scaler = CommonInfantryScaler
    Selector = <Selector>
    DepictionAlternatives = <Alternatives>
    Operators = [ DepictionOperator_FreezeAnimation_Ghost, DepictionOperator_Shader_Ghost ]
)

template TemplateInfantryDepictionSquad
[
    SoundOperator
] is TTimelyDepictionReceiverFactory
(
  DepictionTemplate = TDepictionTemplate
  (
    Selector = OnlyHighDepictionSelector
    DepictionAlternatives = [
      DepictionDescriptor_LOD_High(MeshDescriptor = $/GFX/DepictionResources/Rien),
    ]
    Operators = [ <SoundOperator>, DepictionOperator_AlwaysAliveSoundProbe ]
  )
)

template GhostUnitDepictionTemplate
[ Selector, Scaler, Alternatives ]
is TDepictionTemplate
(
    ResidentMaterialTags = [ MaterialTagGhost ]
    Scaler = <Scaler>
    Selector = <Selector>
    DepictionAlternatives = <Alternatives>
    Operators = [ DepictionOperator_Shader_Ghost ]
)

template GhostVehicleDepictionTemplate
[ Selector, Alternatives ]
is GhostUnitDepictionTemplate
(
    Selector = <Selector>
    Alternatives = <Alternatives>
    Scaler = CommonVehicleScaler
)

template GhostAerialDepictionTemplate
[ Selector, Alternatives ]
is GhostUnitDepictionTemplate
(
    Selector = <Selector>
    Alternatives = <Alternatives>
    Scaler = SpecificFlyingScaler
)

template TacticVehicleDepictionTemplate
[
    CoatingName,
    Selector,
    Alternatives,
    Operators,
    Actions,
    SubDepictions = [],
    SubDepictionGenerators = []
]
is TTimelyDepictionReceiverFactory
(
    DepictionTemplate = TDepictionTemplate
    (
        ResidentMaterialTags = [ MaterialTagMech ]
        PossibleCullingSubBBoxes = ['tourelle_01']
        AdditionalTextures = $/M3D/Shader/VehicleDestTextures
        CoatingName = <CoatingName>
        Scaler = CommonVehicleScaler
        Selector = <Selector>
        DepictionAlternatives = <Alternatives>
        Operators = CommonTacticOperators + <Operators>
        Actions = <Actions>
        SubDepictions = <SubDepictions>
        SubDepictionGenerators = <SubDepictionGenerators>
    )
)

template TacticAerialDepictionTemplate
[
    Deviator = nil,
    AdditionalTextures,
    CoatingName,
    Selector,
    Alternatives,
    Operators,
    Actions,
    SubDepictions = [],
    SubDepictionGenerators = [],
]
is TTimelyDepictionReceiverFactory
(
    DepictionTemplate = TDepictionTemplate
    (
        Deviator = <Deviator>
        AdditionalTextures = <AdditionalTextures>
        ResidentMaterialTags = [ MaterialTagMech ]
        PossibleCullingSubBBoxes =
        [
            'aile_droite',
            'aile_gauche',
            'reacteur_droite',
            'reacteur_gauche',
            'queue',
        ]
        CoatingName = <CoatingName>
        Scaler = SpecificFlyingScaler
        Selector = <Selector>
        DepictionAlternatives = <Alternatives>
        Operators = CommonTacticOperators + [ DepictionOperator_CadaverCrashing ] + <Operators>
        Actions = <Actions>
        SubDepictions = <SubDepictions>
        SubDepictionGenerators = <SubDepictionGenerators>
    )
)

template ShowroomVehicleDepictionTemplate
[
    Alternatives,
    Selector,
    SubDepictions = [],
    SubDepictionGenerators = []
]
is TTimelyDepictionReceiverFactory
(
    DepictionTemplate = TDepictionTemplate
    (
        Selector = <Selector>
        Operators = [ DepictionOperator_ShadowPointCloudProvider ]
        DepictionAlternatives = <Alternatives>
        SubDepictions = <SubDepictions>
        SubDepictionGenerators = <SubDepictionGenerators>
    )
)

template ShowroomDepictionTemplateInternal
[
    MeshDescriptor,
    Operators = [],
    SubDepictions = [],
    SubDepictionGenerators = []
]
is TDepictionTemplate
(
    Selector = OnlyHighDepictionSelector
    DepictionAlternatives = [ DepictionDescriptor_LOD_High( MeshDescriptor = <MeshDescriptor> ) ]

    Operators = <Operators>
    SubDepictions = <SubDepictions>
    SubDepictionGenerators = <SubDepictionGenerators>
)

template ShowroomAerialDepictionTemplate
[
    MeshDescriptor,
    Operators = [],
    SubDepictions = [],
    SubDepictionGenerators = []
]
is TTimelyDepictionReceiverFactory
(
    DepictionTemplate = ShowroomDepictionTemplateInternal
    (
        MeshDescriptor = <MeshDescriptor>
        Operators = <Operators> + [ DepictionOperator_ShadowPointCloudProvider ]
        SubDepictions = <SubDepictions>
        SubDepictionGenerators = <SubDepictionGenerators>
    )
)

template ShowroomLandingGear
[
    MeshDescriptor,
]
is TSubDepiction
(
    Depiction = TDepictionTemplate
    (
        Selector = OnlyHighDepictionSelector
        DepictionAlternatives = [
            TDepictionDescriptor( SelectorId = [LOD_High] MeshDescriptor = <MeshDescriptor> )
        ]
    )
)

template TransportedInfantryDepiction
[
    Index,
] is TDepictionTemplate
(
    Selector = TTransportedInfantrySelector
    (
        Index = <Index>
        TransportedInfantryCatalog = $/DepictionCore/TransportedInfantryCatalog
        HighNoneLimitInMeter = LodHighLowLimit_Infantry
        CameraMoverManager = $/Camera/CameraMoverManager
        OptionalLimit = $/GraphicOption/ModelQuality
    )

    Operators = [ TCosmeticFreezeSkeletalAnimationOperatorDesc(Animation = $/GFX/DepictionResources/FusilierTransport) ]

    DepictionAlternatives = [ DepictionDescriptor_LOD_High( MeshDescriptor = $/GFX/DepictionResources/FuldaSoldier ) ] // pour avoir un 'high' pour le skeleton
)
template TransportedInfantrySubGenerator
[
    Mesh,
] is TTransportedInfantrySubDepictionGenerator
(
    TransportedInfantryCatalog = $/DepictionCore/TransportedInfantryCatalog
    ReferenceMesh = <Mesh>
    Soldiers =
    [
        TransportedInfantryDepiction( Index = 1 ),
        TransportedInfantryDepiction( Index = 2 ),
        TransportedInfantryDepiction( Index = 3 ),
        TransportedInfantryDepiction( Index = 4 ),
        TransportedInfantryDepiction( Index = 5 ),
        TransportedInfantryDepiction( Index = 6 ),
        TransportedInfantryDepiction( Index = 7 ),
        TransportedInfantryDepiction( Index = 8 ),
        TransportedInfantryDepiction( Index = 9 ),
        TransportedInfantryDepiction( Index = 10 ),
    ]
)

AlternativeTowedNone is TMeshlessDepictionDescriptor
(
    SelectorId = ['none']
    ReferenceMeshForSkeleton = $/GFX/DepictionResources/EmptyBoxesWithMissileHooks
)

template TowedUnitSubDepictionGenerator
[
    Mesh,
]
is TTowedUnitSubDepictionGenerator
(
    ReferenceMesh = <Mesh>
    TowedUnitCatalog = $/DepictionCore/TowedUnitCatalog
)

template RocketSubDepictionTemplate
[
    UnitMeshDescriptor,
    RocketMeshDescriptor,
    RocketCount,
    WeaponIndex,
    PhysicalProperty,
    AirStartGroupIndex = 0,
    GroundStartGroupIndex = 0,
] is TRocketSubDepictionGenerator
(
    MissileCarriageConnoisseur = TMissileCarriageConnoisseur
    (
        MeshDescriptor = <UnitMeshDescriptor>
        PylonSet = ~/DepictionPylonSet_ATGM
        AirStartGroupIndex = <AirStartGroupIndex>
        GroundStartGroupIndex = <GroundStartGroupIndex>
        WeaponInfos =
        [
            TMissileCarriageWeaponInfo
            (
                Count = <RocketCount>
                MissileType = eAGM
                WeaponIndex = <WeaponIndex>
            ),
        ]
    )

    Missiles =
    [
        TRocketSubDepictionMissileInfo
        (
            Depiction = TemplateDepictionStaticRockets
            (
                PhysicalProperty = <PhysicalProperty>
                ProjectileModelResource = <RocketMeshDescriptor>
                VisibleRocketCount = <RocketCount>
            )
            MissileCount = <RocketCount>
            WeaponIndex = <WeaponIndex>
        ),
    ]
    Pylons = ~/DepictionPylonSet_ATGM
    ReferenceMesh = <UnitMeshDescriptor>
)

template ShowroomRocketSubDepictionTemplate
[
    UnitMeshDescriptor,
    RocketMeshDescriptor,
    RocketCount,
    WeaponIndex = 1,
    AirStartGroupIndex = 0,
    GroundStartGroupIndex = 0,
] is TStaticMissileCarriageSubDepictionGenerator
(
    MissileCarriageConnoisseur = TMissileCarriageConnoisseur
    (
        MeshDescriptor = <UnitMeshDescriptor>
        PylonSet = ~/DepictionPylonSet_ATGM
        AirStartGroupIndex = <AirStartGroupIndex>
        GroundStartGroupIndex = <GroundStartGroupIndex>
        WeaponInfos =
        [
            TMissileCarriageWeaponInfo
            (
                Count = <RocketCount>
                MissileType = eAGM
                WeaponIndex = <WeaponIndex>
            ),
        ]
    )

    Missiles =
    [
        TStaticMissileCarriageSubDepictionMissileInfo
        (
            Depiction = TemplateDepictionMissileShowroom
            (
                ProjectileModelResource = <RocketMeshDescriptor>
            )
            MissileCount = <RocketCount>
            WeaponIndex = <WeaponIndex>
        ),
    ]
    Pylons = ~/DepictionPylonSet_Vehicle_Default_Showroom
    ReferenceMesh = <UnitMeshDescriptor>
)

// Ne pas éditer, ce fichier est généré par PawnMovementFileWriter


export StrategicMovementDescriptor_aerial is TStrategicMovementModuleDescriptor
(
    MaxSpeed = 1767.0
    NormalTerrainAPConsumptionModifier = 1.0
    SpeedReducerTerrainAPConsumptionModifier = 2.0
    RoadAPConsumptionModifier = 0.8
    ConsumeAPOnMove = False
)
export StrategicMovementDescriptor_helico is TStrategicMovementModuleDescriptor
(
    MaxSpeed = 177.0
    NormalTerrainAPConsumptionModifier = 1.0
    SpeedReducerTerrainAPConsumptionModifier = 1.0
    RoadAPConsumptionModifier = 1.0
    ConsumeAPOnMove = True
)
export StrategicMovementDescriptor_howitzer is TStrategicMovementModuleDescriptor
(
    MaxSpeed = 424.0
    NormalTerrainAPConsumptionModifier = 2.0
    SpeedReducerTerrainAPConsumptionModifier = 4.0
    RoadAPConsumptionModifier = 1.0
    ConsumeAPOnMove = True
)
export StrategicMovementDescriptor_mecanized is TStrategicMovementModuleDescriptor
(
    MaxSpeed = 177.0
    NormalTerrainAPConsumptionModifier = 2.0
    SpeedReducerTerrainAPConsumptionModifier = 4.0
    RoadAPConsumptionModifier = 1.0
    ConsumeAPOnMove = True
)

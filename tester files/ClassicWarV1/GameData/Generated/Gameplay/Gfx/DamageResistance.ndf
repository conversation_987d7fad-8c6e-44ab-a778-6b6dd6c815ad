// Ne pas éditer, ce fichier est généré par DamageResistanceContainerFileWriter

private DamageResistanceParams is TGameplayDamageResistanceContainer
(
    ResistanceFamilyDefinitionList = [
        TResistanceTypeFamilyDefinition(Family=ResistanceFamily_avion MaxIndex=3),
        TResistanceTypeFamilyDefinition(Family=ResistanceFamily_batiment MaxIndex=1),
        TResistanceTypeFamilyDefinition(Family=ResistanceFamily_blindage MaxIndex=30),
        TResistanceTypeFamilyDefinition(Family=ResistanceFamily_canon MaxIndex=3),
        TResistanceTypeFamilyDefinition(Family=ResistanceFamily_helico MaxIndex=3),
        TResistanceTypeFamilyDefinition(Family=ResistanceFamily_infanterie MaxIndex=3),
        TResistanceTypeFamilyDefinition(Family=ResistanceFamily_toit MaxIndex=2),
        TResistanceTypeFamilyDefinition(Family=ResistanceFamily_vehicule MaxIndex=3),
        TResistanceTypeFamilyDefinition(Family=ResistanceFamily_vehicule_leger MaxIndex=1),
    ]

    DamageFamilyDefinitionList = [
        TDamageTypeFamilyDefinition(Family=DamageFamily_ap MaxIndex=40),
        TDamageTypeFamilyDefinition(Family=DamageFamily_ap_missile MaxIndex=31),
        TDamageTypeFamilyDefinition(Family=DamageFamily_ap_missile_sead MaxIndex=3),
        TDamageTypeFamilyDefinition(Family=DamageFamily_artillerie MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_assaut MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_balle MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_balleaa MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_balledca MaxIndex=4),
        TDamageTypeFamilyDefinition(Family=DamageFamily_balle_mg MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_bombe MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_cac MaxIndex=4),
        TDamageTypeFamilyDefinition(Family=DamageFamily_clu_sol_ap MaxIndex=16),
        TDamageTypeFamilyDefinition(Family=DamageFamily_cluster MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_cluster_ap MaxIndex=6),
        TDamageTypeFamilyDefinition(Family=DamageFamily_fmballe MaxIndex=4),
        TDamageTypeFamilyDefinition(Family=DamageFamily_flamme MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_frag MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_grenades MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_he MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_he_dca MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_he_autocanon MaxIndex=2),
        TDamageTypeFamilyDefinition(Family=DamageFamily_howz MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_howz_bombe MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_mmgballe MaxIndex=3),
        TDamageTypeFamilyDefinition(Family=DamageFamily_missile_he MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_pmballe MaxIndex=3),
        TDamageTypeFamilyDefinition(Family=DamageFamily_roquette MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_smoke MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_superhe MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_superhe_sol MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_suppress MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_suppressap MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_thermobarique MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_roquette_ap MaxIndex=1),
        TDamageTypeFamilyDefinition(Family=DamageFamily_suppressdca MaxIndex=1),
    ]

    Values =
    [
        //AP_1
        [2.0, 1.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0, 1.0, 1.0, 0.0, 0.0, 0.0, 3, 1.0, 2.0, 1.0, 1.0, 2.0,],
        //AP_2
        [4.0, 2.0, 1.0, 0.0, 2.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.0, 2.0, 1.0, 0.0, 0.0, 0.0, 3, 1.0, 4.0, 2.0, 1.0, 4.0,],
        //AP_3
        [6.0, 3.0, 2.0, 0.0, 3.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 6.0, 3.0, 2.0, 0.0, 0.0, 0.0, 3, 1.0, 6.0, 3.0, 1.5, 6.0,],
        //AP_4
        [8.0, 4.0, 3.0, 0.0, 4.0, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.0, 4.0, 3.0, 0.0, 0.0, 0.0, 3, 1.0, 8.0, 4.0, 2.0, 8.0,],
        //AP_5
        [10.0, 5.0, 4.0, 0.0, 5.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.0, 5.0, 4.0, 0.0, 0.0, 0.0, 3, 1.0, 10.0, 5.0, 2.5, 10.0,],
        //AP_6
        [12.0, 6.0, 5.0, 0.0, 6.0, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 12.0, 6.0, 5.0, 0.0, 0.0, 0.0, 3, 1.0, 12.0, 6.0, 3.0, 12.0,],
        //AP_7
        [14.0, 7.0, 6.0, 0.0, 7.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 14.0, 7.0, 6.0, 0.0, 0.0, 0.0, 3, 1.0, 14.0, 7.0, 3.5, 14.0,],
        //AP_8
        [16.0, 8.0, 7.0, 0.0, 8.0, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 16.0, 8.0, 7.0, 0.0, 0.0, 0.0, 3, 1.0, 16.0, 8.0, 4.0, 16.0,],
        //AP_9
        [18.0, 9.0, 8.0, 0.0, 9.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 18.0, 9.0, 8.0, 0.0, 0.0, 0.0, 3, 1.0, 18.0, 9.0, 4.5, 18.0,],
        //AP_10
        [20.0, 10.0, 9.0, 0.0, 10.0, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 20.0, 10.0, 9.0, 0.0, 0.0, 0.0, 3, 1.0, 20.0, 10.0, 5.0, 20.0,],
        //AP_11
        [22.0, 11.0, 10.0, 0.0, 11.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 22.0, 11.0, 10.0, 0.0, 0.0, 0.0, 3, 1.0, 22.0, 11.0, 5.5, 22.0,],
        //AP_12
        [24.0, 12.0, 11.0, 0.0, 12.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 24.0, 12.0, 11.0, 0.0, 0.0, 0.0, 3, 1.0, 24.0, 12.0, 6.0, 24.0,],
        //AP_13
        [26.0, 13.0, 12.0, 0.0, 13.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 26.0, 13.0, 12.0, 0.0, 0.0, 0.0, 3, 1.0, 26.0, 13.0, 6.5, 26.0,],
        //AP_14
        [28.0, 14.0, 13.0, 0.0, 14.0, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 28.0, 14.0, 13.0, 0.0, 0.0, 0.0, 3, 1.0, 28.0, 14.0, 7.0, 28.0,],
        //AP_15
        [30.0, 15.0, 14.0, 0.0, 15.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 30.0, 15.0, 14.0, 0.0, 0.0, 0.0, 3, 1.0, 30.0, 15.0, 7.5, 30.0,],
        //AP_16
        [32.0, 16.0, 15.0, 0.0, 16.0, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 32.0, 16.0, 15.0, 0.0, 0.0, 0.0, 3, 1.0, 32.0, 16.0, 8.0, 32.0,],
        //AP_17
        [34.0, 17.0, 16.0, 0.0, 17.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 34.0, 17.0, 16.0, 0.0, 0.0, 0.0, 3, 1.0, 34.0, 17.0, 8.5, 34.0,],
        //AP_18
        [36.0, 18.0, 17.0, 0.0, 18.0, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 36.0, 18.0, 17.0, 0.0, 0.0, 0.0, 3, 1.0, 36.0, 18.0, 9.0, 36.0,],
        //AP_19
        [38.0, 19.0, 18.0, 0.0, 19.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 38.0, 19.0, 18.0, 0.0, 0.0, 0.0, 3, 1.0, 38.0, 19.0, 9.5, 38.0,],
        //AP_20
        [40.0, 20.0, 19.0, 0.0, 20.0, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 40.0, 20.0, 19.0, 0.0, 0.0, 0.0, 3, 1.0, 40.0, 20.0, 10.0, 40.0,],
        //AP_21
        [42.0, 21.0, 20.0, 0.0, 21.0, 10.0, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 42.0, 21.0, 20.0, 0.0, 0.0, 0.0, 3, 1.0, 42.0, 21.0, 10.0, 42.0,],
        //AP_22
        [44.0, 22.0, 21.0, 0.0, 22.0, 10.0, 10.0, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 44.0, 22.0, 21.0, 0.0, 0.0, 0.0, 3, 1.0, 44.0, 22.0, 10.0, 44.0,],
        //AP_23
        [46.0, 23.0, 22.0, 0.0, 23.0, 10.0, 10.0, 10.0, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 46.0, 23.0, 22.0, 0.0, 0.0, 0.0, 3, 1.0, 46.0, 23.0, 10.0, 46.0,],
        //AP_24
        [48.0, 24.0, 23.0, 0.0, 24.0, 10.0, 10.0, 10.0, 10.0, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 48.0, 24.0, 23.0, 0.0, 0.0, 0.0, 3, 1.0, 48.0, 24.0, 10.0, 48.0,],
        //AP_25
        [50.0, 25.0, 24.0, 0.0, 25.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 50.0, 25.0, 24.0, 0.0, 0.0, 0.0, 3, 1.0, 50.0, 25.0, 10.0, 50.0,],
        //AP_26
        [52.0, 26.0, 25.0, 0.0, 26.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 52.0, 26.0, 25.0, 0.0, 0.0, 0.0, 3, 1.0, 52.0, 26.0, 10.0, 52.0,],
        //AP_27
        [54.0, 27.0, 26.0, 0.0, 27.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 54.0, 27.0, 26.0, 0.0, 0.0, 0.0, 3, 1.0, 54.0, 27.0, 10.0, 54.0,],
        //AP_28
        [56.0, 28.0, 27.0, 0.0, 28.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 56.0, 28.0, 27.0, 0.0, 0.0, 0.0, 3, 1.0, 56.0, 28.0, 10.0, 56.0,],
        //AP_29
        [58.0, 29.0, 28.0, 0.0, 29.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 58.0, 29.0, 28.0, 0.0, 0.0, 0.0, 3, 1.0, 58.0, 29.0, 10.0, 58.0,],
        //AP_30
        [60.0, 30.0, 29.0, 0.0, 30.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 60.0, 30.0, 29.0, 0.0, 0.0, 0.0, 3, 1.0, 60.0, 30.0, 10.0, 60.0,],
        //AP_31
        [60.0, 30.0, 30.0, 0.0, 16.0, 15.5, 15.0, 14.5, 14.0, 13.5, 13.0, 12.5, 12.0, 11.5, 11.0, 10.5, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 0.0, 0.0, 0.0, 60.0, 30.0, 30.0, 0.0, 0.0, 0.0, 3, 1.0, 60.0, 30.0, 15.5, 60.0,],
        //AP_32
        [60.0, 30.0, 30.0, 0.0, 16.5, 16.0, 15.5, 15.0, 14.5, 14.0, 13.5, 13.0, 12.5, 12.0, 11.5, 11.0, 10.5, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 0.0, 0.0, 0.0, 60.0, 30.0, 30.0, 0.0, 0.0, 0.0, 3, 1.0, 60.0, 30.0, 16.0, 60.0,],
        //AP_33
        [60.0, 30.0, 30.0, 0.0, 17.0, 16.5, 16.0, 15.5, 15.0, 14.5, 14.0, 13.5, 13.0, 12.5, 12.0, 11.5, 11.0, 10.5, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 0.0, 0.0, 0.0, 60.0, 30.0, 30.0, 0.0, 0.0, 0.0, 3, 1.0, 60.0, 30.0, 16.5, 60.0,],
        //AP_34
        [60.0, 30.0, 30.0, 0.0, 17.5, 17.0, 16.5, 16.0, 15.5, 15.0, 14.5, 14.0, 13.5, 13.0, 12.5, 12.0, 11.5, 11.0, 10.5, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 0.0, 0.0, 0.0, 60.0, 30.0, 30.0, 0.0, 0.0, 0.0, 3, 1.0, 60.0, 30.0, 17.0, 60.0,],
        //AP_35
        [60.0, 30.0, 30.0, 0.0, 18.0, 17.5, 17.0, 16.5, 16.0, 15.5, 15.0, 14.5, 14.0, 13.5, 13.0, 12.5, 12.0, 11.5, 11.0, 10.5, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 0.0, 0.0, 0.0, 60.0, 30.0, 30.0, 0.0, 0.0, 0.0, 3, 1.0, 60.0, 30.0, 17.5, 60.0,],
        //AP_36
        [60.0, 30.0, 30.0, 0.0, 18.5, 18.0, 17.5, 17.0, 16.5, 16.0, 15.5, 15.0, 14.5, 14.0, 13.5, 13.0, 12.5, 12.0, 11.5, 11.0, 10.5, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 0.0, 0.0, 0.0, 60.0, 30.0, 30.0, 0.0, 0.0, 0.0, 3, 1.0, 60.0, 30.0, 18.0, 60.0,],
        //AP_37
        [60.0, 30.0, 30.0, 0.0, 19.0, 18.5, 18.0, 17.5, 17.0, 16.5, 16.0, 15.5, 15.0, 14.5, 14.0, 13.5, 13.0, 12.5, 12.0, 11.5, 11.0, 10.5, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 0.0, 0.0, 0.0, 60.0, 30.0, 30.0, 0.0, 0.0, 0.0, 3, 1.0, 60.0, 30.0, 18.5, 60.0,],
        //AP_38
        [60.0, 30.0, 30.0, 0.0, 19.5, 19.0, 18.5, 18.0, 17.5, 17.0, 16.5, 16.0, 15.5, 15.0, 14.5, 14.0, 13.5, 13.0, 12.5, 12.0, 11.5, 11.0, 10.5, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 0.0, 0.0, 0.0, 60.0, 30.0, 30.0, 0.0, 0.0, 0.0, 3, 1.0, 60.0, 30.0, 19.0, 60.0,],
        //AP_39
        [60.0, 30.0, 30.0, 0.0, 20.0, 19.5, 19.0, 18.5, 18.0, 17.5, 17.0, 16.5, 16.0, 15.5, 15.0, 14.5, 14.0, 13.5, 13.0, 12.5, 12.0, 11.5, 11.0, 10.5, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 0.0, 0.0, 0.0, 60.0, 30.0, 30.0, 0.0, 0.0, 0.0, 3, 1.0, 60.0, 30.0, 19.5, 60.0,],
        //AP_40
        [60.0, 30.0, 30.0, 0.0, 20.5, 20.0, 19.5, 19.0, 18.5, 18.0, 17.5, 17.0, 16.5, 16.0, 15.5, 15.0, 14.5, 14.0, 13.5, 13.0, 12.5, 12.0, 11.5, 11.0, 10.5, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 0.0, 0.0, 0.0, 60.0, 30.0, 30.0, 0.0, 0.0, 0.0, 3, 1.0, 60.0, 30.0, 20.0, 60.0,],
        //AP_Missile_1
        [2.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1, 0.0, 2.0, 1.0, 1.0, 2.0,],
        //AP_Missile_2
        [4.0, 2.0, 1.0, 0.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 4.0, 2.0, 1.0, 0.0, 0.0, 0.0, 4, 4.0, 4.0, 2.0, 1.0, 4.0,],
        //AP_Missile_3
        [6.0, 3.0, 1.0, 0.0, 3.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 6.0, 3.0, 1.0, 0.0, 0.0, 0.0, 6, 6.0, 6.0, 3.0, 1.5, 6.0,],
        //AP_Missile_4
        [8.0, 4.0, 1.0, 0.0, 4.0, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 8.0, 4.0, 1.0, 0.0, 0.0, 0.0, 8, 8.0, 8.0, 4.0, 2.0, 8.0,],
        //AP_Missile_5
        [10.0, 5.0, 1.0, 0.0, 5.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 10.0, 5.0, 1.0, 0.0, 0.0, 0.0, 10, 10.0, 10.0, 5.0, 2.5, 10.0,],
        //AP_Missile_6
        [12.0, 6.0, 1.0, 0.0, 6.0, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 12.0, 6.0, 1.0, 0.0, 0.0, 0.0, 12, 12.0, 12.0, 6.0, 3.0, 12.0,],
        //AP_Missile_7
        [14.0, 7.0, 1.0, 0.0, 7.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 14.0, 7.0, 1.0, 0.0, 0.0, 0.0, 14, 14.0, 14.0, 7.0, 3.5, 14.0,],
        //AP_Missile_8
        [16.0, 8.0, 1.0, 0.0, 8.0, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 16.0, 8.0, 1.0, 0.0, 0.0, 0.0, 16, 16.0, 16.0, 8.0, 4.0, 16.0,],
        //AP_Missile_9
        [18.0, 9.0, 1.0, 0.0, 9.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 18.0, 9.0, 1.0, 0.0, 0.0, 0.0, 18, 18.0, 18.0, 9.0, 4.5, 18.0,],
        //AP_Missile_10
        [20.0, 10.0, 1.0, 0.0, 10.0, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 20.0, 10.0, 1.0, 0.0, 0.0, 0.0, 20, 20.0, 20.0, 10.0, 5.0, 20.0,],
        //AP_Missile_11
        [22.0, 11.0, 1.0, 0.0, 11.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 22.0, 11.0, 1.0, 0.0, 0.0, 0.0, 22, 22.0, 22.0, 11.0, 5.5, 22.0,],
        //AP_Missile_12
        [24.0, 12.0, 1.0, 0.0, 12.0, 12.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 24.0, 12.0, 1.0, 0.0, 0.0, 0.0, 24, 24.0, 24.0, 12.0, 12.0, 24.0,],
        //AP_Missile_13
        [26.0, 13.0, 1.0, 0.0, 13.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 26.0, 13.0, 1.0, 0.0, 0.0, 0.0, 26, 26.0, 26.0, 13.0, 7.0, 26.0,],
        //AP_Missile_14
        [28.0, 14.0, 1.0, 0.0, 14.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 28.0, 14.0, 1.0, 0.0, 0.0, 0.0, 28, 28.0, 28.0, 14.0, 8.0, 28.0,],
        //AP_Missile_15
        [30.0, 15.0, 1.0, 0.0, 15.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 30.0, 15.0, 1.0, 0.0, 0.0, 0.0, 30, 30.0, 30.0, 15.0, 9.0, 30.0,],
        //AP_Missile_16
        [32.0, 16.0, 1.0, 0.0, 16.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 32.0, 16.0, 1.0, 0.0, 0.0, 0.0, 32, 32.0, 32.0, 16.0, 10.0, 32.0,],
        //AP_Missile_17
        [34.0, 17.0, 1.0, 0.0, 17.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 34.0, 17.0, 1.0, 0.0, 0.0, 0.0, 34, 34.0, 34.0, 17.0, 11.0, 34.0,],
        //AP_Missile_18
        [36.0, 18.0, 1.0, 0.0, 18.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 36.0, 18.0, 1.0, 0.0, 0.0, 0.0, 36, 36.0, 36.0, 18.0, 12.0, 36.0,],
        //AP_Missile_19
        [38.0, 19.0, 1.0, 0.0, 19.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 38.0, 19.0, 1.0, 0.0, 0.0, 0.0, 38, 38.0, 38.0, 19.0, 13.0, 38.0,],
        //AP_Missile_20
        [40.0, 20.0, 1.0, 0.0, 20.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 40.0, 20.0, 1.0, 0.0, 0.0, 0.0, 40, 40.0, 40.0, 20.0, 14.0, 40.0,],
        //AP_Missile_21
        [42.0, 21.0, 1.0, 0.0, 21.0, 15.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 42.0, 21.0, 1.0, 0.0, 0.0, 0.0, 42, 42.0, 42.0, 21.0, 15.0, 42.0,],
        //AP_Missile_22
        [44.0, 22.0, 1.0, 0.0, 22.0, 16.0, 15.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 44.0, 22.0, 1.0, 0.0, 0.0, 0.0, 44, 44.0, 44.0, 22.0, 16.0, 44.0,],
        //AP_Missile_23
        [46.0, 23.0, 1.0, 0.0, 23.0, 17.0, 16.0, 15.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 46.0, 23.0, 1.0, 0.0, 0.0, 0.0, 46, 46.0, 46.0, 23.0, 17.0, 46.0,],
        //AP_Missile_24
        [48.0, 24.0, 1.0, 0.0, 24.0, 18.0, 17.0, 16.0, 15.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 48.0, 24.0, 1.0, 0.0, 0.0, 0.0, 48, 48.0, 48.0, 24.0, 18.0, 48.0,],
        //AP_Missile_25
        [50.0, 25.0, 1.0, 0.0, 25.0, 19.0, 18.0, 17.0, 16.0, 15.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 50.0, 25.0, 1.0, 0.0, 0.0, 0.0, 50, 50.0, 50.0, 25.0, 19.0, 50.0,],
        //AP_Missile_26
        [52.0, 26.0, 1.0, 0.0, 26.0, 20.0, 19.0, 18.0, 17.0, 16.0, 15.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 52.0, 26.0, 1.0, 0.0, 0.0, 0.0, 52, 52.0, 52.0, 26.0, 20.0, 52.0,],
        //AP_Missile_27
        [54.0, 27.0, 1.0, 0.0, 27.0, 21.0, 20.0, 19.0, 18.0, 17.0, 16.0, 15.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 54.0, 27.0, 1.0, 0.0, 0.0, 0.0, 54, 54.0, 54.0, 27.0, 21.0, 54.0,],
        //AP_Missile_28
        [56.0, 28.0, 1.0, 0.0, 28.0, 22.0, 21.0, 20.0, 19.0, 18.0, 17.0, 16.0, 15.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 56.0, 28.0, 1.0, 0.0, 0.0, 0.0, 56, 56.0, 56.0, 28.0, 22.0, 56.0,],
        //AP_Missile_29
        [58.0, 29.0, 1.0, 0.0, 29.0, 23.0, 22.0, 21.0, 20.0, 19.0, 18.0, 17.0, 16.0, 15.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 0.0, 0.0, 0.0, 58.0, 29.0, 1.0, 0.0, 0.0, 0.0, 58, 58.0, 58.0, 29.0, 23.0, 58.0,],
        //AP_Missile_30
        [60.0, 30.0, 1.0, 0.0, 30.0, 24.0, 23.0, 22.0, 21.0, 20.0, 19.0, 18.0, 17.0, 16.0, 15.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 60.0, 30.0, 1.0, 0.0, 0.0, 0.0, 60, 60.0, 60.0, 30.0, 24.0, 60.0,],
        //AP_Missile_31
        [62.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 62.0, 1.0, 1.0, 0.0, 0.0, 0.0, 62, 62.0, 62.0, 30.0, 24.0, 62.0,],
        //AP_Missile_SEAD_1
        [40.0, 20.0, 1.0, 0.0, 20.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 40.0, 20.0, 1.0, 5.0, 0.0, 0.0, 40, 40.0, 40.0, 20.0, 14.0, 40.0,],
        //AP_Missile_SEAD_2
        [56.0, 28.0, 1.0, 0.0, 28.0, 22.0, 21.0, 20.0, 19.0, 18.0, 17.0, 16.0, 15.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 56.0, 28.0, 1.0, 5.0, 0.0, 0.0, 56, 56.0, 56.0, 28.0, 22.0, 56.0,],
        //AP_Missile_SEAD_3
        [60.0, 30.0, 1.0, 0.0, 30.0, 24.0, 23.0, 22.0, 21.0, 20.0, 19.0, 18.0, 17.0, 16.0, 15.0, 14.0, 13.0, 12.0, 11.0, 10.0, 9.0, 8.0, 7.0, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 60.0, 30.0, 1.0, 5.0, 0.0, 0.0, 60, 60.0, 60.0, 30.0, 24.0, 60.0,],
        //Artillerie
        [1.0, 1.0, 1.0, 1.0, 1.0, 0.95, 0.9, 0.86, 0.82, 0.8, 0.76, 0.72, 0.68, 0.65, 0.62, 0.59, 0.56, 0.53, 0.5, 0.48, 0.46, 0.44, 0.42, 0.4, 0.38, 0.36, 0.34, 0.32, 0.3, 0.29, 0.28, 0.27, 0.26, 0.25, 0.75, 0.5, 0.25, 1.0, 1.0, 1.0, 1.0, 0.5, 0.5, 8, 6.0, 4.0, 1.0, 0.95, 4.0,],
        //Assaut
        [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1, 0.0, 1.0, 1.0, 1.0, 1.0,],
        //Balle
        [1.0, 1.0, 1.0, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0, 0.0, 1.0, 0.0, 0.0, 1.0,],
        //BalleAA
        [1.0, 1.0, 1.0, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.2, 0.2, 0.2, 1.0, 1.0, 1.0, 0.2, 0.05, 0.05, 4, 0.5, 0.6, 0.0, 0.0, 0.6,],
        //BalleDCA_1
        [1.0, 0.9, 0.8, 0.1, 0.75, 0.5, 0.25, 0.1, 0.1, 0.1, 0.1, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 1.0, 1.0, 1.0, 2.5, 1.8, 1.7, 1.0, 1.0, 1.0, 1, 1.0, 1.25, 0.75, 0.5, 1.25,],
        //BalleDCA_2
        [1.0, 1.0, 0.8, 0.01, 1.5, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.8, 1.02, 0.5, 0.5, 0, 0.0, 2.0, 1.5, 1.0, 2.0,],
        //BalleDCA_3
        [1.0, 1.0, 0.8, 0.01, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.8, 1.36, 0.5, 0.5, 0, 0.0, 2.5, 2.0, 1.5, 2.5,],
        //BalleDCA_4
        [1.0, 1.0, 0.8, 0.01, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.8, 1.36, 0.5, 0.5, 0, 0.0, 2.5, 2.0, 1.5, 2.5,],
        //Balle_MG
        [1.0, 1.0, 1.0, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.1, 0.01, 0, 0.0, 3.0, 0.0, 0.0, 3.0,],
        //Bombe
        [1.0, 1.0, 1.0, 1.0, 0.75, 0.71, 0.67, 0.64, 0.61, 0.58, 0.55, 0.52, 0.49, 0.47, 0.45, 0.43, 0.41, 0.39, 0.37, 0.35, 0.33, 0.31, 0.29, 0.28, 0.27, 0.26, 0.25, 0.24, 0.23, 0.22, 0.21, 0.2, 0.19, 0.18, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5, 0.5, 8, 6.0, 2.0, 0.75, 0.71, 2.0,],
        //CAC_1
        [1.0, 1.0, 1.0, 1.5, 1.0, 1.0, 1.0, 1.0, 0.5, 0.4, 0.3, 0.2, 0.15, 0.1, 0.1, 0.05, 0.05, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8, 1.0, 1.25, 1.0, 1.0, 1.25,],
        //CAC_2
        [1.0, 1.0, 1.0, 1.25, 1.0, 1.0, 0.75, 0.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8, 1.0, 1.5, 1.5, 1.5, 1.5,],
        //CAC_3
        [1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.5, 0.4, 0.3, 0.2, 0.15, 0.1, 0.1, 0.05, 0.05, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8, 1.0, 1.0, 1.0, 1.0, 1.0,],
        //CAC_4
        [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5, 0.4, 0.3, 0.2, 0.15, 0.1, 0.1, 0.05, 0.05, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8, 1.0, 1.0, 1.0, 1.0, 1.0,],
        //Clu_sol_AP_1
        [3.0, 3.0, 2.0, 0.1, 3.0, 2.25, 1.7, 1.0, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 5.5, 5.5, 3.0, 5.5,],
        //Clu_sol_AP_2
        [3.0, 3.0, 2.0, 0.1, 3.0, 2.25, 1.7, 1.2, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 5.5, 5.5, 3.0, 5.5,],
        //Clu_sol_AP_3
        [3.0, 3.0, 2.0, 0.1, 3.0, 2.35, 1.8, 1.2, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 5.5, 5.5, 3.0, 5.5,],
        //Clu_sol_AP_4
        [3.0, 3.0, 2.0, 0.1, 3.0, 2.35, 1.8, 1.2, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 5.5, 5.5, 3.0, 5.5,],
        //Clu_sol_AP_5
        [3.0, 3.0, 2.0, 0.1, 2.7, 2.0, 1.32, 1.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 8.0, 8.0, 3.0, 8.0,],
        //Clu_sol_AP_6
        [3.0, 3.0, 2.0, 0.1, 2.7, 2.0, 1.32, 1.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 8.0, 8.0, 3.0, 8.0,],
        //Clu_sol_AP_7
        [3.0, 3.0, 2.0, 0.1, 2.7, 2.0, 1.32, 1.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 8.0, 8.0, 3.0, 8.0,],
        //Clu_sol_AP_8
        [3.0, 3.0, 2.0, 0.1, 2.7, 2.0, 1.32, 1.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 8.0, 8.0, 3.0, 8.0,],
        //Clu_sol_AP_9
        [3.0, 3.0, 2.0, 0.1, 2.7, 2.0, 1.32, 1.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 8.0, 8.0, 3.0, 8.0,],
        //Clu_sol_AP_10
        [3.0, 3.0, 2.0, 0.1, 4.5, 2.75, 1.65, 1.35, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 6.5, 6.5, 3.0, 6.5,],
        //Clu_sol_AP_11
        [3.0, 3.0, 2.0, 0.1, 4.5, 2.75, 1.65, 1.35, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 6.5, 6.5, 3.0, 6.5,],
        //Clu_sol_AP_12
        [3.0, 3.0, 2.0, 0.1, 4.5, 2.75, 1.65, 1.35, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 6.5, 6.5, 3.0, 6.5,],
        //Clu_sol_AP_13
        [3.0, 3.0, 2.0, 0.1, 4.5, 2.75, 1.65, 1.35, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 6.5, 6.5, 3.0, 6.5,],
        //Clu_sol_AP_14
        [3.0, 3.0, 2.0, 0.1, 4.5, 2.75, 1.65, 1.35, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 6.5, 6.5, 3.0, 6.5,],
        //Clu_sol_AP_15
        [3.0, 3.0, 2.0, 0.1, 3.0, 1.75, 1.0, 0.8, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 10.0, 10.0, 3.0, 10.0,],
        //Clu_sol_AP_16
        [3.0, 3.0, 2.0, 0.1, 11.75, 10.5, 4.25, 3.75, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.0, 0.0, 0.0, 8, 6.0, 10.0, 10.0, 3.0, 10.0,],
        //Cluster
        [3.0, 2.0, 2.0, 0.1, 0.5, 0.25, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.75, 0.75, 0.75, 3.0, 2.0, 2.0, 4.0, 4.0, 4.0, 8, 0.1, 3.0, 2.0, 1.0, 3.0,],
        //Cluster_AP_1
        [3.0, 3.0, 2.0, 0.1, 6.0, 3.0, 2.5, 2.0, 1.5, 1.0, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 12.0, 6.0, 3.0, 4.0,],
        //Cluster_AP_2
        [3.0, 3.0, 2.0, 0.1, 6.0, 3.0, 2.5, 2.0, 1.5, 1.0, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 12.0, 6.0, 3.0, 4.0,],
        //Cluster_AP_3
        [3.0, 3.0, 2.0, 0.1, 6.0, 3.0, 2.5, 2.0, 1.5, 1.0, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 12.0, 6.0, 3.0, 4.0,],
        //Cluster_AP_4
        [3.0, 3.0, 2.0, 0.1, 6.0, 3.0, 2.5, 2.0, 1.5, 1.0, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 12.0, 6.0, 3.0, 4.0,],
        //Cluster_AP_5
        [3.0, 3.0, 2.0, 0.1, 6.0, 3.0, 2.5, 2.0, 1.5, 1.0, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 12.0, 6.0, 3.0, 4.0,],
        //Cluster_AP_6
        [3.0, 3.0, 2.0, 0.1, 6.0, 3.0, 2.5, 2.0, 1.5, 1.0, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.5, 0.5, 0.5, 3.0, 3.0, 2.0, 0.5, 0.5, 0.5, 8, 6.0, 12.0, 6.0, 3.0, 4.0,],
        //FMballe_1
        [0.8, 0.4, 0.2, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.5, 0.25, 1.0, 1.0, 1.0, 0, 0.0, 1.25, 0.25, 0.1, 1.25,],
        //FMballe_2
        [0.4, 0.2, 0.1, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.4, 0.2, 0.1, 1.0, 1.0, 1.0, 0, 0.0, 1.0, 0.5, 0.1, 1.0,],
        //FMballe_3
        [0.4, 0.2, 0.1, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.4, 0.2, 0.1, 1.6, 1.6, 1.6, 0, 0.0, 1.25, 0.75, 0.25, 1.25,],
        //FMballe_4
        [0.4, 0.2, 0.1, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.4, 0.2, 0.1, 3.0, 3.0, 3.0, 0, 0.0, 1.25, 0.75, 0.25, 1.25,],
        //Flamme
        [1.0, 1.0, 1.0, 1.0, 1.75, 1.66, 1.58, 1.5, 1.43, 1.36, 1.29, 1.23, 1.17, 1.11, 1.05, 1.0, 0.95, 0.9, 0.86, 0.82, 0.78, 0.74, 0.7, 0.67, 0.64, 0.61, 0.58, 0.55, 0.52, 0.49, 0.47, 0.45, 0.43, 0.41, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8, 0.5, 3.3, 1.75, 1.66, 3.3,],
        //Frag
        [1.0, 1.0, 1.0, 1.0, 1.25, 1.19, 1.13, 1.07, 1.02, 0.97, 0.92, 0.87, 0.83, 0.79, 0.75, 0.71, 0.67, 0.64, 0.61, 0.58, 0.55, 0.52, 0.49, 0.47, 0.45, 0.43, 0.41, 0.39, 0.37, 0.35, 0.33, 0.31, 0.29, 0.28, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5, 0.5, 8, 0.5, 4.0, 1.25, 1.19, 4.0,],
        //Grenades
        [1.0, 1.0, 1.0, 1.0, 1.0, 0.8, 0.4, 0.2, 0.15, 0.1, 0.1, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.85, 0.85, 0.85, 8, 1.0, 1.0, 0.8, 0.4, 1.0,],
        //HE
        [1.0, 1.0, 1.0, 1.0, 0.8, 0.4, 0.3, 0.2, 0.15, 0.1, 0.1, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8, 1.0, 1.25, 0.8, 0.4, 1.25,],
        //HE_DCA
        [1.2, 1.1, 1.0, 0.3, 1.0, 0.75, 0.5, 0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 2.5, 1.8, 1.0, 1.0, 1.0, 1.0, 8, 1.0, 3.3, 0.8, 0.4, 3.3,],
        //HE_autocanon_1
        [1.2, 1.1, 1.0, 0.3, 2.0, 1.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 2.5, 1.8, 1.7, 1.0, 1.0, 1.0, 8, 1.0, 3.3, 0.8, 0.4, 3.3,],
        //HE_autocanon_2
        [1.0, 1.0, 1.0, 0.3, 1.3333, 0.6666, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.5, 0.0, 1.0, 1.0, 1.0, 8, 1.0, 2.0, 1.67, 1.0, 2.0,],
        //Howz
        [3.0, 2.0, 1.0, 1.33, 1.0, 1.0, 1.0, 0.75, 0.75, 0.75, 0.75, 0.75, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 1.0, 1.0, 1.0, 3.0, 2.0, 1.0, 1.5, 1.0, 1.0, 1, 1.0, 1.25, 1.0, 1.0, 1.25,],
        //Howz_bombe
        [3.0, 2.0, 1.0, 1.33, 1.0, 0.75, 0.5, 0.375, 0.375, 0.375, 0.375, 0.375, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.125, 0.125, 0.125, 0.125, 0.125, 0.125, 0.125, 0.125, 0.125, 0.125, 0.125, 0.125, 0.125, 0.125, 0.125, 0.125, 1.0, 1.0, 1.0, 3.0, 2.0, 1.0, 1.5, 1.0, 1.0, 1, 1.0, 1.25, 0.75, 0.75, 1.25,],
        //MMGballe_1
        [1.0, 1.0, 1.0, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0, 0.0, 0.1, 0.0, 0.0, 0.1,],
        //MMGballe_2
        [1.0, 1.0, 1.0, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0, 0.0, 0.1, 0.0, 0.0, 0.1,],
        //MMGballe_3
        [1.0, 1.0, 1.0, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0, 0.0, 1.0, 0.0, 0.0, 1.0,],
        //Missile_HE
        [1.0, 0.9, 0.8, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8, 1.0, 1.0, 1.0, 1.0, 1.0,],
        //PMballe_1
        [1.0, 1.0, 1.0, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.1, 0.1, 0.1, 0, 0.0, 0.1, 0.0, 0.0, 0.1,],
        //PMballe_2
        [1.0, 1.0, 1.0, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5, 0.5, 0.5, 0, 0.0, 0.1, 0.0, 0.0, 0.1,],
        //PMballe_3
        [1.0, 1.0, 1.0, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0, 0.0, 1.0, 0.0, 0.0, 1.0,],
        //Roquette
        [1.0, 1.0, 1.0, 1.0, 1.0, 0.95, 0.9, 0.86, 0.82, 0.78, 0.74, 0.7, 0.67, 0.64, 0.61, 0.58, 0.55, 0.52, 0.49, 0.47, 0.45, 0.43, 0.41, 0.39, 0.37, 0.35, 0.33, 0.31, 0.29, 0.28, 0.27, 0.26, 0.25, 0.24, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5, 0.5, 8, 0.5, 2.0, 1.0, 0.95, 2.0,],
        //Smoke
        [1.0, 1.0, 1.0, 1.0, 0.5, 0.48, 0.46, 0.44, 0.42, 0.4, 0.38, 0.36, 0.34, 0.32, 0.3, 0.29, 0.28, 0.27, 0.26, 0.25, 0.24, 0.23, 0.22, 0.21, 0.2, 0.19, 0.18, 0.17, 0.16, 0.15, 0.14, 0.13, 0.12, 0.11, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5, 0.5, 8, 1.0, 4.0, 0.5, 0.48, 4.0,],
        //SuperHE
        [1.0, 0.9, 0.8, 0.25, 0.75, 0.5, 0.25, 0.1, 0.1, 0.1, 0.1, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1, 1.0, 1.25, 0.75, 0.5, 1.25,],
        //SuperHE_Sol
        [1.0, 1.0, 1.0, 0.25, 0.75, 0.5, 0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8, 1.0, 1.0, 0.75, 0.5, 1.0,],
        //Suppress
        [1.0, 1.0, 1.0, 0.5, 0.8, 0.76, 0.72, 0.68, 0.65, 0.62, 0.59, 0.56, 0.53, 0.5, 0.48, 0.46, 0.44, 0.42, 0.4, 0.38, 0.36, 0.34, 0.32, 0.3, 0.29, 0.28, 0.27, 0.26, 0.25, 0.24, 0.23, 0.22, 0.21, 0.2, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 4, 2.0, 1.25, 0.8, 0.76, 1.25,],
        //SuppressAP
        [1.0, 0.9, 0.8, 1.0, 2.0, 1.9, 1.8, 1.7, 1.6, 1.5, 1.4, 1.3, 1.2, 1.1, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.25, 0.25, 0.25, 1.0, 0.9, 0.8, 0.75, 0.5, 0.5, 4, 2.0, 0.9, 0.8, 0.7, 1.0,],
        //Thermobarique
        [1.0, 1.0, 1.0, 0.0, 1.0, 0.5, 0.25, 0.1, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1, 1.0, 1.5, 1.0, 1.0, 2.0,],
        //roquette_ap_1
        [1.0, 1.0, 1.0, 1.0, 1.0, 0.9, 0.8, 0.5, 0.5, 0.5, 0.25, 0.25, 0.25, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1, 1.0, 1.25, 0.75, 0.5, 1.25,],
        //suppressDCA
        [1.0, 1.0, 1.0, 1.0, 0.8, 0.4, 0.3, 0.2, 0.15, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 1.0, 1.0, 1.0, 1.0, 0.9, 0.8, 1.0, 1.0, 1.0, 8, 1.0, 1.0, 0.8, 0.4, 1.0,],
    ]
)

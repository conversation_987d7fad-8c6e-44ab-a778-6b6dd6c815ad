// Ne pas éditer, ce fichier est généré par FireDescriptorFileWriter_Specific


export Descriptor_Fire_Incendie is TEntityDescriptor
(
    World              = WorldIndices_Fires
    DescriptorId       = GUID:{329c7b13-6b92-473c-b24f-0212c1c6a252}
    ClassNameForDebug  = 'Fire_Incendie'
    ModulesDescriptors = [
        ~/SimpleTypeUnitModuleDescriptor,
        ~/EmptyTagsModuleDescriptor,
        ~/FirePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TApparenceModuleDescriptor
        (
            ReferenceMesh  = $/GFX/DepictionResources/Rien
            Depiction  = Template_DescriptorFire_Depiction
            (
                Radius               = 27 * 26 * 2.83
                FX                   = $/GFX/GameFx/fx_binder_fire
                Density              = 1
            )
            DefaultVisibility  = True
        ),
        TFireModuleDescriptor
        (
            TimeToLive                     = 60
            TimeBeforeSpreading            = 10
            TimeBeforeSpreadAttempt        = 10
            SpreadProbability              = 0.5
            IsNewFireProbability           = 0.5
            RadiusGRU                      = 27
            AmmunitionForBurn              = $/GFX/Weapon/Ammo_Degats_incendie
            TimeBetweenBurns               = 5
            IgniteDistricts                = False
            OverridenSpreadDescriptor      = nil
            SmokeDescriptor                = nil
        ),
    ]
)
export Descriptor_Fire_Napalm is TEntityDescriptor
(
    World              = WorldIndices_Fires
    DescriptorId       = GUID:{38066a34-ab59-4a5e-98c2-9c65879f679e}
    ClassNameForDebug  = 'Fire_Napalm'
    ModulesDescriptors = [
        ~/SimpleTypeUnitModuleDescriptor,
        ~/EmptyTagsModuleDescriptor,
        ~/FirePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TApparenceModuleDescriptor
        (
            ReferenceMesh  = $/GFX/DepictionResources/Rien
            Depiction  = Template_DescriptorFire_Depiction
            (
                Radius               = 177 * 26 * 2.83
                FX                   = $/GFX/GameFx/fx_binder_fire
                Density              = 1
            )
            DefaultVisibility  = True
        ),
        TFireModuleDescriptor
        (
            TimeToLive                     = 60
            TimeBeforeSpreading            = 1
            TimeBeforeSpreadAttempt        = 1
            SpreadProbability              = 1.0
            IsNewFireProbability           = 0.5
            RadiusGRU                      = 177
            AmmunitionForBurn              = $/GFX/Weapon/Ammo_Degats_napalm
            TimeBetweenBurns               = 5
            IgniteDistricts                = False
            OverridenSpreadDescriptor      = Descriptor_Fire_Incendie
            SmokeDescriptor                = nil
        ),
    ]
)
export Descriptor_Fire_NapalmLeger is TEntityDescriptor
(
    World              = WorldIndices_Fires
    DescriptorId       = GUID:{13815b3d-41eb-46d1-8ab4-703566fb3f26}
    ClassNameForDebug  = 'Fire_NapalmLeger'
    ModulesDescriptors = [
        ~/SimpleTypeUnitModuleDescriptor,
        ~/EmptyTagsModuleDescriptor,
        ~/FirePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TApparenceModuleDescriptor
        (
            ReferenceMesh  = $/GFX/DepictionResources/Rien
            Depiction  = Template_DescriptorFire_Depiction
            (
                Radius               = 53 * 26 * 2.83
                FX                   = $/GFX/GameFx/fx_binder_fire_Napalm_Leger
                Density              = 1
            )
            DefaultVisibility  = True
        ),
        TFireModuleDescriptor
        (
            TimeToLive                     = 60
            TimeBeforeSpreading            = 1
            TimeBeforeSpreadAttempt        = 1
            SpreadProbability              = 1.0
            IsNewFireProbability           = 0.5
            RadiusGRU                      = 53
            AmmunitionForBurn              = $/GFX/Weapon/Ammo_Degats_napalm_leger
            TimeBetweenBurns               = 5
            IgniteDistricts                = False
            OverridenSpreadDescriptor      = Descriptor_Fire_Incendie
            SmokeDescriptor                = nil
        ),
    ]
)
export Descriptor_Fire_NapalmLourd is TEntityDescriptor
(
    World              = WorldIndices_Fires
    DescriptorId       = GUID:{390d2ca4-fd9d-4341-a998-10a910f7b03f}
    ClassNameForDebug  = 'Fire_NapalmLourd'
    ModulesDescriptors = [
        ~/SimpleTypeUnitModuleDescriptor,
        ~/EmptyTagsModuleDescriptor,
        ~/FirePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TApparenceModuleDescriptor
        (
            ReferenceMesh  = $/GFX/DepictionResources/Rien
            Depiction  = Template_DescriptorFire_Depiction
            (
                Radius               = 177 * 26 * 2.83
                FX                   = $/GFX/GameFx/fx_binder_fire_Napalm_Lourd
                Density              = 1
            )
            DefaultVisibility  = True
        ),
        TFireModuleDescriptor
        (
            TimeToLive                     = 60
            TimeBeforeSpreading            = 1
            TimeBeforeSpreadAttempt        = 1
            SpreadProbability              = 1.0
            IsNewFireProbability           = 0.5
            RadiusGRU                      = 177
            AmmunitionForBurn              = $/GFX/Weapon/Ammo_Degats_napalm_lourd
            TimeBetweenBurns               = 1
            IgniteDistricts                = False
            OverridenSpreadDescriptor      = Descriptor_Fire_Incendie
            SmokeDescriptor                = nil
        ),
    ]
)
export Descriptor_Fire_NapalmMoyen is TEntityDescriptor
(
    World              = WorldIndices_Fires
    DescriptorId       = GUID:{9f8584c1-0d2c-40fd-888e-b5f9a86597ff}
    ClassNameForDebug  = 'Fire_NapalmMoyen'
    ModulesDescriptors = [
        ~/SimpleTypeUnitModuleDescriptor,
        ~/EmptyTagsModuleDescriptor,
        ~/FirePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TApparenceModuleDescriptor
        (
            ReferenceMesh  = $/GFX/DepictionResources/Rien
            Depiction  = Template_DescriptorFire_Depiction
            (
                Radius               = 106 * 26 * 2.83
                FX                   = $/GFX/GameFx/fx_binder_fire_Napalm_Moyen
                Density              = 1
            )
            DefaultVisibility  = True
        ),
        TFireModuleDescriptor
        (
            TimeToLive                     = 60
            TimeBeforeSpreading            = 1
            TimeBeforeSpreadAttempt        = 1
            SpreadProbability              = 1.0
            IsNewFireProbability           = 0.5
            RadiusGRU                      = 106
            AmmunitionForBurn              = $/GFX/Weapon/Ammo_Degats_napalm_buratino
            TimeBetweenBurns               = 1
            IgniteDistricts                = False
            OverridenSpreadDescriptor      = Descriptor_Fire_Incendie
            SmokeDescriptor                = nil
        ),
    ]
)

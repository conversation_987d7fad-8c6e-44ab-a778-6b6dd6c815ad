// Ne pas éditer, ce fichier est généré par EffectNDFFileWriter


export UnitEffect_A_BonusType_Calcul is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{4970497b-2c6c-474a-ae2f-ce369ab9772a}
    NameForDebug       = 'A_BonusType_Calcul'
    EffectsDescriptors = [
    ]
)
export UnitEffect_AirUnit_Cohesion_Low is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{9e4a17c5-6dbe-4233-82ae-7f4c04e3320b}
    NameForDebug       = 'AirUnit_Cohesion_Low'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponDispersionMaxRangeDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.20
        ),
    ]
)
export UnitEffect_AirUnit_Cohesion_Mediocre is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{db8eb827-206a-4425-b5b8-12c49dacc716}
    NameForDebug       = 'AirUnit_Cohesion_Mediocre'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponDispersionMaxRangeDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.15
        ),
    ]
)
export UnitEffect_AirUnit_Cohesion_Normal is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{1dd39425-d1ec-4db6-9694-73e85053dbdc}
    NameForDebug       = 'AirUnit_Cohesion_Normal'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponDispersionMaxRangeDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.10
        ),
    ]
)
export UnitEffect_Ajoute_Tag_GSR_ok is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{fc3f2bf6-df0f-4481-97c6-e3ef7dcc0be2}
    NameForDebug       = 'Ajoute_Tag_GSR_ok'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GSR_ok",
            ]
        ),
    ]
)
export UnitEffect_Ajoute_Tag_Hors_Transport is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{bf7bb820-d8fd-4014-ab2a-dcef35331994}
    NameForDebug       = 'Ajoute_Tag_Hors_Transport'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Hors_Transport",
            ]
        ),
    ]
)
export UnitEffect_Ajoute_Tag_Trepied_ok is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{1148ca27-f929-4324-bf78-9d121f03ee56}
    NameForDebug       = 'Ajoute_Tag_Trepied_ok'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Trepied_ok",
            ]
        ),
    ]
)
export UnitEffect_Ajoute_Tag_no_snipe is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{0993037f-694f-418c-99f7-6c9c9549c6fb}
    NameForDebug       = 'Ajoute_Tag_no_snipe'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "no_snipe",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_sniper_off"
        ),
    ]
)
export UnitEffect_Ajoute_Tag_snipe_ok is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{2ee5a140-e7a7-43fd-95ef-0abb7a8d6721}
    NameForDebug       = 'Ajoute_Tag_snipe_ok'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "snipe_ok",
            ]
        ),
    ]
)
export UnitEffect_Ajoute_Tag_xp_elite is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{81a303b4-7674-4df9-9a01-86488425b3ea}
    NameForDebug       = 'Ajoute_Tag_xp_elite'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_elite",
            ]
        ),
    ]
)
export UnitEffect_Ajoute_Tag_xp_regular is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{0d6292f3-eba0-4500-8776-eba7fc8aa30c}
    NameForDebug       = 'Ajoute_Tag_xp_regular'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_regular",
            ]
        ),
    ]
)
export UnitEffect_Ajoute_Tag_xp_veteran is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{e85b86d6-e498-480f-9307-fb10c548f8fe}
    NameForDebug       = 'Ajoute_Tag_xp_veteran'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_veteran",
            ]
        ),
    ]
)
export UnitEffect_AntiBouclier_Suppression_33 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{5662ad12-73e0-4015-b4e0-45c7df6aa00a}
    NameForDebug       = 'AntiBouclier_Suppression_33'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = 33
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_ArmyGen_No_regen_PA is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{8c3f93a7-5d9d-43be-ba0e-dc6a90dc9804}
    NameForDebug       = 'ArmyGen_No_regen_PA'
    EffectsDescriptors = [
        TStrategicSupplyMalusEffectDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            SupplyMalus        = 0
        ),
    ]
)
export UnitEffect_ArtInf_Cowering is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{eb9591b0-e6c5-40fa-9286-1afa83dd162b}
    NameForDebug       = 'ArtInf_Cowering'
    EffectsDescriptors = [
        TUnitEffectIncreaseInfluenceValueDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            Bonus              = 0.66
        ),
        TUnitEffectIncreaseWeaponPorteeMaxDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectIncreaseWeaponPorteeMaxHADescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectIncreaseWeaponPorteeMaxProjectileDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectIncreaseWeaponPorteeMaxTBADescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
                 "Infanterie_Suppressed",
                 "Infanterie_Pinned",
                 "Infanterie_Cowering",
                 "Bloque_MoveAndAttack",
            ]
        ),
    ]
)
export UnitEffect_ArtInf_Engaged is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{7dd188b5-009e-4414-bbd1-c0e96f214f22}
    NameForDebug       = 'ArtInf_Engaged'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 25
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.1
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxTirsDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.1
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
            ]
        ),
    ]
)
export UnitEffect_ArtInf_Pinned is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{9cc749fb-e3ff-4a70-80e0-90977416a133}
    NameForDebug       = 'ArtInf_Pinned'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 150
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.70
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxTirsDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.70
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
                 "Infanterie_Suppressed",
                 "Infanterie_Pinned",
            ]
        ),
    ]
)
export UnitEffect_ArtInf_Stressed is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{4a6faaa5-54ce-4462-a15b-7c157114b7c5}
    NameForDebug       = 'ArtInf_Stressed'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 50
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.25
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxTirsDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.25
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
            ]
        ),
    ]
)
export UnitEffect_ArtInf_Suppressed is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{ea03514d-5098-443d-ac20-d49bf87ced5c}
    NameForDebug       = 'ArtInf_Suppressed'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 100
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.45
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxTirsDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.45
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
                 "Infanterie_Suppressed",
            ]
        ),
    ]
)
export UnitEffect_Artillerie_Cowering is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{6cb9455a-6871-4878-aa32-dc6465a88c96}
    NameForDebug       = 'Artillerie_Cowering'
    EffectsDescriptors = [
        TUnitEffectIncreaseInfluenceValueDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            Bonus              = 0.66
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
                 "GroundUnit_Stressed",
                 "GroundUnit_Suppressed",
                 "GroundUnit_Pinned",
                 "GroundUnit_Cowering",
            ]
        ),
    ]
)
export UnitEffect_Artillerie_Engaged is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{29d2b108-ad95-4d69-9c63-550a186ccc0c}
    NameForDebug       = 'Artillerie_Engaged'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 25
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.1
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
            ]
        ),
    ]
)
export UnitEffect_Artillerie_Pinned is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{17627599-bf0e-4f1e-869a-cd3735509f6c}
    NameForDebug       = 'Artillerie_Pinned'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 150
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.70
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
                 "GroundUnit_Stressed",
                 "GroundUnit_Suppressed",
                 "GroundUnit_Pinned",
            ]
        ),
    ]
)
export UnitEffect_Artillerie_Stressed is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{ee2d6fd4-4df8-47f5-bd98-7f37fcc8285b}
    NameForDebug       = 'Artillerie_Stressed'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 50
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.25
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
                 "GroundUnit_Stressed",
            ]
        ),
    ]
)
export UnitEffect_Artillerie_Suppressed is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{ff5a1f57-0361-4a87-9f27-b1bd988245d6}
    NameForDebug       = 'Artillerie_Suppressed'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 100
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.45
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
                 "GroundUnit_Stressed",
                 "GroundUnit_Suppressed",
            ]
        ),
    ]
)
export UnitEffect_ArtyUnit_Cohesion_High is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{b81ebc33-bcb1-4a6f-b0de-d4a7eab93b76}
    NameForDebug       = 'ArtyUnit_Cohesion_High'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
            ]
        ),
    ]
)
export UnitEffect_ArtyUnit_Cohesion_Low is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{ac339b28-f3ea-4240-9123-1daad25211ae}
    NameForDebug       = 'ArtyUnit_Cohesion_Low'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusChassisRotationSpeed  = -50
        ),
        TUnitEffectIncreaseTurretRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusTurretRotationSpeed  = -50
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Pourcentage
            BonusSpeedBaseInPercent  = -15
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.18
        ),
        TUnitEffectIncreaseWeaponDispersionMaxRangeDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 2
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.40
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
                 "GroundUnit_Stressed",
                 "GroundUnit_Suppressed",
                 "GroundUnit_Pinned",
            ]
        ),
    ]
)
export UnitEffect_ArtyUnit_Cohesion_Mediocre is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{2d2b32d1-7b23-4a5a-a268-ee7d5679246e}
    NameForDebug       = 'ArtyUnit_Cohesion_Mediocre'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusChassisRotationSpeed  = -25
        ),
        TUnitEffectIncreaseTurretRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusTurretRotationSpeed  = -25
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Pourcentage
            BonusSpeedBaseInPercent  = -10
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.12
        ),
        TUnitEffectIncreaseWeaponDispersionMaxRangeDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.40
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.20
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
                 "GroundUnit_Stressed",
                 "GroundUnit_Suppressed",
            ]
        ),
    ]
)
export UnitEffect_ArtyUnit_Cohesion_Normal is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{1b20d33c-1efc-4631-9d12-f30210592c29}
    NameForDebug       = 'ArtyUnit_Cohesion_Normal'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusChassisRotationSpeed  = -10
        ),
        TUnitEffectIncreaseTurretRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusTurretRotationSpeed  = -10
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Pourcentage
            BonusSpeedBaseInPercent  = -5
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.08
        ),
        TUnitEffectIncreaseWeaponDispersionMaxRangeDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.20
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.10
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
                 "GroundUnit_Stressed",
            ]
        ),
    ]
)
export UnitEffect_Aura_Commandement_Plus is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{208b3aea-db04-438b-ab04-7567942fe1f3}
    NameForDebug       = 'Aura_Commandement_Plus'
    EffectsDescriptors = [
        TUnitEffectBonusExperienceLevelDescriptor
        (
            ModifierType            = ~/ModifierType_Remplacement
            ExperienceLevelModifier = 2
        ),
    ]
)
export UnitEffect_Baisse_Dangerosite is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{e4068e74-978a-4b40-952b-7db784bac4da}
    NameForDebug       = 'Baisse_Dangerosite'
    EffectsDescriptors = [
        TUnitEffectIncreaseDangerousnessDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDangerousness    = -90
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Camion_autodestruction",
                 "Panique",
            ]
        ),
    ]
)
export UnitEffect_Baisse_Dangerosite_et_Transport_0_Effet is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{f528c787-2af7-4f88-a80d-2443399029f9}
    NameForDebug       = 'Baisse_Dangerosite_et_Transport_0_Effet'
    EffectsDescriptors = [
        TUnitEffectIncreaseDangerousnessDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDangerousness    = -90
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Camion_autodestruction",
                 "Panique",
            ]
        ),
        TUnitEffectTransportSlotNumberModificationDescriptor
        (
            ModifierType                 = ~/ModifierType_Remplacement
            EffectOnTransportSlotNumber  = 0
        ),
    ]
)
export UnitEffect_Bouclier_Suppression_100 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{71c7c8e9-76e2-4807-b991-f2eaffd304cc}
    NameForDebug       = 'Bouclier_Suppression_100'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -100
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Bouclier_Suppression_33 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{e8f446a5-bb0d-4b61-8b3e-64c7f646cc12}
    NameForDebug       = 'Bouclier_Suppression_33'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -33
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Bouclier_Suppression_50 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{3e6f4d8d-b5c0-4f5b-b2aa-62694bd3c8e1}
    NameForDebug       = 'Bouclier_Suppression_50'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -50
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Bouclier_Suppression_66 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{1bb53e01-d72c-4a5b-a36a-9ee541ddf4cf}
    NameForDebug       = 'Bouclier_Suppression_66'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -66
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Bouclier_Suppression_99 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{7368a0cf-3d6b-4b45-8714-761365faf4d3}
    NameForDebug       = 'Bouclier_Suppression_99'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -99
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Bouclier_Suppression_IFV is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{031f2530-390e-4c84-9a0d-53e492fc4f16}
    NameForDebug       = 'Bouclier_Suppression_IFV'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -33
            DamageType          = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "IFV_ok",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_ifv"
        ),
    ]
)
export UnitEffect_CanonAT_Cowering is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{3db21656-8cd8-4e8d-b3de-ba3849ba444f}
    NameForDebug       = 'CanonAT_Cowering'
    EffectsDescriptors = [
        TUnitEffectIncreaseInfluenceValueDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            Bonus              = 0.66
        ),
        TUnitEffectIncreaseWeaponPorteeMaxDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectIncreaseWeaponPorteeMaxHADescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectIncreaseWeaponPorteeMaxProjectileDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectIncreaseWeaponPorteeMaxTBADescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
                 "Infanterie_Suppressed",
                 "Infanterie_Pinned",
                 "Infanterie_Cowering",
                 "Bloque_MoveAndAttack",
            ]
        ),
    ]
)
export UnitEffect_CanonAT_Engaged is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{04e75810-e548-4537-ab15-22ec952fff4a}
    NameForDebug       = 'CanonAT_Engaged'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 25
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.1
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
            ]
        ),
    ]
)
export UnitEffect_CanonAT_Pinned is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{37b4be6c-43a7-4a13-9da4-e390a7c4bfd3}
    NameForDebug       = 'CanonAT_Pinned'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 150
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.70
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
                 "Infanterie_Suppressed",
                 "Infanterie_Pinned",
            ]
        ),
    ]
)
export UnitEffect_CanonAT_Stressed is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{3061802a-7e9e-4236-bd2c-370f6e932e07}
    NameForDebug       = 'CanonAT_Stressed'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 50
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.25
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
            ]
        ),
    ]
)
export UnitEffect_CanonAT_Suppressed is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{18de6747-9790-4344-9e03-f29cae51463c}
    NameForDebug       = 'CanonAT_Suppressed'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 100
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.45
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
                 "Infanterie_Suppressed",
            ]
        ),
    ]
)
export UnitEffect_Choc is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{e10be162-60bd-4113-8f44-e67daee0191f}
    NameForDebug       = 'Choc'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.85
        ),
        TUnitEffectIncreaseWeaponPhysicalDamagesDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 15
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.85
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxTirsDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -15
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "choc_ok",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_shock"
        ),
    ]
)
export UnitEffect_Choc_feedback is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{3f1521d2-d3c1-4ee5-95a5-1860daf2e023}
    NameForDebug       = 'Choc_feedback'
    EffectsDescriptors = [
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_shock_off"
        ),
    ]
)
export UnitEffect_Critic_Bailed_Out is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{abec5c8e-e288-49ca-a6f9-a0b097c165a1}
    NameForDebug       = 'Critic_Bailed_Out'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            BonusChassisRotationSpeed  = 0
        ),
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 0
            DamageType          = EDamageType/Suppress
        ),
        TUnitEffectIncreaseDangerousnessDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            BonusDangerousness    = 0
        ),
        TUnitEffectIncreaseVisionDescriptor
        (
            ModifierType       = ~/ModifierType_Remplacement
            BonusVisionBase    = 0
        ),
        TUnitEffectIncreaseSpecializedDetectionDescriptor
        (
            ModifierType       = ~/ModifierType_Remplacement
            VisionType         = ~/EVisionUnitType/HighAltitude
            BonusValueGRU      = 0
        ),
        TUnitEffectIncreaseInfluenceValueDescriptor
        (
            ModifierType       = ~/ModifierType_Remplacement
            Bonus              = 0
        ),
        TUnitEffectIncreaseInfluenceValueMinDescriptor
        (
            ModifierType       = ~/ModifierType_Remplacement
            Bonus              = 0
        ),
        TUnitEffectIncreaseTurretRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            BonusTurretRotationSpeed  = 0
        ),
        TUnitEffectIncreaseWeaponPorteeMaxDescriptor
        (
            ModifierType  = ~/ModifierType_Remplacement
            ModifierValueGRU = 0
        ),
        TUnitEffectIncreaseWeaponPorteeMaxHADescriptor
        (
            ModifierType  = ~/ModifierType_Remplacement
            ModifierValueGRU = 0
        ),
        TUnitEffectIncreaseWeaponPorteeMaxProjectileDescriptor
        (
            ModifierType  = ~/ModifierType_Remplacement
            ModifierValueGRU = 0
        ),
        TUnitEffectIncreaseWeaponPorteeMaxTBADescriptor
        (
            ModifierType  = ~/ModifierType_Remplacement
            ModifierValueGRU = 0
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Bailed_Out",
            ]
        ),
        TUnitEffectSetSelectableDescriptor
        (
            Selectable = False
        ),
        TUnitEffectTransportSlotNumberModificationDescriptor
        (
            ModifierType                 = ~/ModifierType_Remplacement
            EffectOnTransportSlotNumber  = 0
        ),
        TUnitEffectStopWithInertiaEffectDescriptor
        (
            UpdateEachTick  = True
        ),
    ]
)
export UnitEffect_Critic_Chenille_Detruite is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{a052a4e6-3b08-47f3-822c-a1c42262f6c4}
    NameForDebug       = 'Critic_Chenille_Detruite'
    EffectsDescriptors = [
        TUnitEffectStopWithInertiaEffectDescriptor
        (
            UpdateEachTick  = True
        ),
    ]
)
export UnitEffect_Critic_Deroute is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{793b916c-3e0a-4c34-b638-12c1c9e136e4}
    NameForDebug       = 'Critic_Deroute'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponPorteeMaxDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectIncreaseWeaponPorteeMaxHADescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectIncreaseWeaponPorteeMaxProjectileDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectIncreaseWeaponPorteeMaxTBADescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TDerouteUnitEffectDescriptor(),
    ]
)
export UnitEffect_Critic_Full_Stop is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{7611167d-5a2c-44c9-a748-eb1d87cead98}
    NameForDebug       = 'Critic_Full_Stop'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            BonusChassisRotationSpeed  = 0
        ),
        TUnitEffectStopWithInertiaEffectDescriptor
        (
            UpdateEachTick  = True
        ),
    ]
)
export UnitEffect_Critic_Perd_1_PV is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{6ab81696-**************-3e2105e148a7}
    NameForDebug       = 'Critic_Perd_1_PV'
    EffectsDescriptors = [
        TEffectInflictPhysicalDamageDescriptor
        (
            ModifierType             = ~/ModifierType_Additionnel
            PhysicalDamageValue      = 1
        ),
    ]
)
export UnitEffect_Critic_Pert_2_PV is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{d2ce1149-f261-48ad-9fef-60b12c8e1fdd}
    NameForDebug       = 'Critic_Pert_2_PV'
    EffectsDescriptors = [
        TEffectInflictPhysicalDamageDescriptor
        (
            ModifierType             = ~/ModifierType_Additionnel
            PhysicalDamageValue      = 2
        ),
    ]
)
export UnitEffect_Critic_Pert_3_PV is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{355daef3-6c21-4a64-b0ce-01f254c2e920}
    NameForDebug       = 'Critic_Pert_3_PV'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            BonusChassisRotationSpeed  = 0
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Multiplicatif
            BonusSpeedBaseInPercent  = 0
        ),
        TEffectInflictPhysicalDamageDescriptor
        (
            ModifierType             = ~/ModifierType_Additionnel
            PhysicalDamageValue      = 3
        ),
    ]
)
export UnitEffect_Critic_Pert_4_PV is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{24569f41-4a75-47e5-86ba-cc206abdc50a}
    NameForDebug       = 'Critic_Pert_4_PV'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            BonusChassisRotationSpeed  = 0
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Multiplicatif
            BonusSpeedBaseInPercent  = 0
        ),
        TEffectInflictPhysicalDamageDescriptor
        (
            ModifierType             = ~/ModifierType_Additionnel
            PhysicalDamageValue      = 4
        ),
    ]
)
export UnitEffect_Critic_Pert_5_PV is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{d84a7efa-ebc0-4bf3-ba1c-3e950e234839}
    NameForDebug       = 'Critic_Pert_5_PV'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            BonusChassisRotationSpeed  = 0
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Multiplicatif
            BonusSpeedBaseInPercent  = 0
        ),
        TEffectInflictPhysicalDamageDescriptor
        (
            ModifierType             = ~/ModifierType_Additionnel
            PhysicalDamageValue      = 5
        ),
    ]
)
export UnitEffect_Critic_Reload_50 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{e4a2d3ae-fef6-4eb7-a018-68d39884e66c}
    NameForDebug       = 'Critic_Reload_50'
    EffectsDescriptors = [
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 2
        ),
    ]
)
export UnitEffect_Critic_Stop_Armes is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{e7f4d4be-090d-43f1-b646-1a0fe111ea91}
    NameForDebug       = 'Critic_Stop_Armes'
    EffectsDescriptors = [
        TUnitEffectIncreaseTurretRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            BonusTurretRotationSpeed  = 0
        ),
        TUnitEffectIncreaseWeaponPorteeMaxDescriptor
        (
            ModifierType  = ~/ModifierType_Remplacement
            ModifierValueGRU = 0
        ),
        TUnitEffectIncreaseWeaponPorteeMaxHADescriptor
        (
            ModifierType  = ~/ModifierType_Remplacement
            ModifierValueGRU = 0
        ),
        TUnitEffectIncreaseWeaponPorteeMaxProjectileDescriptor
        (
            ModifierType  = ~/ModifierType_Remplacement
            ModifierValueGRU = 0
        ),
        TUnitEffectIncreaseWeaponPorteeMaxTBADescriptor
        (
            ModifierType  = ~/ModifierType_Remplacement
            ModifierValueGRU = 0
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Bloque_MoveAndAttack",
            ]
        ),
    ]
)
export UnitEffect_Critic_Stop_Reload is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{d1daf19d-0b5e-46a4-bd72-6966d443ed0a}
    NameForDebug       = 'Critic_Stop_Reload'
    EffectsDescriptors = [
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 100
        ),
    ]
)
export UnitEffect_Critic_Tourelle_Bloquee is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{a10e9410-3a51-4b62-baea-8c77211d7bf8}
    NameForDebug       = 'Critic_Tourelle_Bloquee'
    EffectsDescriptors = [
        TUnitEffectIncreaseTurretRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            BonusTurretRotationSpeed  = 0
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Bloque_MoveAndAttack",
            ]
        ),
    ]
)
export UnitEffect_Critic_Visee_50 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{27aa29f2-9311-4f3e-bac9-98791a8df090}
    NameForDebug       = 'Critic_Visee_50'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 50
        ),
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.5
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.5
        ),
    ]
)
export UnitEffect_Critic_Vitesse_50 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{d2e316e9-b884-42ee-aac9-67d478c885ad}
    NameForDebug       = 'Critic_Vitesse_50'
    EffectsDescriptors = [
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Multiplicatif
            BonusSpeedBaseInPercent  = 0.5
        ),
    ]
)
export UnitEffect_Default_Panicked is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{83e8536b-9cca-41d9-a644-d831e2ed29e5}
    NameForDebug       = 'Default_Panicked'
    EffectsDescriptors = [
        TUnitEffectIncreaseDangerousnessDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDangerousness    = -3
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 3
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxTirsDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 3
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
                 "Infanterie_Suppressed",
            ]
        ),
    ]
)
export UnitEffect_Default_Stressed is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{2448a7db-3d25-49a1-ae98-7b07a0baf008}
    NameForDebug       = 'Default_Stressed'
    EffectsDescriptors = [
        TUnitEffectIncreaseDangerousnessDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDangerousness    = -2
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 2
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxTirsDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 2
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
            ]
        ),
    ]
)
export UnitEffect_Default_Worried is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{78bd0724-6328-44e8-be55-8c7e3cdeb45c}
    NameForDebug       = 'Default_Worried'
    EffectsDescriptors = [
        TUnitEffectIncreaseDangerousnessDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDangerousness    = -1
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.5
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxTirsDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.5
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
            ]
        ),
    ]
)
export UnitEffect_Destruction is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{c61861fb-8693-48ab-9227-c001299c3443}
    NameForDebug       = 'Destruction'
    EffectsDescriptors = [
        TUnitEffectRemoveUnitDescriptor(),
    ]
)
export UnitEffect_Dispersion_Bombes_plus100 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{4969964e-7507-4952-ae23-a55b117c3803}
    NameForDebug       = 'Dispersion_Bombes_plus100'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 100
        ),
    ]
)
export UnitEffect_Dispersion_Bombes_plus150 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{9a77438b-75bb-4276-b77a-93e937c2a8df}
    NameForDebug       = 'Dispersion_Bombes_plus150'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 150
        ),
    ]
)
export UnitEffect_Dispersion_Bombes_plus200 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{c2e52660-b2fa-453d-aefc-113645678553}
    NameForDebug       = 'Dispersion_Bombes_plus200'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 200
        ),
    ]
)
export UnitEffect_Dispersion_Bombes_plus25 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{d921a21b-08f8-4232-ab12-c1ed43de9e19}
    NameForDebug       = 'Dispersion_Bombes_plus25'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 25
        ),
    ]
)
export UnitEffect_Dispersion_Bombes_plus300 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{1e77c6b5-5c39-4f90-acb1-08026293a869}
    NameForDebug       = 'Dispersion_Bombes_plus300'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 300
        ),
    ]
)
export UnitEffect_Dispersion_Bombes_plus50 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{30ceb601-b027-4e5d-904c-3603d647de9e}
    NameForDebug       = 'Dispersion_Bombes_plus50'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 50
        ),
    ]
)
export UnitEffect_Effet_Meurt is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{74f45ca1-8a67-4372-92b4-a217c1178f6f}
    NameForDebug       = 'Effet_Meurt'
    EffectsDescriptors = [
        TKillUnitEffectDescriptor(),
    ]
)
export UnitEffect_Effet_Suppress_Recu_115pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{bcf1a96f-f0ec-4da2-a785-055bbd0d22d6}
    NameForDebug       = 'Effet_Suppress_Recu_115pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 1.15
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Effet_Suppress_Recu_125pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{31bcda46-0576-4703-9cad-0ef821de7923}
    NameForDebug       = 'Effet_Suppress_Recu_125pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 1.25
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Effet_Suppress_Recu_133pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{5fab113e-a6e2-449c-91d2-6b6dfdb2202f}
    NameForDebug       = 'Effet_Suppress_Recu_133pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 1.33
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Effet_Suppress_Recu_25pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{0b92a58f-3e45-469d-86fb-7530729a44df}
    NameForDebug       = 'Effet_Suppress_Recu_25pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 0.25
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Effet_Suppress_Recu_33pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{de182684-98a0-4977-b7de-2da905d25309}
    NameForDebug       = 'Effet_Suppress_Recu_33pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 0.33
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Effet_Suppress_Recu_50pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{913d6ea5-d5c7-4679-a110-316374b4e0de}
    NameForDebug       = 'Effet_Suppress_Recu_50pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 0.50
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Effet_Suppress_Recu_55pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{f6535116-075b-4d59-ac63-fc4a47bd1857}
    NameForDebug       = 'Effet_Suppress_Recu_55pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 0.5
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Effet_Suppress_Recu_66pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{8dbad5e0-4bbd-49aa-a924-b61cd52f8fcc}
    NameForDebug       = 'Effet_Suppress_Recu_66pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 0.66
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Effet_Suppress_Recu_70pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{720c8d14-eacf-41f9-8385-e24b0a6f26a0}
    NameForDebug       = 'Effet_Suppress_Recu_70pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 0.7
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Effet_Suppress_Recu_75pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{55d6c0eb-65e6-4c4f-b50f-2f65af5fd3ce}
    NameForDebug       = 'Effet_Suppress_Recu_75pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 0.75
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Effet_Suppress_Recu_85pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{7b40a35c-0842-407b-b852-3dff05c6e008}
    NameForDebug       = 'Effet_Suppress_Recu_85pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 0.85
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Effet_Suppress_Recu_vet2 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{1c89bd2f-af6d-433a-bec7-d329500b093f}
    NameForDebug       = 'Effet_Suppress_Recu_vet2'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 0.80
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Effet_Suppress_Recu_vet3 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{f97d8383-27cc-4a44-956f-534ba05dea01}
    NameForDebug       = 'Effet_Suppress_Recu_vet3'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 0.60
            DamageType          = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Effet_Suppress_Subit_100 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{9ee326f4-58af-4b4b-9613-782ea6891f8a}
    NameForDebug       = 'Effet_Suppress_Subit_100'
    EffectsDescriptors = [
        TEffectInflictSuppressDamageDescriptor
        (
            ModifierType             = ~/ModifierType_Additionnel
            SuppressDamageValue      = 100
        ),
    ]
)
export UnitEffect_Effet_Suppress_Subit_200 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{46449a3f-62ff-429e-8527-a141483c8cd2}
    NameForDebug       = 'Effet_Suppress_Subit_200'
    EffectsDescriptors = [
        TEffectInflictSuppressDamageDescriptor
        (
            ModifierType             = ~/ModifierType_Additionnel
            SuppressDamageValue      = 200
        ),
    ]
)
export UnitEffect_Effet_Suppress_Subit_300 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{384697c3-ee57-4686-af72-4579494eab8c}
    NameForDebug       = 'Effet_Suppress_Subit_300'
    EffectsDescriptors = [
        TEffectInflictSuppressDamageDescriptor
        (
            ModifierType             = ~/ModifierType_Additionnel
            SuppressDamageValue      = 300
        ),
    ]
)
export UnitEffect_Effet_Suppress_Subit_400 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{b347dfb2-670e-4bc0-a796-b19eea138d85}
    NameForDebug       = 'Effet_Suppress_Subit_400'
    EffectsDescriptors = [
        TEffectInflictSuppressDamageDescriptor
        (
            ModifierType             = ~/ModifierType_Additionnel
            SuppressDamageValue      = 400
        ),
    ]
)
export UnitEffect_Effet_Suppress_Subit_500 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{70054f1f-cce5-45b2-835e-70c356fadecb}
    NameForDebug       = 'Effet_Suppress_Subit_500'
    EffectsDescriptors = [
        TEffectInflictSuppressDamageDescriptor
        (
            ModifierType             = ~/ModifierType_Additionnel
            SuppressDamageValue      = 500
        ),
    ]
)
export UnitEffect_Effet_Vide is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{541d6342-ad2a-4d2b-8a13-7ea8cca6d61d}
    NameForDebug       = 'Effet_Vide'
    EffectsDescriptors = [
    ]
)
export UnitEffect_Esquive_plus_15 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{a8caacfb-b3fa-4168-9b0e-875bc1c36026}
    NameForDebug       = 'Esquive_plus_15'
    EffectsDescriptors = [
        TUnitEffectBonusPrecisionWhenTargetedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusPrecisionWhenTargeted = -15
        ),
    ]
)
export UnitEffect_Esquive_plus_25 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{f5a8463a-5999-484c-ad1e-a107e01d4320}
    NameForDebug       = 'Esquive_plus_25'
    EffectsDescriptors = [
        TUnitEffectBonusPrecisionWhenTargetedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusPrecisionWhenTargeted = -25
        ),
    ]
)
export UnitEffect_Esquive_plus_33 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{d56ca3fa-bd04-4c47-871e-f71410849bee}
    NameForDebug       = 'Esquive_plus_33'
    EffectsDescriptors = [
        TUnitEffectBonusPrecisionWhenTargetedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusPrecisionWhenTargeted = -33
        ),
    ]
)
export UnitEffect_GSR is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{20543d3e-2f60-41a3-a19f-83dc88e8c3f9}
    NameForDebug       = 'GSR'
    EffectsDescriptors = [
        TUnitEffectIncreaseVisionDescriptor
        (
            ModifierType       = ~/ModifierType_Additionnel
            BonusVisionBase    = 2000
        ),
        TUnitEffectIncreaseOpticalStrengthDescriptor
        (
            ModifierType         = ~/ModifierType_Additionnel
            BonusOpticalStrength = 43
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GSR_ok",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_gsr"
        ),
    ]
)
export UnitEffect_Gain_deux_niveau_XP is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{957d6432-b7dd-4bf8-97e2-c203a34fa25d}
    NameForDebug       = 'Gain_deux_niveau_XP'
    EffectsDescriptors = [
        TUnitEffectBonusExperienceLevelDescriptor
        (
            ModifierType            = ~/ModifierType_Additionnel
            ExperienceLevelModifier = 2
        ),
    ]
)
export UnitEffect_Gain_un_niveau_XP is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{8376f767-2514-4ad1-be2f-2d433576d24b}
    NameForDebug       = 'Gain_un_niveau_XP'
    EffectsDescriptors = [
        TUnitEffectBonusExperienceLevelDescriptor
        (
            ModifierType            = ~/ModifierType_Additionnel
            ExperienceLevelModifier = 1
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_leader"
        ),
    ]
)
export UnitEffect_Global_Cowering is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{79577ee7-a2aa-4c01-a565-70d0e5430430}
    NameForDebug       = 'Global_Cowering'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Panique",
            ]
        ),
    ]
)
export UnitEffect_GroundUnit_Cohesion_High is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{16aa7006-5dcd-4cae-92d1-7ebeb6d1123c}
    NameForDebug       = 'GroundUnit_Cohesion_High'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
            ]
        ),
    ]
)
export UnitEffect_GroundUnit_Cohesion_Low is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{954badec-54bd-4332-861f-6d91333c0ef8}
    NameForDebug       = 'GroundUnit_Cohesion_Low'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusChassisRotationSpeed  = -50
        ),
        TUnitEffectIncreaseTurretRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusTurretRotationSpeed  = -50
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Pourcentage
            BonusSpeedBaseInPercent  = -15
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.18
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.40
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
                 "GroundUnit_Stressed",
                 "GroundUnit_Suppressed",
                 "GroundUnit_Pinned",
            ]
        ),
    ]
)
export UnitEffect_GroundUnit_Cohesion_Mediocre is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{96426e5e-cd6a-4188-9a72-0e55b230dbb7}
    NameForDebug       = 'GroundUnit_Cohesion_Mediocre'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusChassisRotationSpeed  = -25
        ),
        TUnitEffectIncreaseTurretRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusTurretRotationSpeed  = -25
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Pourcentage
            BonusSpeedBaseInPercent  = -10
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.12
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.20
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
                 "GroundUnit_Stressed",
                 "GroundUnit_Suppressed",
            ]
        ),
    ]
)
export UnitEffect_GroundUnit_Cohesion_Normal is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{1f643782-e1bb-4f37-885b-4d275206688c}
    NameForDebug       = 'GroundUnit_Cohesion_Normal'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusChassisRotationSpeed  = -10
        ),
        TUnitEffectIncreaseTurretRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusTurretRotationSpeed  = -10
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Pourcentage
            BonusSpeedBaseInPercent  = -5
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.08
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.10
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
                 "GroundUnit_Stressed",
            ]
        ),
    ]
)
export UnitEffect_GroundUnit_Cowering is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{45eb852d-fea1-4f43-a619-8a26c88f9761}
    NameForDebug       = 'GroundUnit_Cowering'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "GroundUnit_Engaged",
                 "GroundUnit_Stressed",
                 "GroundUnit_Suppressed",
                 "GroundUnit_Pinned",
                 "GroundUnit_Cowering",
            ]
        ),
    ]
)
export UnitEffect_IFV_feedback is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{e63f8a3b-1ee2-48e7-abfc-b082850c267d}
    NameForDebug       = 'IFV_feedback'
    EffectsDescriptors = [
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_ifv_off"
        ),
    ]
)
export UnitEffect_Infanterie_Courage_Effet is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{c06041c4-2c91-4c3b-88df-42b21063aa4e}
    NameForDebug       = 'Infanterie_Courage_Effet'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Courage",
            ]
        ),
        TUnitEffectSetSelectableDescriptor
        (
            Selectable = False
        ),
    ]
)
export UnitEffect_Infanterie_Cowering is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{2a16fc83-5a16-48c1-8b58-0eef8d18eb1d}
    NameForDebug       = 'Infanterie_Cowering'
    EffectsDescriptors = [
        TUnitEffectIncreaseInfluenceValueDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            Bonus              = 0.66
        ),
        TUnitEffectIncreaseWeaponPorteeMaxIgnoreSmokeDescriptor
        (
            ModifierType         = ~/ModifierType_Pourcentage
            BonusWeaponPorteeMax = -100
        ),
        TUnitEffectIncreaseWeaponPorteeMaxHADescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectIncreaseWeaponPorteeMaxProjectileDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectIncreaseWeaponPorteeMaxTBADescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -100
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
                 "Infanterie_Suppressed",
                 "Infanterie_Pinned",
                 "Infanterie_Cowering",
                 "Bloque_MoveAndAttack",
            ]
        ),
        TUnitEffectReplaceArmorDescriptor
        (
            ReplacementBlindage = TResistanceTypeRTTI(Family=ResistanceFamily_infanterie Index=2)
            BlindageLocation    = ~/EArmorLocation/Front
        ),
        TUnitEffectReplaceArmorDescriptor
        (
            ReplacementBlindage = TResistanceTypeRTTI(Family=ResistanceFamily_infanterie Index=2)
            BlindageLocation    = ~/EArmorLocation/Rear
        ),
        TUnitEffectReplaceArmorDescriptor
        (
            ReplacementBlindage = TResistanceTypeRTTI(Family=ResistanceFamily_infanterie Index=2)
            BlindageLocation    = ~/EArmorLocation/Sides
        ),
        TUnitEffectReplaceArmorDescriptor
        (
            ReplacementBlindage = TResistanceTypeRTTI(Family=ResistanceFamily_infanterie Index=2)
            BlindageLocation    = ~/EArmorLocation/Top
        ),
    ]
)
export UnitEffect_Infanterie_Cowering_Rampe is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{d41b81d9-519c-48e2-b5a0-44c111fe01c1}
    NameForDebug       = 'Infanterie_Cowering_Rampe'
    EffectsDescriptors = [
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Multiplicatif
            BonusSpeedBaseInPercent  = 0.30
        ),
    ]
)
export UnitEffect_Infanterie_Cowering_Stop is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{616dd9c3-ea9d-475c-b6ed-5e1ea73b0db7}
    NameForDebug       = 'Infanterie_Cowering_Stop'
    EffectsDescriptors = [
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Multiplicatif
            BonusSpeedBaseInPercent  = 0
        ),
    ]
)
export UnitEffect_Infanterie_Engaged is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{a567605f-52a0-4566-88a3-846a6df10c64}
    NameForDebug       = 'Infanterie_Engaged'
    EffectsDescriptors = [
        TUnitEffectIncreaseDangerousnessDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDangerousness    = -1
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.1
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxTirsDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.1
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
            ]
        ),
    ]
)
export UnitEffect_Infanterie_Pinned is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{1e24f203-4882-48d5-96d7-03e283551622}
    NameForDebug       = 'Infanterie_Pinned'
    EffectsDescriptors = [
        TUnitEffectIncreaseDangerousnessDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDangerousness    = -4
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.7
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxTirsDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.70
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
                 "Infanterie_Suppressed",
                 "Infanterie_Pinned",
            ]
        ),
    ]
)
export UnitEffect_Infanterie_Stressed is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{6fd65a62-dfd8-49aa-bbd1-ad2fbe8682b7}
    NameForDebug       = 'Infanterie_Stressed'
    EffectsDescriptors = [
        TUnitEffectIncreaseDangerousnessDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDangerousness    = -2
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.25
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxTirsDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.25
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
            ]
        ),
    ]
)
export UnitEffect_Infanterie_Suppressed is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{8772367b-15ee-464d-8d1b-efd0f44d09ba}
    NameForDebug       = 'Infanterie_Suppressed'
    EffectsDescriptors = [
        TUnitEffectIncreaseDangerousnessDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDangerousness    = -3
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.45
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxTirsDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.45
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Infanterie_Engaged",
                 "Infanterie_Stressed",
                 "Infanterie_Suppressed",
            ]
        ),
    ]
)
export UnitEffect_Infanterie_bonus_defense is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{465acff5-eec4-4d60-b660-0bbb41444012}
    NameForDebug       = 'Infanterie_bonus_defense'
    EffectsDescriptors = [
        TUnitEffectReplaceArmorDescriptor
        (
            ReplacementBlindage = TResistanceTypeRTTI(Family=ResistanceFamily_infanterie Index=2)
            BlindageLocation    = ~/EArmorLocation/Front
        ),
        TUnitEffectReplaceArmorDescriptor
        (
            ReplacementBlindage = TResistanceTypeRTTI(Family=ResistanceFamily_infanterie Index=2)
            BlindageLocation    = ~/EArmorLocation/Rear
        ),
        TUnitEffectReplaceArmorDescriptor
        (
            ReplacementBlindage = TResistanceTypeRTTI(Family=ResistanceFamily_infanterie Index=2)
            BlindageLocation    = ~/EArmorLocation/Sides
        ),
        TUnitEffectReplaceArmorDescriptor
        (
            ReplacementBlindage = TResistanceTypeRTTI(Family=ResistanceFamily_infanterie Index=2)
            BlindageLocation    = ~/EArmorLocation/Top
        ),
    ]
)
export UnitEffect_Infanterie_bonus_discretion is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{01d4dff4-9e85-48d2-9369-45b94ae1dbbc}
    NameForDebug       = 'Infanterie_bonus_discretion'
    EffectsDescriptors = [
        TUnitEffectIncreaseConcealmentBonusDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            BonusConcealmentBonus  = 1.5
        ),
    ]
)
export UnitEffect_Malus_ciblage_UAV is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{8651f756-e794-4dc7-bafd-ff5f79fc5de0}
    NameForDebug       = 'Malus_ciblage_UAV'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -100
            DamageType          = EDamageType/Suppress
        ),
        TUnitEffectBonusPrecisionWhenTargetedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusPrecisionWhenTargeted = -50
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "UAV",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_electronic_warfare"
        ),
    ]
)
export UnitEffect_Mobilite_plus_15pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{6a93df37-ce2a-4706-9f5d-62a677fd532f}
    NameForDebug       = 'Mobilite_plus_15pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusChassisRotationSpeed  = 15
        ),
        TUnitEffectIncreaseTurretRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusTurretRotationSpeed  = 15
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Pourcentage
            BonusSpeedBaseInPercent  = 15
        ),
    ]
)
export UnitEffect_Mobilite_plus_25pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{e4f61a0b-83cb-40a4-a472-3595d706981a}
    NameForDebug       = 'Mobilite_plus_25pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusChassisRotationSpeed  = 25
        ),
        TUnitEffectIncreaseTurretRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusTurretRotationSpeed  = 25
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Pourcentage
            BonusSpeedBaseInPercent  = 25
        ),
    ]
)
export UnitEffect_Mobilite_plus_33pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{394d1c06-034f-4067-879f-e3862da5d944}
    NameForDebug       = 'Mobilite_plus_33pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseChassisRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusChassisRotationSpeed  = 33
        ),
        TUnitEffectIncreaseTurretRotationSpeedDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusTurretRotationSpeed  = 33
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Pourcentage
            BonusSpeedBaseInPercent  = 33
        ),
    ]
)
export UnitEffect_Nb_Places_Transport_0 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{e9d82840-58d0-4650-8398-e333a954e404}
    NameForDebug       = 'Nb_Places_Transport_0'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Camion_autodestruction",
            ]
        ),
        TUnitEffectTransportSlotNumberModificationDescriptor
        (
            ModifierType                 = ~/ModifierType_Remplacement
            EffectOnTransportSlotNumber  = 0
        ),
    ]
)
export UnitEffect_Optiques_plus_15pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{98bd9c54-cf97-4ea5-a218-91ffa2f49821}
    NameForDebug       = 'Optiques_plus_15pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseOpticalStrengthDescriptor
        (
            ModifierType         = ~/ModifierType_Multiplicatif
            BonusOpticalStrength = 1.15
        ),
    ]
)
export UnitEffect_Optiques_plus_25pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{43a9243b-b3fe-44ca-996b-fc12733528e0}
    NameForDebug       = 'Optiques_plus_25pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseOpticalStrengthDescriptor
        (
            ModifierType         = ~/ModifierType_Multiplicatif
            BonusOpticalStrength = 1.25
        ),
    ]
)
export UnitEffect_Optiques_plus_33pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{99832d10-5a5f-49b8-baee-d1ae9a18e783}
    NameForDebug       = 'Optiques_plus_33pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseOpticalStrengthDescriptor
        (
            ModifierType         = ~/ModifierType_Multiplicatif
            BonusOpticalStrength = 1.33
        ),
    ]
)
export UnitEffect_Precision_moins_20 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{604fcc23-ebc2-4620-af2b-2bf470db0be3}
    NameForDebug       = 'Precision_moins_20'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.8
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.8
        ),
    ]
)
export UnitEffect_Precision_moins_25 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{ce48bea4-f247-4893-ae50-e3a3b85262be}
    NameForDebug       = 'Precision_moins_25'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.75
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.75
        ),
    ]
)
export UnitEffect_Precision_plus_10 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{791c8f7f-ed5c-432e-8437-48c8001e8980}
    NameForDebug       = 'Precision_plus_10'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 10
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 10
        ),
    ]
)
export UnitEffect_Precision_plus_15 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{3e1b1472-397d-4d18-9331-a811264c7576}
    NameForDebug       = 'Precision_plus_15'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 15
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 15
        ),
    ]
)
export UnitEffect_Precision_plus_20 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{70907401-52f6-4811-a309-c7bfb52fb9c9}
    NameForDebug       = 'Precision_plus_20'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 20
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 20
        ),
    ]
)
export UnitEffect_Precision_plus_25 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{94a6bb92-c64e-4169-856f-9a6880bf7ff2}
    NameForDebug       = 'Precision_plus_25'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 25
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 25
        ),
    ]
)
export UnitEffect_Precision_plus_33 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{0b434d9d-469f-48a6-8af0-d4625d0ef3d2}
    NameForDebug       = 'Precision_plus_33'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 33
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 33
        ),
    ]
)
export UnitEffect_Precision_plus_5 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{b349ad22-b86d-43f9-b7e9-52672c5e4ac2}
    NameForDebug       = 'Precision_plus_5'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 5
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 5
        ),
    ]
)
export UnitEffect_Precision_plus_50 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{8e5a719b-9b51-4a29-b54d-79d27f3901a8}
    NameForDebug       = 'Precision_plus_50'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 50
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 50
        ),
    ]
)
export UnitEffect_Regen_suppress_elite is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{216d01ed-5210-4b1d-8079-27a574755bdd}
    NameForDebug       = 'Regen_suppress_elite'
    EffectsDescriptors = [
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 2
            DamageType = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Regen_suppress_regular is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{d64b3d1d-ba9e-42a1-ab21-d03f8524b799}
    NameForDebug       = 'Regen_suppress_regular'
    EffectsDescriptors = [
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 0.5
            DamageType = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Regen_suppress_veteran is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{bd6ed428-09c2-4e23-807a-05bb0e74e291}
    NameForDebug       = 'Regen_suppress_veteran'
    EffectsDescriptors = [
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 1.25
            DamageType = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_Resistance_plus_15pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{06431459-7239-48ba-b08a-299a2a33bb9b}
    NameForDebug       = 'Resistance_plus_15pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -15
            DamageType          = EDamageType/Physical
        ),
    ]
)
export UnitEffect_Resistance_plus_25pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{d58271d0-f851-41cd-a3df-aaef4d6c960a}
    NameForDebug       = 'Resistance_plus_25pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -25
            DamageType          = EDamageType/Physical
        ),
    ]
)
export UnitEffect_Resistance_plus_33pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{618d3e8e-729a-46f0-8c0e-bb7b0b13277c}
    NameForDebug       = 'Resistance_plus_33pourcent'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -33
            DamageType          = EDamageType/Physical
        ),
    ]
)
export UnitEffect_RoF_moins_15pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{43eed693-c09b-4ead-a268-61b62a7ef5aa}
    NameForDebug       = 'RoF_moins_15pourcent'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.25
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.15
        ),
        TUnitEffectSetSelectableDescriptor
        (
            Selectable = True
        ),
    ]
)
export UnitEffect_RoF_moins_20pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{65cc4093-d563-4a7c-a36c-6e5c58a56b50}
    NameForDebug       = 'RoF_moins_20pourcent'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.2
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.20
        ),
        TUnitEffectSetSelectableDescriptor
        (
            Selectable = True
        ),
    ]
)
export UnitEffect_RoF_moins_25pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{ca2feb6e-f3eb-4a9c-bc3c-57983c9fa30b}
    NameForDebug       = 'RoF_moins_25pourcent'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.25
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.25
        ),
    ]
)
export UnitEffect_RoF_plus_10pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{b2b6e407-e16f-45bb-890a-72b3386faa3a}
    NameForDebug       = 'RoF_plus_10pourcent'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.9
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.9
        ),
    ]
)
export UnitEffect_RoF_plus_15pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{1903172f-cbb9-42b2-ab64-1e506f74b835}
    NameForDebug       = 'RoF_plus_15pourcent'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.85
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.85
        ),
    ]
)
export UnitEffect_RoF_plus_20pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{460eaa70-92b4-487a-a180-fce1f6009d9d}
    NameForDebug       = 'RoF_plus_20pourcent'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.8
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.8
        ),
    ]
)
export UnitEffect_RoF_plus_25pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{774cd45e-3ec4-4db6-8fb1-1de2e68ade0b}
    NameForDebug       = 'RoF_plus_25pourcent'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.75
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.75
        ),
    ]
)
export UnitEffect_RoF_plus_33pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{0e817153-2cf9-4c5d-a498-5698b06d4b26}
    NameForDebug       = 'RoF_plus_33pourcent'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.67
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.67
        ),
    ]
)
export UnitEffect_RoF_plus_40pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{425fd8c9-5aed-4cd1-9403-811b747f1915}
    NameForDebug       = 'RoF_plus_40pourcent'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.4
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.40
        ),
    ]
)
export UnitEffect_RoF_plus_50pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{ff9b175a-b850-475c-9f71-b88b06618712}
    NameForDebug       = 'RoF_plus_50pourcent'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.5
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.50
        ),
    ]
)
export UnitEffect_RoF_plus_5pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{9cf6920a-f218-454b-a1f9-8632bf7bb2d2}
    NameForDebug       = 'RoF_plus_5pourcent'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.95
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.95
        ),
    ]
)
export UnitEffect_RoF_plus_75pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{d12f339b-6f53-46ea-a0da-d885e1e1db37}
    NameForDebug       = 'RoF_plus_75pourcent'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.25
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.25
        ),
    ]
)
export UnitEffect_SF_feedback is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{af8bc966-6022-4ecb-8d6c-038ee402df9d}
    NameForDebug       = 'SF_feedback'
    EffectsDescriptors = [
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_sf"
        ),
    ]
)
export UnitEffect_Steelman_supprimer_PA is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{f345ea94-ced6-4961-a8ac-d90597b4fddb}
    NameForDebug       = 'Steelman_supprimer_PA'
    EffectsDescriptors = [
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_sniper"
        ),
        TStrategicSupplyMalusEffectDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            SupplyMalus        = 0
        ),
    ]
)
export UnitEffect_Supprime_Zone_Dinfluence is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{e79a1715-5f11-4eed-af1a-a40eec133f27}
    NameForDebug       = 'Supprime_Zone_Dinfluence'
    EffectsDescriptors = [
        TUnitEffectIncreaseInfluenceValueDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            Bonus              = 0
        ),
        TUnitEffectIncreaseInfluenceValueMinDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            Bonus              = 0
        ),
    ]
)
export UnitEffect_Tank_bonus_discretion is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{a8706126-4a68-4139-ab44-febdc4ed29a3}
    NameForDebug       = 'Tank_bonus_discretion'
    EffectsDescriptors = [
        TUnitEffectIncreaseConcealmentBonusDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            BonusConcealmentBonus  = 2
        ),
    ]
)
export UnitEffect_Tps_entre_salve_115pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{f010e9d8-6583-4c03-9a12-57ae271397b6}
    NameForDebug       = 'Tps_entre_salve_115pourcent'
    EffectsDescriptors = [
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.15
        ),
    ]
)
export UnitEffect_Tps_entre_salve_125pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{6893a72c-42ba-454c-b3b3-c5bc4b315e87}
    NameForDebug       = 'Tps_entre_salve_125pourcent'
    EffectsDescriptors = [
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.25
        ),
    ]
)
export UnitEffect_Tps_entre_salve_150pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{e02d854c-ceb5-4e67-b33c-3466367f96db}
    NameForDebug       = 'Tps_entre_salve_150pourcent'
    EffectsDescriptors = [
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.50
        ),
    ]
)
export UnitEffect_Tps_entre_salve_50pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{5905716c-7f01-471d-95e2-19a0480a51be}
    NameForDebug       = 'Tps_entre_salve_50pourcent'
    EffectsDescriptors = [
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.50
        ),
    ]
)
export UnitEffect_Tps_entre_salve_66pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{22ea5e3c-c68f-4aa2-a553-c1a0ea660a3c}
    NameForDebug       = 'Tps_entre_salve_66pourcent'
    EffectsDescriptors = [
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.67
        ),
    ]
)
export UnitEffect_Tps_entre_salve_75pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{c4aa9769-b093-496d-bba0-f436a7501abe}
    NameForDebug       = 'Tps_entre_salve_75pourcent'
    EffectsDescriptors = [
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.75
        ),
    ]
)
export UnitEffect_Tps_entre_salve_85pourcent is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{c200d7af-efe9-4f6f-8c48-715a59ada274}
    NameForDebug       = 'Tps_entre_salve_85pourcent'
    EffectsDescriptors = [
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.85
        ),
    ]
)
export UnitEffect_Trepied is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{9e70af7d-fbc6-499b-bf12-de171c4e5c3b}
    NameForDebug       = 'Trepied'
    EffectsDescriptors = [
        TUnitEffectIncreaseConcealmentBonusDescriptor
        (
            ModifierType       = ~/ModifierType_Additionnel
            BonusConcealmentBonus  = 0.5
        ),
        TUnitEffectIncreaseWeaponPorteeMaxDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.25
        ),
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 10
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Trepied_ok",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_sniper"
        ),
    ]
)
export UnitEffect_Tutorial_MG42_RangeModification is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{eeb70764-984c-4cc7-a70d-4c33240f3b2e}
    NameForDebug       = 'Tutorial_MG42_RangeModification'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponPorteeMaxDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -25
        ),
        TStrategicSupplyMalusEffectDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            SupplyMalus        = 0
        ),
    ]
)
export UnitEffect_Unit_Stunned is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{07ef483f-40f1-463d-8e92-1c17e804e02a}
    NameForDebug       = 'Unit_Stunned'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Stunned",
            ]
        ),
    ]
)
export UnitEffect_Vehicule_Cowering is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{8dc6b11f-cd8d-4882-b12e-775299b25fc9}
    NameForDebug       = 'Vehicule_Cowering'
    EffectsDescriptors = [
        TUnitEffectIncreaseDangerousnessDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDangerousness    = -90
        ),
        TUnitEffectIncreaseInfluenceValueDescriptor
        (
            ModifierType       = ~/ModifierType_Multiplicatif
            Bonus              = 0.66
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "Panique",
            ]
        ),
        TUnitEffectTransportSlotNumberModificationDescriptor
        (
            ModifierType                 = ~/ModifierType_Remplacement
            EffectOnTransportSlotNumber  = 0
        ),
    ]
)
export UnitEffect_capture_allie_solo is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{840c7f3c-4db6-471c-bf4f-7f5ef8540855}
    NameForDebug       = 'capture_allie_solo'
    EffectsDescriptors = [
        TUnitEffectChangeTeamDescriptor
        (
            FutureTeam = TTeamIdentifierForUnitEffect(
                TeamType = ~/ETeamTypeForUnitEffect/Number
                TeamNumber = 0
            )
        ),
    ]
)
export UnitEffect_donne_capacite_capture_allie_solo is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{239e75e3-26a2-43db-b55e-dd40b407a16b}
    NameForDebug       = 'donne_capacite_capture_allie_solo'
    EffectsDescriptors = [
        TUnitEffectAddCapacityDescriptor
        (
            CapacityToAdd = [ ~/Capacite_Aura_capture_allie_solo ]
        ),
    ]
)
export UnitEffect_donne_capacite_commandement_1 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{828c508c-1312-42b9-89f9-f36c2e8ca197}
    NameForDebug       = 'donne_capacite_commandement_1'
    EffectsDescriptors = [
        TUnitEffectAddCapacityDescriptor
        (
            CapacityToAdd = [ ~/Capacite_Aura_Commandement_1 ]
        ),
    ]
)
export UnitEffect_donne_capacite_commandement_2 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{0adf1e40-8716-4574-8ae9-4f76d98c33c9}
    NameForDebug       = 'donne_capacite_commandement_2'
    EffectsDescriptors = [
        TUnitEffectAddCapacityDescriptor
        (
            CapacityToAdd = [ ~/Capacite_Aura_Commandement_2 ]
        ),
    ]
)
export UnitEffect_donne_capacite_commandement_3 is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{b3bbd71d-c023-4a29-938d-26583a2d4a12}
    NameForDebug       = 'donne_capacite_commandement_3'
    EffectsDescriptors = [
        TUnitEffectAddCapacityDescriptor
        (
            CapacityToAdd = [ ~/Capacite_Aura_Commandement_3 ]
        ),
    ]
)
export UnitEffect_electronic_warfare is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{d7705199-26ca-4687-bf5e-3c16c8a4bd3d}
    NameForDebug       = 'electronic_warfare'
    EffectsDescriptors = [
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -20
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = -20
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_electronic_warfare"
        ),
    ]
)
export UnitEffect_electronic_warfare_arty is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{9cfe5bf9-9e94-4141-8b63-5e4089faf144}
    NameForDebug       = 'electronic_warfare_arty'
    EffectsDescriptors = [
        TUnitEffectIncreaseDispersionDescriptor
        (
            ModifierType       = ~/ModifierType_Pourcentage
            BonusDispersion  = 50
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_electronic_warfare"
        ),
    ]
)
export UnitEffect_eo_dazzler is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{7a321bca-c294-462f-a15e-f7f43945d044}
    NameForDebug       = 'eo_dazzler'
    EffectsDescriptors = [
        TUnitEffectBonusHitRollECMDescriptor
        (
            ModifierType       = ~/ModifierType_Additionnel
            BonusHitRollECM    = -0.1
        ),
    ]
)
export UnitEffect_evac_avion is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{a202704a-8657-4e68-b4f6-d039ccdb2db5}
    NameForDebug       = 'evac_avion'
    EffectsDescriptors = [
        TEvacAirplaneEffectDescriptor(),
    ]
)
export UnitEffect_fireDirection is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{5b21c121-a627-4c15-9406-d82e53dee6b4}
    NameForDebug       = 'fireDirection'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.50
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_fireDirection"
        ),
    ]
)
export UnitEffect_jammer is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{29d337e7-9faf-40e4-838c-73aa99954944}
    NameForDebug       = 'jammer'
    EffectsDescriptors = [
        TUnitEffectIncreaseOpticalStrengthDescriptor
        (
            ModifierType         = ~/ModifierType_Additionnel
            BonusOpticalStrength = -43
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "RadioJammed",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_electronic_warfare"
        ),
    ]
)
export UnitEffect_military_police is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{0f4d7e16-6a16-4268-8248-13f357aed0ad}
    NameForDebug       = 'military_police'
    EffectsDescriptors = [
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 1.25
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "reserviste_immune",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_mp_given"
        ),
    ]
)
export UnitEffect_military_police_feedback is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{09071ac9-8900-4e88-b230-54abbdf1b774}
    NameForDebug       = 'military_police_feedback'
    EffectsDescriptors = [
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_mp"
        ),
    ]
)
export UnitEffect_reserviste is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{1dff7959-88b2-406a-97e2-5ea999886bf8}
    NameForDebug       = 'reserviste'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Multiplicatif
            BonusDamage         = 1.5
            DamageType          = EDamageType/Suppress
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.20
        ),
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = -5
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = -5
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.15
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_reserviste"
        ),
    ]
)
export UnitEffect_resolute is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{1fdf04c3-e3f9-4f93-bc43-775773cc8303}
    NameForDebug       = 'resolute'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -20
            DamageType          = EDamageType/Suppress
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 0.5
            DamageType = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_security is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{8a7d7966-8093-4ec0-88ff-28ad4b49d224}
    NameForDebug       = 'security'
    EffectsDescriptors = [
        TUnitEffectIncreaseOpticalStrengthDescriptor
        (
            ModifierType         = ~/ModifierType_Additionnel
            BonusOpticalStrength = 43
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_security"
        ),
    ]
)
export UnitEffect_sigint_close is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{ba449531-00da-441d-b398-2ffc8257d451}
    NameForDebug       = 'sigint_close'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "sigint_close",
                 "sigint_ok",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_sigint_close"
        ),
    ]
)
export UnitEffect_sigint_far is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{10c4c1c6-f2d4-4e92-81d3-a22a348371a9}
    NameForDebug       = 'sigint_far'
    EffectsDescriptors = [
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "sigint_ok",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_sigint_far"
        ),
    ]
)
export UnitEffect_sigint_feedback is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{8dd93fa6-2b20-4386-9741-e83b1909b42a}
    NameForDebug       = 'sigint_feedback'
    EffectsDescriptors = [
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_sigint_off"
        ),
    ]
)
export UnitEffect_sniper is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{e0bdcad9-2b57-415b-bcd0-9f2ba391e1d6}
    NameForDebug       = 'sniper'
    EffectsDescriptors = [
        TUnitEffectIncreaseConcealmentBonusDescriptor
        (
            ModifierType       = ~/ModifierType_Additionnel
            BonusConcealmentBonus  = 0.5
        ),
        TUnitEffectIncreaseWeaponPhysicalDamagesDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 500
        ),
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Pourcentage
            ModifierValue = 20
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "snipe_ok",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_sniper"
        ),
    ]
)
export UnitEffect_sniper_feedback is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{78a076bd-4203-4785-b780-c5531b0b2a87}
    NameForDebug       = 'sniper_feedback'
    EffectsDescriptors = [
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_sniper_off"
        ),
    ]
)
export UnitEffect_stressOnMiss_high is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{d9885cc3-77f6-4736-b3f0-8f446512f82f}
    NameForDebug       = 'stressOnMiss_high'
    EffectsDescriptors = [
        TUnitEffectIncreaseOpticalStrengthDescriptor
        (
            ModifierType         = ~/ModifierType_Additionnel
            BonusOpticalStrength = -43
        ),
        TEffectInflictSuppressDamageDescriptor
        (
            ModifierType             = ~/ModifierType_Additionnel
            SuppressDamageValue      = 250
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "RadioJammed",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_effet_stressOnMiss"
        ),
    ]
)
export UnitEffect_stressOnMiss_low is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{534b7c38-117f-4704-bd48-119646d21ef1}
    NameForDebug       = 'stressOnMiss_low'
    EffectsDescriptors = [
        TUnitEffectIncreaseOpticalStrengthDescriptor
        (
            ModifierType         = ~/ModifierType_Additionnel
            BonusOpticalStrength = -43
        ),
        TEffectInflictSuppressDamageDescriptor
        (
            ModifierType             = ~/ModifierType_Additionnel
            SuppressDamageValue      = 75
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "RadioJammed",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_effet_stressOnMiss"
        ),
    ]
)
export UnitEffect_stressOnMiss_mid is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{6bd1c672-b306-40f3-b5cb-f4f8c2fcce6f}
    NameForDebug       = 'stressOnMiss_mid'
    EffectsDescriptors = [
        TUnitEffectIncreaseOpticalStrengthDescriptor
        (
            ModifierType         = ~/ModifierType_Additionnel
            BonusOpticalStrength = -43
        ),
        TEffectInflictSuppressDamageDescriptor
        (
            ModifierType             = ~/ModifierType_Additionnel
            SuppressDamageValue      = 150
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "RadioJammed",
            ]
        ),
        TUnitEffectShowLabelIconDescriptor
        (
            TextureToken         = "icone_effet_stressOnMiss"
        ),
    ]
)
export UnitEffect_xp_elite is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{3609c0d0-e709-4218-82f3-b17000180ab2}
    NameForDebug       = 'xp_elite'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -32
            DamageType          = EDamageType/Suppress
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.88
        ),
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 15
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 15
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.80
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 2.7
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_elite",
            ]
        ),
    ]
)
export UnitEffect_xp_elite_SF is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{6a5f1848-6743-445e-8ca7-3cb6294908f4}
    NameForDebug       = 'xp_elite_SF'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -40
            DamageType          = EDamageType/Suppress
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Pourcentage
            BonusSpeedBaseInPercent  = 30
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.70
        ),
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 16
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 16
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.70
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 3.3
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_elite",
            ]
        ),
    ]
)
export UnitEffect_xp_elite_arty is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{345aa5ca-d104-4bf9-a763-5f05c84d1c52}
    NameForDebug       = 'xp_elite_arty'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -24
            DamageType          = EDamageType/Suppress
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.55
        ),
        TUnitEffectIncreaseWeaponDispersionMaxRangeDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.84
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.40
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 2.2
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_elite",
            ]
        ),
    ]
)
export UnitEffect_xp_elite_avion is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{fdf98921-e369-4f91-8cbe-0c76a6439f2b}
    NameForDebug       = 'xp_elite_avion'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -40
            DamageType          = EDamageType/Suppress
        ),
        TUnitEffectBonusPrecisionWhenTargetedDescriptor
        (
            ModifierType       = ~/ModifierType_Additionnel
            BonusPrecisionWhenTargeted = -16
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.80
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 12
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 3
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_elite",
            ]
        ),
    ]
)
export UnitEffect_xp_elite_helo is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{eb44f6ec-4392-4414-839f-695eec281fda}
    NameForDebug       = 'xp_elite_helo'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -45
            DamageType          = EDamageType/Suppress
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.40
        ),
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 16
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 16
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 4
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_elite",
            ]
        ),
    ]
)
export UnitEffect_xp_poorly_trained is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{3f22d303-a701-4daa-84bf-67d25e62c5ae}
    NameForDebug       = 'xp_poorly_trained'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = 14
            DamageType          = EDamageType/Suppress
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.08
        ),
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = -10
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 5
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 1.08
        ),
    ]
)
export UnitEffect_xp_rookie is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{320e6c09-c78a-4afb-910e-5616f69b0444}
    NameForDebug       = 'xp_rookie'
    EffectsDescriptors = [
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 0.6
            DamageType = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_xp_rookie_arty is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{de77c916-c5a9-41b0-a86d-ae69fa0b0671}
    NameForDebug       = 'xp_rookie_arty'
    EffectsDescriptors = [
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 0.6
            DamageType = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_xp_rookie_avion is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{9d93a0b1-9970-46b1-b405-64b8c4e19285}
    NameForDebug       = 'xp_rookie_avion'
    EffectsDescriptors = [
    ]
)
export UnitEffect_xp_rookie_helo is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{9cc64b1b-bdd9-4901-90e9-b822bca6dc67}
    NameForDebug       = 'xp_rookie_helo'
    EffectsDescriptors = [
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 0.6
            DamageType = EDamageType/Suppress
        ),
    ]
)
export UnitEffect_xp_trained is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{66db5899-38e9-4a52-8ffd-f9e0ec1f8da9}
    NameForDebug       = 'xp_trained'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -14
            DamageType          = EDamageType/Suppress
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.96
        ),
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 5
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 5
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.90
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 1.9
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_regular",
            ]
        ),
    ]
)
export UnitEffect_xp_trained_SF is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{df7b260d-5cc2-48b6-8f25-09c4affb34f1}
    NameForDebug       = 'xp_trained_SF'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -20
            DamageType          = EDamageType/Suppress
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Pourcentage
            BonusSpeedBaseInPercent  = 10
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.9
        ),
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 8
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 8
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.9
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 2.3
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_regular",
            ]
        ),
    ]
)
export UnitEffect_xp_trained_arty is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{f9e5d851-8f26-494c-ab44-c18d8ec2eebb}
    NameForDebug       = 'xp_trained_arty'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -6
            DamageType          = EDamageType/Suppress
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.85
        ),
        TUnitEffectIncreaseWeaponDispersionMaxRangeDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.94
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.80
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 1.4
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_regular",
            ]
        ),
    ]
)
export UnitEffect_xp_trained_avion is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{02cd7a2d-d05a-4c03-b71d-6f70734d5ba2}
    NameForDebug       = 'xp_trained_avion'
    EffectsDescriptors = [
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 1
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_regular",
            ]
        ),
    ]
)
export UnitEffect_xp_trained_helo is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{95f64a6f-5bd8-497d-ab2c-8b1a129060ce}
    NameForDebug       = 'xp_trained_helo'
    EffectsDescriptors = [
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.8
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 2
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_regular",
            ]
        ),
    ]
)
export UnitEffect_xp_veteran is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{27994994-4e24-4508-8084-56579ebacdb8}
    NameForDebug       = 'xp_veteran'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -22
            DamageType          = EDamageType/Suppress
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.92
        ),
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 10
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 10
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.85
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 2.3
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_veteran",
            ]
        ),
    ]
)
export UnitEffect_xp_veteran_SF is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{ae26a7d2-b9f5-4305-a3dd-c49a3a84c4b3}
    NameForDebug       = 'xp_veteran_SF'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -30
            DamageType          = EDamageType/Suppress
        ),
        TUnitEffectIncreaseSpeedDescriptor
        (
            ModifierType             = ~/ModifierType_Pourcentage
            BonusSpeedBaseInPercent  = 20
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.8
        ),
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 12
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 12
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.8
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 2.9
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_veteran",
            ]
        ),
    ]
)
export UnitEffect_xp_veteran_arty is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{ea968fcf-3393-430f-8673-b13e28de8248}
    NameForDebug       = 'xp_veteran_arty'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -12
            DamageType          = EDamageType/Suppress
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.70
        ),
        TUnitEffectIncreaseWeaponDispersionMaxRangeDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.88
        ),
        TUnitEffectAlterWeaponTempsEntreDeuxSalvesDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.60
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 1.8
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_veteran",
            ]
        ),
    ]
)
export UnitEffect_xp_veteran_avion is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{3bb18d5b-9841-4ba3-9ab2-58fc9645841c}
    NameForDebug       = 'xp_veteran_avion'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -20
            DamageType          = EDamageType/Suppress
        ),
        TUnitEffectBonusPrecisionWhenTargetedDescriptor
        (
            ModifierType       = ~/ModifierType_Additionnel
            BonusPrecisionWhenTargeted = -8
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.90
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 6
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 2
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_veteran",
            ]
        ),
    ]
)
export UnitEffect_xp_veteran_helo is TEffectsPackDescriptor
(
    DescriptorId       = GUID:{0f6f1863-5b9b-49a0-8caa-ad76d319c244}
    NameForDebug       = 'xp_veteran_helo'
    EffectsDescriptors = [
        TUnitEffectIncreaseDamageTakenDescriptor
        (
            ModifierType        = ~/ModifierType_Pourcentage
            BonusDamage         = -25
            DamageType          = EDamageType/Suppress
        ),
        TBonusWeaponAimtimeEffectDescriptor
        (
            ModifierType  = ~/ModifierType_Multiplicatif
            ModifierValue = 0.60
        ),
        TUnitEffectIncreaseWeaponPrecisionArretDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 8
        ),
        TUnitEffectIncreaseWeaponPrecisionMouvementDescriptor
        (
            ModifierType  = ~/ModifierType_Additionnel
            ModifierValue = 8
        ),
        TUnitEffectHealOverTimeDescriptor
        (
            NbUpdatePerSecond  = 1
            HealUnitsPerSecond = 3
            DamageType = EDamageType/Suppress
        ),
        TUnitEffectRaiseTagDescriptor
        (
            TagListToRaise = [
                 "xp_veteran",
            ]
        ),
    ]
)

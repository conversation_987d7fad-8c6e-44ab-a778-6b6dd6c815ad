// Ne pas éditer, ce fichier est généré par InfantryWeaponsUnitsFileWriter_Specific



InfantryWeapon_PM_Sterling is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_Sterling,
        ]
    )
)

InfantryWeapon_RocketInf_LAW_80 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_LAW_80,
        ]
    )
)

InfantryWeapon_RocketInf_Carl_Gustav is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_Carl_Gustav,
        ]
    )
)

InfantryWeapon_FM_M16 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_M16,
        ]
    )
)

InfantryWeapon_MMG_M60_7_62mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_MMG_M60_7_62mm,
        ]
    )
)

InfantryWeapon_RocketInf_M202_Flash_66mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_M202_Flash_66mm,
        ]
    )
)

InfantryWeapon_Grenade_Satchel_Charge is TIntroduceMobileHappening
(
    Happening = fx_traine_grenade_Satchem_2_False_00_00
)

InfantryWeapon_SAW_M249_5_56mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_SAW_M249_5_56mm,
        ]
    )
)

InfantryWeapon_RocketInf_M72_LAW_66mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_M72_LAW_66mm,
        ]
    )
)

InfantryWeapon_RocketInf_AT4_83mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_AT4_83mm,
        ]
    )
)

InfantryWeapon_Grenade_SMOKE is TIntroduceMobileHappening
(
    Happening = fx_traine_grenade_AN_M16_2_False_00_00
)

InfantryWeapon_M47_DRAGON_II is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_missile_ATGM_Gen2_5,
            $/GFX/Sound/SFXWeapon_M47_DRAGON_II,
        ]
    )
)

InfantryWeapon_Sniper_M14 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_7_62_2_False_00_00,
            $/GFX/Sound/SFXWeapon_Sniper_M14,
        ]
    )
)

InfantryWeapon_MMG_inf_M240B_7_62mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_MMG_inf_M240B_7_62mm,
        ]
    )
)

InfantryWeapon_RocketInf_M67_RCL_90mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Recoiless_Gun_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_M67_RCL_90mm,
        ]
    )
)

InfantryWeapon_RocketInf_M67_RCL_90mm_HE is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Recoiless_Gun_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_M67_RCL_90mm_HE,
        ]
    )
)

InfantryWeapon_FM_L85A1 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_L85A1,
        ]
    )
)

InfantryWeapon_MMG_inf_L7A2_7_62mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_MMG_inf_L7A2_7_62mm,
        ]
    )
)

InfantryWeapon_SAW_L86A1_5_56mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_SAW_L86A1_5_56mm,
        ]
    )
)

InfantryWeapon_PM_Skorpion is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_Skorpion,
        ]
    )
)

InfantryWeapon_Sniper_SVD_Dragunov is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_7_62_2_False_00_00,
            $/GFX/Sound/SFXWeapon_Sniper_SVD_Dragunov,
        ]
    )
)

InfantryWeapon_PM_MP_5A3 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_MP_5A3,
        ]
    )
)

InfantryWeapon_FM_G3KA4 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_G3KA4,
        ]
    )
)

InfantryWeapon_MMG_inf__MG3_7_62mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Mg3_Inf_1_2_False_00_00,
                    fx_traine_Mg3_Inf_2_2_False_00_00,
                    fx_traine_Mg3_Inf_3_2_False_00_00,
                    fx_traine_Mg3_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_MMG_inf__MG3_7_62mm,
        ]
    )
)

InfantryWeapon_RocketInf_Blindicide_RL_100 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_Blindicide_RL_100,
        ]
    )
)

InfantryWeapon_FM_FAMAS is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_FAMAS,
        ]
    )
)

InfantryWeapon_MMG_inf_AANF1_7_62mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_MMG_inf_AANF1_7_62mm,
        ]
    )
)

InfantryWeapon_Sniper_FRF1 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_7_62_2_False_00_00,
            $/GFX/Sound/SFXWeapon_Sniper_FRF1,
        ]
    )
)

InfantryWeapon_RocketInf_LRAC_F1 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_LRAC_F1,
        ]
    )
)

InfantryWeapon_PM_uzi is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_uzi,
        ]
    )
)

InfantryWeapon_SAW_Bren_L4A4 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_SAW_Bren_L4A4,
        ]
    )
)

InfantryWeapon_FM_FAL is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_FAL,
        ]
    )
)

InfantryWeapon_Sniper_FAL is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_7_62_2_False_00_00,
            $/GFX/Sound/SFXWeapon_Sniper_FAL,
        ]
    )
)

InfantryWeapon_MMG_inf_MAG_7_62mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_MMG_inf_MAG_7_62mm,
        ]
    )
)

InfantryWeapon_SAW_Minimi_5_56mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_SAW_Minimi_5_56mm,
        ]
    )
)

InfantryWeapon_Sniper_FRF2 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_7_62_2_False_00_00,
            $/GFX/Sound/SFXWeapon_Sniper_FRF2,
        ]
    )
)

InfantryWeapon_RocketInf_WASP_58mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_WASP_58mm,
        ]
    )
)

InfantryWeapon_PM_PM63_RAK is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_PM63_RAK,
        ]
    )
)

InfantryWeapon_RocketInf_RPG7VL is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_RPG7VL,
        ]
    )
)

InfantryWeapon_FM_SIG_543 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_SIG_543,
        ]
    )
)

InfantryWeapon_FM_Tantal is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_Tantal,
        ]
    )
)

InfantryWeapon_MMG_PKM_7_62mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_green_1_2_False_00_00,
                    fx_traine_MMG_Inf_green_2_2_False_00_00,
                    fx_traine_MMG_Inf_green_3_2_False_00_00,
                    fx_traine_MMG_Inf_green_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_MMG_PKM_7_62mm,
        ]
    )
)

InfantryWeapon_RocketInf_RPG7 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_RPG7,
        ]
    )
)

InfantryWeapon_PM_AKSU_74 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_AKSU_74,
        ]
    )
)

InfantryWeapon_RocketInf_RPO_A_93mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_RPO_A_93mm,
        ]
    )
)

InfantryWeapon_SAW_RPK_74_5_56mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_green_1_2_False_00_00,
                    fx_traine_MMG_Inf_green_2_2_False_00_00,
                    fx_traine_MMG_Inf_green_3_2_False_00_00,
                    fx_traine_MMG_Inf_green_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_SAW_RPK_74_5_56mm,
        ]
    )
)

InfantryWeapon_RocketInf_RPG22_72_5mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_RPG22_72_5mm,
        ]
    )
)

InfantryWeapon_FM_AKS_74 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_AKS_74,
        ]
    )
)

InfantryWeapon_RocketInf_RPG18_64mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_RPG18_64mm,
        ]
    )
)

InfantryWeapon_ATGM_9K115_Metis_M is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_missile_ATGM_Gen2_5,
            $/GFX/Sound/SFXWeapon_ATGM_9K115_Metis_M,
        ]
    )
)

InfantryWeapon_RocketInf_RPG16 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_RPG16,
        ]
    )
)

InfantryWeapon_PM_M4_Carbine is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_M4_Carbine,
        ]
    )
)

InfantryWeapon_Sniper_M24 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_7_62_2_False_00_00,
            $/GFX/Sound/SFXWeapon_Sniper_M24,
        ]
    )
)

InfantryWeapon_FM_Mpi_AK_74N is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_Mpi_AK_74N,
        ]
    )
)

InfantryWeapon_RocketInf_AGI_3X40_40mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_AGI_3X40_40mm,
        ]
    )
)

InfantryWeapon_FM_FNC is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_FNC,
        ]
    )
)

InfantryWeapon_RocketInf_Blindicide_RL_83 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_Blindicide_RL_83,
        ]
    )
)

InfantryWeapon_FM_kbk_AK is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_kbk_AK,
        ]
    )
)

InfantryWeapon_RocketInf_RPG76_Komar is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_RPG76_Komar,
        ]
    )
)

InfantryWeapon_FM_AK_74 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_AK_74,
        ]
    )
)

InfantryWeapon_M47_DRAGON is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_missile_ATGM_Gen1_5,
            $/GFX/Sound/SFXWeapon_M47_DRAGON,
        ]
    )
)

InfantryWeapon_RocketInf_HAFLA is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_HAFLA,
        ]
    )
)

InfantryWeapon_flamethrower_LPO is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_depart_tir_lance_flammes_1_2,
                    fx_depart_tir_lance_flammes_2_2,
                    fx_depart_tir_lance_flammes_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_lance_flammes_1_2_False_00_00,
                    fx_tir_lance_flammes_2_2_False_00_00,
                    fx_tir_lance_flammes_3_2_False_00_00,
                    fx_tir_lance_flammes_4_2_False_00_00,
                    fx_tir_lance_flammes_5_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_flamethrower_LPO,
        ]
    )
)

InfantryWeapon_RocketInf_RPO_RYS is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_RPO_RYS,
        ]
    )
)

InfantryWeapon_RocketInf_Handflammpatrone is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_Handflammpatrone,
        ]
    )
)

InfantryWeapon_PM_PPS_43 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_PPS_43,
        ]
    )
)

InfantryWeapon_FM_AKM is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_AKM,
        ]
    )
)

InfantryWeapon_FM_KMS_72 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_KMS_72,
        ]
    )
)

InfantryWeapon_SAW_lMG_K_7_62mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_green_1_2_False_00_00,
                    fx_traine_MMG_Inf_green_2_2_False_00_00,
                    fx_traine_MMG_Inf_green_3_2_False_00_00,
                    fx_traine_MMG_Inf_green_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_SAW_lMG_K_7_62mm,
        ]
    )
)

InfantryWeapon_FM_Mpi_AKS_74NK is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_Mpi_AKS_74NK,
        ]
    )
)

InfantryWeapon_SAW_DP28_7_62mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_SAW_DP28_7_62mm,
        ]
    )
)

InfantryWeapon_FM_L1A1_SLR is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_L1A1_SLR,
        ]
    )
)

InfantryWeapon_FM_kbk_AKM is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_kbk_AKM,
        ]
    )
)

InfantryWeapon_PM_MP_5SD is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_MP_5SD,
        ]
    )
)

InfantryWeapon_SAW_HK21_7_62mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_SAW_HK21_7_62mm,
        ]
    )
)

InfantryWeapon_RocketInf_PzF_3 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_PzF_3,
        ]
    )
)

InfantryWeapon_Sniper_G3A3ZF is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_7_62_2_False_00_00,
            $/GFX/Sound/SFXWeapon_Sniper_G3A3ZF,
        ]
    )
)

InfantryWeapon_RocketInf_RPG29_105mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_RPG29_105mm,
        ]
    )
)

InfantryWeapon_PM_MAT_49 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_MAT_49,
        ]
    )
)

InfantryWeapon_FM_MAS_56 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_MAS_56,
        ]
    )
)

InfantryWeapon_MMG_inf_AA52_7_5mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_MMG_inf_AA52_7_5mm,
        ]
    )
)

InfantryWeapon_RocketInf_APILAS is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_APILAS,
        ]
    )
)

InfantryWeapon_RocketInf_RPG7VR_64mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_RPG7VR_64mm,
        ]
    )
)

InfantryWeapon_RocketInf_RPG26_72_5mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_RPG26_72_5mm,
        ]
    )
)

InfantryWeapon_FM_M16A1 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_M16A1,
        ]
    )
)

InfantryWeapon_RocketInf_PzF_44 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_PzF_44,
        ]
    )
)

InfantryWeapon_PM_MPi_AKSU_74NK is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_AK_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_MPi_AKSU_74NK,
        ]
    )
)

InfantryWeapon_RocketInf_RPG2 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_RPG2,
        ]
    )
)

InfantryWeapon_Sniper_FN_Model_3011 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_7_62_2_False_00_00,
            $/GFX/Sound/SFXWeapon_Sniper_FN_Model_3011,
        ]
    )
)

InfantryWeapon_RocketInf_M72A1_LAW_66mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_M72A1_LAW_66mm,
        ]
    )
)

InfantryWeapon_Sniper_L96A1 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_7_62_2_False_00_00,
            $/GFX/Sound/SFXWeapon_Sniper_L96A1,
        ]
    )
)

InfantryWeapon_RocketInf_Viper_70mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_Viper_70mm,
        ]
    )
)

InfantryWeapon_MANPAD_Blowpipe is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_missile_AA_petit_5,
            $/GFX/Sound/SFXWeapon_MANPAD_Blowpipe,
        ]
    )
)

InfantryWeapon_MANPAD_igla is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_missile_AA_petit_5,
            $/GFX/Sound/SFXWeapon_MANPAD_igla,
        ]
    )
)

InfantryWeapon_Javelin is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_missile_AA_petit_5,
            $/GFX/Sound/SFXWeapon_Javelin,
        ]
    )
)

InfantryWeapon_Mistral is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_missile_AA_petit_5,
            $/GFX/Sound/SFXWeapon_Mistral,
        ]
    )
)

InfantryWeapon_MANPAD_FIM43 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_missile_AA_petit_5,
            $/GFX/Sound/SFXWeapon_MANPAD_FIM43,
        ]
    )
)

InfantryWeapon_MANPAD_FIM92 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_missile_AA_petit_5,
            $/GFX/Sound/SFXWeapon_MANPAD_FIM92,
        ]
    )
)

InfantryWeapon_MANPAD_FIM92_A is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_missile_AA_petit_5,
            $/GFX/Sound/SFXWeapon_MANPAD_FIM92_A,
        ]
    )
)

InfantryWeapon_MANPAD_Strela2M is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_missile_AA_petit_5,
            $/GFX/Sound/SFXWeapon_MANPAD_Strela2M,
        ]
    )
)

InfantryWeapon_MANPAD_Strela3 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_missile_AA_petit_5,
            $/GFX/Sound/SFXWeapon_MANPAD_Strela3,
        ]
    )
)

InfantryWeapon_PM_Vigneron_M2 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_Vigneron_M2,
        ]
    )
)

InfantryWeapon_RocketInf_M20_SuperBaz is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_M20_SuperBaz,
        ]
    )
)

InfantryWeapon_FM_M14 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_M14,
        ]
    )
)

InfantryWeapon_MMG_M60E3_7_62mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_MMG_M60E3_7_62mm,
        ]
    )
)

InfantryWeapon_SAW_FALO is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_1_2_False_00_00,
                    fx_traine_MMG_Inf_2_2_False_00_00,
                    fx_traine_MMG_Inf_3_2_False_00_00,
                    fx_traine_MMG_Inf_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_SAW_FALO,
        ]
    )
)

InfantryWeapon_RocketInf_RPG27_105mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_RPG27_105mm,
        ]
    )
)

InfantryWeapon_PM_GreaseGun is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_GreaseGun,
        ]
    )
)

InfantryWeapon_flamethrower_M2 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_depart_tir_lance_flammes_1_2,
                    fx_depart_tir_lance_flammes_2_2,
                    fx_depart_tir_lance_flammes_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_lance_flammes_1_2_False_00_00,
                    fx_tir_lance_flammes_2_2_False_00_00,
                    fx_tir_lance_flammes_3_2_False_00_00,
                    fx_tir_lance_flammes_4_2_False_00_00,
                    fx_tir_lance_flammes_5_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_flamethrower_M2,
        ]
    )
)

InfantryWeapon_FM_FNC_Para is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_FNC_Para,
        ]
    )
)

InfantryWeapon_SAW_RPK_7_62mm is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_mitrailleuse_M60_douilles_eject_1_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_2_2,
                    fx_tir_mitrailleuse_M60_douilles_eject_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_MMG_Inf_green_1_2_False_00_00,
                    fx_traine_MMG_Inf_green_2_2_False_00_00,
                    fx_traine_MMG_Inf_green_3_2_False_00_00,
                    fx_traine_MMG_Inf_green_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_SAW_RPK_7_62mm,
        ]
    )
)

InfantryWeapon_flamethrower_LFP54 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_depart_tir_lance_flammes_1_2,
                    fx_depart_tir_lance_flammes_2_2,
                    fx_depart_tir_lance_flammes_3_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_lance_flammes_1_2_False_00_00,
                    fx_tir_lance_flammes_2_2_False_00_00,
                    fx_tir_lance_flammes_3_2_False_00_00,
                    fx_tir_lance_flammes_4_2_False_00_00,
                    fx_tir_lance_flammes_5_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_flamethrower_LFP54,
        ]
    )
)

InfantryWeapon_Sniper_Mosin_Nagant is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_7_62_2_False_00_00,
            $/GFX/Sound/SFXWeapon_Sniper_Mosin_Nagant,
        ]
    )
)

InfantryWeapon_RocketInf_LRAC_73 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_LAW_2,
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_LAW_1_2_False_00_00,
                    fx_traine_LAW_2_2_False_00_00,
                    fx_traine_LAW_3_2_False_00_00,
                    fx_traine_LAW_4_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_RocketInf_LRAC_73,
        ]
    )
)

InfantryWeapon_PM_C8_Carbine is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_C8_Carbine,
        ]
    )
)

InfantryWeapon_Sniper_PSG1 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_7_62_2_False_00_00,
            $/GFX/Sound/SFXWeapon_Sniper_PSG1,
        ]
    )
)

InfantryWeapon_PM_AS_Val is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_PM_AS_Val,
        ]
    )
)

InfantryWeapon_FM_SIG_540 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_SIG_540,
        ]
    )
)

InfantryWeapon_FM_StG_942 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_StG_942,
        ]
    )
)

InfantryWeapon_FM_Garand is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
        ]
    )
)

InfantryWeapon_SniperHvy_Barret_M82 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_Lourd_12_7_2_False_00_00,
            $/GFX/Sound/SFXWeapon_SniperHvy_Barret_M82,
        ]
    )
)

InfantryWeapon_Sniper_Remington_700 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_7_62_2_False_00_00,
            $/GFX/Sound/SFXWeapon_Sniper_Remington_700,
        ]
    )
)

InfantryWeapon_Sniper_VSS_Vintorez is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            fx_tir_Sniper_Lourd_12_7_Douille_2,
            fx_Traine_Sniper_7_62_2_False_00_00,
            $/GFX/Sound/SFXWeapon_Sniper_VSS_Vintorez,
        ]
    )
)

InfantryWeapon_FM_StG_941 is TIntroduceMobileHappening
(
    Happening = TCompositeHappening
    (
        SubHappenings = 
        [
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2,
                    fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2,
                ]
            ),
            TRandomHappening
            (
                Alternatives = 
                [
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00,
                    fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00,
                ]
            ),
            $/GFX/Sound/SFXWeapon_FM_StG_941,
        ]
    )
)
fx_tir_missile_ATGM_Gen2_5 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_missile_ATGM_Gen2
    Power           = 5.
    WaitBetweenShot = 1.0
)
fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_1
    Power           = 2.
    WaitBetweenShot = 0.5
)
fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_2
    Power           = 2.
    WaitBetweenShot = 0.5
)
fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_3
    Power           = 2.
    WaitBetweenShot = 0.5
)
fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_4
    Power           = 2.
    WaitBetweenShot = 0.5
)
fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_5
    Power           = 2.
    WaitBetweenShot = 0.5
)
fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_Fusil_Mitrailleur_M16_tres_petit_douilles_6
    Power           = 2.
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_1_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_1
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_10_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_10
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_11_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_11
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_12_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_12
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_13_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_13
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_14_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_14
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_15_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_15
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_2_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_2
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_3_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_3
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_4_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_4
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_5_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_5
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_6_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_6
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_7_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_7
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_8_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_8
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_AK_tres_petite_9_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_AK_tres_petite_9
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_1_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_1
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_10_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_10
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_11_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_11
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_12_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_12
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_13_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_13
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_14_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_14
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_15_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_15
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_2_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_2
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_3_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_3
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_4_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_4
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_5_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_5
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_6_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_6
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_7_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_7
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_8_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_8
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_Fusil_Mitrailleur_M16_tres_petite_9_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Fusil_Mitrailleur_M16_tres_petite_9
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 0.5
)
fx_traine_grenade_AN_M16_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_grenade_AN_M16
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 2.0
)
fx_traine_grenade_Satchem_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_grenade_Satchem
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 8.0
)
fx_tir_missile_AA_petit_5 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_missile_AA_petit
    Power           = 5.
    WaitBetweenShot = 1.0
)
fx_tir_missile_ATGM_Gen1_5 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_missile_ATGM_Gen1
    Power           = 5.
    WaitBetweenShot = 1.0
)
fx_tir_mitrailleuse_M60_douilles_eject_1_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_mitrailleuse_M60_douilles_eject_1
    Power           = 2.
    WaitBetweenShot = 1.0
)
fx_tir_mitrailleuse_M60_douilles_eject_2_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_mitrailleuse_M60_douilles_eject_2
    Power           = 2.
    WaitBetweenShot = 1.0
)
fx_tir_mitrailleuse_M60_douilles_eject_3_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_mitrailleuse_M60_douilles_eject_3
    Power           = 2.
    WaitBetweenShot = 1.0
)
fx_traine_MMG_Inf_1_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_MMG_Inf_1
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_MMG_Inf_2_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_MMG_Inf_2
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_MMG_Inf_3_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_MMG_Inf_3
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_MMG_Inf_4_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_MMG_Inf_4
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_MMG_Inf_green_1_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_MMG_Inf_green_1
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_MMG_Inf_green_2_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_MMG_Inf_green_2
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_MMG_Inf_green_3_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_MMG_Inf_green_3
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_MMG_Inf_green_4_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_MMG_Inf_green_4
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_Mg3_Inf_1_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Mg3_Inf_1
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_Mg3_Inf_2_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Mg3_Inf_2
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_Mg3_Inf_3_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Mg3_Inf_3
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_Mg3_Inf_4_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_Mg3_Inf_4
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_tir_LAW_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_LAW
    Power           = 2.
    WaitBetweenShot = 1.0
)
fx_traine_LAW_1_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_LAW_1
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_LAW_2_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_LAW_2
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_LAW_3_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_LAW_3
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_traine_LAW_4_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_traine_LAW_4
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 1.0
)
fx_tir_Recoiless_Gun_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_Recoiless_Gun
    Power           = 2.
    WaitBetweenShot = 1.0
)
fx_tir_Sniper_Lourd_12_7_Douille_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_tir_Sniper_Lourd_12_7_Douille
    Power           = 2.
    WaitBetweenShot = 2.0
)
fx_Traine_Sniper_7_62_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_Traine_Sniper_7_62
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 2.0
)
fx_Traine_Sniper_Lourd_12_7_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_Traine_Sniper_Lourd_12_7
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 5.0
)
fx_depart_tir_lance_flammes_1_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_depart_tir_lance_flammes_1
    Power           = 2.
    WaitBetweenShot = 3.0
)
fx_depart_tir_lance_flammes_2_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_depart_tir_lance_flammes_2
    Power           = 2.
    WaitBetweenShot = 3.0
)
fx_depart_tir_lance_flammes_3_2 is InfantryFiringHappening
(
    Action          = $/GFX/GameFx/fx_depart_tir_lance_flammes_3
    Power           = 2.
    WaitBetweenShot = 3.0
)
fx_tir_lance_flammes_1_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_tir_lance_flammes_1
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 3.0
)
fx_tir_lance_flammes_2_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_tir_lance_flammes_2
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 3.0
)
fx_tir_lance_flammes_3_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_tir_lance_flammes_3
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 3.0
)
fx_tir_lance_flammes_4_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_tir_lance_flammes_4
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 3.0
)
fx_tir_lance_flammes_5_2_False_00_00 is InfantryProjectileHappening
(
    Action          = $/GFX/GameFx/fx_tir_lance_flammes_5
    Power           = 2.
    WithoutPhysics  = False
    FluidFriction   = 0.0
    InitialSpeed    = 0.0
    WaitBetweenShot = 3.0
)
InfantryHappenings is MAP[
  ( ['FireEffect_ATGM_9K115_Metis_M'], InfantryWeapon_ATGM_9K115_Metis_M ),
  ( ['FireEffect_FM_AKM'], InfantryWeapon_FM_AKM ),
  ( ['FireEffect_FM_AKS_74'], InfantryWeapon_FM_AKS_74 ),
  ( ['FireEffect_FM_AK_74'], InfantryWeapon_FM_AK_74 ),
  ( ['FireEffect_FM_FAL'], InfantryWeapon_FM_FAL ),
  ( ['FireEffect_FM_FAMAS'], InfantryWeapon_FM_FAMAS ),
  ( ['FireEffect_FM_FNC'], InfantryWeapon_FM_FNC ),
  ( ['FireEffect_FM_FNC_Para'], InfantryWeapon_FM_FNC_Para ),
  ( ['FireEffect_FM_G3KA4'], InfantryWeapon_FM_G3KA4 ),
  ( ['FireEffect_FM_Garand'], InfantryWeapon_FM_Garand ),
  ( ['FireEffect_FM_KMS_72'], InfantryWeapon_FM_KMS_72 ),
  ( ['FireEffect_FM_L1A1_SLR'], InfantryWeapon_FM_L1A1_SLR ),
  ( ['FireEffect_FM_L85A1'], InfantryWeapon_FM_L85A1 ),
  ( ['FireEffect_FM_M14'], InfantryWeapon_FM_M14 ),
  ( ['FireEffect_FM_M16'], InfantryWeapon_FM_M16 ),
  ( ['FireEffect_FM_M16A1'], InfantryWeapon_FM_M16A1 ),
  ( ['FireEffect_FM_MAS_56'], InfantryWeapon_FM_MAS_56 ),
  ( ['FireEffect_FM_Mpi_AKS_74NK'], InfantryWeapon_FM_Mpi_AKS_74NK ),
  ( ['FireEffect_FM_Mpi_AK_74N'], InfantryWeapon_FM_Mpi_AK_74N ),
  ( ['FireEffect_FM_SIG_540'], InfantryWeapon_FM_SIG_540 ),
  ( ['FireEffect_FM_SIG_543'], InfantryWeapon_FM_SIG_543 ),
  ( ['FireEffect_FM_StG_941'], InfantryWeapon_FM_StG_941 ),
  ( ['FireEffect_FM_StG_942'], InfantryWeapon_FM_StG_942 ),
  ( ['FireEffect_FM_Tantal'], InfantryWeapon_FM_Tantal ),
  ( ['FireEffect_FM_kbk_AK'], InfantryWeapon_FM_kbk_AK ),
  ( ['FireEffect_FM_kbk_AKM'], InfantryWeapon_FM_kbk_AKM ),
  ( ['FireEffect_Grenade_SMOKE'], InfantryWeapon_Grenade_SMOKE ),
  ( ['FireEffect_Grenade_Satchel_Charge'], InfantryWeapon_Grenade_Satchel_Charge ),
  ( ['FireEffect_Javelin'], InfantryWeapon_Javelin ),
  ( ['FireEffect_M47_DRAGON'], InfantryWeapon_M47_DRAGON ),
  ( ['FireEffect_M47_DRAGON_II'], InfantryWeapon_M47_DRAGON_II ),
  ( ['FireEffect_MANPAD_Blowpipe'], InfantryWeapon_MANPAD_Blowpipe ),
  ( ['FireEffect_MANPAD_FIM43'], InfantryWeapon_MANPAD_FIM43 ),
  ( ['FireEffect_MANPAD_FIM92'], InfantryWeapon_MANPAD_FIM92 ),
  ( ['FireEffect_MANPAD_FIM92_A'], InfantryWeapon_MANPAD_FIM92_A ),
  ( ['FireEffect_MANPAD_Strela2M'], InfantryWeapon_MANPAD_Strela2M ),
  ( ['FireEffect_MANPAD_Strela3'], InfantryWeapon_MANPAD_Strela3 ),
  ( ['FireEffect_MANPAD_igla'], InfantryWeapon_MANPAD_igla ),
  ( ['FireEffect_MMG_M60E3_7_62mm'], InfantryWeapon_MMG_M60E3_7_62mm ),
  ( ['FireEffect_MMG_M60_7_62mm'], InfantryWeapon_MMG_M60_7_62mm ),
  ( ['FireEffect_MMG_PKM_7_62mm'], InfantryWeapon_MMG_PKM_7_62mm ),
  ( ['FireEffect_MMG_inf_AA52_7_5mm'], InfantryWeapon_MMG_inf_AA52_7_5mm ),
  ( ['FireEffect_MMG_inf_AANF1_7_62mm'], InfantryWeapon_MMG_inf_AANF1_7_62mm ),
  ( ['FireEffect_MMG_inf_L7A2_7_62mm'], InfantryWeapon_MMG_inf_L7A2_7_62mm ),
  ( ['FireEffect_MMG_inf_M240B_7_62mm'], InfantryWeapon_MMG_inf_M240B_7_62mm ),
  ( ['FireEffect_MMG_inf_MAG_7_62mm'], InfantryWeapon_MMG_inf_MAG_7_62mm ),
  ( ['FireEffect_MMG_inf__MG3_7_62mm'], InfantryWeapon_MMG_inf__MG3_7_62mm ),
  ( ['FireEffect_Mistral'], InfantryWeapon_Mistral ),
  ( ['FireEffect_PM_AKSU_74'], InfantryWeapon_PM_AKSU_74 ),
  ( ['FireEffect_PM_AS_Val'], InfantryWeapon_PM_AS_Val ),
  ( ['FireEffect_PM_C8_Carbine'], InfantryWeapon_PM_C8_Carbine ),
  ( ['FireEffect_PM_GreaseGun'], InfantryWeapon_PM_GreaseGun ),
  ( ['FireEffect_PM_M4_Carbine'], InfantryWeapon_PM_M4_Carbine ),
  ( ['FireEffect_PM_MAT_49'], InfantryWeapon_PM_MAT_49 ),
  ( ['FireEffect_PM_MP_5A3'], InfantryWeapon_PM_MP_5A3 ),
  ( ['FireEffect_PM_MP_5SD'], InfantryWeapon_PM_MP_5SD ),
  ( ['FireEffect_PM_MPi_AKSU_74NK'], InfantryWeapon_PM_MPi_AKSU_74NK ),
  ( ['FireEffect_PM_PM63_RAK'], InfantryWeapon_PM_PM63_RAK ),
  ( ['FireEffect_PM_PPS_43'], InfantryWeapon_PM_PPS_43 ),
  ( ['FireEffect_PM_Skorpion'], InfantryWeapon_PM_Skorpion ),
  ( ['FireEffect_PM_Sterling'], InfantryWeapon_PM_Sterling ),
  ( ['FireEffect_PM_Vigneron_M2'], InfantryWeapon_PM_Vigneron_M2 ),
  ( ['FireEffect_PM_uzi'], InfantryWeapon_PM_uzi ),
  ( ['FireEffect_RocketInf_AGI_3X40_40mm'], InfantryWeapon_RocketInf_AGI_3X40_40mm ),
  ( ['FireEffect_RocketInf_APILAS'], InfantryWeapon_RocketInf_APILAS ),
  ( ['FireEffect_RocketInf_AT4_83mm'], InfantryWeapon_RocketInf_AT4_83mm ),
  ( ['FireEffect_RocketInf_Blindicide_RL_100'], InfantryWeapon_RocketInf_Blindicide_RL_100 ),
  ( ['FireEffect_RocketInf_Blindicide_RL_83'], InfantryWeapon_RocketInf_Blindicide_RL_83 ),
  ( ['FireEffect_RocketInf_Carl_Gustav'], InfantryWeapon_RocketInf_Carl_Gustav ),
  ( ['FireEffect_RocketInf_HAFLA'], InfantryWeapon_RocketInf_HAFLA ),
  ( ['FireEffect_RocketInf_Handflammpatrone'], InfantryWeapon_RocketInf_Handflammpatrone ),
  ( ['FireEffect_RocketInf_LAW_80'], InfantryWeapon_RocketInf_LAW_80 ),
  ( ['FireEffect_RocketInf_LRAC_73'], InfantryWeapon_RocketInf_LRAC_73 ),
  ( ['FireEffect_RocketInf_LRAC_F1'], InfantryWeapon_RocketInf_LRAC_F1 ),
  ( ['FireEffect_RocketInf_M202_Flash_66mm'], InfantryWeapon_RocketInf_M202_Flash_66mm ),
  ( ['FireEffect_RocketInf_M20_SuperBaz'], InfantryWeapon_RocketInf_M20_SuperBaz ),
  ( ['FireEffect_RocketInf_M67_RCL_90mm'], InfantryWeapon_RocketInf_M67_RCL_90mm ),
  ( ['FireEffect_RocketInf_M67_RCL_90mm_HE'], InfantryWeapon_RocketInf_M67_RCL_90mm_HE ),
  ( ['FireEffect_RocketInf_M72A1_LAW_66mm'], InfantryWeapon_RocketInf_M72A1_LAW_66mm ),
  ( ['FireEffect_RocketInf_M72_LAW_66mm'], InfantryWeapon_RocketInf_M72_LAW_66mm ),
  ( ['FireEffect_RocketInf_PzF_3'], InfantryWeapon_RocketInf_PzF_3 ),
  ( ['FireEffect_RocketInf_PzF_44'], InfantryWeapon_RocketInf_PzF_44 ),
  ( ['FireEffect_RocketInf_RPG16'], InfantryWeapon_RocketInf_RPG16 ),
  ( ['FireEffect_RocketInf_RPG18_64mm'], InfantryWeapon_RocketInf_RPG18_64mm ),
  ( ['FireEffect_RocketInf_RPG2'], InfantryWeapon_RocketInf_RPG2 ),
  ( ['FireEffect_RocketInf_RPG22_72_5mm'], InfantryWeapon_RocketInf_RPG22_72_5mm ),
  ( ['FireEffect_RocketInf_RPG26_72_5mm'], InfantryWeapon_RocketInf_RPG26_72_5mm ),
  ( ['FireEffect_RocketInf_RPG27_105mm'], InfantryWeapon_RocketInf_RPG27_105mm ),
  ( ['FireEffect_RocketInf_RPG29_105mm'], InfantryWeapon_RocketInf_RPG29_105mm ),
  ( ['FireEffect_RocketInf_RPG7'], InfantryWeapon_RocketInf_RPG7 ),
  ( ['FireEffect_RocketInf_RPG76_Komar'], InfantryWeapon_RocketInf_RPG76_Komar ),
  ( ['FireEffect_RocketInf_RPG7VL'], InfantryWeapon_RocketInf_RPG7VL ),
  ( ['FireEffect_RocketInf_RPG7VR_64mm'], InfantryWeapon_RocketInf_RPG7VR_64mm ),
  ( ['FireEffect_RocketInf_RPO_A_93mm'], InfantryWeapon_RocketInf_RPO_A_93mm ),
  ( ['FireEffect_RocketInf_RPO_RYS'], InfantryWeapon_RocketInf_RPO_RYS ),
  ( ['FireEffect_RocketInf_Viper_70mm'], InfantryWeapon_RocketInf_Viper_70mm ),
  ( ['FireEffect_RocketInf_WASP_58mm'], InfantryWeapon_RocketInf_WASP_58mm ),
  ( ['FireEffect_SAW_Bren_L4A4'], InfantryWeapon_SAW_Bren_L4A4 ),
  ( ['FireEffect_SAW_DP28_7_62mm'], InfantryWeapon_SAW_DP28_7_62mm ),
  ( ['FireEffect_SAW_FALO'], InfantryWeapon_SAW_FALO ),
  ( ['FireEffect_SAW_HK21_7_62mm'], InfantryWeapon_SAW_HK21_7_62mm ),
  ( ['FireEffect_SAW_L86A1_5_56mm'], InfantryWeapon_SAW_L86A1_5_56mm ),
  ( ['FireEffect_SAW_M249_5_56mm'], InfantryWeapon_SAW_M249_5_56mm ),
  ( ['FireEffect_SAW_Minimi_5_56mm'], InfantryWeapon_SAW_Minimi_5_56mm ),
  ( ['FireEffect_SAW_RPK_74_5_56mm'], InfantryWeapon_SAW_RPK_74_5_56mm ),
  ( ['FireEffect_SAW_RPK_7_62mm'], InfantryWeapon_SAW_RPK_7_62mm ),
  ( ['FireEffect_SAW_lMG_K_7_62mm'], InfantryWeapon_SAW_lMG_K_7_62mm ),
  ( ['FireEffect_SniperHvy_Barret_M82'], InfantryWeapon_SniperHvy_Barret_M82 ),
  ( ['FireEffect_Sniper_FAL'], InfantryWeapon_Sniper_FAL ),
  ( ['FireEffect_Sniper_FN_Model_3011'], InfantryWeapon_Sniper_FN_Model_3011 ),
  ( ['FireEffect_Sniper_FRF1'], InfantryWeapon_Sniper_FRF1 ),
  ( ['FireEffect_Sniper_FRF2'], InfantryWeapon_Sniper_FRF2 ),
  ( ['FireEffect_Sniper_G3A3ZF'], InfantryWeapon_Sniper_G3A3ZF ),
  ( ['FireEffect_Sniper_L96A1'], InfantryWeapon_Sniper_L96A1 ),
  ( ['FireEffect_Sniper_M14'], InfantryWeapon_Sniper_M14 ),
  ( ['FireEffect_Sniper_M24'], InfantryWeapon_Sniper_M24 ),
  ( ['FireEffect_Sniper_Mosin_Nagant'], InfantryWeapon_Sniper_Mosin_Nagant ),
  ( ['FireEffect_Sniper_PSG1'], InfantryWeapon_Sniper_PSG1 ),
  ( ['FireEffect_Sniper_Remington_700'], InfantryWeapon_Sniper_Remington_700 ),
  ( ['FireEffect_Sniper_SVD_Dragunov'], InfantryWeapon_Sniper_SVD_Dragunov ),
  ( ['FireEffect_Sniper_VSS_Vintorez'], InfantryWeapon_Sniper_VSS_Vintorez ),
  ( ['FireEffect_flamethrower_LFP54'], InfantryWeapon_flamethrower_LFP54 ),
  ( ['FireEffect_flamethrower_LPO'], InfantryWeapon_flamethrower_LPO ),
  ( ['FireEffect_flamethrower_M2'], InfantryWeapon_flamethrower_M2 ),
]


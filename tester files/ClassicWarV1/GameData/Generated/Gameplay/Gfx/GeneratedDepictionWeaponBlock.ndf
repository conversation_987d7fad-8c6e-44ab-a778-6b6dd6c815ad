// Ne pas éditer, ce fichier est généré par DepictionWeaponsBlockFileWriter


Weapon_SAM_9M8M3_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_9M8M3_x2,
        $/GFX/Sound/SFXWeapon_SAM_9M8M3_x2,
    ]
)
Weapon_SAM_9M336_x3 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_9M336_x3,
        $/GFX/Sound/SFXWeapon_SAM_9M336_x3,
    ]
)
Weapon_Howz_Canon_2A64_152mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A64_152mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A64_152mm,
    ]
)
Weapon_Howz_Canon_2A64_152mm_Direct is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A64_152mm_Direct,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A64_152mm_Direct,
    ]
)
Weapon_HMG_12_7_mm_NSVT is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_12_7_mm_NSVT,
        $/GFX/Sound/SFXWeapon_HMG_12_7_mm_NSVT,
    ]
)
Weapon_SMOKE_Vehicle_Grenadex8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SMOKE_Vehicle_Grenadex8,
        $/GFX/Sound/SFXWeapon_SMOKE_Vehicle_Grenadex8,
    ]
)
Weapon_Howz_Canon_2A18_Howitzer_122mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A18_Howitzer_122mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A18_Howitzer_122mm,
    ]
)
Weapon_Howz_Canon_2A18_Howitzer_122mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A18_Howitzer_122mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A18_Howitzer_122mm_SMOKE,
    ]
)
Weapon_Canon_AP_2A18_122mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_2A18_122mm,
        $/GFX/Sound/SFXWeapon_Canon_AP_2A18_122mm,
    ]
)
Weapon_Howz_Canon_2A60_Howitzer_120mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A60_Howitzer_120mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A60_Howitzer_120mm,
    ]
)
Weapon_Howz_Canon_2A60_Howitzer_120mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A60_Howitzer_120mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A60_Howitzer_120mm_SMOKE,
    ]
)
Weapon_Canon_AP_2A60_120mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_2A60_120mm,
        $/GFX/Sound/SFXWeapon_Canon_AP_2A60_120mm,
    ]
)
Weapon_MMG_PKT_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_PKT_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_PKT_7_62mm,
    ]
)
Weapon_Canon_HE_direct_2A51_120mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_direct_2A51_120mm,
        $/GFX/Sound/SFXWeapon_Canon_HE_direct_2A51_120mm,
    ]
)
Weapon_Howz_Canon_D22_Howitzer_152mm_late_guided is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D22_Howitzer_152mm_late_guided,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D22_Howitzer_152mm_late_guided,
    ]
)
Weapon_Howz_Canon_D22_Howitzer_152mm_late_guided_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D22_Howitzer_152mm_late_guided_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D22_Howitzer_152mm_late_guided_SMOKE,
    ]
)
Weapon_Canon_AP_D22_152mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_D22_152mm,
        $/GFX/Sound/SFXWeapon_Canon_AP_D22_152mm,
    ]
)
Weapon_Howz_Canon_D22_Howitzer_152mm_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D22_Howitzer_152mm_late,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D22_Howitzer_152mm_late,
    ]
)
Weapon_Howz_Canon_D22_Howitzer_152mm_late_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D22_Howitzer_152mm_late_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D22_Howitzer_152mm_late_SMOKE,
    ]
)
Weapon_Howz_Canon_2A36_SP_152mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A36_SP_152mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A36_SP_152mm,
    ]
)
Weapon_Howz_Canon_2A36_SP_152mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A36_SP_152mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A36_SP_152mm_SMOKE,
    ]
)
Weapon_Howz_Canon_2A44_Howitzer_203mm_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A44_Howitzer_203mm_late,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A44_Howitzer_203mm_late,
    ]
)
Weapon_Howz_Canon_2A44_Howitzer_203mm_late_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A44_Howitzer_203mm_late_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A44_Howitzer_203mm_late_SMOKE,
    ]
)
Weapon_Howz_Canon_2A44_Howitzer_203mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A44_Howitzer_203mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A44_Howitzer_203mm,
    ]
)
Weapon_Howz_Canon_2A44_Howitzer_203mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A44_Howitzer_203mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A44_Howitzer_203mm_SMOKE,
    ]
)
Weapon_Howz_Canon_2A51_Howitzer_120mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A51_Howitzer_120mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A51_Howitzer_120mm,
    ]
)
Weapon_Howz_Canon_2A51_Howitzer_120mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A51_Howitzer_120mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A51_Howitzer_120mm_SMOKE,
    ]
)
Weapon_Canon_AP_2A51_120mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_2A51_120mm,
        $/GFX/Sound/SFXWeapon_Canon_AP_2A51_120mm,
    ]
)
Weapon_Mortier_M252_81mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M252_81mm,
        $/GFX/Sound/SFXWeapon_Mortier_M252_81mm,
    ]
)
Weapon_Mortier_M252_81mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M252_81mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_M252_81mm_SMOKE,
    ]
)
Weapon_Mortier_81mm_TOWED_CLU_Guided is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_81mm_TOWED_CLU_Guided,
        $/GFX/Sound/SFXWeapon_Mortier_81mm_TOWED_CLU_Guided,
    ]
)
Weapon_Mortier_81mm_SMOKE_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_81mm_SMOKE_TOWED,
        $/GFX/Sound/SFXWeapon_Mortier_81mm_SMOKE_TOWED,
    ]
)
Weapon_Mortier_M29_81mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M29_81mm,
        $/GFX/Sound/SFXWeapon_Mortier_M29_81mm,
    ]
)
Weapon_Mortier_M29_81mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M29_81mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_M29_81mm_SMOKE,
    ]
)
Weapon_Mortier_81mm_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_81mm_TOWED,
        $/GFX/Sound/SFXWeapon_Mortier_81mm_TOWED,
    ]
)
Weapon_Howz_Canon_A222_Howitzer_130mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_A222_Howitzer_130mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_A222_Howitzer_130mm,
    ]
)
Weapon_HMG_12_7_mm_M2HB is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_12_7_mm_M2HB,
        $/GFX/Sound/SFXWeapon_HMG_12_7_mm_M2HB,
    ]
)
Weapon_AutoCanon_AP_25mm_KBA is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_25mm_KBA,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_25mm_KBA,
    ]
)
Weapon_AutoCanon_HE_25mm_KBA is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_25mm_KBA,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_25mm_KBA,
    ]
)
Weapon_MMG_FN_MAG_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_FN_MAG_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_FN_MAG_7_62mm,
    ]
)
Weapon_ATGM_MILAN is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_MILAN,
        $/GFX/Sound/SFXWeapon_ATGM_MILAN,
    ]
)
Weapon_ATGM_BGM71D_TOW_2_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_BGM71D_TOW_2_x2,
        $/GFX/Sound/SFXWeapon_ATGM_BGM71D_TOW_2_x2,
    ]
)
Weapon_Canon_HE_60mm_CM60A1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_60mm_CM60A1,
        $/GFX/Sound/SFXWeapon_Canon_HE_60mm_CM60A1,
    ]
)
Weapon_Canon_HE_60mm_CM60A1_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_60mm_CM60A1_SMOKE,
        $/GFX/Sound/SFXWeapon_Canon_HE_60mm_CM60A1_SMOKE,
    ]
)
Weapon_MMG_AANF1_7_62mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_AANF1_7_62mm_x2,
        $/GFX/Sound/SFXWeapon_MMG_AANF1_7_62mm_x2,
    ]
)
Weapon_Canon_AP_90_mm_GIAT_F1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_90_mm_GIAT_F1,
        $/GFX/Sound/SFXWeapon_Canon_AP_90_mm_GIAT_F1,
    ]
)
Weapon_Canon_HE_90_mm_GIAT_F1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_90_mm_GIAT_F1,
        $/GFX/Sound/SFXWeapon_Canon_HE_90_mm_GIAT_F1,
    ]
)
Weapon_MMG_AANF1_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_AANF1_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_AANF1_7_62mm,
    ]
)
Weapon_ATGM_HOT2_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_HOT2_x4,
        $/GFX/Sound/SFXWeapon_ATGM_HOT2_x4,
    ]
)
Weapon_AutoCanon_AP_M693_F1_20mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_M693_F1_20mm,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_M693_F1_20mm,
    ]
)
Weapon_AutoCanon_HE_M693_F1_20mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_M693_F1_20mm,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_M693_F1_20mm,
    ]
)
Weapon_Canon_AP_105mm_F2_BK_MECA_L48_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_F2_BK_MECA_L48_late,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_F2_BK_MECA_L48_late,
    ]
)
Weapon_Canon_HE_105mm_F2_BK_MECA_L48 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_F2_BK_MECA_L48,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_F2_BK_MECA_L48,
    ]
)
Weapon_Canon_AP_105mm_F2_BK_MECA_L48 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_F2_BK_MECA_L48,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_F2_BK_MECA_L48,
    ]
)
Weapon_Canon_AP_90_mm__F3 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_90_mm__F3,
        $/GFX/Sound/SFXWeapon_Canon_AP_90_mm__F3,
    ]
)
Weapon_Canon_HE_90_mm__F3 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_90_mm__F3,
        $/GFX/Sound/SFXWeapon_Canon_HE_90_mm__F3,
    ]
)
Weapon_DCA_2_canons_HS_831_30mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canons_HS_831_30mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canons_HS_831_30mm,
    ]
)
Weapon_Mortier_M1_81mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M1_81mm,
        $/GFX/Sound/SFXWeapon_Mortier_M1_81mm,
    ]
)
Weapon_Mortier_M1_81mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M1_81mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_M1_81mm_SMOKE,
    ]
)
Weapon_MMG_M1919 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_M1919,
        $/GFX/Sound/SFXWeapon_MMG_M1919,
    ]
)
Weapon_Howz_Canon_GCT_Howitzer_155mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_GCT_Howitzer_155mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_GCT_Howitzer_155mm,
    ]
)
Weapon_Howz_Canon_GCT_Howitzer_155mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_GCT_Howitzer_155mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_GCT_Howitzer_155mm_SMOKE,
    ]
)
Weapon_Howz_Canon_GCT_Howitzer_155mm_Direct is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_GCT_Howitzer_155mm_Direct,
        $/GFX/Sound/SFXWeapon_Howz_Canon_GCT_Howitzer_155mm_Direct,
    ]
)
Weapon_Canon_AP_105mm_CN_105_F1_BRENNUS is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_CN_105_F1_BRENNUS,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_CN_105_F1_BRENNUS,
    ]
)
Weapon_Canon_HE_105mm_CN_105_F1_BRENNUS is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_CN_105_F1_BRENNUS,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_CN_105_F1_BRENNUS,
    ]
)
Weapon_AutoCanon_HE_20mm_M621_GIAT_AMX30 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_20mm_M621_GIAT_AMX30,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_20mm_M621_GIAT_AMX30,
    ]
)
Weapon_AutoCanon_AP_20mm_M621_GIAT_AMX30 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_20mm_M621_GIAT_AMX30,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_20mm_M621_GIAT_AMX30,
    ]
)
Weapon_Canon_AP_105mm_CN_105_F1_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_CN_105_F1_late,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_CN_105_F1_late,
    ]
)
Weapon_Canon_HE_105mmCN_105_F1_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mmCN_105_F1_late,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mmCN_105_F1_late,
    ]
)
Weapon_Canon_AP_105mm_CN_105_F1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_CN_105_F1,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_CN_105_F1,
    ]
)
Weapon_Canon_HE_105mm_CN_105_F1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_CN_105_F1,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_CN_105_F1,
    ]
)
Weapon_Canon_HE_142mm_AVRE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_142mm_AVRE,
        $/GFX/Sound/SFXWeapon_Canon_HE_142mm_AVRE,
    ]
)
Weapon_Canon_AP_85mm_D48 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_85mm_D48,
        $/GFX/Sound/SFXWeapon_Canon_AP_85mm_D48,
    ]
)
Weapon_Canon_HE_85mm_D48 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_85mm_D48,
        $/GFX/Sound/SFXWeapon_Canon_HE_85mm_D48,
    ]
)
Weapon_MMG_SGMB_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_SGMB_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_SGMB_7_62mm,
    ]
)
Weapon_Canon_AP_125mm_2A45_Sprut is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_125mm_2A45_Sprut,
        $/GFX/Sound/SFXWeapon_Canon_AP_125mm_2A45_Sprut,
    ]
)
Weapon_Canon_HE_125mm_2A45_Sprut is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_125mm_2A45_Sprut,
        $/GFX/Sound/SFXWeapon_Canon_HE_125mm_2A45_Sprut,
    ]
)
Weapon_Canon_AP_85mm_D44 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_85mm_D44,
        $/GFX/Sound/SFXWeapon_Canon_AP_85mm_D44,
    ]
)
Weapon_Canon_HE_85mm_D44 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_85mm_D44,
        $/GFX/Sound/SFXWeapon_Canon_HE_85mm_D44,
    ]
)
Weapon_Canon_AP_KSM25_100mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_KSM25_100mm,
        $/GFX/Sound/SFXWeapon_Canon_AP_KSM25_100mm,
    ]
)
Weapon_Canon_HE__KSM25_100mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE__KSM25_100mm,
        $/GFX/Sound/SFXWeapon_Canon_HE__KSM25_100mm,
    ]
)
Weapon_Canon_AP_100mm_2A29_Rapira is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_100mm_2A29_Rapira,
        $/GFX/Sound/SFXWeapon_Canon_AP_100mm_2A29_Rapira,
    ]
)
Weapon_Canon_HE_100mm_2A29_Rapira is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_100mm_2A29_Rapira,
        $/GFX/Sound/SFXWeapon_Canon_HE_100mm_2A29_Rapira,
    ]
)
Weapon_ATGM_9M117_Bastion is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M117_Bastion,
        $/GFX/Sound/SFXWeapon_ATGM_9M117_Bastion,
    ]
)
Weapon_Canon_AP_57mm_ZiS2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_57mm_ZiS2,
        $/GFX/Sound/SFXWeapon_Canon_AP_57mm_ZiS2,
    ]
)
Weapon_Canon_AP_85mm_K52 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_85mm_K52,
        $/GFX/Sound/SFXWeapon_Canon_AP_85mm_K52,
    ]
)
Weapon_Canon_HE_85mm_K52 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_85mm_K52,
        $/GFX/Sound/SFXWeapon_Canon_HE_85mm_K52,
    ]
)
Weapon_ATGM_9K111M_Fagot_M is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9K111M_Fagot_M,
        $/GFX/Sound/SFXWeapon_ATGM_9K111M_Fagot_M,
    ]
)
Weapon_ATGM_BGM71C_ITOW is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_BGM71C_ITOW,
        $/GFX/Sound/SFXWeapon_ATGM_BGM71C_ITOW,
    ]
)
Weapon_ATGM_9M113_KonkursM_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M113_KonkursM_late,
        $/GFX/Sound/SFXWeapon_ATGM_9M113_KonkursM_late,
    ]
)
Weapon_ATGM_9M113_KonkursM is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M113_KonkursM,
        $/GFX/Sound/SFXWeapon_ATGM_9M113_KonkursM,
    ]
)
Weapon_ATGM_MILAN_2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_MILAN_2,
        $/GFX/Sound/SFXWeapon_ATGM_MILAN_2,
    ]
)
Weapon_RocketInf_B11_RCL_107mm_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketInf_B11_RCL_107mm_TOWED,
        $/GFX/Sound/SFXWeapon_RocketInf_B11_RCL_107mm_TOWED,
    ]
)
Weapon_RocketInf_B11_RCL_107mm_HE_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketInf_B11_RCL_107mm_HE_TOWED,
        $/GFX/Sound/SFXWeapon_RocketInf_B11_RCL_107mm_HE_TOWED,
    ]
)
Weapon_RocketInf_M40A1_RCL_106mm_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketInf_M40A1_RCL_106mm_TOWED,
        $/GFX/Sound/SFXWeapon_RocketInf_M40A1_RCL_106mm_TOWED,
    ]
)
Weapon_RocketInf_M40A1_RCL_106mm_HE_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketInf_M40A1_RCL_106mm_HE_TOWED,
        $/GFX/Sound/SFXWeapon_RocketInf_M40A1_RCL_106mm_HE_TOWED,
    ]
)
Weapon_Canon_HEAT_73_mm_SPG9_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HEAT_73_mm_SPG9_TOWED,
        $/GFX/Sound/SFXWeapon_Canon_HEAT_73_mm_SPG9_TOWED,
    ]
)
Weapon_Canon_HE_73_mm_SPG9_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_73_mm_SPG9_TOWED,
        $/GFX/Sound/SFXWeapon_Canon_HE_73_mm_SPG9_TOWED,
    ]
)
Weapon_ATGM_BGM71D_TOW_2A is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_BGM71D_TOW_2A,
        $/GFX/Sound/SFXWeapon_ATGM_BGM71D_TOW_2A,
    ]
)
Weapon_ATGM_BGM71D_TOW_2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_BGM71D_TOW_2,
        $/GFX/Sound/SFXWeapon_ATGM_BGM71D_TOW_2,
    ]
)
Weapon_ATGM_BGM71_TOW is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_BGM71_TOW,
        $/GFX/Sound/SFXWeapon_ATGM_BGM71_TOW,
    ]
)
Weapon_M47_DRAGON_Bipied is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_M47_DRAGON_Bipied,
        $/GFX/Sound/SFXWeapon_M47_DRAGON_Bipied,
    ]
)
Weapon_RocketArt_M24F_240mm_x12 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_M24F_240mm_x12,
        $/GFX/Sound/SFXWeapon_RocketArt_M24F_240mm_x12,
    ]
)
Weapon_RocketArt_M21OF_122mm_x12 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_M21OF_122mm_x12,
        $/GFX/Sound/SFXWeapon_RocketArt_M21OF_122mm_x12,
    ]
)
Weapon_RocketArt_M21OF_122mm_napalm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_M21OF_122mm_napalm,
        $/GFX/Sound/SFXWeapon_RocketArt_M21OF_122mm_napalm,
    ]
)
Weapon_RocketArt_M21OF_122mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_M21OF_122mm,
        $/GFX/Sound/SFXWeapon_RocketArt_M21OF_122mm,
    ]
)
Weapon_RocketArt_MD24F_240mm_x12 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_MD24F_240mm_x12,
        $/GFX/Sound/SFXWeapon_RocketArt_MD24F_240mm_x12,
    ]
)
Weapon_RocketArt_9M27F_HE_220mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_9M27F_HE_220mm,
        $/GFX/Sound/SFXWeapon_RocketArt_9M27F_HE_220mm,
    ]
)
Weapon_RocketArt_9M55K5_300mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_9M55K5_300mm,
        $/GFX/Sound/SFXWeapon_RocketArt_9M55K5_300mm,
    ]
)
Weapon_Canon_AP_73_mm_2A28_Grom is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_73_mm_2A28_Grom,
        $/GFX/Sound/SFXWeapon_Canon_AP_73_mm_2A28_Grom,
    ]
)
Weapon_Canon_HE_73_mm_2A28_Grom is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_73_mm_2A28_Grom,
        $/GFX/Sound/SFXWeapon_Canon_HE_73_mm_2A28_Grom,
    ]
)
Weapon_MMG_PKT_7_62mm_x3 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_PKT_7_62mm_x3,
        $/GFX/Sound/SFXWeapon_MMG_PKT_7_62mm_x3,
    ]
)
Weapon_AutoCanon_AP_30mm_24A2_BMP2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_30mm_24A2_BMP2,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_30mm_24A2_BMP2,
    ]
)
Weapon_AutoCanon_HE_30mm_24A2_BMP2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_30mm_24A2_BMP2,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_30mm_24A2_BMP2,
    ]
)
Weapon_ATGM_9M113_Konkurs_BMP2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M113_Konkurs_BMP2,
        $/GFX/Sound/SFXWeapon_ATGM_9M113_Konkurs_BMP2,
    ]
)
Weapon_MMG_PKT_7_62mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_PKT_7_62mm_x2,
        $/GFX/Sound/SFXWeapon_MMG_PKT_7_62mm_x2,
    ]
)
Weapon_Lance_grenade_AGS17 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Lance_grenade_AGS17,
        $/GFX/Sound/SFXWeapon_Lance_grenade_AGS17,
    ]
)
Weapon_ATGM_9M14_MalyutkaP is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M14_MalyutkaP,
        $/GFX/Sound/SFXWeapon_ATGM_9M14_MalyutkaP,
    ]
)
Weapon_Canon_HE_100mm_2A70 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_100mm_2A70,
        $/GFX/Sound/SFXWeapon_Canon_HE_100mm_2A70,
    ]
)
Weapon_AutoCanon_AP_30mm_2A72_BMP3 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_30mm_2A72_BMP3,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_30mm_2A72_BMP3,
    ]
)
Weapon_AutoCanon_HE_30mm_2A72_BMP3 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_30mm_2A72_BMP3,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_30mm_2A72_BMP3,
    ]
)
Weapon_HMG_12_7_mm_DShKM is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_12_7_mm_DShKM,
        $/GFX/Sound/SFXWeapon_HMG_12_7_mm_DShKM,
    ]
)
Weapon_HMG_14_5_mm_KPVT is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_14_5_mm_KPVT,
        $/GFX/Sound/SFXWeapon_HMG_14_5_mm_KPVT,
    ]
)
Weapon_ATGM_9M113_KonkursM_late_x5 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M113_KonkursM_late_x5,
        $/GFX/Sound/SFXWeapon_ATGM_9M113_KonkursM_late_x5,
    ]
)
Weapon_ATGM_9M113_Konkurs_x5 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M113_Konkurs_x5,
        $/GFX/Sound/SFXWeapon_ATGM_9M113_Konkurs_x5,
    ]
)
Weapon_ATGM_9M14_MalyutkaP_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M14_MalyutkaP_x6,
        $/GFX/Sound/SFXWeapon_ATGM_9M14_MalyutkaP_x6,
    ]
)
Weapon_SAM_Strela1_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_Strela1_x4,
        $/GFX/Sound/SFXWeapon_SAM_Strela1_x4,
    ]
)
Weapon_DCA_2_canon_ZPU4_14_5mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canon_ZPU4_14_5mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canon_ZPU4_14_5mm,
    ]
)
Weapon_RocketArt_PW_LWD is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_PW_LWD,
        $/GFX/Sound/SFXWeapon_RocketArt_PW_LWD,
    ]
)
Weapon_RocketInf_RPO_RYS_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketInf_RPO_RYS_x4,
        $/GFX/Sound/SFXWeapon_RocketInf_RPO_RYS_x4,
    ]
)
Weapon_RocketAir_S5_57mm_x16_BTR is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_S5_57mm_x16_BTR,
        $/GFX/Sound/SFXWeapon_RocketAir_S5_57mm_x16_BTR,
    ]
)
Weapon_RocketAir_B8_80mm_x20_BTR is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_B8_80mm_x20_BTR,
        $/GFX/Sound/SFXWeapon_RocketAir_B8_80mm_x20_BTR,
    ]
)
Weapon_DCA_2_canon_ZU23_2_23mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canon_ZU23_2_23mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canon_ZU23_2_23mm,
    ]
)
Weapon_DCA_1_canon_Bofors_40mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_1_canon_Bofors_40mm,
        $/GFX/Sound/SFXWeapon_DCA_1_canon_Bofors_40mm,
    ]
)
Weapon_SAM_9M38M1_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_9M38M1_x4,
        $/GFX/Sound/SFXWeapon_SAM_9M38M1_x4,
    ]
)
Weapon_MMG_M60D_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_M60D_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_M60D_7_62mm,
    ]
)
Weapon_MMG_M60_7_62mm_coax is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_M60_7_62mm_coax,
        $/GFX/Sound/SFXWeapon_MMG_M60_7_62mm_coax,
    ]
)
Weapon_Lance_grenade_Mk19_40mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Lance_grenade_Mk19_40mm,
        $/GFX/Sound/SFXWeapon_Lance_grenade_Mk19_40mm,
    ]
)
Weapon_AGM_AGM114A_x2_sol is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AGM114A_x2_sol,
        $/GFX/Sound/SFXWeapon_AGM_AGM114A_x2_sol,
    ]
)
Weapon_Canon_HE_105mm_L7_Centurion_AVRE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_L7_Centurion_AVRE,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_L7_Centurion_AVRE,
    ]
)
Weapon_Canon_HEAT_105mm_L7_Centurion_AVRE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HEAT_105mm_L7_Centurion_AVRE,
        $/GFX/Sound/SFXWeapon_Canon_HEAT_105mm_L7_Centurion_AVRE,
    ]
)
Weapon_Canon_AP_120_mm_L11A5_Challenger is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_120_mm_L11A5_Challenger,
        $/GFX/Sound/SFXWeapon_Canon_AP_120_mm_L11A5_Challenger,
    ]
)
Weapon_Canon_HE_120_mm_L11A5_Challenger is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_120_mm_L11A5_Challenger,
        $/GFX/Sound/SFXWeapon_Canon_HE_120_mm_L11A5_Challenger,
    ]
)
Weapon_MMG_L8A2_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_L8A2_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_L8A2_7_62mm,
    ]
)
Weapon_Canon_AP_120_mm_L11A5_Challenger_DU is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_120_mm_L11A5_Challenger_DU,
        $/GFX/Sound/SFXWeapon_Canon_AP_120_mm_L11A5_Challenger_DU,
    ]
)
Weapon_Canon_HE_120_mm_L11A5_Challenger_DU is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_120_mm_L11A5_Challenger_DU,
        $/GFX/Sound/SFXWeapon_Canon_HE_120_mm_L11A5_Challenger_DU,
    ]
)
Weapon_SAM_R440_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_R440_x4,
        $/GFX/Sound/SFXWeapon_SAM_R440_x4,
    ]
)
Weapon_Howz_Canon_DANA_SP_152mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_DANA_SP_152mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_DANA_SP_152mm,
    ]
)
Weapon_Howz_Canon_DANA_SP_152mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_DANA_SP_152mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_DANA_SP_152mm_SMOKE,
    ]
)
Weapon_Howz_Canon_DANA_SP_152mm_Direct is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_DANA_SP_152mm_Direct,
        $/GFX/Sound/SFXWeapon_Howz_Canon_DANA_SP_152mm_Direct,
    ]
)
Weapon_DCA_1_canon_53T2_20mm_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_1_canon_53T2_20mm_TOWED,
        $/GFX/Sound/SFXWeapon_DCA_1_canon_53T2_20mm_TOWED,
    ]
)
Weapon_DCA_2_canon_76T2_20mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canon_76T2_20mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canon_76T2_20mm,
    ]
)
Weapon_DCA_1_canon_S60_57mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_1_canon_S60_57mm,
        $/GFX/Sound/SFXWeapon_DCA_1_canon_S60_57mm,
    ]
)
Weapon_DCA_1_canon_Bofors_40mm_L60 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_1_canon_Bofors_40mm_L60,
        $/GFX/Sound/SFXWeapon_DCA_1_canon_Bofors_40mm_L60,
    ]
)
Weapon_DCA_1_canon_Bofors_upgrade_40mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_1_canon_Bofors_upgrade_40mm,
        $/GFX/Sound/SFXWeapon_DCA_1_canon_Bofors_upgrade_40mm,
    ]
)
Weapon_SAM_FASTA_Strela2M_x4_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_FASTA_Strela2M_x4_TOWED,
        $/GFX/Sound/SFXWeapon_SAM_FASTA_Strela2M_x4_TOWED,
    ]
)
Weapon_DCA_1_canon_FK20_20mm_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_1_canon_FK20_20mm_TOWED,
        $/GFX/Sound/SFXWeapon_DCA_1_canon_FK20_20mm_TOWED,
    ]
)
Weapon_DCA_2_canon_FK20_20mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canon_FK20_20mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canon_FK20_20mm,
    ]
)
Weapon_SAM_I_Hawk_x3 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_I_Hawk_x3,
        $/GFX/Sound/SFXWeapon_SAM_I_Hawk_x3,
    ]
)
Weapon_Javelin_LML is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Javelin_LML,
        $/GFX/Sound/SFXWeapon_Javelin_LML,
    ]
)
Weapon_DCA_1_canon_KS19_100mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_1_canon_KS19_100mm,
        $/GFX/Sound/SFXWeapon_DCA_1_canon_KS19_100mm,
    ]
)
Weapon_Canon_AP_KS19_100mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_KS19_100mm,
        $/GFX/Sound/SFXWeapon_Canon_AP_KS19_100mm,
    ]
)
Weapon_Canon_HE_KS19_100mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_KS19_100mm,
        $/GFX/Sound/SFXWeapon_Canon_HE_KS19_100mm,
    ]
)
Weapon_DCA_1_canon_KS30_130mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_1_canon_KS30_130mm,
        $/GFX/Sound/SFXWeapon_DCA_1_canon_KS30_130mm,
    ]
)
Weapon_Canon_HE_KS30_130mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_KS30_130mm,
        $/GFX/Sound/SFXWeapon_Canon_HE_KS30_130mm,
    ]
)
Weapon_Gatling_M61_Vulcan_20mm_late_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Gatling_M61_Vulcan_20mm_late_TOWED,
        $/GFX/Sound/SFXWeapon_Gatling_M61_Vulcan_20mm_late_TOWED,
    ]
)
Weapon_Gatling_M61_Vulcan_20mm_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Gatling_M61_Vulcan_20mm_TOWED,
        $/GFX/Sound/SFXWeapon_Gatling_M61_Vulcan_20mm_TOWED,
    ]
)
Weapon_DCA_4_canon_Maxson_towed_12_7mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_4_canon_Maxson_towed_12_7mm,
        $/GFX/Sound/SFXWeapon_DCA_4_canon_Maxson_towed_12_7mm,
    ]
)
Weapon_DCA_2_canons_Oerlikon_GDF_002_35mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canons_Oerlikon_GDF_002_35mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canons_Oerlikon_GDF_002_35mm,
    ]
)
Weapon_SAM_RAPIER_DARKFIRE_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_RAPIER_DARKFIRE_x6,
        $/GFX/Sound/SFXWeapon_SAM_RAPIER_DARKFIRE_x6,
    ]
)
Weapon_SAM_RAPIER_FSA_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_RAPIER_FSA_x4,
        $/GFX/Sound/SFXWeapon_SAM_RAPIER_FSA_x4,
    ]
)
Weapon_SAM_RAPIER_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_RAPIER_x4,
        $/GFX/Sound/SFXWeapon_SAM_RAPIER_x4,
    ]
)
Weapon_SAM_MIM72G is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_MIM72G,
        $/GFX/Sound/SFXWeapon_SAM_MIM72G,
    ]
)
Weapon_SAM_ROLAND_3_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_ROLAND_3_x2,
        $/GFX/Sound/SFXWeapon_SAM_ROLAND_3_x2,
    ]
)
Weapon_DCA_4_canon_ZPU4_towed_14_5mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_4_canon_ZPU4_towed_14_5mm,
        $/GFX/Sound/SFXWeapon_DCA_4_canon_ZPU4_towed_14_5mm,
    ]
)
Weapon_DCA_2_canon_Jod_towed_23mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canon_Jod_towed_23mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canon_Jod_towed_23mm,
    ]
)
Weapon_SAM_Strela2M_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_Strela2M_x2,
        $/GFX/Sound/SFXWeapon_SAM_Strela2M_x2,
    ]
)
Weapon_DCA_2_canon_ZU23_2_23mm_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canon_ZU23_2_23mm_TOWED,
        $/GFX/Sound/SFXWeapon_DCA_2_canon_ZU23_2_23mm_TOWED,
    ]
)
Weapon_Canon_AP_90mm_CN90_F2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_90mm_CN90_F2,
        $/GFX/Sound/SFXWeapon_Canon_AP_90mm_CN90_F2,
    ]
)
Weapon_Canon_HE_90mm_CN90_F2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_90mm_CN90_F2,
        $/GFX/Sound/SFXWeapon_Canon_HE_90mm_CN90_F2,
    ]
)
Weapon_MMG_AA52_7_5mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_AA52_7_5mm,
        $/GFX/Sound/SFXWeapon_MMG_AA52_7_5mm,
    ]
)
Weapon_Canon_AP_90mm_CN90_F4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_90mm_CN90_F4,
        $/GFX/Sound/SFXWeapon_Canon_AP_90mm_CN90_F4,
    ]
)
Weapon_Canon_HE_90mm_CN90_F4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_90mm_CN90_F4,
        $/GFX/Sound/SFXWeapon_Canon_HE_90mm_CN90_F4,
    ]
)
Weapon_Howz_Canon_FH70_Howitzer_155mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_FH70_Howitzer_155mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_FH70_Howitzer_155mm,
    ]
)
Weapon_Howz_Canon_FH70_Howitzer_155mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_FH70_Howitzer_155mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_FH70_Howitzer_155mm_SMOKE,
    ]
)
Weapon_Canon_AP_76mm_L23A1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_76mm_L23A1,
        $/GFX/Sound/SFXWeapon_Canon_AP_76mm_L23A1,
    ]
)
Weapon_Canon_HE_76mm_L23A1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_76mm_L23A1,
        $/GFX/Sound/SFXWeapon_Canon_HE_76mm_L23A1,
    ]
)
Weapon_MMG_L43A1_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_L43A1_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_L43A1_7_62mm,
    ]
)
Weapon_ATGM_Swingfire_x5 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_Swingfire_x5,
        $/GFX/Sound/SFXWeapon_ATGM_Swingfire_x5,
    ]
)
Weapon_MMG_L37A2_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_L37A2_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_L37A2_7_62mm,
    ]
)
Weapon_AutoCanon_AP_30mm_L21A1_RARDEN is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_30mm_L21A1_RARDEN,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_30mm_L21A1_RARDEN,
    ]
)
Weapon_AutoCanon_HE_30mm_L21A1_RARDEN is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_30mm_L21A1_RARDEN,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_30mm_L21A1_RARDEN,
    ]
)
Weapon_ATGM_MILAN_2_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_MILAN_2_x2,
        $/GFX/Sound/SFXWeapon_ATGM_MILAN_2_x2,
    ]
)
Weapon_Canon_HE_165mm_AVRE_L9A1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_165mm_AVRE_L9A1,
        $/GFX/Sound/SFXWeapon_Canon_HE_165mm_AVRE_L9A1,
    ]
)
Weapon_Canon_AP_120_mm_L11A5_Mk10 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_120_mm_L11A5_Mk10,
        $/GFX/Sound/SFXWeapon_Canon_AP_120_mm_L11A5_Mk10,
    ]
)
Weapon_Canon_HE_120_mm_L11A5_Mk10 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_120_mm_L11A5_Mk10,
        $/GFX/Sound/SFXWeapon_Canon_HE_120_mm_L11A5_Mk10,
    ]
)
Weapon_Canon_AP_120_mm_L11A5_Mk6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_120_mm_L11A5_Mk6,
        $/GFX/Sound/SFXWeapon_Canon_AP_120_mm_L11A5_Mk6,
    ]
)
Weapon_Canon_HE_120_mm_L11A5_Mk6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_120_mm_L11A5_Mk6,
        $/GFX/Sound/SFXWeapon_Canon_HE_120_mm_L11A5_Mk6,
    ]
)
Weapon_Canon_AP_120_mm_L11A5_Mk9 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_120_mm_L11A5_Mk9,
        $/GFX/Sound/SFXWeapon_Canon_AP_120_mm_L11A5_Mk9,
    ]
)
Weapon_Canon_HE_120_mm_L11A5_Mk9 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_120_mm_L11A5_Mk9,
        $/GFX/Sound/SFXWeapon_Canon_HE_120_mm_L11A5_Mk9,
    ]
)
Weapon_Mortier_L16_82mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_L16_82mm,
        $/GFX/Sound/SFXWeapon_Mortier_L16_82mm,
    ]
)
Weapon_Mortier_L16_82mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_L16_82mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_L16_82mm_SMOKE,
    ]
)
Weapon_DCA_2_canon_SCAT_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canon_SCAT_7_62mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canon_SCAT_7_62mm,
    ]
)
Weapon_RocketInf_WOMBAT_RCL_120mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketInf_WOMBAT_RCL_120mm,
        $/GFX/Sound/SFXWeapon_RocketInf_WOMBAT_RCL_120mm,
    ]
)
Weapon_RocketInf_WOMBAT_RCL_120mm_HE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketInf_WOMBAT_RCL_120mm_HE,
        $/GFX/Sound/SFXWeapon_RocketInf_WOMBAT_RCL_120mm_HE,
    ]
)
Weapon_Howz_Canon_L13A1_Howitzer_105mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_L13A1_Howitzer_105mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_L13A1_Howitzer_105mm,
    ]
)
Weapon_Howz_Canon_L13A1__Howitzer_105mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_L13A1__Howitzer_105mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_L13A1__Howitzer_105mm_SMOKE,
    ]
)
Weapon_Howz_Canon_L13A1_Howitzer_105mm_Direct is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_L13A1_Howitzer_105mm_Direct,
        $/GFX/Sound/SFXWeapon_Howz_Canon_L13A1_Howitzer_105mm_Direct,
    ]
)
Weapon_ATGM_Swingfire_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_Swingfire_x2,
        $/GFX/Sound/SFXWeapon_ATGM_Swingfire_x2,
    ]
)
Weapon_Canon_AP_76mm_L5A1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_76mm_L5A1,
        $/GFX/Sound/SFXWeapon_Canon_AP_76mm_L5A1,
    ]
)
Weapon_Canon_HE_76mm_L5A1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_76mm_L5A1,
        $/GFX/Sound/SFXWeapon_Canon_HE_76mm_L5A1,
    ]
)
Weapon_DCA_1_canon_FK20_20mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_1_canon_FK20_20mm,
        $/GFX/Sound/SFXWeapon_DCA_1_canon_FK20_20mm,
    ]
)
Weapon_Canon_HEAT_73_mm_SPG9 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HEAT_73_mm_SPG9,
        $/GFX/Sound/SFXWeapon_Canon_HEAT_73_mm_SPG9,
    ]
)
Weapon_Canon_HE_73_mm_SPG9 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_73_mm_SPG9,
        $/GFX/Sound/SFXWeapon_Canon_HE_73_mm_SPG9,
    ]
)
Weapon_DCA_2_canons_Oerlikon_GDF_35mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canons_Oerlikon_GDF_35mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canons_Oerlikon_GDF_35mm,
    ]
)
Weapon_DCA_2_canons_Oerlikon_GDF_35mm_NL is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canons_Oerlikon_GDF_35mm_NL,
        $/GFX/Sound/SFXWeapon_DCA_2_canons_Oerlikon_GDF_35mm_NL,
    ]
)
Weapon_MMG_team_7_62mm_AANF1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_team_7_62mm_AANF1,
        $/GFX/Sound/SFXWeapon_MMG_team_7_62mm_AANF1,
    ]
)
Weapon_HMG_team_12_7_mm_DSh_AA is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_team_12_7_mm_DSh_AA,
        $/GFX/Sound/SFXWeapon_HMG_team_12_7_mm_DSh_AA,
    ]
)
Weapon_HMG_team_12_7_mm_DSh is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_team_12_7_mm_DSh,
        $/GFX/Sound/SFXWeapon_HMG_team_12_7_mm_DSh,
    ]
)
Weapon_HMG_team_14_5_mm_KPVT is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_team_14_5_mm_KPVT,
        $/GFX/Sound/SFXWeapon_HMG_team_14_5_mm_KPVT,
    ]
)
Weapon_MMG_team_7_62mm_M1919 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_team_7_62mm_M1919,
        $/GFX/Sound/SFXWeapon_MMG_team_7_62mm_M1919,
    ]
)
Weapon_HMG_team_12_7_mm_M2HB is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_team_12_7_mm_M2HB,
        $/GFX/Sound/SFXWeapon_HMG_team_12_7_mm_M2HB,
    ]
)
Weapon_HMG_team_12_7_mm_M2HB_M63 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_team_12_7_mm_M2HB_M63,
        $/GFX/Sound/SFXWeapon_HMG_team_12_7_mm_M2HB_M63,
    ]
)
Weapon_MMG_team_7_62mm_M60 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_team_7_62mm_M60,
        $/GFX/Sound/SFXWeapon_MMG_team_7_62mm_M60,
    ]
)
Weapon_MMG_team_7_62mm_FN_MAG is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_team_7_62mm_FN_MAG,
        $/GFX/Sound/SFXWeapon_MMG_team_7_62mm_FN_MAG,
    ]
)
Weapon_MMG_team_7_62mm_MG3 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_team_7_62mm_MG3,
        $/GFX/Sound/SFXWeapon_MMG_team_7_62mm_MG3,
    ]
)
Weapon_MMG_team_7_62mm_Maxim is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_team_7_62mm_Maxim,
        $/GFX/Sound/SFXWeapon_MMG_team_7_62mm_Maxim,
    ]
)
Weapon_HMG_team_12_7_mm_NSV_6U6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_team_12_7_mm_NSV_6U6,
        $/GFX/Sound/SFXWeapon_HMG_team_12_7_mm_NSV_6U6,
    ]
)
Weapon_HMG_team_12_7_mm_NSV is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_team_12_7_mm_NSV,
        $/GFX/Sound/SFXWeapon_HMG_team_12_7_mm_NSV,
    ]
)
Weapon_MMG_team_7_62mm_PKM is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_team_7_62mm_PKM,
        $/GFX/Sound/SFXWeapon_MMG_team_7_62mm_PKM,
    ]
)
Weapon_Mortier_Tampella_120mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_Tampella_120mm,
        $/GFX/Sound/SFXWeapon_Mortier_Tampella_120mm,
    ]
)
Weapon_Mortier_Tampella_120mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_Tampella_120mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_Tampella_120mm_SMOKE,
    ]
)
Weapon_MMG_MG3_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_MG3_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_MG3_7_62mm,
    ]
)
Weapon_DCA_2_canon_Jod_SP_23mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canon_Jod_SP_23mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canon_Jod_SP_23mm,
    ]
)
Weapon_Howz_Canon_2A36_towed_152mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A36_towed_152mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A36_towed_152mm,
    ]
)
Weapon_Howz_Canon_2A36_towed_152mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A36_towed_152mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A36_towed_152mm_SMOKE,
    ]
)
Weapon_Howz_Canon_A19_Howitzer_122mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_A19_Howitzer_122mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_A19_Howitzer_122mm,
    ]
)
Weapon_Howz_Canon_A19_Howitzer_122mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_A19_Howitzer_122mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_A19_Howitzer_122mm_SMOKE,
    ]
)
Weapon_Howz_Canon_A19_Howitzer_122mm_Direct is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_A19_Howitzer_122mm_Direct,
        $/GFX/Sound/SFXWeapon_Howz_Canon_A19_Howitzer_122mm_Direct,
    ]
)
Weapon_Howz_Canon_B4M_203mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_B4M_203mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_B4M_203mm,
    ]
)
Weapon_Howz_Canon_B4M_203mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_B4M_203mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_B4M_203mm_SMOKE,
    ]
)
Weapon_Howz_Canon_B4M_203mm_Direct_HE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_B4M_203mm_Direct_HE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_B4M_203mm_Direct_HE,
    ]
)
Weapon_Howz_Canon_BS3_towed_100mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_BS3_towed_100mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_BS3_towed_100mm,
    ]
)
Weapon_Howz_Canon_BS3_towed_100mm_Direct_AP is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_BS3_towed_100mm_Direct_AP,
        $/GFX/Sound/SFXWeapon_Howz_Canon_BS3_towed_100mm_Direct_AP,
    ]
)
Weapon_Howz_Canon_BS3_towed_100mm_Direct_HE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_BS3_towed_100mm_Direct_HE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_BS3_towed_100mm_Direct_HE,
    ]
)
Weapon_Howz_Canon_Br5M_towed_280mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_Br5M_towed_280mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_Br5M_towed_280mm,
    ]
)
Weapon_Howz_Canon_Br5M_towed_280mm_Direct_HE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_Br5M_towed_280mm_Direct_HE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_Br5M_towed_280mm_Direct_HE,
    ]
)
Weapon_Howz_Canon_D1_towed_152mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D1_towed_152mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D1_towed_152mm,
    ]
)
Weapon_Howz_Canon_D1_towed_152mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D1_towed_152mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D1_towed_152mm_SMOKE,
    ]
)
Weapon_Howz_Canon_D1_towed_152mm_Direct is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D1_towed_152mm_Direct,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D1_towed_152mm_Direct,
    ]
)
Weapon_Howz_Canon_D20_Howitzer_152mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D20_Howitzer_152mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D20_Howitzer_152mm,
    ]
)
Weapon_Howz_Canon_D20_Howitzer_152mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D20_Howitzer_152mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D20_Howitzer_152mm_SMOKE,
    ]
)
Weapon_Howz_Canon_D30_Howitzer_122mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D30_Howitzer_122mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D30_Howitzer_122mm,
    ]
)
Weapon_Howz_Canon_D30_Howitzer_122mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D30_Howitzer_122mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D30_Howitzer_122mm_SMOKE,
    ]
)
Weapon_Canon_AP_D30_122mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_D30_122mm,
        $/GFX/Sound/SFXWeapon_Canon_AP_D30_122mm,
    ]
)
Weapon_Howz_Canon_M118_Howitzer_105mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M118_Howitzer_105mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M118_Howitzer_105mm,
    ]
)
Weapon_Howz_Canon_M118_Howitzer_105mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M118_Howitzer_105mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M118_Howitzer_105mm_SMOKE,
    ]
)
Weapon_Howz_Canon_M118_105mm_Direct is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M118_105mm_Direct,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M118_105mm_Direct,
    ]
)
Weapon_Howz_Canon_M101_Howitzer_105mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M101_Howitzer_105mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M101_Howitzer_105mm,
    ]
)
Weapon_Howz_Canon_M101_Howitzer_105mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M101_Howitzer_105mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M101_Howitzer_105mm_SMOKE,
    ]
)
Weapon_Howz_Canon_M102_Howitzer_105mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M102_Howitzer_105mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M102_Howitzer_105mm,
    ]
)
Weapon_Howz_Canon_M102_Howitzer_105mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M102_Howitzer_105mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M102_Howitzer_105mm_SMOKE,
    ]
)
Weapon_Howz_Canon_M114_Howitzer_155mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M114_Howitzer_155mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M114_Howitzer_155mm,
    ]
)
Weapon_Howz_Canon_M114_Howitzer_155mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M114_Howitzer_155mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M114_Howitzer_155mm_SMOKE,
    ]
)
Weapon_Howz_Canon_M114_Howitzer_155mm_Direct_HEAT is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M114_Howitzer_155mm_Direct_HEAT,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M114_Howitzer_155mm_Direct_HEAT,
    ]
)
Weapon_Howz_Canon_M114_39_Howitzer_155mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M114_39_Howitzer_155mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M114_39_Howitzer_155mm,
    ]
)
Weapon_Howz_Canon_M114_39_Howitzer_155mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M114_39_Howitzer_155mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M114_39_Howitzer_155mm_SMOKE,
    ]
)
Weapon_Howz_Canon_M114_39_Howitzer_155mm_Direct_HEAT is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M114_39_Howitzer_155mm_Direct_HEAT,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M114_39_Howitzer_155mm_Direct_HEAT,
    ]
)
Weapon_Howz_Canon_M119_Howitzer_105mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M119_Howitzer_105mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M119_Howitzer_105mm,
    ]
)
Weapon_Howz_Canon_M119_Howitzer_105mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M119_Howitzer_105mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M119_Howitzer_105mm_SMOKE,
    ]
)
Weapon_Howz_Canon_M198_Howitzer_155mm_guided is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M198_Howitzer_155mm_guided,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M198_Howitzer_155mm_guided,
    ]
)
Weapon_Howz_Canon_M198_Howitzer_155mm_guided_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M198_Howitzer_155mm_guided_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M198_Howitzer_155mm_guided_SMOKE,
    ]
)
Weapon_Howz_Canon_M198_Howitzer_155mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M198_Howitzer_155mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M198_Howitzer_155mm,
    ]
)
Weapon_Howz_Canon_M198_Howitzer_155mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M198_Howitzer_155mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M198_Howitzer_155mm_SMOKE,
    ]
)
Weapon_Howz_Canon_M30_Howitzer_122mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M30_Howitzer_122mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M30_Howitzer_122mm,
    ]
)
Weapon_Howz_Canon_M30_Howitzer_122mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M30_Howitzer_122mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M30_Howitzer_122mm_SMOKE,
    ]
)
Weapon_Howz_Canon_M30_Howitzer_122mm_Direct is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M30_Howitzer_122mm_Direct,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M30_Howitzer_122mm_Direct,
    ]
)
Weapon_Howz_Canon_M46_Howitzer_130mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M46_Howitzer_130mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M46_Howitzer_130mm,
    ]
)
Weapon_Howz_Canon_M46_Howitzer_130mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M46_Howitzer_130mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M46_Howitzer_130mm_SMOKE,
    ]
)
Weapon_Howz_Canon_D20_towed_152mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D20_towed_152mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D20_towed_152mm,
    ]
)
Weapon_Howz_Canon_D20_towed_152mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D20_towed_152mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D20_towed_152mm_SMOKE,
    ]
)
Weapon_Howz_Canon_D20_towed_152mm_Direct is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_D20_towed_152mm_Direct,
        $/GFX/Sound/SFXWeapon_Howz_Canon_D20_towed_152mm_Direct,
    ]
)
Weapon_Howz_Canon_2A65_towed_152mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A65_towed_152mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A65_towed_152mm,
    ]
)
Weapon_Howz_Canon_2A65_towed_152mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A65_towed_152mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A65_towed_152mm_SMOKE,
    ]
)
Weapon_Howz_Canon_2A65_152mm_Direct is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2A65_152mm_Direct,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2A65_152mm_Direct,
    ]
)
Weapon_Howz_Canon_ZiS3_Howitzer_76mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_ZiS3_Howitzer_76mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_ZiS3_Howitzer_76mm,
    ]
)
Weapon_Howz_Canon_ZiS3_Howitzer_76mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_ZiS3_Howitzer_76mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_ZiS3_Howitzer_76mm_SMOKE,
    ]
)
Weapon_Canon_AP_76mm_ZiS3 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_76mm_ZiS3,
        $/GFX/Sound/SFXWeapon_Canon_AP_76mm_ZiS3,
    ]
)
Weapon_Canon_AP_122mm_D25T is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_122mm_D25T,
        $/GFX/Sound/SFXWeapon_Canon_AP_122mm_D25T,
    ]
)
Weapon_Canon_HE_122mm_D25T is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_122mm_D25T,
        $/GFX/Sound/SFXWeapon_Canon_HE_122mm_D25T,
    ]
)
Weapon_MMG_DTM_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_DTM_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_DTM_7_62mm,
    ]
)
Weapon_ATGM_HOT1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_HOT1,
        $/GFX/Sound/SFXWeapon_ATGM_HOT1,
    ]
)
Weapon_Canon_AP_90mm_KanJPz_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_90mm_KanJPz_late,
        $/GFX/Sound/SFXWeapon_Canon_AP_90mm_KanJPz_late,
    ]
)
Weapon_Canon_HE_90mm_M48_KanJPz_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_90mm_M48_KanJPz_late,
        $/GFX/Sound/SFXWeapon_Canon_HE_90mm_M48_KanJPz_late,
    ]
)
Weapon_Canon_AP_90mm_KanJPz is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_90mm_KanJPz,
        $/GFX/Sound/SFXWeapon_Canon_AP_90mm_KanJPz,
    ]
)
Weapon_Canon_HE_90mm_M48_KanJPz is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_90mm_M48_KanJPz,
        $/GFX/Sound/SFXWeapon_Canon_HE_90mm_M48_KanJPz,
    ]
)
Weapon_MMG_MG3S_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_MG3S_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_MG3S_7_62mm,
    ]
)
Weapon_AutoCanon_AP_25mm_M242_Bushmaster_Late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_25mm_M242_Bushmaster_Late,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_25mm_M242_Bushmaster_Late,
    ]
)
Weapon_AutoCanon_HE_25mm_M242_Bushmaster_Late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_25mm_M242_Bushmaster_Late,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_25mm_M242_Bushmaster_Late,
    ]
)
Weapon_MMG_M240_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_M240_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_M240_7_62mm,
    ]
)
Weapon_SAM_FASTA_Strela2M_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_FASTA_Strela2M_x4,
        $/GFX/Sound/SFXWeapon_SAM_FASTA_Strela2M_x4,
    ]
)
Weapon_RocketArt_LARS_110mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_LARS_110mm,
        $/GFX/Sound/SFXWeapon_RocketArt_LARS_110mm,
    ]
)
Weapon_Canon_AP_105mm_L7A3_Leo1V is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_L7A3_Leo1V,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_L7A3_Leo1V,
    ]
)
Weapon_Canon_HE_105mm_L7A3_Leo1V is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_L7A3_Leo1V,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_L7A3_Leo1V,
    ]
)
Weapon_Canon_AP_105mm_L7A3 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_L7A3,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_L7A3,
    ]
)
Weapon_Canon_HE_105mm_L7A3 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_L7A3,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_L7A3,
    ]
)
Weapon_Canon_AP_105mm_L7A3_Leo1A5_BE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_L7A3_Leo1A5_BE,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_L7A3_Leo1A5_BE,
    ]
)
Weapon_Canon_HE_105mm_L7A3_Leo1A5_BE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_L7A3_Leo1A5_BE,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_L7A3_Leo1A5_BE,
    ]
)
Weapon_Canon_AP_105mm_L7A3_Leo1A5 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_L7A3_Leo1A5,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_L7A3_Leo1A5,
    ]
)
Weapon_Canon_HE_105mm_L7A3_Leo1A5 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_L7A3_Leo1A5,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_L7A3_Leo1A5,
    ]
)
Weapon_Canon_AP_105mm_L7A3_BE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_L7A3_BE,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_L7A3_BE,
    ]
)
Weapon_Canon_HE_105mm_L7A3_BE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_L7A3_BE,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_L7A3_BE,
    ]
)
Weapon_Canon_AP_120_mm_L44_late87 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_120_mm_L44_late87,
        $/GFX/Sound/SFXWeapon_Canon_AP_120_mm_L44_late87,
    ]
)
Weapon_Canon_HE_120_mm_L44 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_120_mm_L44,
        $/GFX/Sound/SFXWeapon_Canon_HE_120_mm_L44,
    ]
)
Weapon_Canon_AP_120mm_L44_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_120mm_L44_late,
        $/GFX/Sound/SFXWeapon_Canon_AP_120mm_L44_late,
    ]
)
Weapon_Canon_HE_120mm_L44_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_120mm_L44_late,
        $/GFX/Sound/SFXWeapon_Canon_HE_120mm_L44_late,
    ]
)
Weapon_Canon_AP_120mm_L44_late_2A4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_120mm_L44_late_2A4,
        $/GFX/Sound/SFXWeapon_Canon_AP_120mm_L44_late_2A4,
    ]
)
Weapon_Canon_HE_120mm_L44_late_2A4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_120mm_L44_late_2A4,
        $/GFX/Sound/SFXWeapon_Canon_HE_120mm_L44_late_2A4,
    ]
)
Weapon_SAM_Igla is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_Igla,
        $/GFX/Sound/SFXWeapon_SAM_Igla,
    ]
)
Weapon_AutoCanon_AP_20mm_MK_20_Rh_202 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_20mm_MK_20_Rh_202,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_20mm_MK_20_Rh_202,
    ]
)
Weapon_AutoCanon_HE_20mm_MK_20_Rh_202 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_20mm_MK_20_Rh_202,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_20mm_MK_20_Rh_202,
    ]
)
Weapon_Mortier_M40_107mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M40_107mm,
        $/GFX/Sound/SFXWeapon_Mortier_M40_107mm,
    ]
)
Weapon_Mortier_M40_107mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M40_107mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_M40_107mm_SMOKE,
    ]
)
Weapon_Howz_Canon_M113A1_Howitzer_175mm_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M113A1_Howitzer_175mm_late,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M113A1_Howitzer_175mm_late,
    ]
)
Weapon_Howz_Canon_M113A1_Howitzer_175mm_late_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M113A1_Howitzer_175mm_late_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M113A1_Howitzer_175mm_late_SMOKE,
    ]
)
Weapon_Howz_Canon_M185_L39_Howitzer_155mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M185_L39_Howitzer_155mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M185_L39_Howitzer_155mm,
    ]
)
Weapon_Howz_Canon_M185_L39_Howitzer_155mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M185_L39_Howitzer_155mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M185_L39_Howitzer_155mm_SMOKE,
    ]
)
Weapon_Howz_Canon_FH70_SP_Howitzer_155mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_FH70_SP_Howitzer_155mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_FH70_SP_Howitzer_155mm,
    ]
)
Weapon_Howz_Canon_FH70_SP_Howitzer_155mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_FH70_SP_Howitzer_155mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_FH70_SP_Howitzer_155mm_SMOKE,
    ]
)
Weapon_Howz_Canon_M201A1_Howitzer_203mm_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M201A1_Howitzer_203mm_late,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M201A1_Howitzer_203mm_late,
    ]
)
Weapon_Howz_Canon_M201A1_Howitzer_203mm_late_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M201A1_Howitzer_203mm_late_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M201A1_Howitzer_203mm_late_SMOKE,
    ]
)
Weapon_MMG_M60_7_62mm_veh is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_M60_7_62mm_veh,
        $/GFX/Sound/SFXWeapon_MMG_M60_7_62mm_veh,
    ]
)
Weapon_M47_DRAGON is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_M47_DRAGON,
        $/GFX/Sound/SFXWeapon_M47_DRAGON,
    ]
)
Weapon_RocketInf_M40A1_RCL_106mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketInf_M40A1_RCL_106mm,
        $/GFX/Sound/SFXWeapon_RocketInf_M40A1_RCL_106mm,
    ]
)
Weapon_RocketInf_M40A1_RCL_106mm_HE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketInf_M40A1_RCL_106mm_HE,
        $/GFX/Sound/SFXWeapon_RocketInf_M40A1_RCL_106mm_HE,
    ]
)
Weapon_Gatling_M61_Vulcan_20mm_noRadar is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Gatling_M61_Vulcan_20mm_noRadar,
        $/GFX/Sound/SFXWeapon_Gatling_M61_Vulcan_20mm_noRadar,
    ]
)
Weapon_Gatling_M61_Vulcan_20mm_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Gatling_M61_Vulcan_20mm_late,
        $/GFX/Sound/SFXWeapon_Gatling_M61_Vulcan_20mm_late,
    ]
)
Weapon_Canon_AP_120_mm_L52_M68_HA is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_120_mm_L52_M68_HA,
        $/GFX/Sound/SFXWeapon_Canon_AP_120_mm_L52_M68_HA,
    ]
)
Weapon_Canon_HE_120_mm_L52_M68 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_120_mm_L52_M68,
        $/GFX/Sound/SFXWeapon_Canon_HE_120_mm_L52_M68,
    ]
)
Weapon_MMG_M240_abrams_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_M240_abrams_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_M240_abrams_7_62mm,
    ]
)
Weapon_Canon_AP_120_mm_L52_M68 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_120_mm_L52_M68,
        $/GFX/Sound/SFXWeapon_Canon_AP_120_mm_L52_M68,
    ]
)
Weapon_Canon_AP_105mm_M68_M1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_M68_M1,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_M68_M1,
    ]
)
Weapon_Canon_HE_105mm_M68_M1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_M68_M1,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_M68_M1,
    ]
)
Weapon_RocketArt_M26_227mm_Cluster is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_M26_227mm_Cluster,
        $/GFX/Sound/SFXWeapon_RocketArt_M26_227mm_Cluster,
    ]
)
Weapon_RocketArt_M26_227mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_M26_227mm,
        $/GFX/Sound/SFXWeapon_RocketArt_M26_227mm,
    ]
)
Weapon_ATGM_BGM71D_TOW_2A_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_BGM71D_TOW_2A_x2,
        $/GFX/Sound/SFXWeapon_ATGM_BGM71D_TOW_2A_x2,
    ]
)
Weapon_ATGM_BGM71C_ITOW_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_BGM71C_ITOW_x2,
        $/GFX/Sound/SFXWeapon_ATGM_BGM71C_ITOW_x2,
    ]
)
Weapon_DCA_2_canon_Bofors_40mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canon_Bofors_40mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canon_Bofors_40mm,
    ]
)
Weapon_Canon_AP_90mm_M48 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_90mm_M48,
        $/GFX/Sound/SFXWeapon_Canon_AP_90mm_M48,
    ]
)
Weapon_Canon_HE_90mm_M48 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_90mm_M48,
        $/GFX/Sound/SFXWeapon_Canon_HE_90mm_M48,
    ]
)
Weapon_Canon_AP_105mm_L7A3_M48 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_L7A3_M48,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_L7A3_M48,
    ]
)
Weapon_Canon_HE_105mm_L7A3_M48 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_L7A3_M48,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_L7A3_M48,
    ]
)
Weapon_Canon_AP_105mm_M68_M48A5 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_M68_M48A5,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_M68_M48A5,
    ]
)
Weapon_Canon_HE_105mm_M68_M48A5 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_M68_M48A5,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_M68_M48A5,
    ]
)
Weapon_Canon_HEAT_152mm_Sheridan is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HEAT_152mm_Sheridan,
        $/GFX/Sound/SFXWeapon_Canon_HEAT_152mm_Sheridan,
    ]
)
Weapon_Canon_HE_152mm_Sheridan is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_152mm_Sheridan,
        $/GFX/Sound/SFXWeapon_Canon_HE_152mm_Sheridan,
    ]
)
Weapon_ATGM_MGM551C_Shillelagh is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_MGM551C_Shillelagh,
        $/GFX/Sound/SFXWeapon_ATGM_MGM551C_Shillelagh,
    ]
)
Weapon_RocketArt_PW_MICLICS is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_PW_MICLICS,
        $/GFX/Sound/SFXWeapon_RocketArt_PW_MICLICS,
    ]
)
Weapon_Canon_AP_105mm_M68 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_M68,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_M68,
    ]
)
Weapon_Canon_HE_105mm_M68 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_M68,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_M68,
    ]
)
Weapon_HMG_12_7_mm_M85 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_12_7_mm_M85,
        $/GFX/Sound/SFXWeapon_HMG_12_7_mm_M85,
    ]
)
Weapon_Canon_AP_105mm_M68_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_105mm_M68_late,
        $/GFX/Sound/SFXWeapon_Canon_AP_105mm_M68_late,
    ]
)
Weapon_Canon_HE_105mm_M68_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_105mm_M68_late,
        $/GFX/Sound/SFXWeapon_Canon_HE_105mm_M68_late,
    ]
)
Weapon_Canon_HE_165mm_AVRE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_165mm_AVRE,
        $/GFX/Sound/SFXWeapon_Canon_HE_165mm_AVRE,
    ]
)
Weapon_SAM_FIM92_Stinger_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_FIM92_Stinger_x8,
        $/GFX/Sound/SFXWeapon_SAM_FIM92_Stinger_x8,
    ]
)
Weapon_HMG_12_7_mm_avenger_M3P is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_12_7_mm_avenger_M3P,
        $/GFX/Sound/SFXWeapon_HMG_12_7_mm_avenger_M3P,
    ]
)
Weapon_DCA_4_canon_Maxson_SP_12_7mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_4_canon_Maxson_SP_12_7mm,
        $/GFX/Sound/SFXWeapon_DCA_4_canon_Maxson_SP_12_7mm,
    ]
)
Weapon_MMG_L94A1_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_L94A1_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_L94A1_7_62mm,
    ]
)
Weapon_RocketArt_M21OF_122mm_cluster is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_M21OF_122mm_cluster,
        $/GFX/Sound/SFXWeapon_RocketArt_M21OF_122mm_cluster,
    ]
)
Weapon_MLRS_140mm_towed is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MLRS_140mm_towed,
        $/GFX/Sound/SFXWeapon_MLRS_140mm_towed,
    ]
)
Weapon_MLRS_140mm_towed_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MLRS_140mm_towed_SMOKE,
        $/GFX/Sound/SFXWeapon_MLRS_140mm_towed_SMOKE,
    ]
)
Weapon_ATGM_9M114M_KokonM is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M114M_KokonM,
        $/GFX/Sound/SFXWeapon_ATGM_9M114M_KokonM,
    ]
)
Weapon_SAM_Strela10M3_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_Strela10M3_x4,
        $/GFX/Sound/SFXWeapon_SAM_Strela10M3_x4,
    ]
)
Weapon_SAM_Strela10_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_Strela10_x4,
        $/GFX/Sound/SFXWeapon_SAM_Strela10_x4,
    ]
)
Weapon_Mortier_Vasilek_82mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_Vasilek_82mm,
        $/GFX/Sound/SFXWeapon_Mortier_Vasilek_82mm,
    ]
)
Weapon_Mortier_Vasilek_indirect_82mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_Vasilek_indirect_82mm,
        $/GFX/Sound/SFXWeapon_Mortier_Vasilek_indirect_82mm,
    ]
)
Weapon_SAM_ROLAND_2_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_ROLAND_2_x2,
        $/GFX/Sound/SFXWeapon_SAM_ROLAND_2_x2,
    ]
)
Weapon_Mortier_M40_tower_107mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M40_tower_107mm,
        $/GFX/Sound/SFXWeapon_Mortier_M40_tower_107mm,
    ]
)
Weapon_Mortier_M40_towed_107mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M40_towed_107mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_M40_towed_107mm_SMOKE,
    ]
)
Weapon_Mortier_M240_240mm_Cluster is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M240_240mm_Cluster,
        $/GFX/Sound/SFXWeapon_Mortier_M240_240mm_Cluster,
    ]
)
Weapon_Mortier_M240_240mm_EXT_Cluster is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M240_240mm_EXT_Cluster,
        $/GFX/Sound/SFXWeapon_Mortier_M240_240mm_EXT_Cluster,
    ]
)
Weapon_Mortier_M240_240mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M240_240mm,
        $/GFX/Sound/SFXWeapon_Mortier_M240_240mm,
    ]
)
Weapon_Mortier_M240_240mm_EXT is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M240_240mm_EXT,
        $/GFX/Sound/SFXWeapon_Mortier_M240_240mm_EXT,
    ]
)
Weapon_Mortier_2B14_82mm_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_2B14_82mm_TOWED,
        $/GFX/Sound/SFXWeapon_Mortier_2B14_82mm_TOWED,
    ]
)
Weapon_Mortier_2B14_82mm_SMOKE_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_2B14_82mm_SMOKE_TOWED,
        $/GFX/Sound/SFXWeapon_Mortier_2B14_82mm_SMOKE_TOWED,
    ]
)
Weapon_Mortier_Vasilek_82mm_towed is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_Vasilek_82mm_towed,
        $/GFX/Sound/SFXWeapon_Mortier_Vasilek_82mm_towed,
    ]
)
Weapon_Mortier_Vasilek_indirect_82mm_towed is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_Vasilek_indirect_82mm_towed,
        $/GFX/Sound/SFXWeapon_Mortier_Vasilek_indirect_82mm_towed,
    ]
)
Weapon_Mortier_2S12_120mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_2S12_120mm,
        $/GFX/Sound/SFXWeapon_Mortier_2S12_120mm,
    ]
)
Weapon_Mortier_2S12_120mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_2S12_120mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_2S12_120mm_SMOKE,
    ]
)
Weapon_Mortier_M1_81mm_towed is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M1_81mm_towed,
        $/GFX/Sound/SFXWeapon_Mortier_M1_81mm_towed,
    ]
)
Weapon_Mortier_M1_81mm_towed_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M1_81mm_towed_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_M1_81mm_towed_SMOKE,
    ]
)
Weapon_Mortier_81mm_MO81_61C_towed is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_81mm_MO81_61C_towed,
        $/GFX/Sound/SFXWeapon_Mortier_81mm_MO81_61C_towed,
    ]
)
Weapon_Mortier_81mm_MO81_61C_towed_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_81mm_MO81_61C_towed_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_81mm_MO81_61C_towed_SMOKE,
    ]
)
Weapon_Mortier_M43_160mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M43_160mm,
        $/GFX/Sound/SFXWeapon_Mortier_M43_160mm,
    ]
)
Weapon_Mortier_M43_160mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M43_160mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_M43_160mm_SMOKE,
    ]
)
Weapon_Mortier_M43_82mm_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M43_82mm_TOWED,
        $/GFX/Sound/SFXWeapon_Mortier_M43_82mm_TOWED,
    ]
)
Weapon_Mortier_M43_82mm_SMOKE_TOWED is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_M43_82mm_SMOKE_TOWED,
        $/GFX/Sound/SFXWeapon_Mortier_M43_82mm_SMOKE_TOWED,
    ]
)
Weapon_Mortier_RT61_120mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_RT61_120mm,
        $/GFX/Sound/SFXWeapon_Mortier_RT61_120mm,
    ]
)
Weapon_Mortier_RT61_120mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_RT61_120mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_RT61_120mm_SMOKE,
    ]
)
Weapon_Howz_Canon_2B16_NONA_K_120mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2B16_NONA_K_120mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2B16_NONA_K_120mm,
    ]
)
Weapon_Howz_Canon_2B16_NONA_K_120mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_2B16_NONA_K_120mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_2B16_NONA_K_120mm_SMOKE,
    ]
)
Weapon_Canon_AP_2B16_NONA_K_120mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_2B16_NONA_K_120mm,
        $/GFX/Sound/SFXWeapon_Canon_AP_2B16_NONA_K_120mm,
    ]
)
Weapon_Mortier_PM43_120mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_PM43_120mm,
        $/GFX/Sound/SFXWeapon_Mortier_PM43_120mm,
    ]
)
Weapon_Mortier_PM43_120mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_PM43_120mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_PM43_120mm_SMOKE,
    ]
)
Weapon_Mortier_Tampella_towed_120mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_Tampella_towed_120mm,
        $/GFX/Sound/SFXWeapon_Mortier_Tampella_towed_120mm,
    ]
)
Weapon_Mortier_Tampella_towed_120mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_Tampella_towed_120mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_Tampella_towed_120mm_SMOKE,
    ]
)
Weapon_DCA_1_canon_KPVT_14_5mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_1_canon_KPVT_14_5mm,
        $/GFX/Sound/SFXWeapon_DCA_1_canon_KPVT_14_5mm,
    ]
)
Weapon_ATGM_9M14_MalyutkaP_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M14_MalyutkaP_x2,
        $/GFX/Sound/SFXWeapon_ATGM_9M14_MalyutkaP_x2,
    ]
)
Weapon_Howz_Canon_M50_155mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M50_155mm,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M50_155mm,
    ]
)
Weapon_Howz_Canon_M50_155mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Howz_Canon_M50_155mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Howz_Canon_M50_155mm_SMOKE,
    ]
)
Weapon_SAM_9M33M2_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_9M33M2_x6,
        $/GFX/Sound/SFXWeapon_SAM_9M33M2_x6,
    ]
)
Weapon_Canon_AP_76mm_D56T is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_76mm_D56T,
        $/GFX/Sound/SFXWeapon_Canon_AP_76mm_D56T,
    ]
)
Weapon_Canon_HE_76mm_D56T is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_76mm_D56T,
        $/GFX/Sound/SFXWeapon_Canon_HE_76mm_D56T,
    ]
)
Weapon_MMG_HK21_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_HK21_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_HK21_7_62mm,
    ]
)
Weapon_Canon_AP_85mm_S53 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_85mm_S53,
        $/GFX/Sound/SFXWeapon_Canon_AP_85mm_S53,
    ]
)
Weapon_Canon_HE_85mm_S53 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_85mm_S53,
        $/GFX/Sound/SFXWeapon_Canon_HE_85mm_S53,
    ]
)
Weapon_Canon_AP_100mm_D10T_early_HEAT is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_100mm_D10T_early_HEAT,
        $/GFX/Sound/SFXWeapon_Canon_AP_100mm_D10T_early_HEAT,
    ]
)
Weapon_Canon_HE_100mm_D10T_early is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_100mm_D10T_early,
        $/GFX/Sound/SFXWeapon_Canon_HE_100mm_D10T_early,
    ]
)
Weapon_Canon_AP_100mm_D10T_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_100mm_D10T_late,
        $/GFX/Sound/SFXWeapon_Canon_AP_100mm_D10T_late,
    ]
)
Weapon_Canon_HE_100mm_D10T_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_100mm_D10T_late,
        $/GFX/Sound/SFXWeapon_Canon_HE_100mm_D10T_late,
    ]
)
Weapon_Canon_AP_100mm_D10T_Merida is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_100mm_D10T_Merida,
        $/GFX/Sound/SFXWeapon_Canon_AP_100mm_D10T_Merida,
    ]
)
Weapon_Canon_HE_100mm_D10T_Merida is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_100mm_D10T_Merida,
        $/GFX/Sound/SFXWeapon_Canon_HE_100mm_D10T_Merida,
    ]
)
Weapon_Canon_AP_100mm_D10T_mid is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_100mm_D10T_mid,
        $/GFX/Sound/SFXWeapon_Canon_AP_100mm_D10T_mid,
    ]
)
Weapon_Canon_HE_100mm_D10T_mid is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_100mm_D10T_mid,
        $/GFX/Sound/SFXWeapon_Canon_HE_100mm_D10T_mid,
    ]
)
Weapon_Canon_AP_100mm_D10T_early is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_100mm_D10T_early,
        $/GFX/Sound/SFXWeapon_Canon_AP_100mm_D10T_early,
    ]
)
Weapon_Canon_AP_100mm_D10T_T55obr81 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_100mm_D10T_T55obr81,
        $/GFX/Sound/SFXWeapon_Canon_AP_100mm_D10T_T55obr81,
    ]
)
Weapon_Canon_HE_100mm_D10T_T55obr81 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_100mm_D10T_T55obr81,
        $/GFX/Sound/SFXWeapon_Canon_HE_100mm_D10T_T55obr81,
    ]
)
Weapon_Canon_AP_115mm_U5TS_T62M is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_115mm_U5TS_T62M,
        $/GFX/Sound/SFXWeapon_Canon_AP_115mm_U5TS_T62M,
    ]
)
Weapon_Canon_HE_115mm_U5TS_T62M is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_115mm_U5TS_T62M,
        $/GFX/Sound/SFXWeapon_Canon_HE_115mm_U5TS_T62M,
    ]
)
Weapon_Canon_AP_125_mm_2A46_T64A is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_125_mm_2A46_T64A,
        $/GFX/Sound/SFXWeapon_Canon_AP_125_mm_2A46_T64A,
    ]
)
Weapon_Canon_HE_125_mm_2A46_T64A is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_125_mm_2A46_T64A,
        $/GFX/Sound/SFXWeapon_Canon_HE_125_mm_2A46_T64A,
    ]
)
Weapon_Canon_AP_125_mm_2A46_T64A_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_125_mm_2A46_T64A_late,
        $/GFX/Sound/SFXWeapon_Canon_AP_125_mm_2A46_T64A_late,
    ]
)
Weapon_Canon_HE_125_mm_2A46_T64A_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_125_mm_2A46_T64A_late,
        $/GFX/Sound/SFXWeapon_Canon_HE_125_mm_2A46_T64A_late,
    ]
)
Weapon_Canon_AP_125_mm_2A46_T64B_very_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_125_mm_2A46_T64B_very_late,
        $/GFX/Sound/SFXWeapon_Canon_AP_125_mm_2A46_T64B_very_late,
    ]
)
Weapon_Canon_HE_125_mm_2A46_T64B_very_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_125_mm_2A46_T64B_very_late,
        $/GFX/Sound/SFXWeapon_Canon_HE_125_mm_2A46_T64B_very_late,
    ]
)
Weapon_ATGM_9M128_Agona is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M128_Agona,
        $/GFX/Sound/SFXWeapon_ATGM_9M128_Agona,
    ]
)
Weapon_ATGM_9M112_1_Kobra is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M112_1_Kobra,
        $/GFX/Sound/SFXWeapon_ATGM_9M112_1_Kobra,
    ]
)
Weapon_Canon_AP_125_mm_2A46_T72M is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_125_mm_2A46_T72M,
        $/GFX/Sound/SFXWeapon_Canon_AP_125_mm_2A46_T72M,
    ]
)
Weapon_Canon_HE_125_mm_2A46_T72M is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_125_mm_2A46_T72M,
        $/GFX/Sound/SFXWeapon_Canon_HE_125_mm_2A46_T72M,
    ]
)
Weapon_Canon_AP_125_mm_2A46_T72M2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_125_mm_2A46_T72M2,
        $/GFX/Sound/SFXWeapon_Canon_AP_125_mm_2A46_T72M2,
    ]
)
Weapon_Canon_HE_125_mm_2A46_T72M2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_125_mm_2A46_T72M2,
        $/GFX/Sound/SFXWeapon_Canon_HE_125_mm_2A46_T72M2,
    ]
)
Weapon_Canon_AP_125_mm_2A46M is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_125_mm_2A46M,
        $/GFX/Sound/SFXWeapon_Canon_AP_125_mm_2A46M,
    ]
)
Weapon_Canon_HE_125_mm_2A46M is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_125_mm_2A46M,
        $/GFX/Sound/SFXWeapon_Canon_HE_125_mm_2A46M,
    ]
)
Weapon_ATGM_9M119M_Svir is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M119M_Svir,
        $/GFX/Sound/SFXWeapon_ATGM_9M119M_Svir,
    ]
)
Weapon_Canon_AP_125_mm_2A46 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_125_mm_2A46,
        $/GFX/Sound/SFXWeapon_Canon_AP_125_mm_2A46,
    ]
)
Weapon_Canon_HE_125_mm_2A46 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_125_mm_2A46,
        $/GFX/Sound/SFXWeapon_Canon_HE_125_mm_2A46,
    ]
)
Weapon_Canon_AP_125_mm_2A46M_T80B is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_125_mm_2A46M_T80B,
        $/GFX/Sound/SFXWeapon_Canon_AP_125_mm_2A46M_T80B,
    ]
)
Weapon_Canon_HE_125_mm_2A46M_T80B is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_125_mm_2A46M_T80B,
        $/GFX/Sound/SFXWeapon_Canon_HE_125_mm_2A46M_T80B,
    ]
)
Weapon_Canon_AP_125_mm_2A46M_late_T80UD is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_125_mm_2A46M_late_T80UD,
        $/GFX/Sound/SFXWeapon_Canon_AP_125_mm_2A46M_late_T80UD,
    ]
)
Weapon_Canon_HE_125_mm_2A46M_late_T80UD is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_125_mm_2A46M_late_T80UD,
        $/GFX/Sound/SFXWeapon_Canon_HE_125_mm_2A46M_late_T80UD,
    ]
)
Weapon_ATGM_9M119M_Refleks is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_9M119M_Refleks,
        $/GFX/Sound/SFXWeapon_ATGM_9M119M_Refleks,
    ]
)
Weapon_Canon_AP_125_mm_2A46M_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_AP_125_mm_2A46M_late,
        $/GFX/Sound/SFXWeapon_Canon_AP_125_mm_2A46M_late,
    ]
)
Weapon_Canon_HE_125_mm_2A46M_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_HE_125_mm_2A46M_late,
        $/GFX/Sound/SFXWeapon_Canon_HE_125_mm_2A46M_late,
    ]
)
Weapon_RocketArt_thermobaric_220mm_x30 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketArt_thermobaric_220mm_x30,
        $/GFX/Sound/SFXWeapon_RocketArt_thermobaric_220mm_x30,
    ]
)
Weapon_Canon_FLAMME_ATO54 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Canon_FLAMME_ATO54,
        $/GFX/Sound/SFXWeapon_Canon_FLAMME_ATO54,
    ]
)
Weapon_DCA_1_canon_53T2_20mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_1_canon_53T2_20mm,
        $/GFX/Sound/SFXWeapon_DCA_1_canon_53T2_20mm,
    ]
)
Weapon_SAM_9M330_Tor_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_9M330_Tor_x8,
        $/GFX/Sound/SFXWeapon_SAM_9M330_Tor_x8,
    ]
)
Weapon_SAM_RAPIER_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_RAPIER_x8,
        $/GFX/Sound/SFXWeapon_SAM_RAPIER_x8,
    ]
)
Weapon_DCA_2_canons_2A38M_30mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canons_2A38M_30mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canons_2A38M_30mm,
    ]
)
Weapon_SAM_9M311_Tunguska_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_9M311_Tunguska_x8,
        $/GFX/Sound/SFXWeapon_SAM_9M311_Tunguska_x8,
    ]
)
Weapon_RocketAir_S5_57mm_x32_BTR is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_S5_57mm_x32_BTR,
        $/GFX/Sound/SFXWeapon_RocketAir_S5_57mm_x32_BTR,
    ]
)
Weapon_DCA_4_canon_ZPU4_14_5mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_4_canon_ZPU4_14_5mm,
        $/GFX/Sound/SFXWeapon_DCA_4_canon_ZPU4_14_5mm,
    ]
)
Weapon_Mortier_81mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_81mm,
        $/GFX/Sound/SFXWeapon_Mortier_81mm,
    ]
)
Weapon_Mortier_81mm_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_81mm_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_81mm_SMOKE,
    ]
)
Weapon_DCA_1_canon_T20_20mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_1_canon_T20_20mm,
        $/GFX/Sound/SFXWeapon_DCA_1_canon_T20_20mm,
    ]
)
Weapon_Mistral is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mistral,
        $/GFX/Sound/SFXWeapon_Mistral,
    ]
)
Weapon_Mortier_81mm_61C is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_81mm_61C,
        $/GFX/Sound/SFXWeapon_Mortier_81mm_61C,
    ]
)
Weapon_Mortier_81mm_towed_61C_SMOKE is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mortier_81mm_towed_61C_SMOKE,
        $/GFX/Sound/SFXWeapon_Mortier_81mm_towed_61C_SMOKE,
    ]
)
Weapon_DCA_2_canon_2M3_25mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canon_2M3_25mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canon_2M3_25mm,
    ]
)
Weapon_DCA_4_canons_AZP_23_Amur_23mm_Afghan is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_4_canons_AZP_23_Amur_23mm_Afghan,
        $/GFX/Sound/SFXWeapon_DCA_4_canons_AZP_23_Amur_23mm_Afghan,
    ]
)
Weapon_DCA_4_canons_APZ23_23mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_4_canons_APZ23_23mm,
        $/GFX/Sound/SFXWeapon_DCA_4_canons_APZ23_23mm,
    ]
)
Weapon_DCA_4_canons_AZP_23_Amur_23mm_late is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_4_canons_AZP_23_Amur_23mm_late,
        $/GFX/Sound/SFXWeapon_DCA_4_canons_AZP_23_Amur_23mm_late,
    ]
)
Weapon_DCA_4_canons_AZP_23_Amur_23mm_PSNR is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_4_canons_AZP_23_Amur_23mm_PSNR,
        $/GFX/Sound/SFXWeapon_DCA_4_canons_AZP_23_Amur_23mm_PSNR,
    ]
)
Weapon_DCA_2_canons_S60_57mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_DCA_2_canons_S60_57mm,
        $/GFX/Sound/SFXWeapon_DCA_2_canons_S60_57mm,
    ]
)

Weapon_AGM_BGM71D_TOW_2_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_BGM71D_TOW_2_x4,
        $/GFX/Sound/SFXWeapon_AGM_BGM71D_TOW_2_x4,
    ]
)
Weapon_AGM_BGM71D_TOW_2_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_BGM71D_TOW_2_x8,
        $/GFX/Sound/SFXWeapon_AGM_BGM71D_TOW_2_x8,
    ]
)
Weapon_Gatling_M197_20mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Gatling_M197_20mm,
        $/GFX/Sound/SFXWeapon_Gatling_M197_20mm,
    ]
)
Weapon_Gatling_AP_M197_20mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Gatling_AP_M197_20mm,
        $/GFX/Sound/SFXWeapon_Gatling_AP_M197_20mm,
    ]
)
Weapon_RocketAir_Hydra_70mm_x19 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_Hydra_70mm_x19,
        $/GFX/Sound/SFXWeapon_RocketAir_Hydra_70mm_x19,
    ]
)
Weapon_SAM_FIM92_Stinger_CS_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_FIM92_Stinger_CS_x4,
        $/GFX/Sound/SFXWeapon_SAM_FIM92_Stinger_CS_x4,
    ]
)
Weapon_RocketAir_Hydra_70mm_x14 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_Hydra_70mm_x14,
        $/GFX/Sound/SFXWeapon_RocketAir_Hydra_70mm_x14,
    ]
)
Weapon_AGM_BGM71C_ITOW_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_BGM71C_ITOW_x8,
        $/GFX/Sound/SFXWeapon_AGM_BGM71C_ITOW_x8,
    ]
)
Weapon_RocketAir_Zuni_1272mm_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_Zuni_1272mm_x8,
        $/GFX/Sound/SFXWeapon_RocketAir_Zuni_1272mm_x8,
    ]
)
Weapon_Gatling_m134_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Gatling_m134_7_62mm,
        $/GFX/Sound/SFXWeapon_Gatling_m134_7_62mm,
    ]
)
Weapon_Lance_grenade_M129_40mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Lance_grenade_M129_40mm,
        $/GFX/Sound/SFXWeapon_Lance_grenade_M129_40mm,
    ]
)
Weapon_AutoCanon_AP_30mm_M230 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_30mm_M230,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_30mm_M230,
    ]
)
Weapon_AutoCanon_HE_30mm_M230 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_30mm_M230,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_30mm_M230,
    ]
)
Weapon_AGM_AGM114A_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AGM114A_x8,
        $/GFX/Sound/SFXWeapon_AGM_AGM114A_x8,
    ]
)
Weapon_AGM_AGM114A_x16 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AGM114A_x16,
        $/GFX/Sound/SFXWeapon_AGM_AGM114A_x16,
    ]
)
Weapon_Gatling_m134_7_62mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Gatling_m134_7_62mm_x2,
        $/GFX/Sound/SFXWeapon_Gatling_m134_7_62mm_x2,
    ]
)
Weapon_Gatling_GAU19_12_7mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Gatling_GAU19_12_7mm,
        $/GFX/Sound/SFXWeapon_Gatling_GAU19_12_7mm,
    ]
)
Weapon_ATGM_AS11_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_ATGM_AS11_x4,
        $/GFX/Sound/SFXWeapon_ATGM_AS11_x4,
    ]
)
Weapon_AGM_HOT2_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_HOT2_x6,
        $/GFX/Sound/SFXWeapon_AGM_HOT2_x6,
    ]
)
Weapon_AGM_HOT1_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_HOT1_x6,
        $/GFX/Sound/SFXWeapon_AGM_HOT1_x6,
    ]
)
Weapon_AutoCanon_AP_20mm_M621_GIAT is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_20mm_M621_GIAT,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_20mm_M621_GIAT,
    ]
)
Weapon_AutoCanon_HE_20mm_M621_GIAT is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_20mm_M621_GIAT,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_20mm_M621_GIAT,
    ]
)
Weapon_AGM_HOT2_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_HOT2_x4,
        $/GFX/Sound/SFXWeapon_AGM_HOT2_x4,
    ]
)
Weapon_AGM_HOT1_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_HOT1_x4,
        $/GFX/Sound/SFXWeapon_AGM_HOT1_x4,
    ]
)
Weapon_Mistral_Celtic_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Mistral_Celtic_x4,
        $/GFX/Sound/SFXWeapon_Mistral_Celtic_x4,
    ]
)
Weapon_RocketAir_SNEB_68mm_x12 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_SNEB_68mm_x12,
        $/GFX/Sound/SFXWeapon_RocketAir_SNEB_68mm_x12,
    ]
)
Weapon_AutoCanon_AP_30mm_24A2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_30mm_24A2,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_30mm_24A2,
    ]
)
Weapon_AutoCanon_HE_30mm_24A2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_30mm_24A2,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_30mm_24A2,
    ]
)
Weapon_SAM_IglaV_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_IglaV_x4,
        $/GFX/Sound/SFXWeapon_SAM_IglaV_x4,
    ]
)
Weapon_Pod_GUV_Gatling_7_62mm_12_7mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Pod_GUV_Gatling_7_62mm_12_7mm_x2,
        $/GFX/Sound/SFXWeapon_Pod_GUV_Gatling_7_62mm_12_7mm_x2,
    ]
)
Weapon_AGM_9K121_Vikhr_x12 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_9K121_Vikhr_x12,
        $/GFX/Sound/SFXWeapon_AGM_9K121_Vikhr_x12,
    ]
)
Weapon_RocketAir_B8_80mm_x10 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_B8_80mm_x10,
        $/GFX/Sound/SFXWeapon_RocketAir_B8_80mm_x10,
    ]
)
Weapon_AGM_BGM71_TOW_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_BGM71_TOW_x8,
        $/GFX/Sound/SFXWeapon_AGM_BGM71_TOW_x8,
    ]
)
Weapon_MMG_FN_MAG_7_62mm_Helo is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_FN_MAG_7_62mm_Helo,
        $/GFX/Sound/SFXWeapon_MMG_FN_MAG_7_62mm_Helo,
    ]
)
Weapon_AGM_BGM71C_FITOW_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_BGM71C_FITOW_x8,
        $/GFX/Sound/SFXWeapon_AGM_BGM71C_FITOW_x8,
    ]
)
Weapon_RocketAir_SNEB_68mm_x18_helo is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_SNEB_68mm_x18_helo,
        $/GFX/Sound/SFXWeapon_RocketAir_SNEB_68mm_x18_helo,
    ]
)
Weapon_AGM_Kh23M_helo is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_Kh23M_helo,
        $/GFX/Sound/SFXWeapon_AGM_Kh23M_helo,
    ]
)
Weapon_Gatling_JakB_12_7mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Gatling_JakB_12_7mm,
        $/GFX/Sound/SFXWeapon_Gatling_JakB_12_7mm,
    ]
)
Weapon_AA_R60M_Vympel_helo is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R60M_Vympel_helo,
        $/GFX/Sound/SFXWeapon_AA_R60M_Vympel_helo,
    ]
)
Weapon_RocketAir_S5_57mm_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_S5_57mm_x8,
        $/GFX/Sound/SFXWeapon_RocketAir_S5_57mm_x8,
    ]
)
Weapon_RocketAir_S5_57mm_x64 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_S5_57mm_x64,
        $/GFX/Sound/SFXWeapon_RocketAir_S5_57mm_x64,
    ]
)
Weapon_AGM_9M17P_FalangaP_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_9M17P_FalangaP_x4,
        $/GFX/Sound/SFXWeapon_AGM_9M17P_FalangaP_x4,
    ]
)
Weapon_Pod_UPK_23_250_AP_23mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Pod_UPK_23_250_AP_23mm_x2,
        $/GFX/Sound/SFXWeapon_Pod_UPK_23_250_AP_23mm_x2,
    ]
)
Weapon_Pod_UPK_23_250_HE_23mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Pod_UPK_23_250_HE_23mm_x2,
        $/GFX/Sound/SFXWeapon_Pod_UPK_23_250_HE_23mm_x2,
    ]
)
Weapon_AutoCanon_AP_30mm_Bitube_Gsh30k is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_30mm_Bitube_Gsh30k,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_30mm_Bitube_Gsh30k,
    ]
)
Weapon_AutoCanon_HE_30mm_Bitube_Gsh30k is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_30mm_Bitube_Gsh30k,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_30mm_Bitube_Gsh30k,
    ]
)
Weapon_AGM_9M114M_KokonM_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_9M114M_KokonM_x4,
        $/GFX/Sound/SFXWeapon_AGM_9M114M_KokonM_x4,
    ]
)
Weapon_AGM_9M114M_KokonM_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_9M114M_KokonM_x8,
        $/GFX/Sound/SFXWeapon_AGM_9M114M_KokonM_x8,
    ]
)
Weapon_AutoCanon_AP_23mm_Bitube_Gsh23L is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_23mm_Bitube_Gsh23L,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_23mm_Bitube_Gsh23L,
    ]
)
Weapon_AutoCanon_HE_23mm_Bitube_Gsh23L is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_23mm_Bitube_Gsh23L,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_23mm_Bitube_Gsh23L,
    ]
)
Weapon_AGM_9M114M_KokonM_x16 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_9M114M_KokonM_x16,
        $/GFX/Sound/SFXWeapon_AGM_9M114M_KokonM_x16,
    ]
)
Weapon_RocketAir_S13_122mm_x10 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_S13_122mm_x10,
        $/GFX/Sound/SFXWeapon_RocketAir_S13_122mm_x10,
    ]
)
Weapon_AutoCanon_AP_23mm_NS23 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_AP_23mm_NS23,
        $/GFX/Sound/SFXWeapon_AutoCanon_AP_23mm_NS23,
    ]
)
Weapon_AutoCanon_HE_23mm_NS23 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AutoCanon_HE_23mm_NS23,
        $/GFX/Sound/SFXWeapon_AutoCanon_HE_23mm_NS23,
    ]
)
Weapon_SAM_Strela2_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_SAM_Strela2_x4,
        $/GFX/Sound/SFXWeapon_SAM_Strela2_x4,
    ]
)
Weapon_AGM_9M14_MalyutkaP_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_9M14_MalyutkaP_x4,
        $/GFX/Sound/SFXWeapon_AGM_9M14_MalyutkaP_x4,
    ]
)
Weapon_MMG_PKT_7_62mm_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_PKT_7_62mm_x4,
        $/GFX/Sound/SFXWeapon_MMG_PKT_7_62mm_x4,
    ]
)
Weapon_RocketAir_S5_57mm_x32 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_S5_57mm_x32,
        $/GFX/Sound/SFXWeapon_RocketAir_S5_57mm_x32,
    ]
)
Weapon_HMG_12_7_mm_Afanasyev is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_HMG_12_7_mm_Afanasyev,
        $/GFX/Sound/SFXWeapon_HMG_12_7_mm_Afanasyev,
    ]
)
Weapon_AGM_9M14_MalyutkaP_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_9M14_MalyutkaP_x6,
        $/GFX/Sound/SFXWeapon_AGM_9M14_MalyutkaP_x6,
    ]
)
Weapon_RocketAir_B8_80mm_x20 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_B8_80mm_x20,
        $/GFX/Sound/SFXWeapon_RocketAir_B8_80mm_x20,
    ]
)
Weapon_AA_R60M_Vympel is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R60M_Vympel,
        $/GFX/Sound/SFXWeapon_AA_R60M_Vympel,
    ]
)
Weapon_RocketAir_S24_240mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_S24_240mm_x2,
        $/GFX/Sound/SFXWeapon_RocketAir_S24_240mm_x2,
    ]
)
Weapon_Pod_GUV_AGS17_2x is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Pod_GUV_AGS17_2x,
        $/GFX/Sound/SFXWeapon_Pod_GUV_AGS17_2x,
    ]
)
Weapon_AGM_AGM114A_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AGM114A_x4,
        $/GFX/Sound/SFXWeapon_AGM_AGM114A_x4,
    ]
)
Weapon_MMG_MG3_7_62mm_helo is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_MG3_7_62mm_helo,
        $/GFX/Sound/SFXWeapon_MMG_MG3_7_62mm_helo,
    ]
)
Weapon_MMG_M60_7_62mm_helo is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_M60_7_62mm_helo,
        $/GFX/Sound/SFXWeapon_MMG_M60_7_62mm_helo,
    ]
)
Weapon_MMG_M240d_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MMG_M240d_7_62mm,
        $/GFX/Sound/SFXWeapon_MMG_M240d_7_62mm,
    ]
)

Weapon_GatlingAir_GAU8_30mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_GAU8_30mm,
        $/GFX/Sound/SFXWeapon_GatlingAir_GAU8_30mm,
    ]
)
Weapon_GatlingAir_AP_GAU8_30mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_AP_GAU8_30mm,
        $/GFX/Sound/SFXWeapon_GatlingAir_AP_GAU8_30mm,
    ]
)
Weapon_AGM_AGM65D_Maverick is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AGM65D_Maverick,
        $/GFX/Sound/SFXWeapon_AGM_AGM65D_Maverick,
    ]
)
Weapon_AA_AIM9M_Sidewinder is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_AIM9M_Sidewinder,
        $/GFX/Sound/SFXWeapon_AA_AIM9M_Sidewinder,
    ]
)
Weapon_RocketAir_Hydra_70mm_x76 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_Hydra_70mm_x76,
        $/GFX/Sound/SFXWeapon_RocketAir_Hydra_70mm_x76,
    ]
)
Weapon_Bomb_Mk82_250kg_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk82_250kg_x8,
        $/GFX/Sound/SFXWeapon_Bomb_Mk82_250kg_x8,
    ]
)
Weapon_GatlingAir_GAU_2B_7_62mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_GAU_2B_7_62mm,
        $/GFX/Sound/SFXWeapon_GatlingAir_GAU_2B_7_62mm,
    ]
)
Weapon_Bomb_Mk82_250kg_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk82_250kg_x4,
        $/GFX/Sound/SFXWeapon_Bomb_Mk82_250kg_x4,
    ]
)
Weapon_Bomb_Mk81_119kg_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk81_119kg_x4,
        $/GFX/Sound/SFXWeapon_Bomb_Mk81_119kg_x4,
    ]
)
Weapon_Bomb_Mk77_340kg_Napalm_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk77_340kg_Napalm_x4,
        $/GFX/Sound/SFXWeapon_Bomb_Mk77_340kg_Napalm_x4,
    ]
)
Weapon_RocketAir_Hydra_70mm_avion_SMOKE_x14 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_Hydra_70mm_avion_SMOKE_x14,
        $/GFX/Sound/SFXWeapon_RocketAir_Hydra_70mm_avion_SMOKE_x14,
    ]
)
Weapon_RocketAir_Hydra_70mm_x38_avion is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_Hydra_70mm_x38_avion,
        $/GFX/Sound/SFXWeapon_RocketAir_Hydra_70mm_x38_avion,
    ]
)
Weapon_AGM_AGM45_Shrike is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AGM45_Shrike,
        $/GFX/Sound/SFXWeapon_AGM_AGM45_Shrike,
    ]
)
Weapon_Bomb_Mk77_340kg_Napalm_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk77_340kg_Napalm_x8,
        $/GFX/Sound/SFXWeapon_Bomb_Mk77_340kg_Napalm_x8,
    ]
)
Weapon_GatlingAir_M61_Vulcan_20mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_M61_Vulcan_20mm,
        $/GFX/Sound/SFXWeapon_GatlingAir_M61_Vulcan_20mm,
    ]
)
Weapon_AA_AIM9L_Sidewinder is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_AIM9L_Sidewinder,
        $/GFX/Sound/SFXWeapon_AA_AIM9L_Sidewinder,
    ]
)
Weapon_AGM_AGM65B_Maverick is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AGM65B_Maverick,
        $/GFX/Sound/SFXWeapon_AGM_AGM65B_Maverick,
    ]
)
Weapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x12 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x12,
        $/GFX/Sound/SFXWeapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x12,
    ]
)
Weapon_Bomb_Mk84_920kg_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk84_920kg_x4,
        $/GFX/Sound/SFXWeapon_Bomb_Mk84_920kg_x4,
    ]
)
Weapon_GatlingAir_Mauser_BK_27mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_Mauser_BK_27mm,
        $/GFX/Sound/SFXWeapon_GatlingAir_Mauser_BK_27mm,
    ]
)
Weapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x4,
        $/GFX/Sound/SFXWeapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x4,
    ]
)
Weapon_Bomb_Mk77_340kg_Napalm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk77_340kg_Napalm_x2,
        $/GFX/Sound/SFXWeapon_Bomb_Mk77_340kg_Napalm_x2,
    ]
)
Weapon_Bomb_Mk81_119kg_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk81_119kg_x2,
        $/GFX/Sound/SFXWeapon_Bomb_Mk81_119kg_x2,
    ]
)
Weapon_RocketAir_SNEB_68mm_x18 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_SNEB_68mm_x18,
        $/GFX/Sound/SFXWeapon_RocketAir_SNEB_68mm_x18,
    ]
)
Weapon_Bomb_Matra_250kg_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Matra_250kg_x6,
        $/GFX/Sound/SFXWeapon_Bomb_Matra_250kg_x6,
    ]
)
Weapon_Bomb_Bidons_Speciaux_Napalm_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Bidons_Speciaux_Napalm_x4,
        $/GFX/Sound/SFXWeapon_Bomb_Bidons_Speciaux_Napalm_x4,
    ]
)
Weapon_Bomb_Mk83_450kg_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk83_450kg_x4,
        $/GFX/Sound/SFXWeapon_Bomb_Mk83_450kg_x4,
    ]
)
Weapon_AGM_AJ_168_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AJ_168_x2,
        $/GFX/Sound/SFXWeapon_AGM_AJ_168_x2,
    ]
)
Weapon_Bomb_CPU_123_x1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CPU_123_x1,
        $/GFX/Sound/SFXWeapon_Bomb_CPU_123_x1,
    ]
)
Weapon_Bomb_Mk18_RET_513kg_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk18_RET_513kg_x6,
        $/GFX/Sound/SFXWeapon_Bomb_Mk18_RET_513kg_x6,
    ]
)
Weapon_AGM_AS37_Martel_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AS37_Martel_x2,
        $/GFX/Sound/SFXWeapon_AGM_AS37_Martel_x2,
    ]
)
Weapon_GatlingAir_AA52_7_5mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_AA52_7_5mm_x2,
        $/GFX/Sound/SFXWeapon_GatlingAir_AA52_7_5mm_x2,
    ]
)
Weapon_AGM_AS11_x2_air is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AS11_x2_air,
        $/GFX/Sound/SFXWeapon_AGM_AS11_x2_air,
    ]
)
Weapon_AGM_AGM88_HARM is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AGM88_HARM,
        $/GFX/Sound/SFXWeapon_AGM_AGM88_HARM,
    ]
)
Weapon_AGM_AS30 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AS30,
        $/GFX/Sound/SFXWeapon_AGM_AS30,
    ]
)
Weapon_Bomb_Mk83_450kg_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk83_450kg_x2,
        $/GFX/Sound/SFXWeapon_Bomb_Mk83_450kg_x2,
    ]
)
Weapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x8,
        $/GFX/Sound/SFXWeapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x8,
    ]
)
Weapon_Bomb_Mk82_250kg_x12 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk82_250kg_x12,
        $/GFX/Sound/SFXWeapon_Bomb_Mk82_250kg_x12,
    ]
)
Weapon_Bomb_GBU_10_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_GBU_10_x2,
        $/GFX/Sound/SFXWeapon_Bomb_GBU_10_x2,
    ]
)
Weapon_Bomb_GBU_12_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_GBU_12_x2,
        $/GFX/Sound/SFXWeapon_Bomb_GBU_12_x2,
    ]
)
Weapon_Bomb_GBU_27_x1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_GBU_27_x1,
        $/GFX/Sound/SFXWeapon_Bomb_GBU_27_x1,
    ]
)
Weapon_AA_AIM7M_Sparrow is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_AIM7M_Sparrow,
        $/GFX/Sound/SFXWeapon_AA_AIM7M_Sparrow,
    ]
)
Weapon_AA_AIM120A_AMRAAM is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_AIM120A_AMRAAM,
        $/GFX/Sound/SFXWeapon_AA_AIM120A_AMRAAM,
    ]
)
Weapon_AA_AIM9P_Sidewinder is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_AIM9P_Sidewinder,
        $/GFX/Sound/SFXWeapon_AA_AIM9P_Sidewinder,
    ]
)
Weapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x2,
        $/GFX/Sound/SFXWeapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x2,
    ]
)
Weapon_Bomb_Mk82_250kg_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk82_250kg_x6,
        $/GFX/Sound/SFXWeapon_Bomb_Mk82_250kg_x6,
    ]
)
Weapon_Bomb_BL755_cluster_264kg_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_BL755_cluster_264kg_x4,
        $/GFX/Sound/SFXWeapon_Bomb_BL755_cluster_264kg_x4,
    ]
)
Weapon_Bomb_GBU_12_x1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_GBU_12_x1,
        $/GFX/Sound/SFXWeapon_Bomb_GBU_12_x1,
    ]
)
Weapon_AA_AIM9J_Sidewinder is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_AIM9J_Sidewinder,
        $/GFX/Sound/SFXWeapon_AA_AIM9J_Sidewinder,
    ]
)
Weapon_Bomb_Mk83_450kg_x5 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk83_450kg_x5,
        $/GFX/Sound/SFXWeapon_Bomb_Mk83_450kg_x5,
    ]
)
Weapon_AA_Skyflash is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_Skyflash,
        $/GFX/Sound/SFXWeapon_AA_Skyflash,
    ]
)
Weapon_Bomb_Mk18_RET_513kg_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk18_RET_513kg_x4,
        $/GFX/Sound/SFXWeapon_Bomb_Mk18_RET_513kg_x4,
    ]
)
Weapon_GatlingAir_M39A2_20mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_M39A2_20mm,
        $/GFX/Sound/SFXWeapon_GatlingAir_M39A2_20mm,
    ]
)
Weapon_RocketAir_CRV7_70mm_x38 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_CRV7_70mm_x38,
        $/GFX/Sound/SFXWeapon_RocketAir_CRV7_70mm_x38,
    ]
)
Weapon_GatlingAir_Colt_Mk12_20mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_Colt_Mk12_20mm,
        $/GFX/Sound/SFXWeapon_GatlingAir_Colt_Mk12_20mm,
    ]
)
Weapon_AA_Matra_R530 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_Matra_R530,
        $/GFX/Sound/SFXWeapon_AA_Matra_R530,
    ]
)
Weapon_AA_R550_Magic_II is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R550_Magic_II,
        $/GFX/Sound/SFXWeapon_AA_R550_Magic_II,
    ]
)
Weapon_Pave_Claw_HE_30mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Pave_Claw_HE_30mm,
        $/GFX/Sound/SFXWeapon_Pave_Claw_HE_30mm,
    ]
)
Weapon_Pave_Claw_AP_30mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Pave_Claw_AP_30mm,
        $/GFX/Sound/SFXWeapon_Pave_Claw_AP_30mm,
    ]
)
Weapon_GatlingAir_DEFA_30mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_DEFA_30mm_x2,
        $/GFX/Sound/SFXWeapon_GatlingAir_DEFA_30mm_x2,
    ]
)
Weapon_GatlingAir_ADEN_Mk4_30mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_ADEN_Mk4_30mm_x2,
        $/GFX/Sound/SFXWeapon_GatlingAir_ADEN_Mk4_30mm_x2,
    ]
)
Weapon_Bomb_BL755_cluster_264kg_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_BL755_cluster_264kg_x2,
        $/GFX/Sound/SFXWeapon_Bomb_BL755_cluster_264kg_x2,
    ]
)
Weapon_RocketAir_SNEB_68mm_x36 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_SNEB_68mm_x36,
        $/GFX/Sound/SFXWeapon_RocketAir_SNEB_68mm_x36,
    ]
)
Weapon_AGM_AS30L is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AS30L,
        $/GFX/Sound/SFXWeapon_AGM_AS30L,
    ]
)
Weapon_Bomb_Matra_400kg_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Matra_400kg_x4,
        $/GFX/Sound/SFXWeapon_Bomb_Matra_400kg_x4,
    ]
)
Weapon_AGM_ARMAT is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_ARMAT,
        $/GFX/Sound/SFXWeapon_AGM_ARMAT,
    ]
)
Weapon_AGM_AS37_Martel is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_AS37_Martel,
        $/GFX/Sound/SFXWeapon_AGM_AS37_Martel,
    ]
)
Weapon_Bomb_BLG66_Belouga_cluster_305kg_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_BLG66_Belouga_cluster_305kg_x4,
        $/GFX/Sound/SFXWeapon_Bomb_BLG66_Belouga_cluster_305kg_x4,
    ]
)
Weapon_Bomb_CLU_RBK_250kg_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CLU_RBK_250kg_x2,
        $/GFX/Sound/SFXWeapon_Bomb_CLU_RBK_250kg_x2,
    ]
)
Weapon_Bomb_FAB_250kg_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_FAB_250kg_x2,
        $/GFX/Sound/SFXWeapon_Bomb_FAB_250kg_x2,
    ]
)
Weapon_Bomb_FAB_500kg_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_FAB_500kg_x2,
        $/GFX/Sound/SFXWeapon_Bomb_FAB_500kg_x2,
    ]
)
Weapon_Bomb_ZB500_500kg_Napalm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_ZB500_500kg_Napalm_x2,
        $/GFX/Sound/SFXWeapon_Bomb_ZB500_500kg_Napalm_x2,
    ]
)
Weapon_GatlingAir_NR23_23mm_x3 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_NR23_23mm_x3,
        $/GFX/Sound/SFXWeapon_GatlingAir_NR23_23mm_x3,
    ]
)
Weapon_RocketAir_Grom_57mm_x16 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_Grom_57mm_x16,
        $/GFX/Sound/SFXWeapon_RocketAir_Grom_57mm_x16,
    ]
)
Weapon_GatlingAir_GSh_23_23mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_GSh_23_23mm,
        $/GFX/Sound/SFXWeapon_GatlingAir_GSh_23_23mm,
    ]
)
Weapon_RocketAir_S13_122mm_x10_avion is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_S13_122mm_x10_avion,
        $/GFX/Sound/SFXWeapon_RocketAir_S13_122mm_x10_avion,
    ]
)
Weapon_AA_R13M is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R13M,
        $/GFX/Sound/SFXWeapon_AA_R13M,
    ]
)
Weapon_AA_R3R is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R3R,
        $/GFX/Sound/SFXWeapon_AA_R3R,
    ]
)
Weapon_Bomb_CLU_RBK_500kg_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CLU_RBK_500kg_x2,
        $/GFX/Sound/SFXWeapon_Bomb_CLU_RBK_500kg_x2,
    ]
)
Weapon_Bomb_FAB_250kg_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_FAB_250kg_x4,
        $/GFX/Sound/SFXWeapon_Bomb_FAB_250kg_x4,
    ]
)
Weapon_AGM_Kh23M is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_Kh23M,
        $/GFX/Sound/SFXWeapon_AGM_Kh23M,
    ]
)
Weapon_RocketAir_B8_80mm_x40 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_B8_80mm_x40,
        $/GFX/Sound/SFXWeapon_RocketAir_B8_80mm_x40,
    ]
)
Weapon_Bomb_CLU_RBK_250kg_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CLU_RBK_250kg_x6,
        $/GFX/Sound/SFXWeapon_Bomb_CLU_RBK_250kg_x6,
    ]
)
Weapon_Bomb_FAB_500kg_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_FAB_500kg_x4,
        $/GFX/Sound/SFXWeapon_Bomb_FAB_500kg_x4,
    ]
)
Weapon_KMGU_dispenser is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_KMGU_dispenser,
        $/GFX/Sound/SFXWeapon_KMGU_dispenser,
    ]
)
Weapon_Bomb_ZB500_500kg_Napalm_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_ZB500_500kg_Napalm_x6,
        $/GFX/Sound/SFXWeapon_Bomb_ZB500_500kg_Napalm_x6,
    ]
)
Weapon_AA_R23R_Vympel is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R23R_Vympel,
        $/GFX/Sound/SFXWeapon_AA_R23R_Vympel,
    ]
)
Weapon_Bomb_FAB_500kg_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_FAB_500kg_x6,
        $/GFX/Sound/SFXWeapon_Bomb_FAB_500kg_x6,
    ]
)
Weapon_AA_R24R_Vympel is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R24R_Vympel,
        $/GFX/Sound/SFXWeapon_AA_R24R_Vympel,
    ]
)
Weapon_AA_R24MR_Vympel is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R24MR_Vympel,
        $/GFX/Sound/SFXWeapon_AA_R24MR_Vympel,
    ]
)
Weapon_AA_R73_Vympel is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R73_Vympel,
        $/GFX/Sound/SFXWeapon_AA_R73_Vympel,
    ]
)
Weapon_AGM_Kh58U is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_Kh58U,
        $/GFX/Sound/SFXWeapon_AGM_Kh58U,
    ]
)
Weapon_Bomb_FAB_500kg_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_FAB_500kg_x8,
        $/GFX/Sound/SFXWeapon_Bomb_FAB_500kg_x8,
    ]
)
Weapon_GatlingAir_AP_Gsh_30_6_30mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_AP_Gsh_30_6_30mm,
        $/GFX/Sound/SFXWeapon_GatlingAir_AP_Gsh_30_6_30mm,
    ]
)
Weapon_GatlingAir_Gsh_30_6_30mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_Gsh_30_6_30mm,
        $/GFX/Sound/SFXWeapon_GatlingAir_Gsh_30_6_30mm,
    ]
)
Weapon_AGM_Kh29L is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_Kh29L,
        $/GFX/Sound/SFXWeapon_AGM_Kh29L,
    ]
)
Weapon_AGM_Kh29T is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_Kh29T,
        $/GFX/Sound/SFXWeapon_AGM_Kh29T,
    ]
)
Weapon_Bomb_KAB_500Kr_x1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_KAB_500Kr_x1,
        $/GFX/Sound/SFXWeapon_Bomb_KAB_500Kr_x1,
    ]
)
Weapon_AGM_Kh25MP is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_Kh25MP,
        $/GFX/Sound/SFXWeapon_AGM_Kh25MP,
    ]
)
Weapon_Bomb_CLU_RBK_500kg_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CLU_RBK_500kg_x4,
        $/GFX/Sound/SFXWeapon_Bomb_CLU_RBK_500kg_x4,
    ]
)
Weapon_Bomb_ZB500_500kg_Napalm_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_ZB500_500kg_Napalm_x4,
        $/GFX/Sound/SFXWeapon_Bomb_ZB500_500kg_Napalm_x4,
    ]
)
Weapon_RocketAir_B8_80mm_x10_Avion is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_B8_80mm_x10_Avion,
        $/GFX/Sound/SFXWeapon_RocketAir_B8_80mm_x10_Avion,
    ]
)
Weapon_AGM_Kh28_X28 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_Kh28_X28,
        $/GFX/Sound/SFXWeapon_AGM_Kh28_X28,
    ]
)
Weapon_GatlingAir_Gsh_30_1_30mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_Gsh_30_1_30mm,
        $/GFX/Sound/SFXWeapon_GatlingAir_Gsh_30_1_30mm,
    ]
)
Weapon_AA_R27R_Vympel is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R27R_Vympel,
        $/GFX/Sound/SFXWeapon_AA_R27R_Vympel,
    ]
)
Weapon_AA_R27T_Vympel is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R27T_Vympel,
        $/GFX/Sound/SFXWeapon_AA_R27T_Vympel,
    ]
)
Weapon_GatlingAir_Gsh_23_6_23mm is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_Gsh_23_6_23mm,
        $/GFX/Sound/SFXWeapon_GatlingAir_Gsh_23_6_23mm,
    ]
)
Weapon_AA_R37_Vympel is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R37_Vympel,
        $/GFX/Sound/SFXWeapon_AA_R37_Vympel,
    ]
)
Weapon_AA_R33_Vympel is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R33_Vympel,
        $/GFX/Sound/SFXWeapon_AA_R33_Vympel,
    ]
)
Weapon_AA_R40TD1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R40TD1,
        $/GFX/Sound/SFXWeapon_AA_R40TD1,
    ]
)
Weapon_AA_Matra_Super_530D is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_Matra_Super_530D,
        $/GFX/Sound/SFXWeapon_AA_Matra_Super_530D,
    ]
)
Weapon_Bomb_Matra_250kg_x10 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Matra_250kg_x10,
        $/GFX/Sound/SFXWeapon_Bomb_Matra_250kg_x10,
    ]
)
Weapon_GatlingAir_DEFA_30mm_x1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_DEFA_30mm_x1,
        $/GFX/Sound/SFXWeapon_GatlingAir_DEFA_30mm_x1,
    ]
)
Weapon_Bomb_BGL_400_x1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_BGL_400_x1,
        $/GFX/Sound/SFXWeapon_Bomb_BGL_400_x1,
    ]
)
Weapon_AA_Matra_Super_530F is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_Matra_Super_530F,
        $/GFX/Sound/SFXWeapon_AA_Matra_Super_530F,
    ]
)
Weapon_AA_R550_Magic is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R550_Magic,
        $/GFX/Sound/SFXWeapon_AA_R550_Magic,
    ]
)
Weapon_Bomb_Matra_400kg_x16 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Matra_400kg_x16,
        $/GFX/Sound/SFXWeapon_Bomb_Matra_400kg_x16,
    ]
)
Weapon_Bomb_Matra_400kg_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Matra_400kg_x6,
        $/GFX/Sound/SFXWeapon_Bomb_Matra_400kg_x6,
    ]
)
Weapon_RocketAir_Hydra_70mm_avion_SMOKE_x38 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_Hydra_70mm_avion_SMOKE_x38,
        $/GFX/Sound/SFXWeapon_RocketAir_Hydra_70mm_avion_SMOKE_x38,
    ]
)
Weapon_AA_R98MT is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R98MT,
        $/GFX/Sound/SFXWeapon_AA_R98MT,
    ]
)
Weapon_Pod_UPK_23_250_AP_23mm_x2_avion is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Pod_UPK_23_250_AP_23mm_x2_avion,
        $/GFX/Sound/SFXWeapon_Pod_UPK_23_250_AP_23mm_x2_avion,
    ]
)
Weapon_Pod_UPK_23_250_HE_23mm_x2_avion is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Pod_UPK_23_250_HE_23mm_x2_avion,
        $/GFX/Sound/SFXWeapon_Pod_UPK_23_250_HE_23mm_x2_avion,
    ]
)
Weapon_AA_R98MR is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_R98MR,
        $/GFX/Sound/SFXWeapon_AA_R98MR,
    ]
)
Weapon_GatlingAir_NR30_30mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_NR30_30mm_x2,
        $/GFX/Sound/SFXWeapon_GatlingAir_NR30_30mm_x2,
    ]
)
Weapon_RocketAir_S13_122mm_x20 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_S13_122mm_x20,
        $/GFX/Sound/SFXWeapon_RocketAir_S13_122mm_x20,
    ]
)
Weapon_Bomb_CLU_RBK_500kg_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CLU_RBK_500kg_x6,
        $/GFX/Sound/SFXWeapon_Bomb_CLU_RBK_500kg_x6,
    ]
)
Weapon_AGM_Kh25ML is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_Kh25ML,
        $/GFX/Sound/SFXWeapon_AGM_Kh25ML,
    ]
)
Weapon_RocketAir_B8_80mm_x80 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_RocketAir_B8_80mm_x80,
        $/GFX/Sound/SFXWeapon_RocketAir_B8_80mm_x80,
    ]
)
Weapon_Bomb_CLU_RBK_250kg_x4 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CLU_RBK_250kg_x4,
        $/GFX/Sound/SFXWeapon_Bomb_CLU_RBK_250kg_x4,
    ]
)
Weapon_Bomb_KAB_1500L_x1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_KAB_1500L_x1,
        $/GFX/Sound/SFXWeapon_Bomb_KAB_1500L_x1,
    ]
)
Weapon_Bomb_KAB_1500Kr_x1 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_KAB_1500Kr_x1,
        $/GFX/Sound/SFXWeapon_Bomb_KAB_1500Kr_x1,
    ]
)
Weapon_Bomb_CLU_RBK_500kg_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CLU_RBK_500kg_x8,
        $/GFX/Sound/SFXWeapon_Bomb_CLU_RBK_500kg_x8,
    ]
)
Weapon_Bomb_CLU_RBK_250kg_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CLU_RBK_250kg_x8,
        $/GFX/Sound/SFXWeapon_Bomb_CLU_RBK_250kg_x8,
    ]
)
Weapon_Bomb_ODAB_500PM_500kg_Thermobaric_x6 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_ODAB_500PM_500kg_Thermobaric_x6,
        $/GFX/Sound/SFXWeapon_Bomb_ODAB_500PM_500kg_Thermobaric_x6,
    ]
)
Weapon_GatlingAir_Gsh_30_2_30mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_Gsh_30_2_30mm_x2,
        $/GFX/Sound/SFXWeapon_GatlingAir_Gsh_30_2_30mm_x2,
    ]
)
Weapon_GatlingAir_AP_Gsh_30_2_30mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_AP_Gsh_30_2_30mm_x2,
        $/GFX/Sound/SFXWeapon_GatlingAir_AP_Gsh_30_2_30mm_x2,
    ]
)
Weapon_AGM_9K121_Vikhr_x16_avion is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_9K121_Vikhr_x16_avion,
        $/GFX/Sound/SFXWeapon_AGM_9K121_Vikhr_x16_avion,
    ]
)
Weapon_GatlingAir_Mauser_BK_27mm_x2 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_GatlingAir_Mauser_BK_27mm_x2,
        $/GFX/Sound/SFXWeapon_GatlingAir_Mauser_BK_27mm_x2,
    ]
)
Weapon_AGM_ALARM is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AGM_ALARM,
        $/GFX/Sound/SFXWeapon_AGM_ALARM,
    ]
)
Weapon_AA_Skyflash_SuperTEMP is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_AA_Skyflash_SuperTEMP,
        $/GFX/Sound/SFXWeapon_AA_Skyflash_SuperTEMP,
    ]
)
Weapon_Bomb_BL755_cluster_264kg_x8 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_BL755_cluster_264kg_x8,
        $/GFX/Sound/SFXWeapon_Bomb_BL755_cluster_264kg_x8,
    ]
)
Weapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x5 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x5,
        $/GFX/Sound/SFXWeapon_Bomb_CBU_Mk20_Rockeye_II_250kg_x5,
    ]
)
Weapon_Bomb_Mk83_450kg_x3 is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_Bomb_Mk83_450kg_x3,
        $/GFX/Sound/SFXWeapon_Bomb_Mk83_450kg_x3,
    ]
)
Weapon_MW1_dispenser is TCompositeHappening
(
    SubHappenings = 
    [
        FXWeapon_MW1_dispenser,
        $/GFX/Sound/SFXWeapon_MW1_dispenser,
    ]
)

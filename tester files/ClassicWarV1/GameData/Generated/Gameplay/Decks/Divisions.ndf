// Ne pas éditer, ce fichier est généré par DivisionFileWriter


Descriptor_Deck_Division_BEL_16e_Mecanisee_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{7265322a-3993-4b09-9f2c-d775bcc7e14e}
    CfgName = 'BEL_16e_Mecanisee_multi'
    DivisionName = 'DCPLBIYTNN'
    InterfaceOrder = 25.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'BEL', 'Allied', 'armored', 'DC_PWR1']
    DescriptionHintTitleToken = 'DCPLBIYTNN'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_BEL_16e_Mecanisee_multi_Rule
    CostMatrix = MatrixCostName_BEL_16e_Mecanisee_multi
    EmblemTexture     = "Texture_Division_Emblem_BEL_16e_Meca"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "BEL"
)
Descriptor_Deck_Division_FR_11e_Para_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{beaeddc6-2819-4025-b72f-9448e16b39ec}
    CfgName = 'FR_11e_Para_multi'
    DivisionName = 'PPYRYDDJRK'
    InterfaceOrder = 22.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'FR', 'Allied', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'PPYRYDDJRK'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_FR_11e_Para_multi_Rule
    CostMatrix = MatrixCostName_FR_11e_Para_multi
    EmblemTexture     = "Texture_Division_Emblem_FR_11e_Para"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "FR"
)
Descriptor_Deck_Division_FR_152e_Infanterie_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{adb9f240-782d-44d1-bb70-20f2736182de}
    CfgName = 'FR_152e_Infanterie_multi'
    DivisionName = 'YHHSJATYLU'
    InterfaceOrder = 23.0
    DivisionPowerClassification = 'DC_PWR3'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'FR', 'Allied', 'infantryReg', 'DC_PWR3']
    DescriptionHintTitleToken = 'AEINBNGLTE'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_FR_152e_Infanterie_multi_Rule
    CostMatrix = MatrixCostName_FR_152e_Infanterie_multi
    EmblemTexture     = "Texture_Division_Emblem_FR_152eID"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "FR"
)
Descriptor_Deck_Division_FR_5e_Blindee_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{3eea10f1-c482-46f2-a747-f49879749d82}
    CfgName = 'FR_5e_Blindee_multi'
    DivisionName = 'MVUBDSQMBB'
    InterfaceOrder = 20.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'FR', 'Allied', 'armored', 'DC_PWR1']
    DescriptionHintTitleToken = 'MVUBDSQMBB'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_FR_5e_Blindee_multi_Rule
    CostMatrix = MatrixCostName_FR_5e_Blindee_multi
    EmblemTexture     = "Texture_Division_Emblem_FR_5eDB"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "FR"
)
Descriptor_Deck_Division_NATO_Garnison_Berlin_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{9ab11079-b134-4ec3-beba-48cf4e885757}
    CfgName = 'NATO_Garnison_Berlin_multi'
    DivisionName = 'EYKCGOHKBG'
    InterfaceOrder = 18.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'UK', 'Allied', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'EYKCGOHKBG'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_NATO_Garnison_Berlin_multi_Rule
    CostMatrix = MatrixCostName_NATO_Garnison_Berlin_multi
    EmblemTexture     = "Texture_Division_Emblem_Berlin_Command"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "UK"
)
Descriptor_Deck_Division_NL_4e_Divisie_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{87f7b86f-6455-403f-ba2b-415030226d6b}
    CfgName = 'NL_4e_Divisie_multi'
    DivisionName = 'MRBOKCBPXP'
    InterfaceOrder = 27.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'NL', 'Allied', 'armored', 'DC_PWR1']
    DescriptionHintTitleToken = 'FUYMSLGESC'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_NL_4e_Divisie_multi_Rule
    CostMatrix = MatrixCostName_NL_4e_Divisie_multi
    EmblemTexture     = "Texture_Division_Emblem_NL_4e_Divisie"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "NL"
)
Descriptor_Deck_Division_POL_20_Pancerna_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{a13936c6-b279-4430-b456-93d953339572}
    CfgName = 'POL_20_Pancerna_multi'
    DivisionName = 'EGPXSAYCXO'
    InterfaceOrder = 22.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'POL', 'Axis', 'armored', 'DC_PWR1']
    DescriptionHintTitleToken = 'EGPXSAYCXO'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_POL_20_Pancerna_multi_Rule
    CostMatrix = MatrixCostName_POL_20_Pancerna_multi
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "POL"
)
Descriptor_Deck_Division_POL_4_Zmechanizowana_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2b303d19-27e5-4a2c-8e3e-e497328bbba8}
    CfgName = 'POL_4_Zmechanizowana_multi'
    DivisionName = 'ZYGJBHWQHW'
    InterfaceOrder = 21.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'POL', 'Axis', 'armored', 'DC_PWR1']
    DescriptionHintTitleToken = 'ZYGJBHWQHW'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_POL_4_Zmechanizowana_multi_Rule
    CostMatrix = MatrixCostName_POL_4_Zmechanizowana_multi
    EmblemTexture     = "Texture_Division_Emblem_POL_4_ZMech"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "POL"
)
Descriptor_Deck_Division_POL_Korpus_Desantowy_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{1d182660-9f0c-47b0-a90d-bc857ab4d032}
    CfgName = 'POL_Korpus_Desantowy_multi'
    DivisionName = 'CZOHUXQNCE'
    InterfaceOrder = 23.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'POL', 'Axis', 'infantryReg', 'DC_PWR1']
    DescriptionHintTitleToken = 'CZOHUXQNCE'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_POL_Korpus_Desantowy_multi_Rule
    CostMatrix = MatrixCostName_POL_Korpus_Desantowy_multi
    EmblemTexture     = "Texture_Division_Emblem_POL_Korpus_Desantowy"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "POL"
)
Descriptor_Deck_Division_RDA_4_MSD_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{97b5d7d2-0a51-461e-b97e-617dd6892858}
    CfgName = 'RDA_4_MSD_multi'
    DivisionName = 'XPXHEQABVZ'
    InterfaceOrder = 15.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'DDR', 'Axis', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'XPXHEQABVZ'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_RDA_4_MSD_multi_Rule
    CostMatrix = MatrixCostName_RDA_4_MSD_multi
    EmblemTexture     = "Texture_Division_Emblem_4_MSD_DDR"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "DDR"
)
Descriptor_Deck_Division_RDA_7_Panzer_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{81abdbb9-422f-47b0-a073-013c920c04fb}
    CfgName = 'RDA_7_Panzer_multi'
    DivisionName = 'NIZAYXAMPN'
    InterfaceOrder = 16.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'DDR', 'Axis', 'armored', 'DC_PWR1']
    DescriptionHintTitleToken = 'NIZAYXAMPN'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_RDA_7_Panzer_multi_Rule
    CostMatrix = MatrixCostName_RDA_7_Panzer_multi
    EmblemTexture     = "Texture_Division_Emblem_7_Panzerdivision_DDR"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "DDR"
)
Descriptor_Deck_Division_RDA_9_Panzer_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{3ea1f70e-119a-4dcc-b533-f462a9980686}
    CfgName = 'RDA_9_Panzer_multi'
    DivisionName = 'SXATWWYSVI'
    InterfaceOrder = 17.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'DDR', 'Axis', 'armored', 'DC_PWR1']
    DescriptionHintTitleToken = 'SXATWWYSVI'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_RDA_9_Panzer_multi_Rule
    CostMatrix = MatrixCostName_RDA_9_Panzer_multi
    EmblemTexture     = "Texture_Division_Emblem_RDA__9_Panzer"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "DDR"
)
Descriptor_Deck_Division_RDA_KdA_Bezirk_Erfurt_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{bd44546a-1cab-43a2-b38e-a44595ac1968}
    CfgName = 'RDA_KdA_Bezirk_Erfurt_multi'
    DivisionName = 'OVYVCMLGYY'
    InterfaceOrder = 18.0
    DivisionPowerClassification = 'DC_PWR3'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'DDR', 'Axis', 'infantryReg', 'DC_PWR3']
    DescriptionHintTitleToken = 'OVYVCMLGYY'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_RDA_KdA_Bezirk_Erfurt_multi_Rule
    CostMatrix = MatrixCostName_RDA_KdA_Bezirk_Erfurt_multi
    EmblemTexture     = "Texture_Division_Emblem_RDA_KDA_Erfurt"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "DDR"
)
Descriptor_Deck_Division_RDA_Rugen_Gruppierung is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{8fb7a4b4-1fab-461d-bb09-a94320ba22f7}
    CfgName = 'RDA_Rugen_Gruppierung'
    DivisionName = 'PKOEWMXLJF'
    InterfaceOrder = 20.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'DDR', 'Axis', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'MTGLPEEASU'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_RDA_Rugen_Gruppierung_Rule
    CostMatrix = MatrixCostName_RDA_Rugen_Gruppierung
    EmblemTexture     = "Texture_Division_Emblem_RDA_Rugen_Gruppierung"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "DDR"
)
Descriptor_Deck_Division_RFA_2_PzGrenadier_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{15fa50e1-7534-4f5c-ae1c-afd4098c6ce2}
    CfgName = 'RFA_2_PzGrenadier_multi'
    DivisionName = 'XKYVHEFNED'
    InterfaceOrder = 11.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'RFA', 'Allied', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'XKYVHEFNED'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_RFA_2_PzGrenadier_multi_Rule
    CostMatrix = MatrixCostName_RFA_2_PzGrenadier_multi
    EmblemTexture     = "Texture_Division_Emblem_2_PzGrenDivision_RFA"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "RFA"
)
Descriptor_Deck_Division_RFA_5_Panzer_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{ec8b43f9-6e9b-40de-8167-304ef4370747}
    CfgName = 'RFA_5_Panzer_multi'
    DivisionName = 'WOEQADLOKM'
    InterfaceOrder = 12.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'RFA', 'Allied', 'armored', 'DC_PWR1']
    DescriptionHintTitleToken = 'WOEQADLOKM'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_RFA_5_Panzer_multi_Rule
    CostMatrix = MatrixCostName_RFA_5_Panzer_multi
    EmblemTexture     = "Texture_Division_Emblem_5_Panzerdivision_RFA"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "RFA"
)
Descriptor_Deck_Division_RFA_TerrKdo_Sud_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{a461f564-2c09-471b-9a6b-2c134dd87802}
    CfgName = 'RFA_TerrKdo_Sud_multi'
    DivisionName = 'BIHXAJRCJA'
    InterfaceOrder = 13.0
    DivisionPowerClassification = 'DC_PWR3'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'RFA', 'Allied', 'infantryReg', 'DC_PWR3']
    DescriptionHintTitleToken = 'BIHXAJRCJA'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_RFA_TerrKdo_Sud_multi_Rule
    CostMatrix = MatrixCostName_RFA_TerrKo_Sud_multi
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "RFA"
)
Descriptor_Deck_Division_SOV_119IndTkBrig_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{b611cb29-bf44-4d02-a529-36312df5ec2c}
    CfgName = 'SOV_119IndTkBrig_multi'
    DivisionName = 'QGUHEAAYWW'
    InterfaceOrder = 13.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'SOV', 'Axis', 'armored', 'DC_PWR2']
    DescriptionHintTitleToken = 'QGUHEAAYWW'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_SOV_119IndTkBrig_multi_Rule
    CostMatrix = MatrixCostName_SOV_119IndTkBrig_multi
    EmblemTexture     = "Texture_Division_Emblem_SOV_119OdtTP"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "SOV"
)
Descriptor_Deck_Division_SOV_157_Rifle_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{56d6f3a5-0cec-4134-bb16-0cd3ace64671}
    CfgName = 'SOV_157_Rifle_multi'
    DivisionName = 'QAULTYGPIU'
    InterfaceOrder = 8.0
    DivisionPowerClassification = 'DC_PWR3'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'SOV', 'Axis', 'infantryReg', 'DC_PWR3']
    DescriptionHintTitleToken = 'ZMRQMSTUED'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_SOV_157_Rifle_multi_Rule
    CostMatrix = MatrixCostName_SOV_157_Rifle_multi
    EmblemTexture     = "Texture_Division_Emblem_SOV_157MSD"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "SOV"
)
Descriptor_Deck_Division_SOV_25_Tank_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{d82bb5a9-6360-4e01-ba86-d1f14a051ca6}
    CfgName = 'SOV_25_Tank_multi'
    DivisionName = 'UGKDOPAYTF'
    InterfaceOrder = 6.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'SOV', 'Axis', 'armored', 'DC_PWR1']
    DescriptionHintTitleToken = 'AFSKFYGENE'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_SOV_25_Tank_multi_Rule
    CostMatrix = MatrixCostName_SOV_25_Tank_multi
    EmblemTexture     = "Texture_Division_Emblem_SOV_25th_TD"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "SOV"
)
Descriptor_Deck_Division_SOV_27_Gds_Rifle_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c2f0b0e1-b1df-4615-ba56-e9810c89bb00}
    CfgName = 'SOV_27_Gds_Rifle_multi'
    DivisionName = 'PFMPJNZAXB'
    InterfaceOrder = 3.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'SOV', 'Axis', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'QTEOMZACJF'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_SOV_27_Gds_Rifle_multi_Rule
    CostMatrix = MatrixCostName_SOV_27_Gds_Rifle_multi
    EmblemTexture     = "Texture_Division_Emblem_SOV_27GvMSD"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "SOV"
)
Descriptor_Deck_Division_SOV_35_AirAslt_Brig_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{7076e442-e50d-4f50-8b1d-7e8c2ad404e3}
    CfgName = 'SOV_35_AirAslt_Brig_multi'
    DivisionName = 'PFEYNGDKVR'
    InterfaceOrder = 9.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'SOV', 'Axis', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'PFEYNGDKVR'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_SOV_35_AirAslt_Brig_multi_Rule
    CostMatrix = MatrixCostName_SOV_35_AirAslt_Brig_multi
    EmblemTexture     = "Texture_Division_Emblem_35th_VDV_Brigade"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "SOV"
)
Descriptor_Deck_Division_SOV_39_Gds_Rifle_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{b8baa312-d7a5-4e10-a32a-ac72a9dd15f2}
    CfgName = 'SOV_39_Gds_Rifle_multi'
    DivisionName = 'QBSDOMYDBX'
    InterfaceOrder = 4.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'SOV', 'Axis', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'QBSDOMYDBX'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_SOV_39_Gds_Rifle_multi_Rule
    CostMatrix = MatrixCostName_SOV_39_Gds_Rifle_multi
    EmblemTexture     = "Texture_Division_Emblem_39th_gmsd"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "SOV"
)
Descriptor_Deck_Division_SOV_56_AirAslt_Brig_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{95704837-2d8d-455b-a29f-db8c81d9ac76}
    CfgName = 'SOV_56_AirAslt_Brig_multi'
    DivisionName = 'OJZIMECUCU'
    InterfaceOrder = 10.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'SOV', 'Axis', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'SGEEXVOOPI'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_SOV_56_AirAslt_Brig_multi_Rule
    CostMatrix = MatrixCostName_SOV_56_AirAslt_Brig_multi
    EmblemTexture     = "Texture_Division_Emblem_SOV_56th_DSh_Brigade"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "SOV"
)
Descriptor_Deck_Division_SOV_6IndMSBrig_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{39320a62-4064-4feb-a84f-cecbe7fe8285}
    CfgName = 'SOV_6IndMSBrig_multi'
    DivisionName = 'VKPNQYHZZN'
    InterfaceOrder = 12.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'SOV', 'Axis', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'DVUAMGKBXK'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_SOV_6IndMSBrig_multi_Rule
    CostMatrix = MatrixCostName_SOV_6IndMSBrig_multi
    EmblemTexture     = "Texture_Division_Emblem_SOV_6OdtMSB"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "SOV"
)
Descriptor_Deck_Division_SOV_76_VDV_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{e9691c93-e51b-4587-9529-159ab6e225f0}
    CfgName = 'SOV_76_VDV_multi'
    DivisionName = 'HOVSLMGDMN'
    InterfaceOrder = 11.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'SOV', 'Axis', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'CRUYREPWFR'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_SOV_76_VDV_multi_Rule
    CostMatrix = MatrixCostName_SOV_76_VDV_multi
    EmblemTexture     = "Texture_Division_Emblem_SOV_76th_VDV"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "SOV"
)
Descriptor_Deck_Division_SOV_79_Gds_Tank_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{52703924-4dca-4647-b0d5-376d8410b4a8}
    CfgName = 'SOV_79_Gds_Tank_multi'
    DivisionName = 'ABQKCQLERY'
    InterfaceOrder = 5.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'SOV', 'Axis', 'armored', 'DC_PWR1']
    DescriptionHintTitleToken = 'ABQKCQLERY'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_SOV_79_Gds_Tank_multi_Rule
    CostMatrix = MatrixCostName_SOV_79_Gds_Tank_multi
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "SOV"
)
Descriptor_Deck_Division_UK_1st_Armoured_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{5dff6faa-211f-4743-b3ef-9737d350978f}
    CfgName = 'UK_1st_Armoured_multi'
    DivisionName = 'YPUIEGDPJD'
    InterfaceOrder = 14.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'UK', 'Allied', 'armored', 'DC_PWR1']
    DescriptionHintTitleToken = 'YPUIEGDPJD'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_UK_1st_Armoured_multi_Rule
    CostMatrix = MatrixCostName_UK_1st_Armoured_multi
    EmblemTexture     = "Texture_Division_Emblem_UK_1st_Armoured"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "UK"
)
Descriptor_Deck_Division_UK_2nd_Infantry_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{e27fa0f1-53f1-472c-8380-cad2de5b77fb}
    CfgName = 'UK_2nd_Infantry_multi'
    DivisionName = 'RSIUYDSXND'
    InterfaceOrder = 15.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'UK', 'Allied', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'RSIUYDSXND'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_UK_2nd_Infantry_multi_Rule
    CostMatrix = MatrixCostName_UK_2nd_Infantry_multi
    EmblemTexture     = "Texture_Division_Emblem_UK_2nd_Infantry"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "UK"
)
Descriptor_Deck_Division_UK_4th_Armoured_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{823c2671-564c-4b82-af1c-26de7afcbe0e}
    CfgName = 'UK_4th_Armoured_multi'
    DivisionName = 'PFQAMEYXCU'
    InterfaceOrder = 16.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'UK', 'Allied', 'armored', 'DC_PWR1']
    DescriptionHintTitleToken = 'PFQAMEYXCU'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_UK_4th_Armoured_multi_Rule
    CostMatrix = MatrixCostName_UK_4th_Armoured_multi
    EmblemTexture     = "Texture_Division_Emblem_UK_4th_Armoured"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "UK"
)
Descriptor_Deck_Division_UK_5th_Airborne_Brigade_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{fb6db100-d944-44d9-8c6f-df6b54a05d36}
    CfgName = 'UK_5th_Airborne_Brigade_multi'
    DivisionName = 'PWMGFMXLIK'
    InterfaceOrder = 17.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'UK', 'Allied', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'GOMSACYMBS'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_UK_5th_Airborne_Brigade_multi_Rule
    CostMatrix = MatrixCostName_UK_5th_Airborne_Brigade_multi
    EmblemTexture     = "Texture_Division_Emblem_UK_5th_Airborne"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "UK"
)
Descriptor_Deck_Division_US_101st_Airmobile_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{e2a36cd8-ec00-4d1b-bba4-4a5ce94860bb}
    CfgName = 'US_101st_Airmobile_multi'
    DivisionName = 'VLZCYKIFPB'
    InterfaceOrder = 8.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'US', 'Allied', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'XSZZQFMIYD'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_US_101st_Airmobile_multi_Rule
    CostMatrix = MatrixCostName_US_101st_Airmobile_multi
    EmblemTexture     = "Texture_Division_Emblem_US_101st_Airmobile"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "US"
)
Descriptor_Deck_Division_US_11ACR_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{73e51c86-2d41-4fd7-818a-a8f5b288ab4b}
    CfgName = 'US_11ACR_multi'
    DivisionName = 'AJSWBANTOA'
    InterfaceOrder = 9.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'US', 'Allied', 'armoredRecon', 'DC_PWR2']
    DescriptionHintTitleToken = 'AJSWBANTOA'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_US_11ACR_multi_Rule
    CostMatrix = MatrixCostName_US_11ACR_multi
    EmblemTexture     = "Texture_Division_Emblem_11ACR"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armoredRecon"
    CountryId = "US"
)
Descriptor_Deck_Division_US_24th_Inf_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{d0c2a4d9-6ace-4595-b505-a0998b59b974}
    CfgName = 'US_24th_Inf_multi'
    DivisionName = 'DGUURLVDNS'
    InterfaceOrder = 5.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'US', 'Allied', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'HEFRKMJXZB'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_US_24th_Inf_multi_Rule
    CostMatrix = MatrixCostName_US_24th_Inf_multi
    EmblemTexture     = "Texture_Division_Emblem_24th_infantry_division"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "US"
)
Descriptor_Deck_Division_US_35th_Inf_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{12587ec5-8ee5-4c0d-bb5e-103b741c4f14}
    CfgName = 'US_35th_Inf_multi'
    DivisionName = 'EUQUXKDWZK'
    InterfaceOrder = 6.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'US', 'Allied', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'LNXOPMIBHC'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_US_35th_Inf_multi_Rule
    CostMatrix = MatrixCostName_US_35th_Inf_multi
    EmblemTexture     = "Texture_Division_Emblem_US_35th_infantry_division"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "US"
)
Descriptor_Deck_Division_US_3rd_Arm_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{462b0cc2-0952-47d9-baf9-ad3af39c68a9}
    CfgName = 'US_3rd_Arm_multi'
    DivisionName = 'RSEACNWCQI'
    InterfaceOrder = 1.0
    DivisionPowerClassification = 'DC_PWR1'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'US', 'Allied', 'armored', 'DC_PWR1']
    DescriptionHintTitleToken = 'RSEACNWCQI'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_US_3rd_Arm_multi_Rule
    CostMatrix = MatrixCostName_US_3rd_Arm_multi
    EmblemTexture     = "Texture_Division_Emblem_3rd_Armored_Division"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_armored"
    CountryId = "US"
)
Descriptor_Deck_Division_US_6th_Light_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{276e2b18-1732-4259-8638-eb1d7839acf6}
    CfgName = 'US_6th_Light_multi'
    DivisionName = 'VHQMMDXDKP'
    InterfaceOrder = 2.0
    DivisionPowerClassification = 'DC_PWR3'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'US', 'Allied', 'infantryReg', 'DC_PWR3']
    DescriptionHintTitleToken = 'CLFOCJLIHB'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_US_6th_Light_multi_Rule
    CostMatrix = MatrixCostName_US_6th_Light_multi
    EmblemTexture     = "Texture_Division_Emblem_US_6th_Light"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "US"
)
Descriptor_Deck_Division_US_82nd_Airborne_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{27ac92b1-73ed-4c9c-80d3-9044aa08c38f}
    CfgName = 'US_82nd_Airborne_multi'
    DivisionName = 'TATZFZUVYM'
    InterfaceOrder = 7.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'US', 'Allied', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'TATZFZUVYM'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_US_82nd_Airborne_multi_Rule
    CostMatrix = MatrixCostName_US_82nd_Airborne_multi
    EmblemTexture     = "Texture_Division_Emblem_82nd_airborne"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "US"
)
Descriptor_Deck_Division_US_8th_Inf_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{f483cb5b-aaf9-4e87-9bc1-05644a246a0c}
    CfgName = 'US_8th_Inf_multi'
    DivisionName = 'ECGMWQOEZA'
    InterfaceOrder = 3.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'US', 'Allied', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'ECGMWQOEZA'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_US_8th_Inf_multi_Rule
    CostMatrix = MatrixCostName_US_8th_Inf_multi
    EmblemTexture     = "Texture_Division_Emblem_8th_infantry_division"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "US"
)
Descriptor_Deck_Division_US_9th_Mot_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{6b4ecebb-ed2d-473a-92c0-1acb4b719103}
    CfgName = 'US_9th_Mot_multi'
    DivisionName = 'RYTYXPJEGM'
    InterfaceOrder = 4.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['DEFAULT', 'US', 'Allied', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'RYTYXPJEGM'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_US_9th_Mot_multi_Rule
    CostMatrix = MatrixCostName_US_9th_Mot_multi
    EmblemTexture     = "Texture_Division_Emblem_US_9th_Motorized"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "US"
)
Descriptor_Deck_Division_WP_Unternehmen_Zentrum_multi is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{4d41c309-2758-4f06-8995-2a3d07e56474}
    CfgName = 'WP_Unternehmen_Zentrum_multi'
    DivisionName = 'CHKSNFWOEZ'
    InterfaceOrder = 19.0
    DivisionPowerClassification = 'DC_PWR2'
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['DEFAULT', 'DDR', 'Axis', 'infantryReg', 'DC_PWR2']
    DescriptionHintTitleToken = 'CHKSNFWOEZ'
    MaxActivationPoints = 50
    DivisionRule = Descriptor_Deck_Division_WP_Unternehmen_Zentrum_multi_Rule
    CostMatrix = MatrixCostName_WP_Unternehmen_Zentrum_multi
    EmblemTexture     = "Texture_Division_Emblem_WP_Unternehmen_Stoss"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = "Texture_Division_Type_infantryReg"
    CountryId = "DDR"
)

Descriptor_Deck_Division_BEL_10PtserBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{72f129df-d345-4f5c-b487-2dd212186f94}
    CfgName = 'BEL_10PtserBrig_solo'
    DivisionName = 'DTNMQBUQTC'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_16e_Meca"
    StrategicLabelColor     = "BlancEquipe"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_BEL_10Wing_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{83245b8b-859b-4cf3-83ff-0882975cec68}
    CfgName = 'BEL_10Wing_solo'
    DivisionName = 'NQFNHFVZRL'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_Air_Force"
    StrategicLabelColor     = "division_BE_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_BEL_12BrigInf_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{5e403750-c0c2-47aa-9804-1cdfdab14bef}
    CfgName = 'BEL_12BrigInf_solo'
    DivisionName = 'AQCDISWEEA'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_1e_Infanterie"
    StrategicLabelColor     = "division_BE_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_BEL_16de_PtserDiv_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{ab0b3fe3-2977-4982-9bc8-fd0f00953cf6}
    CfgName = 'BEL_16de_PtserDiv_solo'
    DivisionName = 'HWCKRNBHGQ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_16e_Meca"
    StrategicLabelColor     = "division_BE_Boar_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_BEL_17BrigBlind_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{7afa3f90-2af9-4069-a1c2-6e89a0391085}
    CfgName = 'BEL_17BrigBlind_solo'
    DivisionName = 'BQXKOGZJOB'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_16e_Meca"
    StrategicLabelColor     = "BlancEquipe"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_BEL_1DivInfanterie_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{5c139241-80b6-4fe6-ae66-248830c36b16}
    CfgName = 'BEL_1DivInfanterie_solo'
    DivisionName = 'IJZDSAJYMF'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_1e_Infanterie"
    StrategicLabelColor     = "division_BE_Boar_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_BEL_1PtserInfBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{a9a31e7b-2489-4218-ac63-d6d602801683}
    CfgName = 'BEL_1PtserInfBrig_solo'
    DivisionName = 'HVTIECLPHD'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_1e_Infanterie"
    StrategicLabelColor     = "BlancEquipe"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_BEL_1Wing_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2698f8f1-8207-40f3-8f31-b25307a6314a}
    CfgName = 'BEL_1Wing_solo'
    DivisionName = 'RPEDYNISZM'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_Air_Force"
    StrategicLabelColor     = "division_BE_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_BEL_2Wing_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{037fed5c-**************-6e108d5fe3b1}
    CfgName = 'BEL_2Wing_solo'
    DivisionName = 'ACCLMCNNMU'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_Air_Force"
    StrategicLabelColor     = "division_BE_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_BEL_3Wing_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{4364acdb-7fa2-4dac-9a93-a3f9f7739a72}
    CfgName = 'BEL_3Wing_solo'
    DivisionName = 'GSJNHUIYCT'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_Air_Force"
    StrategicLabelColor     = "division_BE_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_BEL_4PtserBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{825945d8-7c2d-4c2c-84dc-69346b8de961}
    CfgName = 'BEL_4PtserBrig_solo'
    DivisionName = 'LGVADKMAFW'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_16e_Meca"
    StrategicLabelColor     = "division_BE_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_BEL_7BrigInfBlind_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{a56e3348-421e-4bfd-b917-f8807ae70c8a}
    CfgName = 'BEL_7BrigInfBlind_solo'
    DivisionName = 'DKCHAOPGKW'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_1e_Infanterie"
    StrategicLabelColor     = "BlancEquipe"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_BEL_9Wing_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{f420d626-d0f9-4196-8101-1cf79a7f5b32}
    CfgName = 'BEL_9Wing_solo'
    DivisionName = 'OYSSDXPNZZ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_Air_Force"
    StrategicLabelColor     = "division_BE_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_BEL_BAF_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{00752745-6c24-458b-ba47-466a046bf25b}
    CfgName = 'BEL_BAF_solo'
    DivisionName = 'UCVXXLOUBF'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_Air_Force"
    StrategicLabelColor     = "division_BE_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_NATO_32ADG_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{8ba0faee-fc50-49ef-97f3-ac79a1652a7a}
    CfgName = 'NATO_32ADG_solo'
    DivisionName = 'TJRVFXGQDQ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_3rd_Armored_Division"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_NATO_CENTAG_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{9094d5a8-80f4-4baa-85c1-cfeb44e1f612}
    CfgName = 'NATO_CENTAG_solo'
    DivisionName = 'SSUSFSRKEO'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_flag_us"
    StrategicLabelColor     = "corps_otan"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_NL_1Div_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{7366cbe4-2007-4daf-b1b6-f9811fda84e6}
    CfgName = 'NL_1Div_solo'
    DivisionName = 'DBYNGRSHOC'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_NL_4e_Divisie"
    StrategicLabelColor     = "division_NL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_NL_1LK_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{f02413e8-8616-4ae8-96cc-1cddf01e42cc}
    CfgName = 'NL_1LK_solo'
    DivisionName = 'TKPXWLNVCG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_NL_4e_Divisie"
    StrategicLabelColor     = "division_NL_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_NL_4Div_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{d538774c-20d1-41ce-ada9-3936dc6afb67}
    CfgName = 'NL_4Div_solo'
    DivisionName = 'MRBOKCBPXP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_NL_4e_Divisie"
    StrategicLabelColor     = "division_NL_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_NL_5Div_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{0bae6ef1-bdf1-4503-83cd-6e8251a2bd2c}
    CfgName = 'NL_5Div_solo'
    DivisionName = 'NYBZOPZDST'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_NL_4e_Divisie"
    StrategicLabelColor     = "division_NL_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_NL_CLKA_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{b8695b79-6000-408e-b1c0-b185ffc71f0c}
    CfgName = 'NL_CLKA_solo'
    DivisionName = 'INCNCOSXQE'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_NL_4e_Divisie"
    StrategicLabelColor     = "division_NL_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_10MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{65bc3a4e-80e7-4995-8a76-161a9a5df7ef}
    CfgName = 'POL_10MSD_solo'
    DivisionName = 'RIQLNYTBHG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_11TkD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{853e4a10-05b9-407c-bfb5-0f4466b5bca6}
    CfgName = 'POL_11TkD_solo'
    DivisionName = 'NOVGSEAZZR'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_12MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{096301d8-55f6-4ed2-a38f-715180a3de89}
    CfgName = 'POL_12MSD_solo'
    DivisionName = 'SPPVUGHCVH'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_15MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{66a3ce51-f948-43e6-b2e1-092eb75baf78}
    CfgName = 'POL_15MSD_solo'
    DivisionName = 'TQFYOEIPLP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_16MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{7cc0a6d4-01ba-4f36-8eb6-093feceadf3e}
    CfgName = 'POL_16MSD_solo'
    DivisionName = 'WXWLGURWUX'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_1Armia_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{7bd51261-d25b-4767-83a1-e8acc8a645fc}
    CfgName = 'POL_1Armia_solo'
    DivisionName = 'OTXYLCTVRH'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_20TkD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{cca5f33a-4f46-4ced-aaaa-247bd013d43f}
    CfgName = 'POL_20TkD_solo'
    DivisionName = 'EGPXSAYCXO'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_23ArtGr_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{4d696c99-2eba-432c-a6aa-a53f14a5dbe0}
    CfgName = 'POL_23ArtGr_solo'
    DivisionName = 'RQFANWIXRN'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_24ArtGr_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{697598dd-f40c-4bea-9cbf-6a2901d0aed3}
    CfgName = 'POL_24ArtGr_solo'
    DivisionName = 'WKOKVVSDOZ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_2Armia_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{67f9585f-c807-4f98-b674-7c8f7daf0cf5}
    CfgName = 'POL_2Armia_solo'
    DivisionName = 'OKHYSASGMM'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_2MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{bb6df8ca-0d18-439b-84c4-fb2b3f6e67b6}
    CfgName = 'POL_2MSD_solo'
    DivisionName = 'LMXEXFRVYJ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_30RMD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{d88f69fd-1847-4032-95f5-01960e7a5990}
    CfgName = 'POL_30RMD_solo'
    DivisionName = 'MTEHADENDP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_3_DLMB_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{09aa50b0-638c-4750-8c7a-598486ca7884}
    CfgName = 'POL_3_DLMB_solo'
    DivisionName = 'QAFHCYROEI'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_4MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2c70acb3-5cd7-467c-8bec-c713fd0af0ea}
    CfgName = 'POL_4MSD_solo'
    DivisionName = 'JPGSSGIHHN'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_5ArtGr_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{edd42b53-f806-4eee-8d13-028692183db8}
    CfgName = 'POL_5ArtGr_solo'
    DivisionName = 'DBLFBJNJMP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_5TkD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2d1a0cde-f8d6-4f9e-bef2-597c39518108}
    CfgName = 'POL_5TkD_solo'
    DivisionName = 'EXEFNUFAYO'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_61SAMBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{b78669c6-27c0-495d-ab3a-62799d7d9fe4}
    CfgName = 'POL_61SAMBrig_solo'
    DivisionName = 'VFDWDVGFLR'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_6AirBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{fab6f5fa-4ff0-4549-a0cd-cfc4cd43a973}
    CfgName = 'POL_6AirBrig_solo'
    DivisionName = 'XKNLFKIZGQ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_6ArtGr_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{cfa3a46a-868f-4e3b-9989-e142e2008586}
    CfgName = 'POL_6ArtGr_solo'
    DivisionName = 'QUANLMLBFX'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_7ArtGr_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{d04bc57f-317b-40f7-bf8a-24f7093e25ae}
    CfgName = 'POL_7ArtGr_solo'
    DivisionName = 'AIITRYYOBF'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_7NavalBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{ab5f1d03-7a15-42b0-a94a-4ca28aea4612}
    CfgName = 'POL_7NavalBrig_solo'
    DivisionName = 'DIYVSUJTZC'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_8ArtGr_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{4920c600-c734-4d7d-ba6f-c025282f16e6}
    CfgName = 'POL_8ArtGr_solo'
    DivisionName = 'MFTHYAFPKR'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_8MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{84548d26-1dd8-4e45-888b-42898a27d548}
    CfgName = 'POL_8MSD_solo'
    DivisionName = 'KEAOQOBUTH'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_DesantK_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{1a193e58-d15c-4042-8acd-9d905c8b0e31}
    CfgName = 'POL_DesantK_solo'
    DivisionName = 'MUYAMIWFVR'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_LWP_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2c9636b0-2ede-49e9-9fc7-d94f03628f00}
    CfgName = 'POL_LWP_solo'
    DivisionName = 'UJABTWUSMV'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    StrategicLabelColor     = "division_POL_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_10MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{1fe621f0-d1b4-4655-b3c1-abf8294b6a34}
    CfgName = 'RDA_10MSD_solo'
    DivisionName = 'CUEWTCIFNK'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_DDR_GroundForces_SansFond"
    StrategicLabelColor     = "division_DDR_11"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_11MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{b09825ee-0d71-415a-aeac-f28b83072739}
    CfgName = 'RDA_11MSD_solo'
    DivisionName = 'VIHBYIAHTG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_DDR_GroundForces_SansFond"
    StrategicLabelColor     = "division_DDR_11"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_15ResPzR_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{3709e8a9-9552-47ce-bdcb-80cb827f371c}
    CfgName = 'RDA_15ResPzR_solo'
    DivisionName = 'TIDKVZHKFD'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_4_MSD_DDR"
    StrategicLabelColor     = "division_DDR_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_17MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2a167fc2-23ed-47f7-9b17-22ffcac608a2}
    CfgName = 'RDA_17MSD_solo'
    DivisionName = 'HFMCQDUSQA'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_DDR_GroundForces_SansFond"
    StrategicLabelColor     = "division_DDR_11"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_19MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2f9269e2-b11a-462b-a940-2d278aa1f639}
    CfgName = 'RDA_19MSD_solo'
    DivisionName = 'SOZSQRHTUP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_DDR_19_MSD"
    StrategicLabelColor     = "division_DDR_hq"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_1MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{703d339b-c754-4549-890b-fa11bb16a630}
    CfgName = 'RDA_1MSD_solo'
    DivisionName = 'DZTTOSCXCV'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_4_MSD_DDR"
    StrategicLabelColor     = "division_DDR_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_20MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{32699ea0-be0b-4359-815e-67e97c576cc9}
    CfgName = 'RDA_20MSD_solo'
    DivisionName = 'AZZCODQNFN'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_20_MSD_DDR"
    StrategicLabelColor     = "division_DDR_20"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_25MSR_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{eee52363-3e75-4121-a0e9-e6cebff3e753}
    CfgName = 'RDA_25MSR_solo'
    DivisionName = 'AMASYORJLT'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_4_MSD_DDR"
    StrategicLabelColor     = "division_DDR_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_4MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{745beef0-62d6-46d3-8d78-383e26c97194}
    CfgName = 'RDA_4MSD_solo'
    DivisionName = 'WKYGGWDXIQ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_4_MSD_DDR"
    StrategicLabelColor     = "division_DDR_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_5ArtBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{e39dfd84-f68b-4535-9e51-04f919607a6c}
    CfgName = 'RDA_5ArtBrig_solo'
    DivisionName = 'NVGFNTOKVC'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_4_MSD_DDR"
    StrategicLabelColor     = "division_DDR_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_5GEWR_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c6aab06a-303c-4b19-978a-8639bf4ddb0b}
    CfgName = 'RDA_5GEWR_solo'
    DivisionName = 'WKHJDSOXDM'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_4_MSD_DDR"
    StrategicLabelColor     = "division_DDR_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_7PzD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{bff44c3a-b9c5-4a9a-b722-9a6c98942f2c}
    CfgName = 'RDA_7PzD_solo'
    DivisionName = 'UJMWGQIZDB'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_7_Panzerdivision_DDR"
    StrategicLabelColor     = "division_DDR_7"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_8MSD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{653f2387-50d6-4de6-b06b-83714a7d1527}
    CfgName = 'RDA_8MSD_solo'
    DivisionName = 'ATNHPRZBUA'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_DDR_8_MSD"
    StrategicLabelColor     = "division_DDR_8"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_9PzD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{3a7fb8a1-51be-491b-96de-0bae8577bff0}
    CfgName = 'RDA_9PzD_solo'
    DivisionName = 'BTMJHETUOU'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_DDR_9_PzD"
    StrategicLabelColor     = "division_DDR_9"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_ArtRgt3_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{199e80db-81f3-428f-a614-9a6e44ca75d8}
    CfgName = 'RDA_ArtRgt3_solo'
    DivisionName = 'VWRVQKNFEL'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RDA_MB_III"
    StrategicLabelColor     = "division_DDR_hq"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_ArtRgt40_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{4a41ed05-f2f2-4df9-8aae-19b1d02072d3}
    CfgName = 'RDA_ArtRgt40_solo'
    DivisionName = 'ZVJPHODORW'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RDA_MB_III"
    StrategicLabelColor     = "division_DDR_hq"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_LStR40_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{a97ab573-cef5-45e3-aadd-795c84b57246}
    CfgName = 'RDA_LStR40_solo'
    DivisionName = 'WURNOJAGAT'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RDA_LStR40"
    StrategicLabelColor     = "division_DDR_kassel_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_MBDIII_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{a1581958-0b79-4b73-9f3c-8903eea5ab68}
    CfgName = 'RDA_MBDIII_solo'
    DivisionName = 'KOGFRBNZHG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RDA_MB_III"
    StrategicLabelColor     = "division_DDR_hq"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_MBDV_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{812dc38e-1dad-4234-a1ee-79ae68276ced}
    CfgName = 'RDA_MBDV_solo'
    DivisionName = 'WKYGGWDXIQ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_4_MSD_DDR"
    StrategicLabelColor     = "division_DDR_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_11PzGrD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{90c90d75-4eca-4176-95ad-7a6150554377}
    CfgName = 'RFA_11PzGrD_solo'
    DivisionName = 'OGRDRQFQOG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_5_Panzerdivision_RFA"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_12PzD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{29d67efc-2772-44f2-a812-1a32e0932a69}
    CfgName = 'RFA_12PzD_solo'
    DivisionName = 'BFHCIUHWQJ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_12_Panzer"
    StrategicLabelColor     = "division_RFA_5"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_1Korps_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{18562b6a-4d2f-4d5b-a9fb-fa5517f7881f}
    CfgName = 'RFA_1Korps_solo'
    DivisionName = 'ZUFYSTIZQW'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_5_Panzerdivision_RFA"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_1PioKommando_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{575eebca-47ae-4f8c-b9e2-4e1efe76d3f2}
    CfgName = 'RFA_1PioKommando_solo'
    DivisionName = 'UMPVBDYXCY'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_5_Panzerdivision_RFA"
    StrategicLabelColor     = "division_RFA_kassel_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_1PzD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{760665d7-35ec-4c9c-ad6f-68a4efce3ff4}
    CfgName = 'RFA_1PzD_solo'
    DivisionName = 'ZUIJYUBKPY'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem__vide"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_26AB_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c1293e88-ff58-4cc1-a1bf-0ca1a06e44db}
    CfgName = 'RFA_26AB_solo'
    DivisionName = 'VZRWVXGGRI'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_26LLB"
    StrategicLabelColor     = "division_RFA_4"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_2LwDiv_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{e765a513-f63f-4b58-b5e2-23280b5060a8}
    CfgName = 'RFA_2LwDiv_solo'
    DivisionName = 'SHMXFYGRWE'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_Luftwaffe"
    StrategicLabelColor     = "division_RFA_air_kassel_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_2PzGrD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{6efc1536-e20e-434c-81cf-26cdab0bade4}
    CfgName = 'RFA_2PzGrD_solo'
    DivisionName = 'XKYVHEFNED'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_2_PzGrenDivision_RFA"
    StrategicLabelColor     = "division_RFA_kassel_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_3EngBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{159ba176-f696-4bd7-831d-0cfdb544810f}
    CfgName = 'RFA_3EngBrig_solo'
    DivisionName = 'WCMKOZAWFU'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_III_Korps"
    StrategicLabelColor     = "division_RFA_kdo"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_3FlakBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{a7da83be-d8b5-4d37-8230-ecb53a30adcd}
    CfgName = 'RFA_3FlakBrig_solo'
    DivisionName = 'POSMNWFHJY'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_III_Korps"
    StrategicLabelColor     = "division_RFA_kdo"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_3HFG_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{99014316-8d98-46fa-85b4-2b3a2ad9c018}
    CfgName = 'RFA_3HFG_solo'
    DivisionName = 'HTXKTCYTUO'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_III_Korps"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_3Korps_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2a159301-60c9-4ec6-a2da-d2cb903c5e56}
    CfgName = 'RFA_3Korps_solo'
    DivisionName = 'NXNQFFTQWE'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_III_Korps"
    StrategicLabelColor     = "division_RFA_kdo"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_3PzD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{3c0f629c-c34d-4666-b42d-1d4ce6a03a1a}
    CfgName = 'RFA_3PzD_solo'
    DivisionName = 'MINOBGNNQN'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem__vide"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_54Brig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{4e7b2dc7-4969-4e19-b073-b16e763ebd27}
    CfgName = 'RFA_54Brig_solo'
    DivisionName = 'XFNPQTAXXH'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_kdo"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_5PzD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{9b7c3ae2-4fca-4eda-97b3-b69896ab6ffe}
    CfgName = 'RFA_5PzD_solo'
    DivisionName = 'LKSVNPCUYG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_5_Panzerdivision_RFA"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_64Brig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{e8b74c1b-83e2-46af-8562-7e9e8eb47c44}
    CfgName = 'RFA_64Brig_solo'
    DivisionName = 'QAYHWXLBRP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_kdo"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_74PioRgt_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{9b1778d5-dea5-4e9c-b0c6-f0e511f80af6}
    CfgName = 'RFA_74PioRgt_solo'
    DivisionName = 'PPHRFSJBZP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_7PzD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{6325c679-6582-49e6-bc8f-c704111a382c}
    CfgName = 'RFA_7PzD_solo'
    DivisionName = 'WUDSACQWCO'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem__vide"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_FlaKdo1_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{95f6d65b-47ae-42fb-888d-dcb249fb0a28}
    CfgName = 'RFA_FlaKdo1_solo'
    DivisionName = 'SHOPGELWAY'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_5_Panzerdivision_RFA"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_FlakRakRgt14_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{6856b3a3-91a6-4283-aba7-8eceb9642c1e}
    CfgName = 'RFA_FlakRakRgt14_solo'
    DivisionName = 'WJIWZEREHI'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_default"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_FlakRakRgt3_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{5f5712b4-54aa-4f95-b424-e0183dfdf831}
    CfgName = 'RFA_FlakRakRgt3_solo'
    DivisionName = 'RYHEICFCRL'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_default"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_FlakRakRgt4_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{7fbf7a15-65cf-4e5c-af92-ba9db3e298f2}
    CfgName = 'RFA_FlakRakRgt4_solo'
    DivisionName = 'AGBQFAGBHU'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_default"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_FlakRakRgt6_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{afd09fdb-a302-42a8-9327-d23a8261ac16}
    CfgName = 'RFA_FlakRakRgt6_solo'
    DivisionName = 'MEITBYLAKO'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_default"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_Heimat71_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{d8188d3f-2c8e-4b7e-a2e8-e3debcdb5362}
    CfgName = 'RFA_Heimat71_solo'
    DivisionName = 'LTMLXRICID'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_Heimat84_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{528ade22-382b-4c6b-82cd-daf2982c0acd}
    CfgName = 'RFA_Heimat84_solo'
    DivisionName = 'NHKOOQHRRZ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_Heimat94_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{94d44a57-14ab-4e22-aaad-fcf3ade840c6}
    CfgName = 'RFA_Heimat94_solo'
    DivisionName = 'YPXMWNQODK'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_Luftwaffe_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{4e4912ad-251e-436c-b481-6e6fc30754fa}
    CfgName = 'RFA_Luftwaffe_solo'
    DivisionName = 'QERCZGUPTN'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_Luftwaffe"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_TK_Nord_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{3d72bbf2-e81c-4bfc-a8fb-e96514a1c18a}
    CfgName = 'RFA_TK_Nord_solo'
    DivisionName = 'SRNIPMIGYA'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_kassel_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_TK_Nord_solo_30 is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c25fda23-c397-4d9f-9bc7-aa3c27f298b1}
    CfgName = 'RFA_TK_Nord_solo_30'
    DivisionName = 'SRNIPMIGYA'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_kassel_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_TK_Sud_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{27b1422e-33bb-4d99-921d-cdce835cbd81}
    CfgName = 'RFA_TK_Sud_solo'
    DivisionName = 'EQJNLVYMVL'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_VBK_41_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{71436814-2a05-40f5-83dd-8b6dcb487b66}
    CfgName = 'RFA_VBK_41_solo'
    DivisionName = 'SDTDGFUTEL'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_VBK_42_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c57fffa9-d518-4d05-a16d-f61e0a583c58}
    CfgName = 'RFA_VBK_42_solo'
    DivisionName = 'QTJLQWWZPJ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_VBK_43_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{85c9fcbe-f479-4a67-8ee6-d643f0eafddf}
    CfgName = 'RFA_VBK_43_solo'
    DivisionName = 'AUZXMJPIPI'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_VBK_44_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{5433d34b-a97d-45a4-a8f3-6dd5217f537e}
    CfgName = 'RFA_VBK_44_solo'
    DivisionName = 'WRYDHVWUFR'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_violet_sombre"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_VBK_45_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{bf98bf02-a37b-4cf1-903a-da1774fbfc41}
    CfgName = 'RFA_VBK_45_solo'
    DivisionName = 'TXJSUWVGLD'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_VBK_46_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{7c1f8493-14f5-4e7b-b94a-887e6b02587d}
    CfgName = 'RFA_VBK_46_solo'
    DivisionName = 'KUAJNYEVQX'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_VBK_47_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{bf6c1448-20ad-4580-80d1-295bca688298}
    CfgName = 'RFA_VBK_47_solo'
    DivisionName = 'PLDWPZVFDV'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    StrategicLabelColor     = "division_RFA_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_105ADIB_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{24ee19a9-23a2-4c95-9ac9-50baa12b2607}
    CfgName = 'SOV_105ADIB_solo'
    DivisionName = 'YZQRQUWKHE'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_AirForce"
    StrategicLabelColor     = "SovDivision_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_10GTkD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{e8780c96-4924-4e04-b2b3-7b23d526001d}
    CfgName = 'SOV_10GTkD_solo'
    DivisionName = 'SXJRJLZBUU'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision_kassel_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_115TkInde_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{6d38a45b-f3f1-4eb5-8199-af50141c7439}
    CfgName = 'SOV_115TkInde_solo'
    DivisionName = 'PKHOCERPIW'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_119IndTkBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{5aed016f-d0ba-4bb2-9c94-d8d84f944fad}
    CfgName = 'SOV_119IndTkBrig_solo'
    DivisionName = 'QGUHEAAYWW'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_119OdtTP"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_11GTkD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{fd40abbe-26c5-49e4-9bc6-fb75c88a82ac}
    CfgName = 'SOV_11GTkD_solo'
    DivisionName = 'XMQYPCOXKV'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_defaut"
    StrategicLabelColor     = "SovDivision_boar_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_11ORAP_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{dab3cd2c-b1c8-4d1f-aa83-173118f0960d}
    CfgName = 'SOV_11ORAP_solo'
    DivisionName = 'GAFYKHWDVX'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_AirForce"
    StrategicLabelColor     = "SovDivision_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_125ADIB_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{91ff1793-59f0-4bd7-97a6-7b2763643173}
    CfgName = 'SOV_125ADIB_solo'
    DivisionName = 'LBFRPMYQQL'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_AirForce"
    StrategicLabelColor     = "SovDivision_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_126IAD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{cf58a287-3b47-4c4b-a27c-69b97dd7d635}
    CfgName = 'SOV_126IAD_solo'
    DivisionName = 'GCCUEIHHKG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_AirForce"
    StrategicLabelColor     = "SovDivision_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_12GTkD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{478d55b6-2a7d-40e1-84a3-c15091a2fc6c}
    CfgName = 'SOV_12GTkD_solo'
    DivisionName = 'DFHBGEUXHW'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_138TkInde_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{9d4abf0a-749a-4fca-824a-8234adad1429}
    CfgName = 'SOV_138TkInde_solo'
    DivisionName = 'UQMSVYKMIO'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_145TkInde_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{be65eabf-0dfb-4f92-b15a-9ea0a6196ac4}
    CfgName = 'SOV_145TkInde_solo'
    DivisionName = 'COBHYHAZNB'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_16GTkD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{1b8a95f3-f37f-4f5d-a062-942986cb970e}
    CfgName = 'SOV_16GTkD_solo'
    DivisionName = 'NWNEUMQHLF'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_16GvIAD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{0147a489-6838-4839-9db3-207948b35132}
    CfgName = 'SOV_16GvIAD_solo'
    DivisionName = 'AFJWXFWHID'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_AirForce"
    StrategicLabelColor     = "SovDivision_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_1GTA_SAM_Brig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{d1a3a5a9-5146-423d-be9b-0ff07db5fc2a}
    CfgName = 'SOV_1GTA_SAM_Brig_solo'
    DivisionName = 'NFWANBEEGH'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_defaut"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_1GTA_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{0cf10d82-fee7-4dd7-bb4a-4240b374f279}
    CfgName = 'SOV_1GTA_solo'
    DivisionName = 'JGEKAKPDYF'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_defaut"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_1LVD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{9e370ec5-cbfa-4fd7-9081-fff8dd8c83ca}
    CfgName = 'SOV_1LVD_solo'
    DivisionName = 'EBECBWLJER'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_AirForce"
    StrategicLabelColor     = "SovDivision_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_207MRD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{447e100d-4745-4d5c-971d-d0c64c0b8084}
    CfgName = 'SOV_207MRD_solo'
    DivisionName = 'RCUXZYXOTD'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_20GMRD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{474ea3f0-2388-4ae8-8145-f98bc38a4b31}
    CfgName = 'SOV_20GMRD_solo'
    DivisionName = 'HKQRLEJVQX'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_defaut"
    StrategicLabelColor     = "SovDivision_kassel_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_21MRD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c4af1c75-588f-4a44-94f0-a22f07aa8029}
    CfgName = 'SOV_21MRD_solo'
    DivisionName = 'UEGUTODADY'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_221TkInde_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{6d7aa39d-f457-440f-bf1a-76530f84d1ca}
    CfgName = 'SOV_221TkInde_solo'
    DivisionName = 'EVJFJYDIXI'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_27GMRD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c4e983d5-b2e3-4df0-9b77-23813fcc94ee}
    CfgName = 'SOV_27GMRD_solo'
    DivisionName = 'DIDKXPCIFL'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_27GvMSD"
    StrategicLabelColor     = "SovDivision_boar_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_290ArtBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{f94ae8bd-1c70-4963-8bb5-68537e5c16fb}
    CfgName = 'SOV_290ArtBrig_solo'
    DivisionName = 'IDADXAUEHZ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_294ORAP_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{27bdccc7-58ad-44e7-8008-4e7db88f5167}
    CfgName = 'SOV_294ORAP_solo'
    DivisionName = 'ZADWXNWCXK'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_AirForce"
    StrategicLabelColor     = "SovDivision_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_2GTA_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{160e977c-9bcc-45ab-8c66-0cc7e7550200}
    CfgName = 'SOV_2GTA_solo'
    DivisionName = 'KLGKHIPTLF'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_308ArtBr_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{81bac9d6-7bdd-4869-9cbd-e9449c7baf20}
    CfgName = 'SOV_308ArtBr_solo'
    DivisionName = 'BEGLGQZKJN'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_defaut"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_34ArtDiv_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{1d4fd8e8-dd6f-4261-84da-c6e74051488f}
    CfgName = 'SOV_34ArtDiv_solo'
    DivisionName = 'IAQCIDWWAN'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_defaut"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_357OCHAP_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{d1a3c219-f38e-423a-8a8f-241cb685e13a}
    CfgName = 'SOV_357OCHAP_solo'
    DivisionName = 'CLBSQAABHG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_AirForce"
    StrategicLabelColor     = "SovDivision_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_35GvDSHB_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2f12deda-2290-4c93-95f4-a572dff66f3a}
    CfgName = 'SOV_35GvDSHB_solo'
    DivisionName = 'YMFLAGHXMM'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_35th_VDV_Brigade"
    StrategicLabelColor     = "SovDivision_kassel_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_368OCHAP_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2aecd89e-b84d-40ae-ba64-09bcabf14865}
    CfgName = 'SOV_368OCHAP_solo'
    DivisionName = 'BABJKBKYAG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_AirForce"
    StrategicLabelColor     = "SovDivision_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_385ArtBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{3fecd0d9-f73b-4f7d-99e4-9c57199fc9a9}
    CfgName = 'SOV_385ArtBrig_solo'
    DivisionName = 'JCKEGESFSQ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_390ArtBr_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{a73457cc-518d-4bbb-a84b-e65e1e0f8881}
    CfgName = 'SOV_390ArtBr_solo'
    DivisionName = 'JCKEGESFSQ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_defaut"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_39GMRD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{bcd773f9-ac51-4ee0-b1ac-a0c5917383d7}
    CfgName = 'SOV_39GMRD_solo'
    DivisionName = 'YVLMVYSOCN'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_39th_gmsd"
    StrategicLabelColor     = "SovDivision1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_3A_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c67a956f-f757-4913-9d24-8f35ad331398}
    CfgName = 'SOV_3A_solo'
    DivisionName = 'JQEMAWSOOB'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_3FrontUkr_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{fa605841-872d-460e-a486-b173733bd6c3}
    CfgName = 'SOV_3FrontUkr_solo'
    DivisionName = 'DPZFNAINUO'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_defaut"
    StrategicLabelColor     = "Vert"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_3LVD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{9cc1212e-e774-4736-badf-2483ebbff55c}
    CfgName = 'SOV_3LVD_solo'
    DivisionName = 'UQCTRBWSWG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_AirForce"
    StrategicLabelColor     = "SovDivision_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_46SAMBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{a75fbc82-e9e9-4c0e-87b2-8bab13c744d4}
    CfgName = 'SOV_46SAMBrig_solo'
    DivisionName = 'VPQKVRDDWT'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_47GTkD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{27711816-8d8e-4e58-997a-46e7dc4e89e1}
    CfgName = 'SOV_47GTkD_solo'
    DivisionName = 'UZSEOTPZYB'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_57GMRD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{16cd5ce0-8e54-43e6-b42f-f7f428b8ada8}
    CfgName = 'SOV_57GMRD_solo'
    DivisionName = 'CXOLPZKDDB'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_Blazon57th_gtd"
    StrategicLabelColor     = "SovDivision3"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_61SAMBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{cf36f5ff-cbfb-458e-a3a0-d0e43e7a69d4}
    CfgName = 'SOV_61SAMBrig_solo'
    DivisionName = 'JJXSZSZODB'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_6GvIAD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{7c4fe8e9-d029-4d06-b3b1-3d453cadc870}
    CfgName = 'SOV_6GvIAD_solo'
    DivisionName = 'PCERLWWWMI'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_AirForce"
    StrategicLabelColor     = "SovDivision_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_79GTkD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{3870239d-265d-4d9c-8db1-27f355ba378a}
    CfgName = 'SOV_79GTkD_solo'
    DivisionName = 'TANGJJSVLC'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_7GTkD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{46a9679d-49d9-401b-812a-f47ffad5d03a}
    CfgName = 'SOV_7GTkD_solo'
    DivisionName = 'HGPGDUUVOQ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision_boar_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_8GA_SAM_Brig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{11ce534b-a7d9-46ec-9087-c65ec1cead8a}
    CfgName = 'SOV_8GA_SAM_Brig_solo'
    DivisionName = 'XJTAWJBJGT'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_defaut"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_8GA_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{86ce956f-7014-4add-a288-f5cee367a392}
    CfgName = 'SOV_8GA_solo'
    DivisionName = 'JVORBKCZCQ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_defaut"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_931ORAP_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2fcca32f-75af-46b6-bc3b-a8fdb32ba386}
    CfgName = 'SOV_931ORAP_solo'
    DivisionName = 'OJCVUVYNHK'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_AirForce"
    StrategicLabelColor     = "SovDivision_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_94GMRD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{209c1128-e8fa-474d-bdc1-b82292b7a071}
    CfgName = 'SOV_94GMRD_solo'
    DivisionName = 'EMWRLJXZXG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_9TkD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{03580d44-74b2-41dd-ad79-b741eed3c8c1}
    CfgName = 'SOV_9TkD_solo'
    DivisionName = 'GNIFXKTHNO'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_SOV_defaut"
    StrategicLabelColor     = "SovDivision_kassel_3"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_NGF_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{57de2b1c-2a6c-4814-81b3-44662de50322}
    CfgName = 'SOV_NGF_solo'
    DivisionName = 'HSQNSLSYKC'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_WGF_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{255cac74-83bf-41ba-bd06-f44cccf9ad0c}
    CfgName = 'SOV_WGF_solo'
    DivisionName = 'HVWPWZYUMW'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['STRAT', 'Axis']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_flag_soviet"
    StrategicLabelColor     = "SovDivision2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_1ArmDiv_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{1129f48e-9b44-4c8d-8f87-e3161937f5cd}
    CfgName = 'UK_1ArmDiv_solo'
    DivisionName = 'YPUIEGDPJD'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_1st_Armoured"
    StrategicLabelColor     = "division_UK_boar_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_1BrC_solo_30 is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{a878b549-c55a-42d3-8f25-566220ea06aa}
    CfgName = 'UK_1BrC_solo_30'
    DivisionName = 'YOWBMGVFWE'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_1_Corps"
    StrategicLabelColor     = "division_UK_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_2ATAF_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{50c0f14f-acc8-4d4e-923a-ee65d4e2b4cd}
    CfgName = 'UK_2ATAF_solo'
    DivisionName = 'FJPGXBJJHP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_RAF"
    StrategicLabelColor     = "division_UK_corps"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_2InfDiv_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{18411278-a861-4d6e-94a4-f8584613e72e}
    CfgName = 'UK_2InfDiv_solo'
    DivisionName = 'HDQPSYLNXS'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_2nd_Infantry"
    StrategicLabelColor     = "division_UK_boar_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_4ArmDiv_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{3fd4beb5-2cf3-464a-808a-0ab009f2a40b}
    CfgName = 'UK_4ArmDiv_solo'
    DivisionName = 'LCVRURPXLV'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_4th_Armoured"
    StrategicLabelColor     = "division_UK_corps"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_MNAD_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{53215730-b6a3-4f2c-879c-2d29de2aa926}
    CfgName = 'UK_MNAD_solo'
    DivisionName = 'WHOHCKMOET'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_1st_Armoured"
    StrategicLabelColor     = "division_UK_corps"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_NORTHAG_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{de9e5aca-aac1-4118-88f7-67174f3224db}
    CfgName = 'UK_NORTHAG_solo'
    DivisionName = 'CXCRBGXKQG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_BAOR"
    StrategicLabelColor     = "division_UK_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_RAFG_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{e8c159ad-b3c7-45a9-a78f-c3935a3341d0}
    CfgName = 'UK_RAFG_solo'
    DivisionName = 'MZONZDXLPW'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_RAF"
    StrategicLabelColor     = "division_UK_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_RAF_Bruggen_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{fc3a14f1-817b-44a7-81bc-171519ff5cf6}
    CfgName = 'UK_RAF_Bruggen_solo'
    DivisionName = 'FOWWAHLVMF'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_RAF"
    StrategicLabelColor     = "division_UK_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_RAF_Gutersloh_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{7d675b7e-84e9-499b-9422-c08585740e04}
    CfgName = 'UK_RAF_Gutersloh_solo'
    DivisionName = 'RYRVTKNPAV'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_RAF"
    StrategicLabelColor     = "division_UK_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_RAF_Laarbruch_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{b61eb340-5901-415f-bcb0-56767d149acf}
    CfgName = 'UK_RAF_Laarbruch_solo'
    DivisionName = 'BFMMTTTZXB'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_RAF"
    StrategicLabelColor     = "division_UK_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_RAF_Wildenrath_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{420a0e93-259f-42c6-ae05-8198573a0de4}
    CfgName = 'UK_RAF_Wildenrath_solo'
    DivisionName = 'AUBZYRMSFQ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_RAF"
    StrategicLabelColor     = "division_UK_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_10TFW_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{8add8e26-08ab-404d-bb51-9ee48704fd6e}
    CfgName = 'US_10TFW_solo'
    DivisionName = 'QOWCXNHSTP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_AirForce"
    StrategicLabelColor     = "division_US_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_11ACR_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{7d61c6a2-67b8-497e-b1d2-6dd4738f7fe6}
    CfgName = 'US_11ACR_solo'
    DivisionName = 'AJSWBANTOA'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_11ACR"
    StrategicLabelColor     = "division_US_6"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_11AviaBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{0d9383ce-b215-4e61-8922-5929bdb3c10c}
    CfgName = 'US_11AviaBrig_solo'
    DivisionName = 'ZBTYQDKGDQ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_VII_Corps"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_12AviaGroup_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{112c2573-9516-463f-bac0-40973d24d99a}
    CfgName = 'US_12AviaGroup_solo'
    DivisionName = 'OJWMXBRPQJ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_V_Corps"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_130Eng_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{e1c99c46-e52c-4c5c-b582-8433957f9364}
    CfgName = 'US_130Eng_solo'
    DivisionName = 'BGKQWCFSDP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_V_Corps"
    StrategicLabelColor     = "corps_otan"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_14MPBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{abea9354-0999-451a-a806-e65b6104c770}
    CfgName = 'US_14MPBrig_solo'
    DivisionName = 'QBDIBHTMDV'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_VII_Corps"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_17ABCorps_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{915f2151-e927-4b16-9a63-b43c0f8d4bd2}
    CfgName = 'US_17ABCorps_solo'
    DivisionName = 'CZSQNXGGTS'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_XVII_Corps"
    StrategicLabelColor     = "corps_otan"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_17AF_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{55a8f071-b420-4df8-9ac5-a7967de6d1d5}
    CfgName = 'US_17AF_solo'
    DivisionName = 'QIKUVABKDH'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_AirForce"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_17ArtBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{a0048cf3-c6ed-48b7-93c6-d4bf787d2d0b}
    CfgName = 'US_17ArtBrig_solo'
    DivisionName = 'BPCQDIABSJ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_VII_Corps"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_18Eng_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{944b81cf-d8f3-499f-ad0d-2509499eafc0}
    CfgName = 'US_18Eng_solo'
    DivisionName = 'HZFHDVBEKR'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_flag_us"
    StrategicLabelColor     = "corps_otan"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_18MPBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{b108a262-ec1b-4224-b5f0-c2258233d626}
    CfgName = 'US_18MPBrig_solo'
    DivisionName = 'NXSAJFPRZN'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_V_Corps"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_194ArmBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{3d115f8e-ce7b-4357-8f40-d9f1b382017c}
    CfgName = 'US_194ArmBrig_solo'
    DivisionName = 'RQYMEKQIKU'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_194th_Armored_Brigade"
    StrategicLabelColor     = "corps_otan"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_197MechBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{98c109a9-49e5-4503-adbb-e9f691e1f013}
    CfgName = 'US_197MechBrig_solo'
    DivisionName = 'STYBNFYSZO'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_197th_Mechanized_Brigade"
    StrategicLabelColor     = "corps_otan"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_1Arm_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{97df0a5e-b768-4da6-8002-6d93bf592276}
    CfgName = 'US_1Arm_solo'
    DivisionName = 'PRTCDCACMB'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_1st_Armored_Division"
    StrategicLabelColor     = "division_US_4"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_20TFW_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2292035c-8c52-4a0c-9ccd-2d69514c5a97}
    CfgName = 'US_20TFW_solo'
    DivisionName = 'NMXKEWXSXC'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_AirForce"
    StrategicLabelColor     = "division_US_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_210ArtBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{3ddd13ed-0526-4b92-9b88-402a016bb01d}
    CfgName = 'US_210ArtBrig_solo'
    DivisionName = 'HFPUEYYLWJ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_VII_Corps"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_2ACR_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{42ff0a6e-5c8a-4ca9-9bd2-214175c18211}
    CfgName = 'US_2ACR_solo'
    DivisionName = 'BAHLYMAFMD'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_VII_Corps"
    StrategicLabelColor     = "division_US_6"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_2Arm_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c75d90f0-1cff-4df7-8dcf-94465f08d37f}
    CfgName = 'US_2Arm_solo'
    DivisionName = 'RPZVLOJMAT'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_3rd_Armored_Division"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_36TFW_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{580b903e-9d5e-4383-8752-ff19b9d56bad}
    CfgName = 'US_36TFW_solo'
    DivisionName = 'YUJKFMIJOF'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_AirForce"
    StrategicLabelColor     = "division_US_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_3AF_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{9bc863f7-4f49-4cf9-896f-05ae098544f6}
    CfgName = 'US_3AF_solo'
    DivisionName = 'LAFILSKGDX'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_AirForce"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_3Arm_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{e3541154-22c4-4d2b-bfff-ed2715c15341}
    CfgName = 'US_3Arm_solo'
    DivisionName = 'RSEACNWCQI'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_3rd_Armored_Division"
    StrategicLabelColor     = "division_US_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_3Corps_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{1504d743-bb40-4492-a048-3667f6d6878a}
    CfgName = 'US_3Corps_solo'
    DivisionName = 'ZTKHWWMFHP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_3rd_Armored_Division"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_3Inf_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{ca37d83b-ee58-4b12-b38f-7f69a56e46e4}
    CfgName = 'US_3Inf_solo'
    DivisionName = 'GXVZANBEUY'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_3rd_infantry_division"
    StrategicLabelColor     = "division_US_3"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_3rd_Arm_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{26f0b49e-2ad7-42f3-b0bd-958492fff5b4}
    CfgName = 'US_3rd_Arm_solo'
    DivisionName = 'RSEACNWCQI'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_3rd_Armored_Division"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_41FABrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{430ea825-bd13-4f35-b525-4dc6de375709}
    CfgName = 'US_41FABrig_solo'
    DivisionName = 'KXDHOZGFIO'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_V_Corps"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_42FABrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{60cbb9ff-9c5f-44a7-9136-97877bff8b10}
    CfgName = 'US_42FABrig_solo'
    DivisionName = 'APWHVTICZW'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_V_Corps"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_48TFW_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{f8257fe9-7a6b-4891-831b-23b980429422}
    CfgName = 'US_48TFW_solo'
    DivisionName = 'PCOYCDRMXA'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_AirForce"
    StrategicLabelColor     = "division_US_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_4ATAF_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{a170388e-5741-4f7a-b915-a2fa2e962a90}
    CfgName = 'US_4ATAF_solo'
    DivisionName = 'XAZRSRXGOP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_AirForce"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_50TFW_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{ad0f89b7-829c-4daf-a62f-525aec9cc6b4}
    CfgName = 'US_50TFW_solo'
    DivisionName = 'QOVRQVZAOI'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_AirForce"
    StrategicLabelColor     = "division_US_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_52TFW_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c265593a-2c66-4ebd-8370-6bb804615cf6}
    CfgName = 'US_52TFW_solo'
    DivisionName = 'SSVYVMHOZM'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_AirForce"
    StrategicLabelColor     = "division_US_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_5Corps_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{74b627d4-927b-4ca7-b2ff-5660cf5009a1}
    CfgName = 'US_5Corps_solo'
    DivisionName = 'XIFVCZXVJC'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_V_Corps"
    StrategicLabelColor     = "corps_otan"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_72ArtBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{00b277bd-aa16-4616-a5d5-55889ec52a5f}
    CfgName = 'US_72ArtBrig_solo'
    DivisionName = 'RBNPNWZIMF'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_VII_Corps"
    StrategicLabelColor     = "H2_bleu_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_7Corps_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{507168ec-b9db-48a9-bc60-001780ba657b}
    CfgName = 'US_7Corps_solo'
    DivisionName = 'JNWISSLMCA'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_VII_Corps"
    StrategicLabelColor     = "corps_otan"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_7Eng_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{3dc2b50b-48d1-48d2-84ba-e9aad27ae141}
    CfgName = 'US_7Eng_solo'
    DivisionName = 'ESGVHRWUCP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_VII_Corps"
    StrategicLabelColor     = "corps_otan"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_81TFW_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2c5c6b4c-4797-44c0-9afa-017373f23287}
    CfgName = 'US_81TFW_solo'
    DivisionName = 'MQOZMVGKHB'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_AirForce"
    StrategicLabelColor     = "division_US_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_82AB_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{e95fcb80-85ba-4e50-9e2c-7d99847e860d}
    CfgName = 'US_82AB_solo'
    DivisionName = 'AWINESUMIZ'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_82nd_airborne"
    StrategicLabelColor     = "division_US_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_86TFW_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{dfbcb2e6-e1af-4165-999b-449e629c6bc7}
    CfgName = 'US_86TFW_solo'
    DivisionName = 'VWMNPXFNRP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_US_AirForce"
    StrategicLabelColor     = "division_US_air"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_8Inf_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{6137fd07-18bc-4d26-a1fe-01ca7ad10eae}
    CfgName = 'US_8Inf_solo'
    DivisionName = 'PFWJUTBNGI'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_8th_infantry_division"
    StrategicLabelColor     = "division_US_8th"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_BEL_1BEC_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{deacbb5c-5867-42ad-af87-6af7993f2473}
    CfgName = 'US_BEL_1BEC_solo'
    DivisionName = 'CXRQZNZYVC'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_1_Corps"
    StrategicLabelColor     = "division_BE_kassel_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_BEL_COMRECCE_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{923b33a1-617c-4a03-a131-bfb8082610d1}
    CfgName = 'US_BEL_COMRECCE_solo'
    DivisionName = 'MINGNQHLTO'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_BEL_1_Corps"
    StrategicLabelColor     = "division_BE_Boar_2"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_CENTAG_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{a35b46b4-46e7-45b4-b058-9ef8c792a1e5}
    CfgName = 'US_CENTAG_solo'
    DivisionName = 'COBTUEBJOK'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_flag_us"
    StrategicLabelColor     = "corps_otan"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_UK_1BrC_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{67c0dc15-fc2b-44e5-868a-a86f02977b93}
    CfgName = 'US_UK_1BrC_solo'
    DivisionName = 'YOWBMGVFWE'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_1_Corps"
    StrategicLabelColor     = "division_UK_corps"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_UK_33ArmBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{bc4ed516-a4a8-41ca-8b2d-7a25f093f1df}
    CfgName = 'US_UK_33ArmBrig_solo'
    DivisionName = 'BRFWUEGMRN'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_3rd_Armoured"
    StrategicLabelColor     = "division_UK_boar_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_UK_3ArmDiv_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{1ad98f56-7fad-4867-b3ff-d33aaa37b41d}
    CfgName = 'US_UK_3ArmDiv_solo'
    DivisionName = 'GYPOTPNSDB'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_3rd_Armoured"
    StrategicLabelColor     = "division_UK_boar_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_UK_6ArmBrig_solo is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{740ab5e5-932b-49cb-a046-41425ba6f520}
    CfgName = 'US_UK_6ArmBrig_solo'
    DivisionName = 'KMMAQOBPDS'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['STRAT', 'Allied']
    DivisionRule = nil
    EmblemTexture     = "Texture_Division_Emblem_UK_3rd_Armoured"
    StrategicLabelColor     = "division_UK_boar_1"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)

Descriptor_Deck_Division_BEL_1BEC_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{0f232540-0d3b-42ae-9257-08dc5ed528f5}
    CfgName = 'BEL_1BEC_challenge'
    DivisionName = 'ZVXSHDXJZE'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_BEL_1_Corps"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_DDR_20MSD_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{ec4e6c8f-03d3-43a9-96cc-7749caea3fa8}
    CfgName = 'DDR_20MSD_challenge'
    DivisionName = 'SANJZXSUTI'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_20_MSD_DDR"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_DDR_23_MSB_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{d5111f64-085e-47f0-b39f-3b5c0a51321e}
    CfgName = 'DDR_23_MSB_challenge'
    DivisionName = 'RSLOTCIYSH'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_DDR_GroundForces_SansFond"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_FR_11e_para_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{b6d756fc-1542-438d-9860-4363cd7befec}
    CfgName = 'FR_11e_para_challenge'
    DivisionName = 'NBETCNKODY'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_FR_11e_Para"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_FR_5e_Blindee_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c017e991-adb7-4dd2-be96-e1e0c7903e75}
    CfgName = 'FR_5e_Blindee_challenge'
    DivisionName = 'SWOESKMZMF'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_FR_5eDB"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_FR_7e_Blindee_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{1d6de536-94b6-48ad-97ae-70ab8dc37745}
    CfgName = 'FR_7e_Blindee_challenge'
    DivisionName = 'OUTIKXRJRP'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_FR_7eDB"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_NL_4e_Divisie_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{d75c132d-4a24-4463-a69e-a1d74d3ea8c4}
    CfgName = 'NL_4e_Divisie_challenge'
    DivisionName = 'EWOIOHQJJT'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_NL_4e_Divisie"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_11TkD_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{f9358daa-ca30-4709-bafd-2b20df30b06a}
    CfgName = 'POL_11TkD_challenge'
    DivisionName = ''
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_POL_11_TkD"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_1SF_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{47c797f6-4d25-4baa-858b-f234680f5f3c}
    CfgName = 'POL_1SF_challenge'
    DivisionName = 'TQUMUBRSBF'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_POL_30RMD_84MSR_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{e1d44fb8-9a14-49b8-b6b5-00e16e623e3b}
    CfgName = 'POL_30RMD_84MSR_challenge'
    DivisionName = 'EQTOVWSYGC'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_POL_20_Pc"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_11MSD_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{35dba35c-2786-4948-a5f1-d28ff86083df}
    CfgName = 'RDA_11MSD_challenge'
    DivisionName = 'VIHBYIAHTG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_DDR_GroundForces_SansFond"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RDA_4_MSD_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{7b172fb3-ac2a-4c9f-9643-940b97fba1db}
    CfgName = 'RDA_4_MSD_challenge'
    DivisionName = 'QTLZNDFNJC'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_4_MSD_DDR"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_12_Panzer_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{10a1a286-2ebb-48b1-9fd9-0852318f9acd}
    CfgName = 'RFA_12_Panzer_challenge'
    DivisionName = 'OVWMHFFBFY'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_RFA_12_Panzer"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_2_PzGrenadier_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{d95a18f7-36c7-428b-b51c-aadb14bdc145}
    CfgName = 'RFA_2_PzGrenadier_challenge'
    DivisionName = 'YFXMMZXILW'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Nord"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_5_Panzer_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{3e64db05-a7f9-4f8d-b4f9-************}
    CfgName = 'RFA_5_Panzer_challenge'
    DivisionName = 'THWOAZQTOB'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_5_Panzerdivision_RFA"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_RFA_TerrKdo_Sud_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{1567ee5a-42bf-4200-b5a2-8c14d8a133b6}
    CfgName = 'RFA_TerrKdo_Sud_challenge'
    DivisionName = 'BIHXAJRCJA'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_RFA_TerrKdo_Sud"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_138_TkInde_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{6e6fd404-348d-436d-9892-c6db42eba23b}
    CfgName = 'SOV_138_TkInde_challenge'
    DivisionName = 'WYNOTADMUW'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_SOV_138OdtTP"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_27_Gds_Rifle_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{92231eaa-b44f-4ab7-802d-b45019295bb9}
    CfgName = 'SOV_27_Gds_Rifle_challenge'
    DivisionName = 'PFMPJNZAXB'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_SOV_27GvMSD"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_35_AirAslt_Brig_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{697d227f-a521-4a1a-ab72-5e3574ea68b9}
    CfgName = 'SOV_35_AirAslt_Brig_challenge'
    DivisionName = 'PFEYNGDKVR'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_35th_VDV_Brigade"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_39_Gds_Rifle_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{4ccf9ab4-2032-4397-a22f-dc92a1f2a103}
    CfgName = 'SOV_39_Gds_Rifle_challenge'
    DivisionName = 'WHXBXIENCF'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_39th_gmsd"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_57_GMRD_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{7ebffbc2-7b74-411c-8e2a-9c29bfd16398}
    CfgName = 'SOV_57_GMRD_challenge'
    DivisionName = 'JLEMTCILBD'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_Blazon57th_gtd"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_79_Gds_Tank_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c834463e-332f-439e-a31b-aff27f56a094}
    CfgName = 'SOV_79_Gds_Tank_challenge'
    DivisionName = 'ABQKCQLERY'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_79th_gtd"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_SOV_9_Gds_Tank_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c068effe-28e6-4e44-b62b-76a311bf2c06}
    CfgName = 'SOV_9_Gds_Tank_challenge'
    DivisionName = 'UCHGNEBJWY'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Axis
    DivisionTags = ['HB', 'Axis']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_SOV_9TD"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_1st_Armoured_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{63120a71-5e63-45df-b4ea-974f0e54f819}
    CfgName = 'UK_1st_Armoured_challenge'
    DivisionName = 'YPUIEGDPJD'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_UK_1st_Armoured"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_2nd_Infantry_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{4701444a-c2cf-4679-a881-4019b503d615}
    CfgName = 'UK_2nd_Infantry_challenge'
    DivisionName = ''
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_UK_2nd_Infantry"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_4th_Armoured_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2f7f7000-7dbf-43d5-95a5-775017cae9f2}
    CfgName = 'UK_4th_Armoured_challenge'
    DivisionName = 'EDKSESFKOW'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_UK_4th_Armoured"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_5th_Airborne_Brigade_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{d50a844f-54a9-475f-82c0-d56d3b27a302}
    CfgName = 'UK_5th_Airborne_Brigade_challenge'
    DivisionName = 'HEWSNUTIYY'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_UK_5th_Airborne"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_Blues_Royals_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{aaf0bdf7-0edc-437f-b211-a8341d0f1dd6}
    CfgName = 'UK_Blues_Royals_challenge'
    DivisionName = 'MSVIEZBTGS'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_UK_3rd_Armoured"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_UK_Queens_Own_Highlanders_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{c7e0641a-863b-44db-a3fc-9d9998e8898c}
    CfgName = 'UK_Queens_Own_Highlanders_challenge'
    DivisionName = 'XMPEHKSGGF'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_UK_3rd_Armoured"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_11ACR_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{07128cc1-4137-4b06-bec4-384c6530872f}
    CfgName = 'US_11ACR_challenge'
    DivisionName = 'GKSQUEBHOA'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_11ACR"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_3rd_Arm_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{b85095ba-e95f-471e-a356-889908febadb}
    CfgName = 'US_3rd_Arm_challenge'
    DivisionName = 'RSEACNWCQI'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_3rd_Armored_Division"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_82nd_Airborne_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{2fcf5e2f-09a5-4125-85f1-c7fc176644b9}
    CfgName = 'US_82nd_Airborne_challenge'
    DivisionName = 'XWAETHXNYG'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_82nd_airborne"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)
Descriptor_Deck_Division_US_8th_Inf_challenge is TDeckDivisionDescriptor
(
    DescriptorId = GUID:{f6f69a4a-4dc6-48ff-89cf-9e3c90e4d5ea}
    CfgName = 'US_8th_Inf_challenge'
    DivisionName = 'ECGMWQOEZA'
    InterfaceOrder = -1.0
    DivisionCoalition = ECoalition/Allied
    DivisionTags = ['HB', 'Allied']
    MaxActivationPoints = 9999
    DivisionRule = nil
    CostMatrix = MatrixCostName_Challenge
    EmblemTexture     = "Texture_Division_Emblem_8th_infantry_division"
    PortraitTexture   = "Texture_Division_Portrait_default"
    TypeTexture       = ""
)

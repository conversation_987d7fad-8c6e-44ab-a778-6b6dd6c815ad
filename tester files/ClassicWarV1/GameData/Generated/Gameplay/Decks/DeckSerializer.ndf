// Ne pas éditer, ce fichier est généré par DeckSerializerFileWriter

unnamed TDeckSerializerEntries
(
    DivisionIds = MAP
    [
        (Descriptor_Deck_Division_SOV_3FrontUkr_solo, 4),
        (Descriptor_Deck_Division_US_CENTAG_solo, 5),
        (Descriptor_Deck_Division_US_11ACR_solo, 8),
        (Descriptor_Deck_Division_RDA_7_Panzer_multi, 9),
        (Descriptor_Deck_Division_RFA_5_Panzer_multi, 10),
        (Descriptor_Deck_Division_SOV_79_Gds_Tank_multi, 11),
        (Descriptor_Deck_Division_US_11ACR_multi, 12),
        (Descriptor_Deck_Division_US_3rd_Arm_multi, 13),
        (Descriptor_Deck_Division_US_8th_Inf_multi, 14),
        (Descriptor_Deck_Division_NATO_Garnison_Berlin_multi, 15),
        (Descriptor_Deck_Division_SOV_39_Gds_Rifle_multi, 17),
        (Descriptor_Deck_Division_US_3rd_Arm_solo, 18),
        (Descriptor_Deck_Division_RDA_4_MSD_multi, 19),
        (Descriptor_Deck_Division_RFA_2_PzGrenadier_multi, 20),
        (Descriptor_Deck_Division_SOV_39GMRD_solo, 21),
        (Descriptor_Deck_Division_SOV_57GMRD_solo, 22),
        (Descriptor_Deck_Division_SOV_35_AirAslt_Brig_multi, 23),
        (Descriptor_Deck_Division_US_82nd_Airborne_multi, 24),
        (Descriptor_Deck_Division_FR_11e_Para_multi, 25),
        (Descriptor_Deck_Division_FR_5e_Blindee_multi, 26),
        (Descriptor_Deck_Division_RDA_KdA_Bezirk_Erfurt_multi, 27),
        (Descriptor_Deck_Division_RFA_TerrKdo_Sud_multi, 28),
        (Descriptor_Deck_Division_UK_1st_Armoured_multi, 29),
        (Descriptor_Deck_Division_UK_2nd_Infantry_multi, 30),
        (Descriptor_Deck_Division_WP_Unternehmen_Zentrum_multi, 33),
        (Descriptor_Deck_Division_NATO_CENTAG_solo, 34),
        (Descriptor_Deck_Division_RFA_12PzD_solo, 35),
        (Descriptor_Deck_Division_RFA_2PzGrD_solo, 36),
        (Descriptor_Deck_Division_RFA_3Korps_solo, 37),
        (Descriptor_Deck_Division_RFA_54Brig_solo, 38),
        (Descriptor_Deck_Division_RFA_5PzD_solo, 39),
        (Descriptor_Deck_Division_RFA_64Brig_solo, 40),
        (Descriptor_Deck_Division_RFA_TK_Sud_solo, 41),
        (Descriptor_Deck_Division_US_130Eng_solo, 42),
        (Descriptor_Deck_Division_US_17ABCorps_solo, 43),
        (Descriptor_Deck_Division_US_194ArmBrig_solo, 44),
        (Descriptor_Deck_Division_US_197MechBrig_solo, 45),
        (Descriptor_Deck_Division_US_1Arm_solo, 46),
        (Descriptor_Deck_Division_US_2ACR_solo, 47),
        (Descriptor_Deck_Division_US_3Arm_solo, 48),
        (Descriptor_Deck_Division_US_3Inf_solo, 49),
        (Descriptor_Deck_Division_US_5Corps_solo, 50),
        (Descriptor_Deck_Division_US_7Corps_solo, 51),
        (Descriptor_Deck_Division_US_82AB_solo, 52),
        (Descriptor_Deck_Division_US_8Inf_solo, 53),
        (Descriptor_Deck_Division_RFA_26AB_solo, 54),
        (Descriptor_Deck_Division_RFA_VBK_41_solo, 55),
        (Descriptor_Deck_Division_RFA_VBK_42_solo, 56),
        (Descriptor_Deck_Division_RFA_VBK_43_solo, 57),
        (Descriptor_Deck_Division_RFA_VBK_44_solo, 58),
        (Descriptor_Deck_Division_RFA_VBK_45_solo, 59),
        (Descriptor_Deck_Division_US_18Eng_solo, 60),
        (Descriptor_Deck_Division_US_7Eng_solo, 61),
        (Descriptor_Deck_Division_RDA_11MSD_solo, 62),
        (Descriptor_Deck_Division_RDA_4MSD_solo, 63),
        (Descriptor_Deck_Division_RDA_7PzD_solo, 64),
        (Descriptor_Deck_Division_RDA_MBDIII_solo, 65),
        (Descriptor_Deck_Division_SOV_11GTkD_solo, 66),
        (Descriptor_Deck_Division_SOV_1GTA_solo, 67),
        (Descriptor_Deck_Division_SOV_20GMRD_solo, 68),
        (Descriptor_Deck_Division_SOV_27GMRD_solo, 69),
        (Descriptor_Deck_Division_SOV_79GTkD_solo, 70),
        (Descriptor_Deck_Division_SOV_8GA_solo, 71),
        (Descriptor_Deck_Division_SOV_9TkD_solo, 72),
        (Descriptor_Deck_Division_SOV_WGF_solo, 73),
        (Descriptor_Deck_Division_SOV_308ArtBr_solo, 74),
        (Descriptor_Deck_Division_SOV_35GvDSHB_solo, 75),
        (Descriptor_Deck_Division_SOV_390ArtBr_solo, 76),
        (Descriptor_Deck_Division_RDA_4_MSD_challenge, 77),
        (Descriptor_Deck_Division_RFA_5_Panzer_challenge, 78),
        (Descriptor_Deck_Division_RFA_2_PzGrenadier_challenge, 79),
        (Descriptor_Deck_Division_SOV_79_Gds_Tank_challenge, 81),
        (Descriptor_Deck_Division_US_3rd_Arm_challenge, 82),
        (Descriptor_Deck_Division_RFA_TerrKdo_Sud_challenge, 83),
        (Descriptor_Deck_Division_SOV_35_AirAslt_Brig_challenge, 84),
        (Descriptor_Deck_Division_US_8th_Inf_challenge, 85),
        (Descriptor_Deck_Division_NL_4e_Divisie_multi, 87),
        (Descriptor_Deck_Division_US_9th_Mot_multi, 89),
        (Descriptor_Deck_Division_UK_5th_Airborne_Brigade_multi, 90),
        (Descriptor_Deck_Division_BEL_16e_Mecanisee_multi, 91),
        (Descriptor_Deck_Division_RDA_9_Panzer_multi, 93),
        (Descriptor_Deck_Division_UK_4th_Armoured_multi, 94),
        (Descriptor_Deck_Division_POL_20_Pancerna_multi, 95),
        (Descriptor_Deck_Division_POL_4_Zmechanizowana_multi, 96),
        (Descriptor_Deck_Division_POL_Korpus_Desantowy_multi, 97),
        (Descriptor_Deck_Division_SOV_39_Gds_Rifle_challenge, 98),
        (Descriptor_Deck_Division_SOV_27_Gds_Rifle_challenge, 99),
        (Descriptor_Deck_Division_FR_5e_Blindee_challenge, 103),
        (Descriptor_Deck_Division_UK_4th_Armoured_challenge, 104),
        (Descriptor_Deck_Division_UK_1st_Armoured_challenge, 105),
        (Descriptor_Deck_Division_RDA_11MSD_challenge, 106),
        (Descriptor_Deck_Division_RFA_12_Panzer_challenge, 107),
        (Descriptor_Deck_Division_BEL_10PtserBrig_solo, 108),
        (Descriptor_Deck_Division_BEL_10Wing_solo, 109),
        (Descriptor_Deck_Division_BEL_16de_PtserDiv_solo, 110),
        (Descriptor_Deck_Division_BEL_17BrigBlind_solo, 111),
        (Descriptor_Deck_Division_BEL_1Wing_solo, 112),
        (Descriptor_Deck_Division_BEL_2Wing_solo, 113),
        (Descriptor_Deck_Division_BEL_3Wing_solo, 114),
        (Descriptor_Deck_Division_BEL_4PtserBrig_solo, 115),
        (Descriptor_Deck_Division_BEL_9Wing_solo, 116),
        (Descriptor_Deck_Division_BEL_BAF_solo, 117),
        (Descriptor_Deck_Division_NATO_32ADG_solo, 118),
        (Descriptor_Deck_Division_RFA_2LwDiv_solo, 119),
        (Descriptor_Deck_Division_RFA_3EngBrig_solo, 120),
        (Descriptor_Deck_Division_RFA_3FlakBrig_solo, 121),
        (Descriptor_Deck_Division_RFA_3HFG_solo, 122),
        (Descriptor_Deck_Division_RFA_74PioRgt_solo, 123),
        (Descriptor_Deck_Division_RFA_FlakRakRgt14_solo, 124),
        (Descriptor_Deck_Division_RFA_FlakRakRgt3_solo, 125),
        (Descriptor_Deck_Division_RFA_FlakRakRgt4_solo, 126),
        (Descriptor_Deck_Division_RFA_FlakRakRgt6_solo, 127),
        (Descriptor_Deck_Division_RFA_Heimat71_solo, 128),
        (Descriptor_Deck_Division_RFA_Heimat84_solo, 129),
        (Descriptor_Deck_Division_RFA_Heimat94_solo, 130),
        (Descriptor_Deck_Division_RFA_Luftwaffe_solo, 131),
        (Descriptor_Deck_Division_RFA_VBK_46_solo, 132),
        (Descriptor_Deck_Division_RFA_VBK_47_solo, 133),
        (Descriptor_Deck_Division_UK_2ATAF_solo, 134),
        (Descriptor_Deck_Division_UK_NORTHAG_solo, 135),
        (Descriptor_Deck_Division_UK_RAFG_solo, 136),
        (Descriptor_Deck_Division_UK_RAF_Bruggen_solo, 137),
        (Descriptor_Deck_Division_UK_RAF_Gutersloh_solo, 138),
        (Descriptor_Deck_Division_UK_RAF_Laarbruch_solo, 139),
        (Descriptor_Deck_Division_UK_RAF_Wildenrath_solo, 140),
        (Descriptor_Deck_Division_US_10TFW_solo, 141),
        (Descriptor_Deck_Division_US_11AviaBrig_solo, 142),
        (Descriptor_Deck_Division_US_12AviaGroup_solo, 143),
        (Descriptor_Deck_Division_US_14MPBrig_solo, 144),
        (Descriptor_Deck_Division_US_17AF_solo, 145),
        (Descriptor_Deck_Division_US_17ArtBrig_solo, 146),
        (Descriptor_Deck_Division_US_18MPBrig_solo, 147),
        (Descriptor_Deck_Division_US_20TFW_solo, 148),
        (Descriptor_Deck_Division_US_210ArtBrig_solo, 149),
        (Descriptor_Deck_Division_US_36TFW_solo, 150),
        (Descriptor_Deck_Division_US_3AF_solo, 151),
        (Descriptor_Deck_Division_US_41FABrig_solo, 152),
        (Descriptor_Deck_Division_US_42FABrig_solo, 153),
        (Descriptor_Deck_Division_US_48TFW_solo, 154),
        (Descriptor_Deck_Division_US_4ATAF_solo, 155),
        (Descriptor_Deck_Division_US_50TFW_solo, 156),
        (Descriptor_Deck_Division_US_52TFW_solo, 157),
        (Descriptor_Deck_Division_US_72ArtBrig_solo, 158),
        (Descriptor_Deck_Division_US_81TFW_solo, 159),
        (Descriptor_Deck_Division_US_86TFW_solo, 160),
        (Descriptor_Deck_Division_US_BEL_1BEC_solo, 161),
        (Descriptor_Deck_Division_US_BEL_COMRECCE_solo, 162),
        (Descriptor_Deck_Division_US_UK_1BrC_solo, 163),
        (Descriptor_Deck_Division_US_UK_33ArmBrig_solo, 164),
        (Descriptor_Deck_Division_US_UK_3ArmDiv_solo, 165),
        (Descriptor_Deck_Division_US_UK_6ArmBrig_solo, 166),
        (Descriptor_Deck_Division_RDA_ArtRgt3_solo, 167),
        (Descriptor_Deck_Division_RDA_ArtRgt40_solo, 168),
        (Descriptor_Deck_Division_RDA_LStR40_solo, 169),
        (Descriptor_Deck_Division_SOV_105ADIB_solo, 170),
        (Descriptor_Deck_Division_SOV_119IndTkBrig_solo, 171),
        (Descriptor_Deck_Division_SOV_11ORAP_solo, 172),
        (Descriptor_Deck_Division_SOV_125ADIB_solo, 173),
        (Descriptor_Deck_Division_SOV_126IAD_solo, 174),
        (Descriptor_Deck_Division_SOV_16GvIAD_solo, 175),
        (Descriptor_Deck_Division_SOV_1GTA_SAM_Brig_solo, 176),
        (Descriptor_Deck_Division_SOV_1LVD_solo, 177),
        (Descriptor_Deck_Division_SOV_294ORAP_solo, 178),
        (Descriptor_Deck_Division_SOV_34ArtDiv_solo, 179),
        (Descriptor_Deck_Division_SOV_357OCHAP_solo, 180),
        (Descriptor_Deck_Division_SOV_368OCHAP_solo, 181),
        (Descriptor_Deck_Division_SOV_6GvIAD_solo, 182),
        (Descriptor_Deck_Division_SOV_8GA_SAM_Brig_solo, 183),
        (Descriptor_Deck_Division_SOV_931ORAP_solo, 184),
        (Descriptor_Deck_Division_FR_7e_Blindee_challenge, 185),
        (Descriptor_Deck_Division_UK_Blues_Royals_challenge, 194),
        (Descriptor_Deck_Division_SOV_119IndTkBrig_multi, 195),
        (Descriptor_Deck_Division_UK_Queens_Own_Highlanders_challenge, 196),
        (Descriptor_Deck_Division_FR_11e_para_challenge, 200),
        (Descriptor_Deck_Division_US_82nd_Airborne_challenge, 201),
        (Descriptor_Deck_Division_SOV_25_Tank_multi, 205),
        (Descriptor_Deck_Division_SOV_27_Gds_Rifle_multi, 207),
        (Descriptor_Deck_Division_US_24th_Inf_multi, 208),
        (Descriptor_Deck_Division_RFA_TK_Nord_solo, 209),
        (Descriptor_Deck_Division_BEL_12BrigInf_solo, 210),
        (Descriptor_Deck_Division_BEL_1DivInfanterie_solo, 211),
        (Descriptor_Deck_Division_BEL_1PtserInfBrig_solo, 212),
        (Descriptor_Deck_Division_BEL_7BrigInfBlind_solo, 213),
        (Descriptor_Deck_Division_RDA_10MSD_solo, 214),
        (Descriptor_Deck_Division_RDA_17MSD_solo, 215),
        (Descriptor_Deck_Division_SOV_6IndMSBrig_multi, 216),
        (Descriptor_Deck_Division_NL_4e_Divisie_challenge, 217),
        (Descriptor_Deck_Division_DDR_20MSD_challenge, 218),
        (Descriptor_Deck_Division_SOV_138_TkInde_challenge, 219),
        (Descriptor_Deck_Division_SOV_56_AirAslt_Brig_multi, 220),
        (Descriptor_Deck_Division_US_101st_Airmobile_multi, 221),
        (Descriptor_Deck_Division_POL_10MSD_solo, 222),
        (Descriptor_Deck_Division_POL_11TkD_solo, 223),
        (Descriptor_Deck_Division_POL_12MSD_solo, 224),
        (Descriptor_Deck_Division_POL_15MSD_solo, 225),
        (Descriptor_Deck_Division_POL_16MSD_solo, 226),
        (Descriptor_Deck_Division_POL_1Armia_solo, 227),
        (Descriptor_Deck_Division_POL_20TkD_solo, 228),
        (Descriptor_Deck_Division_POL_23ArtGr_solo, 229),
        (Descriptor_Deck_Division_POL_24ArtGr_solo, 230),
        (Descriptor_Deck_Division_POL_2Armia_solo, 231),
        (Descriptor_Deck_Division_POL_2MSD_solo, 232),
        (Descriptor_Deck_Division_POL_30RMD_solo, 233),
        (Descriptor_Deck_Division_POL_4MSD_solo, 234),
        (Descriptor_Deck_Division_POL_5ArtGr_solo, 235),
        (Descriptor_Deck_Division_POL_5TkD_solo, 236),
        (Descriptor_Deck_Division_POL_61SAMBrig_solo, 237),
        (Descriptor_Deck_Division_POL_6AirBrig_solo, 238),
        (Descriptor_Deck_Division_POL_6ArtGr_solo, 239),
        (Descriptor_Deck_Division_POL_7ArtGr_solo, 240),
        (Descriptor_Deck_Division_POL_7NavalBrig_solo, 241),
        (Descriptor_Deck_Division_POL_8ArtGr_solo, 242),
        (Descriptor_Deck_Division_POL_8MSD_solo, 243),
        (Descriptor_Deck_Division_POL_DesantK_solo, 244),
        (Descriptor_Deck_Division_POL_LWP_solo, 245),
        (Descriptor_Deck_Division_RDA_15ResPzR_solo, 246),
        (Descriptor_Deck_Division_RDA_19MSD_solo, 247),
        (Descriptor_Deck_Division_RDA_1MSD_solo, 248),
        (Descriptor_Deck_Division_RDA_20MSD_solo, 249),
        (Descriptor_Deck_Division_RDA_25MSR_solo, 250),
        (Descriptor_Deck_Division_RDA_5ArtBrig_solo, 251),
        (Descriptor_Deck_Division_RDA_5GEWR_solo, 252),
        (Descriptor_Deck_Division_RDA_8MSD_solo, 253),
        (Descriptor_Deck_Division_RDA_9PzD_solo, 254),
        (Descriptor_Deck_Division_RDA_MBDV_solo, 255),
        (Descriptor_Deck_Division_SOV_10GTkD_solo, 256),
        (Descriptor_Deck_Division_SOV_115TkInde_solo, 257),
        (Descriptor_Deck_Division_SOV_12GTkD_solo, 258),
        (Descriptor_Deck_Division_SOV_138TkInde_solo, 259),
        (Descriptor_Deck_Division_SOV_145TkInde_solo, 260),
        (Descriptor_Deck_Division_SOV_16GTkD_solo, 261),
        (Descriptor_Deck_Division_SOV_207MRD_solo, 262),
        (Descriptor_Deck_Division_SOV_21MRD_solo, 263),
        (Descriptor_Deck_Division_SOV_221TkInde_solo, 264),
        (Descriptor_Deck_Division_SOV_290ArtBrig_solo, 265),
        (Descriptor_Deck_Division_SOV_2GTA_solo, 266),
        (Descriptor_Deck_Division_SOV_385ArtBrig_solo, 267),
        (Descriptor_Deck_Division_SOV_3A_solo, 268),
        (Descriptor_Deck_Division_SOV_46SAMBrig_solo, 269),
        (Descriptor_Deck_Division_SOV_47GTkD_solo, 270),
        (Descriptor_Deck_Division_SOV_61SAMBrig_solo, 271),
        (Descriptor_Deck_Division_SOV_7GTkD_solo, 272),
        (Descriptor_Deck_Division_SOV_94GMRD_solo, 273),
        (Descriptor_Deck_Division_SOV_NGF_solo, 274),
        (Descriptor_Deck_Division_POL_11TkD_challenge, 275),
        (Descriptor_Deck_Division_UK_2nd_Infantry_challenge, 276),
        (Descriptor_Deck_Division_UK_5th_Airborne_Brigade_challenge, 277),
        (Descriptor_Deck_Division_POL_30RMD_84MSR_challenge, 278),
        (Descriptor_Deck_Division_POL_1SF_challenge, 279),
        (Descriptor_Deck_Division_NL_1Div_solo, 280),
        (Descriptor_Deck_Division_NL_1LK_solo, 281),
        (Descriptor_Deck_Division_NL_4Div_solo, 282),
        (Descriptor_Deck_Division_NL_5Div_solo, 283),
        (Descriptor_Deck_Division_NL_CLKA_solo, 284),
        (Descriptor_Deck_Division_RFA_11PzGrD_solo, 285),
        (Descriptor_Deck_Division_RFA_1Korps_solo, 286),
        (Descriptor_Deck_Division_RFA_1PioKommando_solo, 287),
        (Descriptor_Deck_Division_RFA_1PzD_solo, 288),
        (Descriptor_Deck_Division_RFA_3PzD_solo, 289),
        (Descriptor_Deck_Division_RFA_7PzD_solo, 290),
        (Descriptor_Deck_Division_RFA_FlaKdo1_solo, 291),
        (Descriptor_Deck_Division_UK_1ArmDiv_solo, 292),
        (Descriptor_Deck_Division_UK_1BrC_solo_30, 293),
        (Descriptor_Deck_Division_UK_2InfDiv_solo, 294),
        (Descriptor_Deck_Division_UK_4ArmDiv_solo, 295),
        (Descriptor_Deck_Division_UK_MNAD_solo, 296),
        (Descriptor_Deck_Division_US_2Arm_solo, 297),
        (Descriptor_Deck_Division_US_3Corps_solo, 298),
        (Descriptor_Deck_Division_RFA_TK_Nord_solo_30, 299),
        (Descriptor_Deck_Division_RDA_Rugen_Gruppierung, 300),
        (Descriptor_Deck_Division_US_35th_Inf_multi, 301),
        (Descriptor_Deck_Division_SOV_3LVD_solo, 303),
        (Descriptor_Deck_Division_FR_152e_Infanterie_multi, 304),
        (Descriptor_Deck_Division_SOV_76_VDV_multi, 305),
        (Descriptor_Deck_Division_POL_3_DLMB_solo, 307),
        (Descriptor_Deck_Division_SOV_9_Gds_Tank_challenge, 308),
        (Descriptor_Deck_Division_BEL_1BEC_challenge, 309),
        (Descriptor_Deck_Division_DDR_23_MSB_challenge, 310),
        (Descriptor_Deck_Division_US_6th_Light_multi, 319),
        (Descriptor_Deck_Division_SOV_157_Rifle_multi, 321),
        (Descriptor_Deck_Division_US_11ACR_challenge, 389),
        (Descriptor_Deck_Division_SOV_57_GMRD_challenge, 390),
    ]

    UnitIds = MAP
    [
        ($/GFX/Unit/Descriptor_Unit_HEMTT_US, 12),
        ($/GFX/Unit/Descriptor_Unit_M106A2_HOWZ_US, 14),
        ($/GFX/Unit/Descriptor_Unit_M109A2_HOWZ_US, 15),
        ($/GFX/Unit/Descriptor_Unit_M110A2_HOWZ_US, 17),
        ($/GFX/Unit/Descriptor_Unit_M113A1_TOW_US, 19),
        ($/GFX/Unit/Descriptor_Unit_M113A3_US, 21),
        ($/GFX/Unit/Descriptor_Unit_M163_PIVADS_US, 24),
        ($/GFX/Unit/Descriptor_Unit_M1A1HA_Abrams_US, 25),
        ($/GFX/Unit/Descriptor_Unit_M1A1_Abrams_US, 26),
        ($/GFX/Unit/Descriptor_Unit_M1IP_Abrams_US, 27),
        ($/GFX/Unit/Descriptor_Unit_M1_Abrams_US, 28),
        ($/GFX/Unit/Descriptor_Unit_M270_MLRS_US, 29),
        ($/GFX/Unit/Descriptor_Unit_M2A1_Bradley_IFV_US, 30),
        ($/GFX/Unit/Descriptor_Unit_M3A1_Bradley_CFV_US, 31),
        ($/GFX/Unit/Descriptor_Unit_M48_Chaparral_MIM72F_US, 33),
        ($/GFX/Unit/Descriptor_Unit_M577_US, 35),
        ($/GFX/Unit/Descriptor_Unit_M60A3_Patton_US, 37),
        ($/GFX/Unit/Descriptor_Unit_M901A1_ITW_US, 39),
        ($/GFX/Unit/Descriptor_Unit_M901_TOW_US, 40),
        ($/GFX/Unit/Descriptor_Unit_AH1E_Cobra_US, 42),
        ($/GFX/Unit/Descriptor_Unit_AH1F_Cobra_US, 43),
        ($/GFX/Unit/Descriptor_Unit_AH1F_Hog_US, 45),
        ($/GFX/Unit/Descriptor_Unit_AH64_Apache_US, 46),
        ($/GFX/Unit/Descriptor_Unit_AH6C_Little_Bird_US, 47),
        ($/GFX/Unit/Descriptor_Unit_CH47_Chinook_US, 48),
        ($/GFX/Unit/Descriptor_Unit_CH47_Super_Chinook_US, 49),
        ($/GFX/Unit/Descriptor_Unit_OH58D_Combat_Scout_US, 52),
        ($/GFX/Unit/Descriptor_Unit_OH58D_Kiowa_Warrior_US, 53),
        ($/GFX/Unit/Descriptor_Unit_OH58_CS_US, 54),
        ($/GFX/Unit/Descriptor_Unit_UH1A_US, 55),
        ($/GFX/Unit/Descriptor_Unit_UH1H_Huey_US, 56),
        ($/GFX/Unit/Descriptor_Unit_UH60A_Black_Hawk_US, 57),
        ($/GFX/Unit/Descriptor_Unit_EF111_Raven_US, 60),
        ($/GFX/Unit/Descriptor_Unit_T64BV_SOV, 77),
        ($/GFX/Unit/Descriptor_Unit_T64B_SOV, 78),
        ($/GFX/Unit/Descriptor_Unit_T80B_SOV, 79),
        ($/GFX/Unit/Descriptor_Unit_T80U_SOV, 80),
        ($/GFX/Unit/Descriptor_Unit_Buk_9K37M_SOV, 81),
        ($/GFX/Unit/Descriptor_Unit_MTLB_Strela10_SOV, 82),
        ($/GFX/Unit/Descriptor_Unit_Osa_9K33M3_SOV, 83),
        ($/GFX/Unit/Descriptor_Unit_Tunguska_2K22_SOV, 84),
        ($/GFX/Unit/Descriptor_Unit_BM30_Smerch_SOV, 85),
        ($/GFX/Unit/Descriptor_Unit_TOS1_Buratino_SOV, 86),
        ($/GFX/Unit/Descriptor_Unit_2S1_Gvozdika_SOV, 87),
        ($/GFX/Unit/Descriptor_Unit_2S3M_Akatsiya_SOV, 88),
        ($/GFX/Unit/Descriptor_Unit_2S7M_Malka_SOV, 89),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_CMD_SOV, 90),
        ($/GFX/Unit/Descriptor_Unit_BMP_1P_SOV, 91),
        ($/GFX/Unit/Descriptor_Unit_BMP_2_SOV, 92),
        ($/GFX/Unit/Descriptor_Unit_BMP_3_SOV, 93),
        ($/GFX/Unit/Descriptor_Unit_BRDM_2_Konkurs_SOV, 94),
        ($/GFX/Unit/Descriptor_Unit_BTR_70_SOV, 95),
        ($/GFX/Unit/Descriptor_Unit_BTR_80_SOV, 96),
        ($/GFX/Unit/Descriptor_Unit_MTLB_Shturm_SOV, 97),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_AGL_SOV, 99),
        ($/GFX/Unit/Descriptor_Unit_Ural_4320_SOV, 101),
        ($/GFX/Unit/Descriptor_Unit_Ka_50_SOV, 102),
        ($/GFX/Unit/Descriptor_Unit_Mi_24VP_SOV, 104),
        ($/GFX/Unit/Descriptor_Unit_Mi_24V_SOV, 105),
        ($/GFX/Unit/Descriptor_Unit_Mi_26_SOV, 106),
        ($/GFX/Unit/Descriptor_Unit_MiG_23P_SOV, 109),
        ($/GFX/Unit/Descriptor_Unit_MiG_25BM_SOV, 110),
        ($/GFX/Unit/Descriptor_Unit_MiG_27M_SOV, 111),
        ($/GFX/Unit/Descriptor_Unit_MiG_31M_SOV, 113),
        ($/GFX/Unit/Descriptor_Unit_Su_24MP_SOV, 114),
        ($/GFX/Unit/Descriptor_Unit_Su_24M_SOV, 115),
        ($/GFX/Unit/Descriptor_Unit_Su_25T_SOV, 116),
        ($/GFX/Unit/Descriptor_Unit_Su_27S_SOV, 118),
        ($/GFX/Unit/Descriptor_Unit_M998_Humvee_US, 122),
        ($/GFX/Unit/Descriptor_Unit_Su_25_SOV, 124),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Dragon_US, 133),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Flash_US, 134),
        ($/GFX/Unit/Descriptor_Unit_Rifles_US, 136),
        ($/GFX/Unit/Descriptor_Unit_Rifles_half_Dragon_US, 137),
        ($/GFX/Unit/Descriptor_Unit_Rifles_half_LAW_US, 138),
        ($/GFX/Unit/Descriptor_Unit_Scout_US, 139),
        ($/GFX/Unit/Descriptor_Unit_MiG_23ML_SOV, 141),
        ($/GFX/Unit/Descriptor_Unit_T80BV_SOV, 163),
        ($/GFX/Unit/Descriptor_Unit_M2A2_Bradley_IFV_US, 164),
        ($/GFX/Unit/Descriptor_Unit_ZSU_23_Shilka_SOV, 165),
        ($/GFX/Unit/Descriptor_Unit_BM21_Grad_SOV, 166),
        ($/GFX/Unit/Descriptor_Unit_M1025_Humvee_TOW_US, 167),
        ($/GFX/Unit/Descriptor_Unit_M1025_Humvee_AGL_US, 168),
        ($/GFX/Unit/Descriptor_Unit_Iltis_RFA, 169),
        ($/GFX/Unit/Descriptor_Unit_Gepard_1A2_RFA, 170),
        ($/GFX/Unit/Descriptor_Unit_HS30_Panzermorser_120mm_RFA, 173),
        ($/GFX/Unit/Descriptor_Unit_CH53G_RFA, 175),
        ($/GFX/Unit/Descriptor_Unit_VDV_SOV, 177),
        ($/GFX/Unit/Descriptor_Unit_AeroRifles_US, 180),
        ($/GFX/Unit/Descriptor_Unit_Engineer_CMD_US, 183),
        ($/GFX/Unit/Descriptor_Unit_M1025_Humvee_CMD_US, 185),
        ($/GFX/Unit/Descriptor_Unit_M113A2_supply_US, 186),
        ($/GFX/Unit/Descriptor_Unit_M151_MUTT_CMD_US, 187),
        ($/GFX/Unit/Descriptor_Unit_M1A1_Abrams_CMD_US, 189),
        ($/GFX/Unit/Descriptor_Unit_M35_supply_US, 190),
        ($/GFX/Unit/Descriptor_Unit_M728_CEV_US, 191),
        ($/GFX/Unit/Descriptor_Unit_M981_FISTV_US, 192),
        ($/GFX/Unit/Descriptor_Unit_MP_RCL_US, 194),
        ($/GFX/Unit/Descriptor_Unit_MP_US, 195),
        ($/GFX/Unit/Descriptor_Unit_Rifles_CMD_US, 196),
        ($/GFX/Unit/Descriptor_Unit_OH58C_CMD_US, 198),
        ($/GFX/Unit/Descriptor_Unit_OH58C_Scout_US, 199),
        ($/GFX/Unit/Descriptor_Unit_UH1H_supply_US, 200),
        ($/GFX/Unit/Descriptor_Unit_UH60A_CO_US, 201),
        ($/GFX/Unit/Descriptor_Unit_UH60A_Supply_US, 202),
        ($/GFX/Unit/Descriptor_Unit_Engineers_US, 203),
        ($/GFX/Unit/Descriptor_Unit_2S1_DDR, 204),
        ($/GFX/Unit/Descriptor_Unit_2S3_DDR, 205),
        ($/GFX/Unit/Descriptor_Unit_BMP_1_SP2_DDR, 206),
        ($/GFX/Unit/Descriptor_Unit_BMP_2_DDR, 207),
        ($/GFX/Unit/Descriptor_Unit_BTR_70_DDR, 208),
        ($/GFX/Unit/Descriptor_Unit_Osa_9K33M3_DDR, 209),
        ($/GFX/Unit/Descriptor_Unit_ZSU_23_Shilka_DDR, 210),
        ($/GFX/Unit/Descriptor_Unit_Ural_4320_DDR, 211),
        ($/GFX/Unit/Descriptor_Unit_GreenBerets_MP5_US, 212),
        ($/GFX/Unit/Descriptor_Unit_GreenBerets_US, 213),
        ($/GFX/Unit/Descriptor_Unit_Ranger_US, 214),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Flam_SOV, 217),
        ($/GFX/Unit/Descriptor_Unit_Engineers_SOV, 218),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Scout_SOV, 219),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Igla_SOV, 220),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_BTR_SOV, 221),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_Metis_SOV, 222),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_SOV, 223),
        ($/GFX/Unit/Descriptor_Unit_Scout_LRRP_SOV, 224),
        ($/GFX/Unit/Descriptor_Unit_Scout_SOV, 225),
        ($/GFX/Unit/Descriptor_Unit_Spetsnaz_SOV, 226),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_CMD_DDR, 227),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_Reco_DDR, 229),
        ($/GFX/Unit/Descriptor_Unit_Faun_kraka_RFA, 230),
        ($/GFX/Unit/Descriptor_Unit_Lars_2_RFA, 231),
        ($/GFX/Unit/Descriptor_Unit_Unimog_S_404_RFA, 232),
        ($/GFX/Unit/Descriptor_Unit_Luchs_A1_RFA, 233),
        ($/GFX/Unit/Descriptor_Unit_TPZ_Fuchs_1_RFA, 234),
        ($/GFX/Unit/Descriptor_Unit_Leopard_1A1_RFA, 235),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_2_RFA, 236),
        ($/GFX/Unit/Descriptor_Unit_M109A2_UK, 239),
        ($/GFX/Unit/Descriptor_Unit_Gazelle_UK, 240),
        ($/GFX/Unit/Descriptor_Unit_Puma_UK, 241),
        ($/GFX/Unit/Descriptor_Unit_FV107_Scimitar_UK, 243),
        ($/GFX/Unit/Descriptor_Unit_MCV_80_Warrior_UK, 244),
        ($/GFX/Unit/Descriptor_Unit_M107A2_175mm_UK, 245),
        ($/GFX/Unit/Descriptor_Unit_FV432_Mortar_UK, 246),
        ($/GFX/Unit/Descriptor_Unit_FV432_Rarden_UK, 247),
        ($/GFX/Unit/Descriptor_Unit_FV432_UK, 248),
        ($/GFX/Unit/Descriptor_Unit_81mm_mortar_UK, 249),
        ($/GFX/Unit/Descriptor_Unit_Challenger_1_Mk1_UK, 250),
        ($/GFX/Unit/Descriptor_Unit_Tracked_Rapier_UK, 251),
        ($/GFX/Unit/Descriptor_Unit_Lynx_AH_Mk1_UK, 252),
        ($/GFX/Unit/Descriptor_Unit_Ferret_Mk2_UK, 253),
        ($/GFX/Unit/Descriptor_Unit_FV433_Abbot_UK, 254),
        ($/GFX/Unit/Descriptor_Unit_FV105_Sultan_UK, 255),
        ($/GFX/Unit/Descriptor_Unit_FV103_Spartan_UK, 256),
        ($/GFX/Unit/Descriptor_Unit_FV101_Scorpion_UK, 257),
        ($/GFX/Unit/Descriptor_Unit_FH70_155mm_UK, 258),
        ($/GFX/Unit/Descriptor_Unit_FV721_Fox_UK, 260),
        ($/GFX/Unit/Descriptor_Unit_Saxon_UK, 261),
        ($/GFX/Unit/Descriptor_Unit_Harrier_UK, 263),
        ($/GFX/Unit/Descriptor_Unit_FV4201_Chieftain_UK, 264),
        ($/GFX/Unit/Descriptor_Unit_2K12_KUB_DDR, 265),
        ($/GFX/Unit/Descriptor_Unit_AMX_30_AuF1_FR, 267),
        ($/GFX/Unit/Descriptor_Unit_AML_60_FR, 269),
        ($/GFX/Unit/Descriptor_Unit_AML_90_FR, 270),
        ($/GFX/Unit/Descriptor_Unit_AMX_13_DCA_FR, 271),
        ($/GFX/Unit/Descriptor_Unit_Alouette_III_FR, 272),
        ($/GFX/Unit/Descriptor_Unit_Gazelle_20mm_FR, 273),
        ($/GFX/Unit/Descriptor_Unit_Gazelle_HOT_FR, 274),
        ($/GFX/Unit/Descriptor_Unit_Gazelle_Mistral_FR, 275),
        ($/GFX/Unit/Descriptor_Unit_T813_DDR, 276),
        ($/GFX/Unit/Descriptor_Unit_MFRW_RM70_DDR, 277),
        ($/GFX/Unit/Descriptor_Unit_BRM_1_DDR, 278),
        ($/GFX/Unit/Descriptor_Unit_BRDM_Konkurs_DDR, 279),
        ($/GFX/Unit/Descriptor_Unit_BRDM_Malyu_P_DDR, 280),
        ($/GFX/Unit/Descriptor_Unit_BRDM_Strela_1_DDR, 281),
        ($/GFX/Unit/Descriptor_Unit_MiG_21PFM_DDR, 282),
        ($/GFX/Unit/Descriptor_Unit_Mirage_III_E_FR, 283),
        ($/GFX/Unit/Descriptor_Unit_T72_DDR, 287),
        ($/GFX/Unit/Descriptor_Unit_TRM_2000_FR, 288),
        ($/GFX/Unit/Descriptor_Unit_Mirage_IV_FR, 289),
        ($/GFX/Unit/Descriptor_Unit_Mirage_2000_C_FR, 290),
        ($/GFX/Unit/Descriptor_Unit_Mirage_5_F_FR, 291),
        ($/GFX/Unit/Descriptor_Unit_Mirage_F1_CT_FR, 292),
        ($/GFX/Unit/Descriptor_Unit_Mirage_F1_C_FR, 293),
        ($/GFX/Unit/Descriptor_Unit_Puma_FR, 294),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Stinger_C_US, 296),
        ($/GFX/Unit/Descriptor_Unit_Mortier_107mm_US, 298),
        ($/GFX/Unit/Descriptor_Unit_Rifles_LAW_US, 301),
        ($/GFX/Unit/Descriptor_Unit_M125_HOWZ_US, 304),
        ($/GFX/Unit/Descriptor_Unit_M1025_Humvee_scout_US, 305),
        ($/GFX/Unit/Descriptor_Unit_M151A2_scout_US, 306),
        ($/GFX/Unit/Descriptor_Unit_SPW_152K_DDR, 308),
        ($/GFX/Unit/Descriptor_Unit_VLTT_P4_FR, 309),
        ($/GFX/Unit/Descriptor_Unit_Super_Etendard_FR, 310),
        ($/GFX/Unit/Descriptor_Unit_Roland_2_FR, 312),
        ($/GFX/Unit/Descriptor_Unit_Super_Puma_FR, 313),
        ($/GFX/Unit/Descriptor_Unit_MiG_23MF_DDR, 314),
        ($/GFX/Unit/Descriptor_Unit_Su_22_DDR, 315),
        ($/GFX/Unit/Descriptor_Unit_AMX_13_90mm_FR, 316),
        ($/GFX/Unit/Descriptor_Unit_AMX_13_VCI_12_7mm_FR, 317),
        ($/GFX/Unit/Descriptor_Unit_AMX_13_VCI_20mm_FR, 318),
        ($/GFX/Unit/Descriptor_Unit_VAB_FR, 320),
        ($/GFX/Unit/Descriptor_Unit_VAB_HOT_FR, 321),
        ($/GFX/Unit/Descriptor_Unit_VAB_MILAN_FR, 322),
        ($/GFX/Unit/Descriptor_Unit_MotSchutzen_DDR, 324),
        ($/GFX/Unit/Descriptor_Unit_Grenzer_DDR, 326),
        ($/GFX/Unit/Descriptor_Unit_BGS_RFA, 327),
        ($/GFX/Unit/Descriptor_Unit_Fallschirm_RFA, 328),
        ($/GFX/Unit/Descriptor_Unit_Fernspaher_RFA, 329),
        ($/GFX/Unit/Descriptor_Unit_Jager_RFA, 330),
        ($/GFX/Unit/Descriptor_Unit_PzGrenadier_RFA, 331),
        ($/GFX/Unit/Descriptor_Unit_AMX_10_P_FR, 332),
        ($/GFX/Unit/Descriptor_Unit_AMX_10_P_MILAN_FR, 333),
        ($/GFX/Unit/Descriptor_Unit_AMX_10_RC_FR, 334),
        ($/GFX/Unit/Descriptor_Unit_AMX_10_HOT_FR, 335),
        ($/GFX/Unit/Descriptor_Unit_AMX_30_B2_FR, 336),
        ($/GFX/Unit/Descriptor_Unit_AMX_30_B_FR, 337),
        ($/GFX/Unit/Descriptor_Unit_Rifles_FR, 338),
        ($/GFX/Unit/Descriptor_Unit_Paratroopers_UK, 339),
        ($/GFX/Unit/Descriptor_Unit_RMP_UK, 340),
        ($/GFX/Unit/Descriptor_Unit_Rifles_UK, 341),
        ($/GFX/Unit/Descriptor_Unit_SAS_UK, 342),
        ($/GFX/Unit/Descriptor_Unit_Territorial_UK, 343),
        ($/GFX/Unit/Descriptor_Unit_Engineers_DDR, 349),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Flam_DDR, 350),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Flam_VDV_SOV, 351),
        ($/GFX/Unit/Descriptor_Unit_Engineers_RFA, 352),
        ($/GFX/Unit/Descriptor_Unit_Engineers_VDV_SOV, 353),
        ($/GFX/Unit/Descriptor_Unit_Fallschirmjager_DDR, 354),
        ($/GFX/Unit/Descriptor_Unit_Fallschirmjager_FalseFlag_DDR, 355),
        ($/GFX/Unit/Descriptor_Unit_Fallschirmjager_Metys_DDR, 356),
        ($/GFX/Unit/Descriptor_Unit_Feldgendarmerie_RFA, 358),
        ($/GFX/Unit/Descriptor_Unit_HeimatschutzJager_RFA, 359),
        ($/GFX/Unit/Descriptor_Unit_HvyScout_DDR, 360),
        ($/GFX/Unit/Descriptor_Unit_HvyScout_SOV, 361),
        ($/GFX/Unit/Descriptor_Unit_Jager_Aufk_RFA, 362),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Igla_VDV_SOV, 365),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Redeye_RFA, 366),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Strela_2M_DDR, 367),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Strela_2M_FJ_DDR, 368),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_BTR_DDR, 369),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_DDR, 370),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_Metis_DDR, 371),
        ($/GFX/Unit/Descriptor_Unit_Panzergrenadier_APC_RFA, 374),
        ($/GFX/Unit/Descriptor_Unit_Panzergrenadier_IFV_RFA, 375),
        ($/GFX/Unit/Descriptor_Unit_Scout_DDR, 376),
        ($/GFX/Unit/Descriptor_Unit_Scout_FJ_DDR, 377),
        ($/GFX/Unit/Descriptor_Unit_Scout_RFA, 379),
        ($/GFX/Unit/Descriptor_Unit_Scout_VDV_SOV, 380),
        ($/GFX/Unit/Descriptor_Unit_VDV_Mech_SOV, 383),
        ($/GFX/Unit/Descriptor_Unit_VDV_Metis_SOV, 384),
        ($/GFX/Unit/Descriptor_Unit_Security_RFA, 385),
        ($/GFX/Unit/Descriptor_Unit_Tornado_ADV_UK, 386),
        ($/GFX/Unit/Descriptor_Unit_M113_ACAV_US, 388),
        ($/GFX/Unit/Descriptor_Unit_M113_Dragon_US, 389),
        ($/GFX/Unit/Descriptor_Unit_M1IP_Abrams_CMD_US, 390),
        ($/GFX/Unit/Descriptor_Unit_M35_trans_US, 391),
        ($/GFX/Unit/Descriptor_Unit_M60A3_CMD_US, 392),
        ($/GFX/Unit/Descriptor_Unit_Airborne_half_Dragon_US, 393),
        ($/GFX/Unit/Descriptor_Unit_Airborne_half_LAW_US, 394),
        ($/GFX/Unit/Descriptor_Unit_LightRifles_RCL_US, 396),
        ($/GFX/Unit/Descriptor_Unit_Rangers_CMD_US, 397),
        ($/GFX/Unit/Descriptor_Unit_M151_MUTT_trans_US, 398),
        ($/GFX/Unit/Descriptor_Unit_AeroRifles_CMD_US, 399),
        ($/GFX/Unit/Descriptor_Unit_GreenBerets_CMD_US, 400),
        ($/GFX/Unit/Descriptor_Unit_AMX_10_RCR_FR, 404),
        ($/GFX/Unit/Descriptor_Unit_F4_Phantom_GR2_UK, 405),
        ($/GFX/Unit/Descriptor_Unit_2K12_KUB_SOV, 406),
        ($/GFX/Unit/Descriptor_Unit_BMP_1P_reco_SOV, 407),
        ($/GFX/Unit/Descriptor_Unit_BMP_1_CMD_SOV, 408),
        ($/GFX/Unit/Descriptor_Unit_BRDM_2_CMD_SOV, 409),
        ($/GFX/Unit/Descriptor_Unit_BRDM_2_SOV, 410),
        ($/GFX/Unit/Descriptor_Unit_BRDM_Strela_1_SOV, 411),
        ($/GFX/Unit/Descriptor_Unit_BRM_1_SOV, 412),
        ($/GFX/Unit/Descriptor_Unit_BTR_60_CMD_SOV, 413),
        ($/GFX/Unit/Descriptor_Unit_BTR_60_SOV, 414),
        ($/GFX/Unit/Descriptor_Unit_Engineers_CMD_SOV, 415),
        ($/GFX/Unit/Descriptor_Unit_MTLB_supply_SOV, 416),
        ($/GFX/Unit/Descriptor_Unit_MTLB_transp_SOV, 417),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_CMD_SOV, 418),
        ($/GFX/Unit/Descriptor_Unit_Spetsnaz_CMD_SOV, 419),
        ($/GFX/Unit/Descriptor_Unit_T64BV_CMD_SOV, 420),
        ($/GFX/Unit/Descriptor_Unit_T80BV_CMD_SOV, 421),
        ($/GFX/Unit/Descriptor_Unit_T80B_CMD_SOV, 422),
        ($/GFX/Unit/Descriptor_Unit_T80U_CMD_SOV, 423),
        ($/GFX/Unit/Descriptor_Unit_TO_55_SOV, 424),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_Reco_SOV, 425),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_SOV, 426),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_SPG9_SOV, 427),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_supply_SOV, 428),
        ($/GFX/Unit/Descriptor_Unit_VDV_CMD_SOV, 429),
        ($/GFX/Unit/Descriptor_Unit_Engineers_CMD_VDV_SOV, 430),
        ($/GFX/Unit/Descriptor_Unit_AH64_Apache_emp1_US, 431),
        ($/GFX/Unit/Descriptor_Unit_AH64_Apache_emp2_US, 432),
        ($/GFX/Unit/Descriptor_Unit_F117_Nighthawk_US, 435),
        ($/GFX/Unit/Descriptor_Unit_F111E_Aardvark_CBU_US, 437),
        ($/GFX/Unit/Descriptor_Unit_F111E_Aardvark_US, 438),
        ($/GFX/Unit/Descriptor_Unit_F111E_Aardvark_napalm_US, 439),
        ($/GFX/Unit/Descriptor_Unit_F4E_Phantom_II_AA_US, 441),
        ($/GFX/Unit/Descriptor_Unit_F4E_Phantom_II_CBU_US, 442),
        ($/GFX/Unit/Descriptor_Unit_F4_Wild_Weasel_US, 443),
        ($/GFX/Unit/Descriptor_Unit_F4E_Phantom_II_napalm_US, 444),
        ($/GFX/Unit/Descriptor_Unit_M270_MLRS_cluster_US, 445),
        ($/GFX/Unit/Descriptor_Unit_F111F_Aardvark_CBU_US, 447),
        ($/GFX/Unit/Descriptor_Unit_F111F_Aardvark_US, 448),
        ($/GFX/Unit/Descriptor_Unit_F111F_Aardvark_napalm_US, 449),
        ($/GFX/Unit/Descriptor_Unit_A10_Thunderbolt_II_ATGM_US, 450),
        ($/GFX/Unit/Descriptor_Unit_A10_Thunderbolt_II_US, 451),
        ($/GFX/Unit/Descriptor_Unit_F15C_Eagle_AA_US, 452),
        ($/GFX/Unit/Descriptor_Unit_F16E_HE_US, 456),
        ($/GFX/Unit/Descriptor_Unit_F16E_AGM_US, 457),
        ($/GFX/Unit/Descriptor_Unit_F16E_AA_US, 458),
        ($/GFX/Unit/Descriptor_Unit_F16E_SEAD_US, 459),
        ($/GFX/Unit/Descriptor_Unit_F16E_CBU_US, 460),
        ($/GFX/Unit/Descriptor_Unit_F16E_napalm_US, 461),
        ($/GFX/Unit/Descriptor_Unit_MiG_27M_bombe_SOV, 464),
        ($/GFX/Unit/Descriptor_Unit_MiG_27M_napalm_SOV, 465),
        ($/GFX/Unit/Descriptor_Unit_MiG_27M_rkt_SOV, 466),
        ($/GFX/Unit/Descriptor_Unit_MiG_27M_sead_SOV, 467),
        ($/GFX/Unit/Descriptor_Unit_MiG_29_AA_SOV, 476),
        ($/GFX/Unit/Descriptor_Unit_Su_24M_clu_SOV, 481),
        ($/GFX/Unit/Descriptor_Unit_Su_24M_nplm_SOV, 482),
        ($/GFX/Unit/Descriptor_Unit_Su_25_clu_SOV, 483),
        ($/GFX/Unit/Descriptor_Unit_Su_25_he_SOV, 484),
        ($/GFX/Unit/Descriptor_Unit_Su_25_nplm_SOV, 485),
        ($/GFX/Unit/Descriptor_Unit_Su_25_rkt_SOV, 486),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TV_SOV, 487),
        ($/GFX/Unit/Descriptor_Unit_MTLB_CMD_SOV, 488),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TZ_SOV, 489),
        ($/GFX/Unit/Descriptor_Unit_Mi_8K_CMD_SOV, 490),
        ($/GFX/Unit/Descriptor_Unit_Mi_8R_SOV, 491),
        ($/GFX/Unit/Descriptor_Unit_F4E_Phantom_II_HE_US, 492),
        ($/GFX/Unit/Descriptor_Unit_GAZ_66_SOV, 493),
        ($/GFX/Unit/Descriptor_Unit_M1A1HA_Abrams_CMD_US, 494),
        ($/GFX/Unit/Descriptor_Unit_Jager_CMD_RFA, 495),
        ($/GFX/Unit/Descriptor_Unit_KanJagdPanzer_RFA, 496),
        ($/GFX/Unit/Descriptor_Unit_M1_Abrams_CMD_US, 497),
        ($/GFX/Unit/Descriptor_Unit_Unimog_trans_RFA, 498),
        ($/GFX/Unit/Descriptor_Unit_Iltis_trans_RFA, 499),
        ($/GFX/Unit/Descriptor_Unit_M113A1G_RFA, 500),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_SPG9_DDR, 501),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_trans_DDR, 502),
        ($/GFX/Unit/Descriptor_Unit_W50_LA_A_DDR, 503),
        ($/GFX/Unit/Descriptor_Unit_Grenzer_CMD_DDR, 504),
        ($/GFX/Unit/Descriptor_Unit_BM27_Uragan_SOV, 506),
        ($/GFX/Unit/Descriptor_Unit_Alpha_Jet_A_clu_RFA, 507),
        ($/GFX/Unit/Descriptor_Unit_Alpha_Jet_A_he_RFA, 508),
        ($/GFX/Unit/Descriptor_Unit_Alpha_Jet_A_nplm_RFA, 509),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TV_s80_SOV, 511),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TV_s57_16_SOV, 512),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TV_s57_32_SOV, 513),
        ($/GFX/Unit/Descriptor_Unit_Mi_24V_AT_SOV, 514),
        ($/GFX/Unit/Descriptor_Unit_Engineers_CMD_RFA, 515),
        ($/GFX/Unit/Descriptor_Unit_Fallschirmjager_CMD_DDR, 516),
        ($/GFX/Unit/Descriptor_Unit_Fallschirmjager_CMD_RFA, 517),
        ($/GFX/Unit/Descriptor_Unit_Fallschirmjager_FalseFlag_CMD_DDR, 518),
        ($/GFX/Unit/Descriptor_Unit_Fallschirmjager_FlaseFlag_Demo_DDR, 519),
        ($/GFX/Unit/Descriptor_Unit_Fallschirmjager_Scout_RFA, 520),
        ($/GFX/Unit/Descriptor_Unit_KdA_CMD_DDR, 522),
        ($/GFX/Unit/Descriptor_Unit_KdA_DDR, 523),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_CMD_DDR, 524),
        ($/GFX/Unit/Descriptor_Unit_Panzergrenadier_CMD_RFA, 525),
        ($/GFX/Unit/Descriptor_Unit_Scout_LRRP_DDR, 528),
        ($/GFX/Unit/Descriptor_Unit_Wachregiment_CMD_DDR, 529),
        ($/GFX/Unit/Descriptor_Unit_Wachregiment_DDR, 530),
        ($/GFX/Unit/Descriptor_Unit_Wachregiment_SMG_DDR, 531),
        ($/GFX/Unit/Descriptor_Unit_Mi_24V_AA_SOV, 532),
        ($/GFX/Unit/Descriptor_Unit_Mi_24V_RKT_SOV, 533),
        ($/GFX/Unit/Descriptor_Unit_Su_22_clu_DDR, 534),
        ($/GFX/Unit/Descriptor_Unit_Su_22_nplm_DDR, 535),
        ($/GFX/Unit/Descriptor_Unit_BM21_Grad_DDR, 536),
        ($/GFX/Unit/Descriptor_Unit_BMP_1P_reco_DDR, 537),
        ($/GFX/Unit/Descriptor_Unit_BMP_1_CMD_DDR, 538),
        ($/GFX/Unit/Descriptor_Unit_BRDM_2_CMD_DDR, 539),
        ($/GFX/Unit/Descriptor_Unit_BRDM_2_DDR, 540),
        ($/GFX/Unit/Descriptor_Unit_BTR_50_CMD_DDR, 541),
        ($/GFX/Unit/Descriptor_Unit_BTR_50_DDR, 542),
        ($/GFX/Unit/Descriptor_Unit_BTR_60_CMD_DDR, 543),
        ($/GFX/Unit/Descriptor_Unit_BTR_60_DDR, 544),
        ($/GFX/Unit/Descriptor_Unit_Engineers_CMD_DDR, 545),
        ($/GFX/Unit/Descriptor_Unit_FireSupport_SOV, 546),
        ($/GFX/Unit/Descriptor_Unit_Iltis_MILAN_RFA, 547),
        ($/GFX/Unit/Descriptor_Unit_LRRP_US, 549),
        ($/GFX/Unit/Descriptor_Unit_Leopard_1A1_CMD_RFA, 550),
        ($/GFX/Unit/Descriptor_Unit_Leopard_1A5_CMD_RFA, 551),
        ($/GFX/Unit/Descriptor_Unit_Leopard_1A5_RFA, 552),
        ($/GFX/Unit/Descriptor_Unit_Leopard_2A3_CMD_RFA, 553),
        ($/GFX/Unit/Descriptor_Unit_Leopard_2A3_RFA, 554),
        ($/GFX/Unit/Descriptor_Unit_M109A3G_HOWZ_RFA, 555),
        ($/GFX/Unit/Descriptor_Unit_M110A2_Howz_RFA, 556),
        ($/GFX/Unit/Descriptor_Unit_M113A1G_reco_RFA, 557),
        ($/GFX/Unit/Descriptor_Unit_M113A1G_supply_RFA, 558),
        ($/GFX/Unit/Descriptor_Unit_M113_PzMorser_RFA, 559),
        ($/GFX/Unit/Descriptor_Unit_M48A2C_RFA, 560),
        ($/GFX/Unit/Descriptor_Unit_M577_RFA, 561),
        ($/GFX/Unit/Descriptor_Unit_MTLB_Strela10_DDR, 562),
        ($/GFX/Unit/Descriptor_Unit_MTLB_supply_DDR, 563),
        ($/GFX/Unit/Descriptor_Unit_MTLB_trans_DDR, 564),
        ($/GFX/Unit/Descriptor_Unit_Marder_1A2_MILAN_RFA, 565),
        ($/GFX/Unit/Descriptor_Unit_Marder_1A2_RFA, 566),
        ($/GFX/Unit/Descriptor_Unit_Marder_1A3_MILAN_RFA, 567),
        ($/GFX/Unit/Descriptor_Unit_Marder_1A3_RFA, 568),
        ($/GFX/Unit/Descriptor_Unit_Marder_Roland_RFA, 569),
        ($/GFX/Unit/Descriptor_Unit_T55AM2B_DDR, 570),
        ($/GFX/Unit/Descriptor_Unit_T55AM2_CMD_DDR, 571),
        ($/GFX/Unit/Descriptor_Unit_T55AM2_DDR, 572),
        ($/GFX/Unit/Descriptor_Unit_T55A_CMD_DDR, 573),
        ($/GFX/Unit/Descriptor_Unit_T55A_DDR, 574),
        ($/GFX/Unit/Descriptor_Unit_T72M_CMD_DDR, 575),
        ($/GFX/Unit/Descriptor_Unit_T72M_DDR, 576),
        ($/GFX/Unit/Descriptor_Unit_TPZ_Fuchs_CMD_RFA, 577),
        ($/GFX/Unit/Descriptor_Unit_TPZ_Fuchs_MILAN_RFA, 578),
        ($/GFX/Unit/Descriptor_Unit_Mi_24P_SOV, 579),
        ($/GFX/Unit/Descriptor_Unit_F4F_Phantom_II_AA_RFA, 580),
        ($/GFX/Unit/Descriptor_Unit_ERC_90_Sagaie_FR, 611),
        ($/GFX/Unit/Descriptor_Unit_ZSU_57_2_DDR, 612),
        ($/GFX/Unit/Descriptor_Unit_MAN_Kat_6x6_RFA, 613),
        ($/GFX/Unit/Descriptor_Unit_Rifles_half_AT4_US, 614),
        ($/GFX/Unit/Descriptor_Unit_Alpha_Jet_A_rkt_RFA, 615),
        ($/GFX/Unit/Descriptor_Unit_G91_R3_Gina_HE_RFA, 617),
        ($/GFX/Unit/Descriptor_Unit_G91_R3_Gina_NPL_RFA, 618),
        ($/GFX/Unit/Descriptor_Unit_G91_R3_Gina_RKT_RFA, 619),
        ($/GFX/Unit/Descriptor_Unit_Su_22_RKT_DDR, 620),
        ($/GFX/Unit/Descriptor_Unit_BMP_1_SP1_DDR, 621),
        ($/GFX/Unit/Descriptor_Unit_Engineers_AT_RFA, 622),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Flam_RFA, 623),
        ($/GFX/Unit/Descriptor_Unit_FV102_Striker_BEL, 624),
        ($/GFX/Unit/Descriptor_Unit_FV102_Striker_UK, 625),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_1_RFA, 627),
        ($/GFX/Unit/Descriptor_Unit_M270_MLRS_RFA, 628),
        ($/GFX/Unit/Descriptor_Unit_M48A2GA2_RFA, 629),
        ($/GFX/Unit/Descriptor_Unit_Marder_Roland_2_RFA, 630),
        ($/GFX/Unit/Descriptor_Unit_Scout_BEL, 631),
        ($/GFX/Unit/Descriptor_Unit_T72M1_DDR, 632),
        ($/GFX/Unit/Descriptor_Unit_TO_55_DDR, 633),
        ($/GFX/Unit/Descriptor_Unit_Wiesel_20mm_RFA, 634),
        ($/GFX/Unit/Descriptor_Unit_Wiesel_TOW_RFA, 636),
        ($/GFX/Unit/Descriptor_Unit_Alouette_II_reco_RFA, 637),
        ($/GFX/Unit/Descriptor_Unit_Bo_105_PAH_1A1_RFA, 638),
        ($/GFX/Unit/Descriptor_Unit_Bo_105_PAH_1_RFA, 639),
        ($/GFX/Unit/Descriptor_Unit_Mi_24D_s5_AT_DDR, 640),
        ($/GFX/Unit/Descriptor_Unit_Mi_24P_s8_AT_DDR, 641),
        ($/GFX/Unit/Descriptor_Unit_Mi_2_reco_DDR, 642),
        ($/GFX/Unit/Descriptor_Unit_Mi_2_trans_DDR, 643),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TV_DDR, 644),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TV_PodGatling_DDR, 645),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TV_PodGatling_PodAGL_SOV, 646),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TV_s57_32_DDR, 647),
        ($/GFX/Unit/Descriptor_Unit_Mi_8T_DDR, 648),
        ($/GFX/Unit/Descriptor_Unit_UH1D_RFA, 649),
        ($/GFX/Unit/Descriptor_Unit_F104G_Starfighter_HE_RFA, 650),
        ($/GFX/Unit/Descriptor_Unit_F104G_Starfighter_RFA, 651),
        ($/GFX/Unit/Descriptor_Unit_F4F_Phantom_II_AT_RFA, 652),
        ($/GFX/Unit/Descriptor_Unit_F4F_Phantom_II_HE1_RFA, 653),
        ($/GFX/Unit/Descriptor_Unit_F4F_Phantom_II_HE2_RFA, 654),
        ($/GFX/Unit/Descriptor_Unit_F4F_Phantom_II_RKT2_RFA, 656),
        ($/GFX/Unit/Descriptor_Unit_MiG_21PFM_AA_DDR, 657),
        ($/GFX/Unit/Descriptor_Unit_MiG_21bis_HE_DDR, 658),
        ($/GFX/Unit/Descriptor_Unit_MiG_23BN_AT_DDR, 659),
        ($/GFX/Unit/Descriptor_Unit_MiG_29_AA_DDR, 660),
        ($/GFX/Unit/Descriptor_Unit_Su_22_AT_DDR, 661),
        ($/GFX/Unit/Descriptor_Unit_Su_22_SEAD_DDR, 662),
        ($/GFX/Unit/Descriptor_Unit_Tornado_IDS_AT1_RFA, 663),
        ($/GFX/Unit/Descriptor_Unit_Tornado_IDS_CLUS_RFA, 665),
        ($/GFX/Unit/Descriptor_Unit_Tornado_IDS_HE1_RFA, 666),
        ($/GFX/Unit/Descriptor_Unit_Tornado_IDS_MW1_RFA, 668),
        ($/GFX/Unit/Descriptor_Unit_Tornado_ADV_SEAD_UK, 707),
        ($/GFX/Unit/Descriptor_Unit_Harrier_RKT2_UK, 708),
        ($/GFX/Unit/Descriptor_Unit_TPZ_Fuchs_RASIT_RFA, 709),
        ($/GFX/Unit/Descriptor_Unit_M113_GreenArcher_RFA, 711),
        ($/GFX/Unit/Descriptor_Unit_M113_GreenArcher_UK, 712),
        ($/GFX/Unit/Descriptor_Unit_T813_trans_DDR, 713),
        ($/GFX/Unit/Descriptor_Unit_AH1F_ATAS_US, 714),
        ($/GFX/Unit/Descriptor_Unit_F104G_Starfighter_AT_RFA, 715),
        ($/GFX/Unit/Descriptor_Unit_MiG_21bis_AA2_DDR, 716),
        ($/GFX/Unit/Descriptor_Unit_Mi_2_trans_SOV, 717),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Fagot_FJ_DDR, 718),
        ($/GFX/Unit/Descriptor_Unit_ATteam_TOW_US, 719),
        ($/GFX/Unit/Descriptor_Unit_BMP_1PG_SOV, 721),
        ($/GFX/Unit/Descriptor_Unit_DShV_CMD_SOV, 752),
        ($/GFX/Unit/Descriptor_Unit_DShV_SOV, 753),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Fagot_DDR, 754),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Fagot_SOV, 755),
        ($/GFX/Unit/Descriptor_Unit_ATteam_ITOW_US, 756),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Konkurs_DDR, 757),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Konkurs_SOV, 758),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_1_FR, 759),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_1_RFA, 760),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_2_RFA, 761),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_2_para_RFA, 762),
        ($/GFX/Unit/Descriptor_Unit_ATteam_RCL_M40A1_RFA, 763),
        ($/GFX/Unit/Descriptor_Unit_ATteam_RCL_SPG9_DDR, 764),
        ($/GFX/Unit/Descriptor_Unit_ATteam_RCL_SPG9_FJ_DDR, 765),
        ($/GFX/Unit/Descriptor_Unit_ATteam_TOW2_US, 766),
        ($/GFX/Unit/Descriptor_Unit_ATteam_TOW2_para_US, 767),
        ($/GFX/Unit/Descriptor_Unit_BMP_1P_DDR, 768),
        ($/GFX/Unit/Descriptor_Unit_FV101_Scorpion_BEL, 769),
        ($/GFX/Unit/Descriptor_Unit_FV103_Spartan_BEL, 770),
        ($/GFX/Unit/Descriptor_Unit_FV105_Sultan_BEL, 771),
        ($/GFX/Unit/Descriptor_Unit_FV107_Scimitar_BEL, 772),
        ($/GFX/Unit/Descriptor_Unit_MAN_Kat_6x6_trans_RFA, 773),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_HMG_DDR, 774),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_HMG_SOV, 775),
        ($/GFX/Unit/Descriptor_Unit_PT76B_DDR, 776),
        ($/GFX/Unit/Descriptor_Unit_Rifles_HMG_US, 777),
        ($/GFX/Unit/Descriptor_Unit_VAB_RASIT_FR, 780),
        ($/GFX/Unit/Descriptor_Unit_AH1F_HeavyHog_US, 781),
        ($/GFX/Unit/Descriptor_Unit_Alouette_II_CMD_RFA, 782),
        ($/GFX/Unit/Descriptor_Unit_Bo_105_CMD_RFA, 783),
        ($/GFX/Unit/Descriptor_Unit_Bo_105_reco_RFA, 784),
        ($/GFX/Unit/Descriptor_Unit_Bo_105_trans_RFA, 785),
        ($/GFX/Unit/Descriptor_Unit_CH53G_trans_RFA, 786),
        ($/GFX/Unit/Descriptor_Unit_Mi_24D_AA_DDR, 787),
        ($/GFX/Unit/Descriptor_Unit_Mi_24D_s8_AT_DDR, 788),
        ($/GFX/Unit/Descriptor_Unit_Mi_24P_s8_AT2_DDR, 789),
        ($/GFX/Unit/Descriptor_Unit_Mi_2_reco_SOV, 790),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TB_DDR, 791),
        ($/GFX/Unit/Descriptor_Unit_Mi_8_supply_DDR, 792),
        ($/GFX/Unit/Descriptor_Unit_Mi_9_DDR, 793),
        ($/GFX/Unit/Descriptor_Unit_UH1D_Supply_RFA, 794),
        ($/GFX/Unit/Descriptor_Unit_MiG_23MF_AA_DDR, 795),
        ($/GFX/Unit/Descriptor_Unit_MiG_23ML_DDR, 796),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_1_UK, 797),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_2_FR, 798),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_2_UK, 799),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_2_para_FR, 800),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_2_para_UK, 801),
        ($/GFX/Unit/Descriptor_Unit_ATteam_RCL_SPG9_VDV_SOV, 802),
        ($/GFX/Unit/Descriptor_Unit_Airborne_Engineer_CMD_US, 803),
        ($/GFX/Unit/Descriptor_Unit_Airborne_Engineers_US, 804),
        ($/GFX/Unit/Descriptor_Unit_Airborne_MP_RCL_US, 805),
        ($/GFX/Unit/Descriptor_Unit_Airborne_MP_US, 806),
        ($/GFX/Unit/Descriptor_Unit_Atteam_Fagot_VDV_SOV, 807),
        ($/GFX/Unit/Descriptor_Unit_Atteam_Konkurs_VDV_SOV, 808),
        ($/GFX/Unit/Descriptor_Unit_BM21V_GradV_SOV, 809),
        ($/GFX/Unit/Descriptor_Unit_FH70_155mm_RFA, 810),
        ($/GFX/Unit/Descriptor_Unit_T62M1_SOV, 811),
        ($/GFX/Unit/Descriptor_Unit_T62MV_SOV, 812),
        ($/GFX/Unit/Descriptor_Unit_T62M_CMD_SOV, 813),
        ($/GFX/Unit/Descriptor_Unit_T62M_SOV, 814),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_AGL_VDV_SOV, 815),
        ($/GFX/Unit/Descriptor_Unit_A10_Thunderbolt_II_Rkt_US, 816),
        ($/GFX/Unit/Descriptor_Unit_2S9_Nona_SOV, 817),
        ($/GFX/Unit/Descriptor_Unit_AT_D44_85mm_DDR, 818),
        ($/GFX/Unit/Descriptor_Unit_AT_D44_85mm_VDV_SOV, 820),
        ($/GFX/Unit/Descriptor_Unit_AT_T12_Rapira_DDR, 821),
        ($/GFX/Unit/Descriptor_Unit_AT_T12_Rapira_SOV, 822),
        ($/GFX/Unit/Descriptor_Unit_Airborne_CMD_US, 823),
        ($/GFX/Unit/Descriptor_Unit_Airborne_Dragon_US, 824),
        ($/GFX/Unit/Descriptor_Unit_Airborne_HMG_US, 825),
        ($/GFX/Unit/Descriptor_Unit_Airborne_Scout_US, 826),
        ($/GFX/Unit/Descriptor_Unit_Airborne_US, 827),
        ($/GFX/Unit/Descriptor_Unit_BMD_1_CMD_SOV, 828),
        ($/GFX/Unit/Descriptor_Unit_BMD_1_SOV, 829),
        ($/GFX/Unit/Descriptor_Unit_BMD_2_SOV, 830),
        ($/GFX/Unit/Descriptor_Unit_BTR_D_Robot_SOV, 831),
        ($/GFX/Unit/Descriptor_Unit_BTR_D_SOV, 832),
        ($/GFX/Unit/Descriptor_Unit_BTR_ZD_Skrezhet_SOV, 833),
        ($/GFX/Unit/Descriptor_Unit_DCA_FK20_2_20mm_Zwillinge_RFA, 834),
        ($/GFX/Unit/Descriptor_Unit_DCA_I_Hawk_RFA, 835),
        ($/GFX/Unit/Descriptor_Unit_DCA_I_Hawk_US, 836),
        ($/GFX/Unit/Descriptor_Unit_DCA_M167_Vulcan_20mm_US, 837),
        ($/GFX/Unit/Descriptor_Unit_DCA_ZU_23_2_SOV, 838),
        ($/GFX/Unit/Descriptor_Unit_Fallschirmjager_HMG_DDR, 839),
        ($/GFX/Unit/Descriptor_Unit_GAZ_66B_SOV, 840),
        ($/GFX/Unit/Descriptor_Unit_GAZ_66B_ZU_SOV, 841),
        ($/GFX/Unit/Descriptor_Unit_Howz_D20_152mm_DDR, 842),
        ($/GFX/Unit/Descriptor_Unit_Howz_D20_152mm_SOV, 843),
        ($/GFX/Unit/Descriptor_Unit_Howz_D30_122mm_DDR, 844),
        ($/GFX/Unit/Descriptor_Unit_Howz_D30_122mm_SOV, 845),
        ($/GFX/Unit/Descriptor_Unit_Howz_D30_122mm_VDV_SOV, 846),
        ($/GFX/Unit/Descriptor_Unit_Howz_M102_105mm_US, 847),
        ($/GFX/Unit/Descriptor_Unit_Howz_M46_130mm_DDR, 848),
        ($/GFX/Unit/Descriptor_Unit_M1025_Humvee_CMD_para_US, 849),
        ($/GFX/Unit/Descriptor_Unit_M1025_Humvee_TOW_para_US, 850),
        ($/GFX/Unit/Descriptor_Unit_M551A1_TTS_Sheridan_CMD_US, 851),
        ($/GFX/Unit/Descriptor_Unit_M551A1_TTS_Sheridan_US, 852),
        ($/GFX/Unit/Descriptor_Unit_M998_Avenger_US, 853),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Stinger_C_para_US, 854),
        ($/GFX/Unit/Descriptor_Unit_MTLB_Vasilek_SOV, 855),
        ($/GFX/Unit/Descriptor_Unit_Mortier_107mm_Airborne_US, 856),
        ($/GFX/Unit/Descriptor_Unit_Mortier_2B9_Vasilek_SOV, 857),
        ($/GFX/Unit/Descriptor_Unit_Mortier_2S12_120mm_SOV, 858),
        ($/GFX/Unit/Descriptor_Unit_Mortier_2S12_120mm_VDV_SOV, 859),
        ($/GFX/Unit/Descriptor_Unit_Mortier_PM43_120mm_DDR, 860),
        ($/GFX/Unit/Descriptor_Unit_Mortier_Tampella_120mm_RFA, 861),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_CMD_VDV_SOV, 862),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_Konkurs_VDV_SOV, 863),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_SPG9_VDV_SOV, 864),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_supply_VDV_SOV, 865),
        ($/GFX/Unit/Descriptor_Unit_VDV_HMG_SOV, 866),
        ($/GFX/Unit/Descriptor_Unit_Mi_24K_reco_SOV, 867),
        ($/GFX/Unit/Descriptor_Unit_MiG_21bis_RKT2_DDR, 869),
        ($/GFX/Unit/Descriptor_Unit_MiG_25RBF_SOV, 870),
        ($/GFX/Unit/Descriptor_Unit_Howz_M101_105mm_RFA, 871),
        ($/GFX/Unit/Descriptor_Unit_M48A2GA2_CMD_RFA, 872),
        ($/GFX/Unit/Descriptor_Unit_PT76B_CMD_DDR, 873),
        ($/GFX/Unit/Descriptor_Unit_Bedford_MJ_4t_UK, 874),
        ($/GFX/Unit/Descriptor_Unit_Howz_L118_105mm_UK, 875),
        ($/GFX/Unit/Descriptor_Unit_DCA_ZU_23_2_DDR, 876),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_1_para_FR, 877),
        ($/GFX/Unit/Descriptor_Unit_Airborne_Engineers_Flash_US, 878),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Reserve_RFA, 879),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_SPG9_FJ_DDR, 882),
        ($/GFX/Unit/Descriptor_Unit_Tornado_ADV_HE_UK, 883),
        ($/GFX/Unit/Descriptor_Unit_Tornado_ADV_clu_UK, 884),
        ($/GFX/Unit/Descriptor_Unit_Bofors_40mm_RFA, 885),
        ($/GFX/Unit/Descriptor_Unit_DCA_FK20_2_20mm_RFA, 886),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_AANF1_FR, 887),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_AGS17_SOV, 888),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_KPVT_SOV, 889),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M2HB_US, 890),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M60_US, 891),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_MAG_UK, 892),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_MG3_RFA, 893),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_Mk19_US, 894),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_PKM_DDR, 895),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_PKM_SOV, 896),
        ($/GFX/Unit/Descriptor_Unit_Rifles_Aero_CMD_FR, 898),
        ($/GFX/Unit/Descriptor_Unit_Rifles_Aero_FR, 899),
        ($/GFX/Unit/Descriptor_Unit_Rifles_CMD_FR, 900),
        ($/GFX/Unit/Descriptor_Unit_T34_85M_DDR, 901),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_Fagot_DDR, 902),
        ($/GFX/Unit/Descriptor_Unit_VLTT_P4_MILAN_FR, 903),
        ($/GFX/Unit/Descriptor_Unit_VLTT_P4_MILAN_para_FR, 904),
        ($/GFX/Unit/Descriptor_Unit_Volkspolizei_CMD_DDR, 905),
        ($/GFX/Unit/Descriptor_Unit_Volkspolizei_DDR, 906),
        ($/GFX/Unit/Descriptor_Unit_Gazelle_20mm_reco_FR, 907),
        ($/GFX/Unit/Descriptor_Unit_Gazelle_HOT_2_FR, 908),
        ($/GFX/Unit/Descriptor_Unit_Gazelle_reco_FR, 909),
        ($/GFX/Unit/Descriptor_Unit_Ka_50_AA_SOV, 910),
        ($/GFX/Unit/Descriptor_Unit_Mirage_5_F_clu_FR, 911),
        ($/GFX/Unit/Descriptor_Unit_Su_17M4_SOV, 912),
        ($/GFX/Unit/Descriptor_Unit_Fallschirm_B1_RFA, 913),
        ($/GFX/Unit/Descriptor_Unit_PT76B_tank_DDR, 914),
        ($/GFX/Unit/Descriptor_Unit_F111F_Aardvark_LGB2_US, 915),
        ($/GFX/Unit/Descriptor_Unit_F111F_Aardvark_LGB_US, 916),
        ($/GFX/Unit/Descriptor_Unit_F15C_Eagle_AA2_US, 1036),
        ($/GFX/Unit/Descriptor_Unit_2S5_GiatsintS_SOV, 1037),
        ($/GFX/Unit/Descriptor_Unit_M1038_Humvee_US, 1039),
        ($/GFX/Unit/Descriptor_Unit_MFRW_RM70_cluster_DDR, 1040),
        ($/GFX/Unit/Descriptor_Unit_T72M1_CMD_DDR, 1041),
        ($/GFX/Unit/Descriptor_Unit_T80BV_Beast_SOV, 1042),
        ($/GFX/Unit/Descriptor_Unit_T80UD_SOV, 1043),
        ($/GFX/Unit/Descriptor_Unit_AMX_10_PC_CMD_FR, 1044),
        ($/GFX/Unit/Descriptor_Unit_AMX_10_P_VOA_FR, 1045),
        ($/GFX/Unit/Descriptor_Unit_AMX_30_B2_Brennus_FR, 1046),
        ($/GFX/Unit/Descriptor_Unit_AMX_30_B2_CMD_FR, 1047),
        ($/GFX/Unit/Descriptor_Unit_AMX_30_EBG_FR, 1048),
        ($/GFX/Unit/Descriptor_Unit_Chasseurs_CMD_FR, 1049),
        ($/GFX/Unit/Descriptor_Unit_Chasseurs_FR, 1050),
        ($/GFX/Unit/Descriptor_Unit_Commandos_FR, 1051),
        ($/GFX/Unit/Descriptor_Unit_Escorte_FR, 1052),
        ($/GFX/Unit/Descriptor_Unit_Gendarmerie_FR, 1053),
        ($/GFX/Unit/Descriptor_Unit_Groupe_AT_FR, 1054),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_AANF1_para_FR, 1055),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_AGS17_DDR, 1056),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_AGS17_VDV_SOV, 1057),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M2HB_AB_US, 1058),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M2HB_FR, 1059),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M2HB_para_FR, 1060),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M2HB_para_UK, 1061),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M60_AB_US, 1062),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_MAG_para_UK, 1063),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_MG3_FJ_RFA, 1064),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_Mk19_AB_US, 1065),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_NSV_DDR, 1066),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_NSV_SOV, 1067),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_PKM_FJ_DDR, 1068),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_PKM_VDV_SOV, 1069),
        ($/GFX/Unit/Descriptor_Unit_Leopard_2A4_RFA, 1070),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Mistral_FR, 1071),
        ($/GFX/Unit/Descriptor_Unit_Mortier_MORT61_120mm_FR, 1072),
        ($/GFX/Unit/Descriptor_Unit_Reserviste_FR, 1073),
        ($/GFX/Unit/Descriptor_Unit_Rifles_APILAS_FR, 1074),
        ($/GFX/Unit/Descriptor_Unit_Roland_3_FR, 1075),
        ($/GFX/Unit/Descriptor_Unit_Sapeurs_CMD_FR, 1076),
        ($/GFX/Unit/Descriptor_Unit_Sapeurs_FR, 1077),
        ($/GFX/Unit/Descriptor_Unit_Sapeurs_Flam_FR, 1078),
        ($/GFX/Unit/Descriptor_Unit_Scout_FR, 1079),
        ($/GFX/Unit/Descriptor_Unit_TRM_2000_20mm_FR, 1080),
        ($/GFX/Unit/Descriptor_Unit_TRM_2000_supply_FR, 1081),
        ($/GFX/Unit/Descriptor_Unit_VAB_CMD_FR, 1082),
        ($/GFX/Unit/Descriptor_Unit_VAB_Mortar_81_FR, 1083),
        ($/GFX/Unit/Descriptor_Unit_VAB_T20_FR, 1084),
        ($/GFX/Unit/Descriptor_Unit_VLRA_HMG_FR, 1085),
        ($/GFX/Unit/Descriptor_Unit_VLTT_P4_PC_FR, 1086),
        ($/GFX/Unit/Descriptor_Unit_Alouette_III_reco_FR, 1087),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_HE_FR, 1088),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_SEAD_FR, 1089),
        ($/GFX/Unit/Descriptor_Unit_Mirage_5_F_nplm_FR, 1090),
        ($/GFX/Unit/Descriptor_Unit_Bedford_MJ_4t_trans_UK, 1091),
        ($/GFX/Unit/Descriptor_Unit_Challenger_1_Mk1_CMD_UK, 1092),
        ($/GFX/Unit/Descriptor_Unit_DCA_Javelin_LML_UK, 1093),
        ($/GFX/Unit/Descriptor_Unit_Engineers_AT_UK, 1094),
        ($/GFX/Unit/Descriptor_Unit_Engineers_CMD_UK, 1095),
        ($/GFX/Unit/Descriptor_Unit_Engineers_UK, 1096),
        ($/GFX/Unit/Descriptor_Unit_FV120_Spartan_MCT_UK, 1097),
        ($/GFX/Unit/Descriptor_Unit_FV4003_Centurion_AVRE_UK, 1098),
        ($/GFX/Unit/Descriptor_Unit_FV4201_Chieftain_Mk11_CMD_UK, 1099),
        ($/GFX/Unit/Descriptor_Unit_FV4201_Chieftain_Mk11_UK, 1100),
        ($/GFX/Unit/Descriptor_Unit_FV4201_Chieftain_Mk9_UK, 1101),
        ($/GFX/Unit/Descriptor_Unit_FV432_CMD_UK, 1102),
        ($/GFX/Unit/Descriptor_Unit_FV438_Swingfire_UK, 1103),
        ($/GFX/Unit/Descriptor_Unit_Gun_Group_UK, 1104),
        ($/GFX/Unit/Descriptor_Unit_LRRP_UK, 1105),
        ($/GFX/Unit/Descriptor_Unit_LandRover_CMD_UK, 1106),
        ($/GFX/Unit/Descriptor_Unit_LandRover_UK, 1107),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Javelin_UK, 1108),
        ($/GFX/Unit/Descriptor_Unit_MCV_80_Warrior_MILAN_UK, 1109),
        ($/GFX/Unit/Descriptor_Unit_Rifles_AT_UK, 1110),
        ($/GFX/Unit/Descriptor_Unit_Rifles_CMD_UK, 1111),
        ($/GFX/Unit/Descriptor_Unit_Scout_AT_UK, 1112),
        ($/GFX/Unit/Descriptor_Unit_Scout_UK, 1113),
        ($/GFX/Unit/Descriptor_Unit_TUTO_M1025_Humvee_US, 1114),
        ($/GFX/Unit/Descriptor_Unit_VBL_MILAN_FR, 1115),
        ($/GFX/Unit/Descriptor_Unit_VBL_Reco_FR, 1116),
        ($/GFX/Unit/Descriptor_Unit_CH47D_Chinook_supply_UK, 1117),
        ($/GFX/Unit/Descriptor_Unit_Gazelle_SNEB_UK, 1118),
        ($/GFX/Unit/Descriptor_Unit_Gazelle_trans_UK, 1119),
        ($/GFX/Unit/Descriptor_Unit_Lynx_AH_Mk1_TOW_UK, 1120),
        ($/GFX/Unit/Descriptor_Unit_Lynx_AH_Mk7_I_TOW_UK, 1121),
        ($/GFX/Unit/Descriptor_Unit_Harrier_CLU_UK, 1122),
        ($/GFX/Unit/Descriptor_Unit_Harrier_HE2_UK, 1123),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_HE2_UK, 1124),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_RKT_UK, 1125),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_ATGM_FR, 1126),
        ($/GFX/Unit/Descriptor_Unit_AT_Group_TA_UK, 1127),
        ($/GFX/Unit/Descriptor_Unit_Airmobile_CMD_UK, 1128),
        ($/GFX/Unit/Descriptor_Unit_Airmobile_MILAN_UK, 1129),
        ($/GFX/Unit/Descriptor_Unit_Airmobile_UK, 1130),
        ($/GFX/Unit/Descriptor_Unit_DCA_Rapier_UK, 1131),
        ($/GFX/Unit/Descriptor_Unit_Engineers_TA_UK, 1132),
        ($/GFX/Unit/Descriptor_Unit_Faun_Kraka_20mm_RFA, 1133),
        ($/GFX/Unit/Descriptor_Unit_Faun_Kraka_Log_RFA, 1134),
        ($/GFX/Unit/Descriptor_Unit_Faun_Kraka_TOW_RFA, 1135),
        ($/GFX/Unit/Descriptor_Unit_Gun_Group_TA_UK, 1136),
        ($/GFX/Unit/Descriptor_Unit_LandRover_MILAN_UK, 1137),
        ($/GFX/Unit/Descriptor_Unit_RCL_L6_Wombat_UK, 1138),
        ($/GFX/Unit/Descriptor_Unit_Scout_TA_UK, 1140),
        ($/GFX/Unit/Descriptor_Unit_Territorial_CMD_UK, 1141),
        ($/GFX/Unit/Descriptor_Unit_Puma_PC_FR, 1142),
        ($/GFX/Unit/Descriptor_Unit_Puma_Pirate_FR, 1143),
        ($/GFX/Unit/Descriptor_Unit_F4_Phantom_AA_F3_UK, 1144),
        ($/GFX/Unit/Descriptor_Unit_Harrier_HE1_UK, 1145),
        ($/GFX/Unit/Descriptor_Unit_Harrier_RKT1_UK, 1146),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_CLU_UK, 1147),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_HE1_UK, 1148),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_RKT_FR, 1149),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_SEAD2_FR, 1150),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_clu_FR, 1151),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_nplm_FR, 1152),
        ($/GFX/Unit/Descriptor_Unit_Scout_Airmobile_UK, 1153),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Blowpipe_UK, 1154),
        ($/GFX/Unit/Descriptor_Unit_DCA_53T2_20mm_Para_FR, 1155),
        ($/GFX/Unit/Descriptor_Unit_Groupe_AT_para_FR, 1156),
        ($/GFX/Unit/Descriptor_Unit_Howz_M101_105mm_FR, 1157),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Mistral_para_FR, 1158),
        ($/GFX/Unit/Descriptor_Unit_Mortier_MORT61_120mm_para_FR, 1159),
        ($/GFX/Unit/Descriptor_Unit_OT_62_TOPAS_JOD_POL, 1160),
        ($/GFX/Unit/Descriptor_Unit_OT_62_TOPAS_SPG9_POL, 1161),
        ($/GFX/Unit/Descriptor_Unit_Para_CMD_FR, 1162),
        ($/GFX/Unit/Descriptor_Unit_Para_FR, 1163),
        ($/GFX/Unit/Descriptor_Unit_Para_Legion_FR, 1164),
        ($/GFX/Unit/Descriptor_Unit_Para_Sapeurs_CMD_FR, 1165),
        ($/GFX/Unit/Descriptor_Unit_Para_Sapeurs_FR, 1166),
        ($/GFX/Unit/Descriptor_Unit_Para_Sapeurs_Flam_FR, 1167),
        ($/GFX/Unit/Descriptor_Unit_SAS_FR, 1168),
        ($/GFX/Unit/Descriptor_Unit_Scout_para_FR, 1169),
        ($/GFX/Unit/Descriptor_Unit_Spetsnaz_FireSupport_SOV, 1170),
        ($/GFX/Unit/Descriptor_Unit_VBL_PC_FR, 1171),
        ($/GFX/Unit/Descriptor_Unit_VLRA_20mm_FR, 1172),
        ($/GFX/Unit/Descriptor_Unit_VLRA_MILAN_FR, 1173),
        ($/GFX/Unit/Descriptor_Unit_VLRA_Mistral_FR, 1174),
        ($/GFX/Unit/Descriptor_Unit_VLRA_Mortier81_FR, 1175),
        ($/GFX/Unit/Descriptor_Unit_VLRA_supply_FR, 1176),
        ($/GFX/Unit/Descriptor_Unit_VLRA_trans_FR, 1177),
        ($/GFX/Unit/Descriptor_Unit_ZSU_23_Shilka_Afghan_SOV, 1178),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TV_UPK_DDR, 1179),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TV_non_arme_SOV, 1180),
        ($/GFX/Unit/Descriptor_Unit_Mi_8T_non_arme_DDR, 1181),
        ($/GFX/Unit/Descriptor_Unit_Saxon_CMD_UK, 1182),
        ($/GFX/Unit/Descriptor_Unit_F16C_LGB_US, 1183),
        ($/GFX/Unit/Descriptor_Unit_MiG_27K_LGB_SOV, 1184),
        ($/GFX/Unit/Descriptor_Unit_LandRover_MILAN_Para_UK, 1185),
        ($/GFX/Unit/Descriptor_Unit_Paratroopers_CMD_UK, 1186),
        ($/GFX/Unit/Descriptor_Unit_Paratroopers_MILAN_TA_UK, 1187),
        ($/GFX/Unit/Descriptor_Unit_Paratroopers_TA_UK, 1188),
        ($/GFX/Unit/Descriptor_Unit_DCA_Rapier_FSA_UK, 1189),
        ($/GFX/Unit/Descriptor_Unit_M1025_Humvee_AGL_nonPara_US, 1190),
        ($/GFX/Unit/Descriptor_Unit_AMX_30_B_CMD_FR, 1192),
        ($/GFX/Unit/Descriptor_Unit_AT_ZiS2_57mm_DDR, 1193),
        ($/GFX/Unit/Descriptor_Unit_BMD_1_Reostat_SOV, 1194),
        ($/GFX/Unit/Descriptor_Unit_DCA_53T2_20mm_FR, 1195),
        ($/GFX/Unit/Descriptor_Unit_Engineers_AGI_DDR, 1196),
        ($/GFX/Unit/Descriptor_Unit_FV4201_Chieftain_CMD_UK, 1197),
        ($/GFX/Unit/Descriptor_Unit_Grenzer_Mot_DDR, 1198),
        ($/GFX/Unit/Descriptor_Unit_Howz_M198_155mm_US, 1199),
        ($/GFX/Unit/Descriptor_Unit_Howz_ZiS3_76mm_DDR, 1200),
        ($/GFX/Unit/Descriptor_Unit_LandRover_WOMBAT_UK, 1201),
        ($/GFX/Unit/Descriptor_Unit_LightRifles_CMD_US, 1202),
        ($/GFX/Unit/Descriptor_Unit_PSzH_IV_DDR, 1203),
        ($/GFX/Unit/Descriptor_Unit_Rifles_RAF_UK, 1204),
        ($/GFX/Unit/Descriptor_Unit_Mi_8MTV_SOV, 1205),
        ($/GFX/Unit/Descriptor_Unit_Mirage_IV_SEAD_FR, 1207),
        ($/GFX/Unit/Descriptor_Unit_Su_22_UPK_DDR, 1208),
        ($/GFX/Unit/Descriptor_Unit_OT_65_DDR, 1209),
        ($/GFX/Unit/Descriptor_Unit_Reserve_Polizei_RFA, 1210),
        ($/GFX/Unit/Descriptor_Unit_M2A2_Bradley_Leader_US, 1211),
        ($/GFX/Unit/Descriptor_Unit_Rifles_half_CMD_US, 1213),
        ($/GFX/Unit/Descriptor_Unit_Sonderwagen_4_RFA, 1214),
        ($/GFX/Unit/Descriptor_Unit_Sonderwagen_4_recon_RFA, 1215),
        ($/GFX/Unit/Descriptor_Unit_MiG_29_AA2_SOV, 1216),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Igla_DDR, 1217),
        ($/GFX/Unit/Descriptor_Unit_2K12_KUB_POL, 1218),
        ($/GFX/Unit/Descriptor_Unit_2S19_MstaS_SOV, 1220),
        ($/GFX/Unit/Descriptor_Unit_2S1_POL, 1221),
        ($/GFX/Unit/Descriptor_Unit_81mm_mortar_Para_UK, 1224),
        ($/GFX/Unit/Descriptor_Unit_81mm_mortar_US, 1225),
        ($/GFX/Unit/Descriptor_Unit_AIFV_B_50_BEL, 1226),
        ($/GFX/Unit/Descriptor_Unit_AIFV_B_50_NL, 1227),
        ($/GFX/Unit/Descriptor_Unit_AIFV_B_C25_BEL, 1228),
        ($/GFX/Unit/Descriptor_Unit_AIFV_B_C25_NL, 1229),
        ($/GFX/Unit/Descriptor_Unit_AIFV_B_CMD_BEL, 1230),
        ($/GFX/Unit/Descriptor_Unit_AIFV_B_CMD_NL, 1231),
        ($/GFX/Unit/Descriptor_Unit_AIFV_B_Cargo_NL, 1232),
        ($/GFX/Unit/Descriptor_Unit_AIFV_B_MILAN_BEL, 1233),
        ($/GFX/Unit/Descriptor_Unit_AIFV_B_Radar_NL, 1234),
        ($/GFX/Unit/Descriptor_Unit_AIFV_B_TOW_NL, 1235),
        ($/GFX/Unit/Descriptor_Unit_AMX_13_mod56_CMD_BEL, 1237),
        ($/GFX/Unit/Descriptor_Unit_AMX_13_mod56_MILAN_BEL, 1239),
        ($/GFX/Unit/Descriptor_Unit_AMX_13_mod56_Mortier_BEL, 1240),
        ($/GFX/Unit/Descriptor_Unit_AMX_13_mod56_VCI_BEL, 1241),
        ($/GFX/Unit/Descriptor_Unit_AT_2A45_SprutB_SOV, 1242),
        ($/GFX/Unit/Descriptor_Unit_AT_D44_85mm_POL, 1243),
        ($/GFX/Unit/Descriptor_Unit_ATteam_ITOW_NL, 1246),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_1_BEL, 1247),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_2_BEL, 1249),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_2_para_BEL, 1250),
        ($/GFX/Unit/Descriptor_Unit_ATteam_RCL_M40A1_FR, 1251),
        ($/GFX/Unit/Descriptor_Unit_ATteam_RCL_SPG9_POL, 1254),
        ($/GFX/Unit/Descriptor_Unit_ATteam_RCL_SPG9_Para_POL, 1255),
        ($/GFX/Unit/Descriptor_Unit_ATteam_TOW_NL, 1257),
        ($/GFX/Unit/Descriptor_Unit_Alvis_Stalwart_UK, 1258),
        ($/GFX/Unit/Descriptor_Unit_Atteam_Fagot_POL, 1260),
        ($/GFX/Unit/Descriptor_Unit_Atteam_Fagot_Para_POL, 1261),
        ($/GFX/Unit/Descriptor_Unit_Atteam_Konkurs_POL, 1262),
        ($/GFX/Unit/Descriptor_Unit_BM21_Grad_POL, 1264),
        ($/GFX/Unit/Descriptor_Unit_BM24M_DDR, 1265),
        ($/GFX/Unit/Descriptor_Unit_BM24M_POL, 1266),
        ($/GFX/Unit/Descriptor_Unit_BMP_1P_reco_POL, 1267),
        ($/GFX/Unit/Descriptor_Unit_BMP_1_CMD_POL, 1268),
        ($/GFX/Unit/Descriptor_Unit_BMP_1_SP2_POL, 1270),
        ($/GFX/Unit/Descriptor_Unit_BMP_2_POL, 1273),
        ($/GFX/Unit/Descriptor_Unit_BRDM_2_CMD_POL, 1275),
        ($/GFX/Unit/Descriptor_Unit_BRDM_2_Konkurs_POL, 1276),
        ($/GFX/Unit/Descriptor_Unit_BRDM_2_Malyu_P_POL, 1278),
        ($/GFX/Unit/Descriptor_Unit_BRDM_2_POL, 1279),
        ($/GFX/Unit/Descriptor_Unit_BRDM_Strela_1_POL, 1280),
        ($/GFX/Unit/Descriptor_Unit_BRM_1_POL, 1281),
        ($/GFX/Unit/Descriptor_Unit_BTR_50_MRF_DDR, 1282),
        ($/GFX/Unit/Descriptor_Unit_Bofors_40mm_capture_DDR, 1284),
        ($/GFX/Unit/Descriptor_Unit_CUCV_Hellfire_US, 1285),
        ($/GFX/Unit/Descriptor_Unit_CUCV_US, 1286),
        ($/GFX/Unit/Descriptor_Unit_Commandos_CMD_POL, 1288),
        ($/GFX/Unit/Descriptor_Unit_Commandos_POL, 1291),
        ($/GFX/Unit/Descriptor_Unit_Crotale_FR, 1292),
        ($/GFX/Unit/Descriptor_Unit_DAF_YA_4400_NL, 1293),
        ($/GFX/Unit/Descriptor_Unit_DAF_YHZ_2300_NL, 1294),
        ($/GFX/Unit/Descriptor_Unit_DANA_POL, 1295),
        ($/GFX/Unit/Descriptor_Unit_DCA_AZP_S60_POL, 1298),
        ($/GFX/Unit/Descriptor_Unit_DCA_Bofors_upgrade_NL, 1300),
        ($/GFX/Unit/Descriptor_Unit_DCA_FASTA_4_DDR, 1301),
        ($/GFX/Unit/Descriptor_Unit_DCA_I_Hawk_BEL, 1303),
        ($/GFX/Unit/Descriptor_Unit_DCA_I_Hawk_NL, 1305),
        ($/GFX/Unit/Descriptor_Unit_DCA_I_Hawk_capture_DDR, 1306),
        ($/GFX/Unit/Descriptor_Unit_DCA_M167_Vulcan_20mm_BEL, 1307),
        ($/GFX/Unit/Descriptor_Unit_DCA_M167_Vulcan_20mm_nonPara_US, 1308),
        ($/GFX/Unit/Descriptor_Unit_DCA_M167_Vulcan_para_20mm_BEL, 1309),
        ($/GFX/Unit/Descriptor_Unit_DCA_M55_NL, 1311),
        ($/GFX/Unit/Descriptor_Unit_DCA_Oerlikon_GDF_002_35mm_UK, 1312),
        ($/GFX/Unit/Descriptor_Unit_DCA_XM85_Chaparral_US, 1313),
        ($/GFX/Unit/Descriptor_Unit_DCA_ZPU4_DDR, 1314),
        ($/GFX/Unit/Descriptor_Unit_DCA_ZPU4_POL, 1315),
        ($/GFX/Unit/Descriptor_Unit_DCA_ZUR_23_2S_JOD_POL, 1316),
        ($/GFX/Unit/Descriptor_Unit_DCA_ZU_23_2_POL, 1317),
        ($/GFX/Unit/Descriptor_Unit_Engineers_AT_BEL, 1318),
        ($/GFX/Unit/Descriptor_Unit_Engineers_BEL, 1319),
        ($/GFX/Unit/Descriptor_Unit_Engineers_CMD_BEL, 1320),
        ($/GFX/Unit/Descriptor_Unit_Engineers_CMD_NL, 1321),
        ($/GFX/Unit/Descriptor_Unit_Engineers_CMD_POL, 1322),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Flam_BEL, 1323),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Flam_POL, 1325),
        ($/GFX/Unit/Descriptor_Unit_Engineers_NL, 1326),
        ($/GFX/Unit/Descriptor_Unit_Engineers_POL, 1327),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Scout_NL, 1328),
        ($/GFX/Unit/Descriptor_Unit_Engineers_paras_CMD_POL, 1329),
        ($/GFX/Unit/Descriptor_Unit_Engineers_paras_Flam_POL, 1330),
        ($/GFX/Unit/Descriptor_Unit_Engineers_paras_POL, 1331),
        ($/GFX/Unit/Descriptor_Unit_FAV_AGL_US, 1332),
        ($/GFX/Unit/Descriptor_Unit_FAV_HMG_US, 1333),
        ($/GFX/Unit/Descriptor_Unit_FAV_TOW_US, 1334),
        ($/GFX/Unit/Descriptor_Unit_FV101_Scorpion_para_BEL, 1335),
        ($/GFX/Unit/Descriptor_Unit_FV102_Striker_para_UK, 1337),
        ($/GFX/Unit/Descriptor_Unit_FV103_Spartan_GSR_UK, 1338),
        ($/GFX/Unit/Descriptor_Unit_FV105_Sultan_para_UK, 1339),
        ($/GFX/Unit/Descriptor_Unit_FV107_Scimitar_para_BEL, 1340),
        ($/GFX/Unit/Descriptor_Unit_FV432_WOMBAT_UK, 1342),
        ($/GFX/Unit/Descriptor_Unit_GAZ_66B_POL, 1344),
        ($/GFX/Unit/Descriptor_Unit_GAZ_66_POL, 1346),
        ($/GFX/Unit/Descriptor_Unit_GAZ_66_trans_POL, 1348),
        ($/GFX/Unit/Descriptor_Unit_Gepard_1A2_BEL, 1352),
        ($/GFX/Unit/Descriptor_Unit_Gepard_1A2_NL, 1353),
        ($/GFX/Unit/Descriptor_Unit_Groupe_AT_POL, 1354),
        ($/GFX/Unit/Descriptor_Unit_Gun_Group_Paras_UK, 1355),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_AGS17_POL, 1356),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M1919A4_NL, 1358),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M2HB_BEL, 1359),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M2HB_LUX, 1360),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M2HB_NL, 1361),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_MAG_BEL, 1362),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_MAG_NL, 1363),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_MAG_para_BEL, 1364),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_NSV_POL, 1365),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_PKM_POL, 1366),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_PKM_para_POL, 1367),
        ($/GFX/Unit/Descriptor_Unit_Hibneryt_KG_POL, 1368),
        ($/GFX/Unit/Descriptor_Unit_Hibneryt_POL, 1369),
        ($/GFX/Unit/Descriptor_Unit_Honker_4011_POL, 1370),
        ($/GFX/Unit/Descriptor_Unit_Honker_RYS_POL, 1371),
        ($/GFX/Unit/Descriptor_Unit_Howz_2A36_Giatsint_B_SOV, 1372),
        ($/GFX/Unit/Descriptor_Unit_Howz_A19_122mm_POL, 1373),
        ($/GFX/Unit/Descriptor_Unit_Howz_L118_105mm_LUX, 1375),
        ($/GFX/Unit/Descriptor_Unit_Howz_M101_105mm_para_BEL, 1378),
        ($/GFX/Unit/Descriptor_Unit_Howz_M114_155mm_NL, 1379),
        ($/GFX/Unit/Descriptor_Unit_Howz_M114_39_155mm_NL, 1380),
        ($/GFX/Unit/Descriptor_Unit_Howz_M30_122mm_POL, 1383),
        ($/GFX/Unit/Descriptor_Unit_Howz_M46_130mm_POL, 1384),
        ($/GFX/Unit/Descriptor_Unit_Howz_ML20_152mm_POL, 1386),
        ($/GFX/Unit/Descriptor_Unit_Howz_MstaB_150mm_SOV, 1387),
        ($/GFX/Unit/Descriptor_Unit_HvyScout_POL, 1389),
        ($/GFX/Unit/Descriptor_Unit_Iltis_CMD_BEL, 1390),
        ($/GFX/Unit/Descriptor_Unit_Iltis_HMG_BEL, 1391),
        ($/GFX/Unit/Descriptor_Unit_Iltis_MILAN_BEL, 1392),
        ($/GFX/Unit/Descriptor_Unit_Iltis_para_CMD_BEL, 1393),
        ($/GFX/Unit/Descriptor_Unit_Iltis_para_CMD_RFA, 1394),
        ($/GFX/Unit/Descriptor_Unit_Iltis_trans_BEL, 1395),
        ($/GFX/Unit/Descriptor_Unit_KanJagdPanzer_BEL, 1396),
        ($/GFX/Unit/Descriptor_Unit_KrAZ_255B_POL, 1398),
        ($/GFX/Unit/Descriptor_Unit_KrAZ_255B_SOV, 1399),
        ($/GFX/Unit/Descriptor_Unit_KrAZ_255B_supply_POL, 1401),
        ($/GFX/Unit/Descriptor_Unit_KrAZ_255B_supply_SOV, 1402),
        ($/GFX/Unit/Descriptor_Unit_LO_1800_FASTA_4_DDR, 1403),
        ($/GFX/Unit/Descriptor_Unit_LRRP_BEL, 1404),
        ($/GFX/Unit/Descriptor_Unit_LRRP_FR, 1405),
        ($/GFX/Unit/Descriptor_Unit_LRRP_NL, 1406),
        ($/GFX/Unit/Descriptor_Unit_LandRover_CMD_NL, 1407),
        ($/GFX/Unit/Descriptor_Unit_LandRover_CMD_Para_UK, 1408),
        ($/GFX/Unit/Descriptor_Unit_LandRover_NL, 1410),
        ($/GFX/Unit/Descriptor_Unit_Leopard_1A1A1_CMD_NL, 1411),
        ($/GFX/Unit/Descriptor_Unit_Leopard_1A1A1_NL, 1412),
        ($/GFX/Unit/Descriptor_Unit_Leopard_1A1_NL, 1413),
        ($/GFX/Unit/Descriptor_Unit_Leopard_1A5_BEL, 1415),
        ($/GFX/Unit/Descriptor_Unit_Leopard_1A5_CMD_BEL, 1416),
        ($/GFX/Unit/Descriptor_Unit_Leopard_1BE_BEL, 1417),
        ($/GFX/Unit/Descriptor_Unit_Leopard_1BE_CMD_BEL, 1418),
        ($/GFX/Unit/Descriptor_Unit_Leopard_2A1_CMD_RFA, 1419),
        ($/GFX/Unit/Descriptor_Unit_Leopard_2A1_RFA, 1420),
        ($/GFX/Unit/Descriptor_Unit_Leopard_2A4_CMD_NL, 1421),
        ($/GFX/Unit/Descriptor_Unit_Leopard_2A4_NL, 1423),
        ($/GFX/Unit/Descriptor_Unit_LightRifles_CMD_LUX, 1424),
        ($/GFX/Unit/Descriptor_Unit_LightRifles_LUX, 1425),
        ($/GFX/Unit/Descriptor_Unit_M1025_Humvee_CMD_LUX, 1426),
        ($/GFX/Unit/Descriptor_Unit_M1025_Humvee_HMG_LUX, 1427),
        ($/GFX/Unit/Descriptor_Unit_M1025_Humvee_TOW_LUX, 1428),
        ($/GFX/Unit/Descriptor_Unit_M1038_Humvee_LUX, 1429),
        ($/GFX/Unit/Descriptor_Unit_M106A2_Mortar_NL, 1430),
        ($/GFX/Unit/Descriptor_Unit_M109A2_BEL, 1433),
        ($/GFX/Unit/Descriptor_Unit_M109A2_NL, 1434),
        ($/GFX/Unit/Descriptor_Unit_M110A2_HOWZ_BEL, 1436),
        ($/GFX/Unit/Descriptor_Unit_M110A2_HOWZ_NL, 1437),
        ($/GFX/Unit/Descriptor_Unit_M110A2_Howz_UK, 1438),
        ($/GFX/Unit/Descriptor_Unit_M113A1B_BEL, 1439),
        ($/GFX/Unit/Descriptor_Unit_M113A1B_MILAN_BEL, 1440),
        ($/GFX/Unit/Descriptor_Unit_M113A1_NL, 1441),
        ($/GFX/Unit/Descriptor_Unit_M113A1_reco_NL, 1442),
        ($/GFX/Unit/Descriptor_Unit_M113_CV_25mm_NL, 1443),
        ($/GFX/Unit/Descriptor_Unit_M113_GreenArcher_NL, 1444),
        ($/GFX/Unit/Descriptor_Unit_M38A1_CMD_NL, 1447),
        ($/GFX/Unit/Descriptor_Unit_M38A1_MG_NL, 1449),
        ($/GFX/Unit/Descriptor_Unit_M38A1_NL, 1450),
        ($/GFX/Unit/Descriptor_Unit_M38A1_RCL_NL, 1452),
        ($/GFX/Unit/Descriptor_Unit_M38A1_TOW_NL, 1453),
        ($/GFX/Unit/Descriptor_Unit_M548A2_supply_US, 1457),
        ($/GFX/Unit/Descriptor_Unit_M577_NL, 1458),
        ($/GFX/Unit/Descriptor_Unit_M998_Avenger_nonPara_US, 1459),
        ($/GFX/Unit/Descriptor_Unit_M998_Humvee_LUX, 1460),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Javelin_para_UK, 1461),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Mistral_BEL, 1462),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Stinger_C_Marine_NL, 1463),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Stinger_C_NL, 1464),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Strela_2M_POL, 1465),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Strela_2M_Para_POL, 1466),
        ($/GFX/Unit/Descriptor_Unit_MLRS_WP_8z_POL, 1468),
        ($/GFX/Unit/Descriptor_Unit_MP_BEL, 1469),
        ($/GFX/Unit/Descriptor_Unit_MP_DDR, 1471),
        ($/GFX/Unit/Descriptor_Unit_MP_NL, 1473),
        ($/GFX/Unit/Descriptor_Unit_MP_SOV, 1474),
        ($/GFX/Unit/Descriptor_Unit_MTLB_Strela10_POL, 1476),
        ($/GFX/Unit/Descriptor_Unit_Marine_Scout_NL, 1477),
        ($/GFX/Unit/Descriptor_Unit_Marines_CMD_NL, 1478),
        ($/GFX/Unit/Descriptor_Unit_Marines_NL, 1480),
        ($/GFX/Unit/Descriptor_Unit_Mech_Rifles_AT_BEL, 1481),
        ($/GFX/Unit/Descriptor_Unit_Mech_Rifles_CMD_BEL, 1482),
        ($/GFX/Unit/Descriptor_Unit_Mech_Rifles_MG_BEL, 1483),
        ($/GFX/Unit/Descriptor_Unit_Mortier_107mm_BEL, 1484),
        ($/GFX/Unit/Descriptor_Unit_Mortier_107mm_para_BEL, 1485),
        ($/GFX/Unit/Descriptor_Unit_Mortier_2B9_Vasilek_Para_POL, 1486),
        ($/GFX/Unit/Descriptor_Unit_Mortier_2S12_120mm_POL, 1487),
        ($/GFX/Unit/Descriptor_Unit_Mortier_2S12_120mm_Para_POL, 1488),
        ($/GFX/Unit/Descriptor_Unit_Mortier_81mm_BEL, 1489),
        ($/GFX/Unit/Descriptor_Unit_Mortier_81mm_LUX, 1490),
        ($/GFX/Unit/Descriptor_Unit_Mortier_81mm_para_BEL, 1491),
        ($/GFX/Unit/Descriptor_Unit_Mortier_M29_81mm_NL, 1492),
        ($/GFX/Unit/Descriptor_Unit_Mortier_M43_160mm_POL, 1493),
        ($/GFX/Unit/Descriptor_Unit_Mortier_M43_82mm_DDR, 1494),
        ($/GFX/Unit/Descriptor_Unit_Mortier_M43_82mm_FJ_DDR, 1495),
        ($/GFX/Unit/Descriptor_Unit_Mortier_M43_82mm_POL, 1496),
        ($/GFX/Unit/Descriptor_Unit_Mortier_MORT61_120mm_NL, 1497),
        ($/GFX/Unit/Descriptor_Unit_Mortier_Nona_K_120mm_SOV, 1498),
        ($/GFX/Unit/Descriptor_Unit_Mortier_PM43_120mm_POL, 1499),
        ($/GFX/Unit/Descriptor_Unit_Mortier_Tampella_120mm_para_RFA, 1501),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_CMD_POL, 1502),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_POL, 1503),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_SVD_POL, 1504),
        ($/GFX/Unit/Descriptor_Unit_NatGuard_CMD_US, 1505),
        ($/GFX/Unit/Descriptor_Unit_NatGuard_Dragon_US, 1506),
        ($/GFX/Unit/Descriptor_Unit_NatGuard_LAW_US, 1507),
        ($/GFX/Unit/Descriptor_Unit_Naval_Rifle_CMD_POL, 1508),
        ($/GFX/Unit/Descriptor_Unit_Naval_Rifle_POL, 1509),
        ($/GFX/Unit/Descriptor_Unit_OT_62_TOPAS_2AP_POL, 1510),
        ($/GFX/Unit/Descriptor_Unit_OT_62_TOPAS_POL, 1514),
        ($/GFX/Unit/Descriptor_Unit_OT_62_TOPAS_R3M_CMD_POL, 1515),
        ($/GFX/Unit/Descriptor_Unit_OT_64_SKOT_2AM_POL, 1517),
        ($/GFX/Unit/Descriptor_Unit_OT_64_SKOT_2A_POL, 1518),
        ($/GFX/Unit/Descriptor_Unit_OT_64_SKOT_2P_POL, 1520),
        ($/GFX/Unit/Descriptor_Unit_OT_64_SKOT_2_POL, 1521),
        ($/GFX/Unit/Descriptor_Unit_OT_64_SKOT_CMD_POL, 1523),
        ($/GFX/Unit/Descriptor_Unit_OT_65_CMD_POL, 1526),
        ($/GFX/Unit/Descriptor_Unit_OT_65_POL, 1528),
        ($/GFX/Unit/Descriptor_Unit_Obusier_155mm_mle1950_FR, 1531),
        ($/GFX/Unit/Descriptor_Unit_Osa_9K33M3_POL, 1532),
        ($/GFX/Unit/Descriptor_Unit_PT76B_CMD_POL, 1533),
        ($/GFX/Unit/Descriptor_Unit_PT76B_POL, 1535),
        ($/GFX/Unit/Descriptor_Unit_PT76B_tank_POL, 1537),
        ($/GFX/Unit/Descriptor_Unit_ParaCmdo_AT_BEL, 1538),
        ($/GFX/Unit/Descriptor_Unit_ParaCmdo_BEL, 1539),
        ($/GFX/Unit/Descriptor_Unit_ParaCmdo_CMD_BEL, 1540),
        ($/GFX/Unit/Descriptor_Unit_ParaCmdo_Pionier_BEL, 1541),
        ($/GFX/Unit/Descriptor_Unit_Para_CMD_POL, 1542),
        ($/GFX/Unit/Descriptor_Unit_Para_HMG_POL, 1543),
        ($/GFX/Unit/Descriptor_Unit_Para_Metis_POL, 1544),
        ($/GFX/Unit/Descriptor_Unit_Para_POL, 1545),
        ($/GFX/Unit/Descriptor_Unit_Paratroopers_Engineers_CMD_UK, 1546),
        ($/GFX/Unit/Descriptor_Unit_Paratroopers_Engineers_UK, 1547),
        ($/GFX/Unit/Descriptor_Unit_Pathfinders_UK, 1548),
        ($/GFX/Unit/Descriptor_Unit_RM70_85_DDR, 1553),
        ($/GFX/Unit/Descriptor_Unit_RM70_85_POL, 1554),
        ($/GFX/Unit/Descriptor_Unit_Reserve_NL, 1557),
        ($/GFX/Unit/Descriptor_Unit_Reserve_SOV, 1558),
        ($/GFX/Unit/Descriptor_Unit_Reserviste_CMD_FR, 1559),
        ($/GFX/Unit/Descriptor_Unit_Rifles_AT_BEL, 1560),
        ($/GFX/Unit/Descriptor_Unit_Rifles_BEL, 1561),
        ($/GFX/Unit/Descriptor_Unit_Rifles_CMD_BEL, 1562),
        ($/GFX/Unit/Descriptor_Unit_Rifles_CMD_NL, 1563),
        ($/GFX/Unit/Descriptor_Unit_Rifles_CMD_POL, 1564),
        ($/GFX/Unit/Descriptor_Unit_Rifles_Carl_NL, 1565),
        ($/GFX/Unit/Descriptor_Unit_Rifles_Gurkhas_CMD_UK, 1567),
        ($/GFX/Unit/Descriptor_Unit_Rifles_Gurkhas_UK, 1568),
        ($/GFX/Unit/Descriptor_Unit_Rifles_HMG_POL, 1569),
        ($/GFX/Unit/Descriptor_Unit_Rifles_M72_LAW_NL, 1570),
        ($/GFX/Unit/Descriptor_Unit_Rifles_POL, 1575),
        ($/GFX/Unit/Descriptor_Unit_Rover_101FC_UK, 1576),
        ($/GFX/Unit/Descriptor_Unit_Rover_101FC_supply_UK, 1577),
        ($/GFX/Unit/Descriptor_Unit_SAS_Sniper_FR, 1578),
        ($/GFX/Unit/Descriptor_Unit_Scout_AT_BEL, 1580),
        ($/GFX/Unit/Descriptor_Unit_Scout_AT_NL, 1581),
        ($/GFX/Unit/Descriptor_Unit_Scout_LRRP_POL, 1582),
        ($/GFX/Unit/Descriptor_Unit_Scout_LUX, 1583),
        ($/GFX/Unit/Descriptor_Unit_Scout_NL, 1584),
        ($/GFX/Unit/Descriptor_Unit_Scout_POL, 1585),
        ($/GFX/Unit/Descriptor_Unit_Scout_ParaCmdo_BEL, 1586),
        ($/GFX/Unit/Descriptor_Unit_Scout_ParaCmdo_Mech_BEL, 1587),
        ($/GFX/Unit/Descriptor_Unit_Scout_Para_UK, 1588),
        ($/GFX/Unit/Descriptor_Unit_Scout_Reserviste_FR, 1591),
        ($/GFX/Unit/Descriptor_Unit_Scout_para_POL, 1592),
        ($/GFX/Unit/Descriptor_Unit_Security_UK, 1593),
        ($/GFX/Unit/Descriptor_Unit_Sniper_ESR_BEL, 1594),
        ($/GFX/Unit/Descriptor_Unit_Sniper_FJ_DDR, 1595),
        ($/GFX/Unit/Descriptor_Unit_Sniper_Fern_RFA, 1596),
        ($/GFX/Unit/Descriptor_Unit_Sniper_POL, 1597),
        ($/GFX/Unit/Descriptor_Unit_Sniper_Spetsnaz_SOV, 1598),
        ($/GFX/Unit/Descriptor_Unit_Sniper_UK, 1599),
        ($/GFX/Unit/Descriptor_Unit_Sniper_US, 1600),
        ($/GFX/Unit/Descriptor_Unit_Sniper_paras_POL, 1601),
        ($/GFX/Unit/Descriptor_Unit_Star_266_POL, 1602),
        ($/GFX/Unit/Descriptor_Unit_T55AMS_Merida_POL, 1608),
        ($/GFX/Unit/Descriptor_Unit_T55AM_Merida_CMD_POL, 1609),
        ($/GFX/Unit/Descriptor_Unit_T55AM_Merida_POL, 1610),
        ($/GFX/Unit/Descriptor_Unit_T55AS_POL, 1611),
        ($/GFX/Unit/Descriptor_Unit_T55A_CMD_POL, 1612),
        ($/GFX/Unit/Descriptor_Unit_T55A_POL, 1614),
        ($/GFX/Unit/Descriptor_Unit_T64AV_SOV, 1616),
        ($/GFX/Unit/Descriptor_Unit_T64A_SOV, 1617),
        ($/GFX/Unit/Descriptor_Unit_T72M1_CMD_POL, 1619),
        ($/GFX/Unit/Descriptor_Unit_T72M1_POL, 1621),
        ($/GFX/Unit/Descriptor_Unit_T72M1_Wilk_POL, 1623),
        ($/GFX/Unit/Descriptor_Unit_T72MUV2_DDR, 1624),
        ($/GFX/Unit/Descriptor_Unit_T72M_CMD_POL, 1625),
        ($/GFX/Unit/Descriptor_Unit_T72M_POL, 1627),
        ($/GFX/Unit/Descriptor_Unit_T72S_DDR, 1629),
        ($/GFX/Unit/Descriptor_Unit_T815_supply_DDR, 1636),
        ($/GFX/Unit/Descriptor_Unit_TRM_10000_FR, 1637),
        ($/GFX/Unit/Descriptor_Unit_TRM_10000_supply_FR, 1638),
        ($/GFX/Unit/Descriptor_Unit_Tor_SOV, 1639),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_CMD_POL, 1640),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_CMD_Para_POL, 1641),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_Fagot_POL, 1643),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_MP_SOV, 1644),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_Reco_POL, 1646),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_SPG9_Para_POL, 1647),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_supply_Para_POL, 1648),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_trans_POL, 1649),
        ($/GFX/Unit/Descriptor_Unit_Unimog_supply_BEL, 1651),
        ($/GFX/Unit/Descriptor_Unit_Unimog_trans_BEL, 1652),
        ($/GFX/Unit/Descriptor_Unit_Volvo_N10_supply_BEL, 1655),
        ($/GFX/Unit/Descriptor_Unit_Volvo_N10_trans_BEL, 1656),
        ($/GFX/Unit/Descriptor_Unit_W50_LA_A_25mm_DDR, 1657),
        ($/GFX/Unit/Descriptor_Unit_WSW_POL, 1658),
        ($/GFX/Unit/Descriptor_Unit_ZSU_23_Shilka_POL, 1663),
        ($/GFX/Unit/Descriptor_Unit_Alouette_III_NL, 1665),
        ($/GFX/Unit/Descriptor_Unit_Alouette_III_trans_NL, 1666),
        ($/GFX/Unit/Descriptor_Unit_Alouette_II_reco_BEL, 1667),
        ($/GFX/Unit/Descriptor_Unit_Bo_105_CB_NL, 1668),
        ($/GFX/Unit/Descriptor_Unit_Gazelle_CMD_UK, 1670),
        ($/GFX/Unit/Descriptor_Unit_Gazelle_SNEB_reco_UK, 1671),
        ($/GFX/Unit/Descriptor_Unit_Mi_24D_POL, 1672),
        ($/GFX/Unit/Descriptor_Unit_Mi_24V_POL, 1673),
        ($/GFX/Unit/Descriptor_Unit_Mi_2Ro_reco_POL, 1674),
        ($/GFX/Unit/Descriptor_Unit_Mi_2_AA_POL, 1675),
        ($/GFX/Unit/Descriptor_Unit_Mi_2_ATGM_POL, 1676),
        ($/GFX/Unit/Descriptor_Unit_Mi_2_CMD_DDR, 1677),
        ($/GFX/Unit/Descriptor_Unit_Mi_2_CMD_POL, 1678),
        ($/GFX/Unit/Descriptor_Unit_Mi_2_gunship_DDR, 1679),
        ($/GFX/Unit/Descriptor_Unit_Mi_2_gunship_POL, 1680),
        ($/GFX/Unit/Descriptor_Unit_Mi_2_rocket_DDR, 1681),
        ($/GFX/Unit/Descriptor_Unit_Mi_2_rocket_POL, 1682),
        ($/GFX/Unit/Descriptor_Unit_Mi_2_trans_POL, 1683),
        ($/GFX/Unit/Descriptor_Unit_Mi_8T_POL, 1684),
        ($/GFX/Unit/Descriptor_Unit_Mi_8T_non_arme_POL, 1685),
        ($/GFX/Unit/Descriptor_Unit_Mi_8_supply_POL, 1686),
        ($/GFX/Unit/Descriptor_Unit_UH1D_NL, 1689),
        ($/GFX/Unit/Descriptor_Unit_W3RR_Procjon_POL, 1690),
        ($/GFX/Unit/Descriptor_Unit_W3W_Sokol_AA_POL, 1691),
        ($/GFX/Unit/Descriptor_Unit_W3W_Sokol_RKT_POL, 1692),
        ($/GFX/Unit/Descriptor_Unit_W3_Sokol_POL, 1693),
        ($/GFX/Unit/Descriptor_Unit_Westland_Scout_SS11_UK, 1694),
        ($/GFX/Unit/Descriptor_Unit_Alpha_Jet_BEL, 1695),
        ($/GFX/Unit/Descriptor_Unit_Alpha_Jet_E_FR, 1696),
        ($/GFX/Unit/Descriptor_Unit_Alpha_Jet_HE2_BEL, 1697),
        ($/GFX/Unit/Descriptor_Unit_Buccaneer_S2B_ATGM_UK, 1698),
        ($/GFX/Unit/Descriptor_Unit_Buccaneer_S2B_GBU_UK, 1699),
        ($/GFX/Unit/Descriptor_Unit_Buccaneer_S2B_HE_UK, 1700),
        ($/GFX/Unit/Descriptor_Unit_Buccaneer_S2B_SEAD_UK, 1701),
        ($/GFX/Unit/Descriptor_Unit_F15E_StrikeEagle_US, 1703),
        ($/GFX/Unit/Descriptor_Unit_F16A_AA_BEL, 1704),
        ($/GFX/Unit/Descriptor_Unit_F16A_AA_NL, 1705),
        ($/GFX/Unit/Descriptor_Unit_F16A_CLU_NL, 1706),
        ($/GFX/Unit/Descriptor_Unit_F16A_HE_NL, 1707),
        ($/GFX/Unit/Descriptor_Unit_F5A_FreedomFighter_CLU_NL, 1710),
        ($/GFX/Unit/Descriptor_Unit_F5A_FreedomFighter_NL, 1711),
        ($/GFX/Unit/Descriptor_Unit_F5A_FreedomFighter_NPLM_NL, 1712),
        ($/GFX/Unit/Descriptor_Unit_F8P_Crusader_FR, 1713),
        ($/GFX/Unit/Descriptor_Unit_FA16_CAS_US, 1714),
        ($/GFX/Unit/Descriptor_Unit_Harrier_GR5_UK, 1715),
        ($/GFX/Unit/Descriptor_Unit_Jaguar_overwing_UK, 1718),
        ($/GFX/Unit/Descriptor_Unit_L39ZO_DDR, 1720),
        ($/GFX/Unit/Descriptor_Unit_MiG_17PF_POL, 1721),
        ($/GFX/Unit/Descriptor_Unit_MiG_21bis_POL, 1722),
        ($/GFX/Unit/Descriptor_Unit_MiG_23BN_DDR, 1723),
        ($/GFX/Unit/Descriptor_Unit_MiG_29_AA_POL, 1724),
        ($/GFX/Unit/Descriptor_Unit_Mirage_5_BA_BEL, 1725),
        ($/GFX/Unit/Descriptor_Unit_Mirage_5_BA_CLU_BEL, 1726),
        ($/GFX/Unit/Descriptor_Unit_Mirage_5_BA_MIRSIP_BEL, 1727),
        ($/GFX/Unit/Descriptor_Unit_Mirage_5_BA_NPLM_BEL, 1728),
        ($/GFX/Unit/Descriptor_Unit_Mirage_5_BA_RKT_BEL, 1729),
        ($/GFX/Unit/Descriptor_Unit_Su_22_POL, 1731),
        ($/GFX/Unit/Descriptor_Unit_FOB_SOV, 1732),
        ($/GFX/Unit/Descriptor_Unit_FOB_US, 1733),
        ($/GFX/Unit/Descriptor_Unit_GAZ_66_supply_SOV, 1735),
        ($/GFX/Unit/Descriptor_Unit_FOB_DDR, 1753),
        ($/GFX/Unit/Descriptor_Unit_FOB_FR, 1754),
        ($/GFX/Unit/Descriptor_Unit_FOB_RFA, 1755),
        ($/GFX/Unit/Descriptor_Unit_FOB_UK, 1757),
        ($/GFX/Unit/Descriptor_Unit_BAV_485_Supply_POL, 1793),
        ($/GFX/Unit/Descriptor_Unit_Centurion_AVRE_105_UK, 1794),
        ($/GFX/Unit/Descriptor_Unit_DCA_XMIM_115A_Roland_US, 1795),
        ($/GFX/Unit/Descriptor_Unit_GAZ_46_DDR, 1797),
        ($/GFX/Unit/Descriptor_Unit_GAZ_46_POL, 1798),
        ($/GFX/Unit/Descriptor_Unit_GAZ_46_SOV, 1799),
        ($/GFX/Unit/Descriptor_Unit_Howz_M198_155mm_Copperhead_US, 1800),
        ($/GFX/Unit/Descriptor_Unit_LSV_M2HB_UK, 1801),
        ($/GFX/Unit/Descriptor_Unit_LSV_MILAN_UK, 1802),
        ($/GFX/Unit/Descriptor_Unit_Supacat_ATMP_Javelin_LML_UK, 1803),
        ($/GFX/Unit/Descriptor_Unit_Supacat_ATMP_MILAN_UK, 1804),
        ($/GFX/Unit/Descriptor_Unit_Supacat_ATMP_UK, 1805),
        ($/GFX/Unit/Descriptor_Unit_Supacat_ATMP_supply_UK, 1806),
        ($/GFX/Unit/Descriptor_Unit_MAN_Z311_BEL, 1809),
        ($/GFX/Unit/Descriptor_Unit_MAN_Z311_Mi50_BEL, 1810),
        ($/GFX/Unit/Descriptor_Unit_AML_60_Gendarmerie_FR, 1847),
        ($/GFX/Unit/Descriptor_Unit_ERC_90_Sagaie_reco_FR, 1851),
        ($/GFX/Unit/Descriptor_Unit_Howz_B4M_203mm_SOV, 1855),
        ($/GFX/Unit/Descriptor_Unit_Mortier_240mm_M240_SOV, 1856),
        ($/GFX/Unit/Descriptor_Unit_AT_T12R_Ruta_SOV, 1862),
        ($/GFX/Unit/Descriptor_Unit_MH_60A_DAP_US, 1882),
        ($/GFX/Unit/Descriptor_Unit_T55A_SOV, 1903),
        ($/GFX/Unit/Descriptor_Unit_Reserve_BEL, 1918),
        ($/GFX/Unit/Descriptor_Unit_FOB_NL, 1919),
        ($/GFX/Unit/Descriptor_Unit_T55A_CMD_SOV, 1920),
        ($/GFX/Unit/Descriptor_Unit_Grenzer_Flam_DDR, 1921),
        ($/GFX/Unit/Descriptor_Unit_LightRifles_LAW_US, 1922),
        ($/GFX/Unit/Descriptor_Unit_Reserve_DDR, 1923),
        ($/GFX/Unit/Descriptor_Unit_SEK_RFA, 1924),
        ($/GFX/Unit/Descriptor_Unit_M1A1_Abrams_reco_US, 1925),
        ($/GFX/Unit/Descriptor_Unit_Rifles_Cavalry_US, 1926),
        ($/GFX/Unit/Descriptor_Unit_Mech_Rifles_CMD_NL, 1927),
        ($/GFX/Unit/Descriptor_Unit_Mech_Rifles_Carl_NL, 1928),
        ($/GFX/Unit/Descriptor_Unit_Mech_Rifles_Dragon_NL, 1929),
        ($/GFX/Unit/Descriptor_Unit_Mech_Rifles_M72_LAW_NL, 1930),
        ($/GFX/Unit/Descriptor_Unit_FOB_BEL, 1931),
        ($/GFX/Unit/Descriptor_Unit_Mortier_2B9_Vasilek_nonPara_SOV, 1932),
        ($/GFX/Unit/Descriptor_Unit_BMD_1P_SOV, 1933),
        ($/GFX/Unit/Descriptor_Unit_DCA_Rapier_Darkfire_UK, 1934),
        ($/GFX/Unit/Descriptor_Unit_Lynx_AH_Mk7_SNEB_UK, 1935),
        ($/GFX/Unit/Descriptor_Unit_MQM_105_Aquila_US, 1936),
        ($/GFX/Unit/Descriptor_Unit_Pchela_1T_SOV, 1937),
        ($/GFX/Unit/Descriptor_Unit_Security_DDR, 1938),
        ($/GFX/Unit/Descriptor_Unit_VDV_Afgantsy_SOV, 1939),
        ($/GFX/Unit/Descriptor_Unit_Rifles_RIMa_APILAS_FR, 1944),
        ($/GFX/Unit/Descriptor_Unit_Rifles_RIMa_CMD_FR, 1945),
        ($/GFX/Unit/Descriptor_Unit_Rifles_RIMa_FR, 1946),
        ($/GFX/Unit/Descriptor_Unit_AH1F_CNITE_US, 1953),
        ($/GFX/Unit/Descriptor_Unit_Ural_4320_trans_SOV, 1954),
        ($/GFX/Unit/Descriptor_Unit_Su_24M_clu2_SOV, 1955),
        ($/GFX/Unit/Descriptor_Unit_Su_24M_LGB_SOV, 1956),
        ($/GFX/Unit/Descriptor_Unit_MiG_23BN_AT2_DDR, 1957),
        ($/GFX/Unit/Descriptor_Unit_MiG_23BN_CLU_DDR, 1958),
        ($/GFX/Unit/Descriptor_Unit_MiG_23BN_RKT_DDR, 1959),
        ($/GFX/Unit/Descriptor_Unit_MiG_23BN_nplm_DDR, 1960),
        ($/GFX/Unit/Descriptor_Unit_Lynx_AH_Mk7_I_TOW2_UK, 1961),
        ($/GFX/Unit/Descriptor_Unit_BMP_2AG_SOV, 1963),
        ($/GFX/Unit/Descriptor_Unit_BMP_2D_SOV, 1964),
        ($/GFX/Unit/Descriptor_Unit_T64B1_SOV, 1966),
        ($/GFX/Unit/Descriptor_Unit_T64BV1_SOV, 1967),
        ($/GFX/Unit/Descriptor_Unit_T64B_CMD_SOV, 1968),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_SVD_DDR, 1970),
        ($/GFX/Unit/Descriptor_Unit_MTLB_trans_POL, 1971),
        ($/GFX/Unit/Descriptor_Unit_Mi_24D_s8_AT_POL, 1972),
        ($/GFX/Unit/Descriptor_Unit_DCA_M167A2_Vulcan_20mm_US, 1973),
        ($/GFX/Unit/Descriptor_Unit_Mi_8MT_POL, 1974),
        ($/GFX/Unit/Descriptor_Unit_Mi_6_POL, 1975),
        ($/GFX/Unit/Descriptor_Unit_Commandos_Para_POL, 1976),
        ($/GFX/Unit/Descriptor_Unit_Scout_LRRP_Para_POL, 1977),
        ($/GFX/Unit/Descriptor_Unit_2S7_Pion_POL, 1978),
        ($/GFX/Unit/Descriptor_Unit_Para_Security_POL, 1979),
        ($/GFX/Unit/Descriptor_Unit_DCA_ZUR_23_2S_JOD_Para_POL, 1980),
        ($/GFX/Unit/Descriptor_Unit_DCA_ZU_23_2_Para_POL, 1981),
        ($/GFX/Unit/Descriptor_Unit_Naval_Engineers_CMD_POL, 1982),
        ($/GFX/Unit/Descriptor_Unit_Naval_Engineers_Flam_POL, 1983),
        ($/GFX/Unit/Descriptor_Unit_Naval_Engineers_POL, 1984),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_Fagot_Para_POL, 1985),
        ($/GFX/Unit/Descriptor_Unit_MiG_21bis_AA_POL, 1986),
        ($/GFX/Unit/Descriptor_Unit_MiG_21bis_HE_POL, 1987),
        ($/GFX/Unit/Descriptor_Unit_MiG_21bis_RKT2_POL, 1988),
        ($/GFX/Unit/Descriptor_Unit_MiG_23MF_AA2_POL, 1989),
        ($/GFX/Unit/Descriptor_Unit_MiG_23MF_AA_POL, 1990),
        ($/GFX/Unit/Descriptor_Unit_Su_22_AT_POL, 1991),
        ($/GFX/Unit/Descriptor_Unit_Su_22_RKT2_POL, 1992),
        ($/GFX/Unit/Descriptor_Unit_Su_22_RKT_POL, 1993),
        ($/GFX/Unit/Descriptor_Unit_Su_22_SEAD_POL, 1994),
        ($/GFX/Unit/Descriptor_Unit_Su_22_clu_POL, 1995),
        ($/GFX/Unit/Descriptor_Unit_Su_22_nplm_POL, 1996),
        ($/GFX/Unit/Descriptor_Unit_BMP_2_CMD_SOV, 1997),
        ($/GFX/Unit/Descriptor_Unit_M1025_Humvee_scout_tuto_US, 1998),
        ($/GFX/Unit/Descriptor_Unit_M35_trans_tuto_US, 1999),
        ($/GFX/Unit/Descriptor_Unit_Ranger_tuto_US, 2000),
        ($/GFX/Unit/Descriptor_Unit_Challenger_1_Mk3_UK, 2001),
        ($/GFX/Unit/Descriptor_Unit_2S23_Nona_SVK_SOV, 2002),
        ($/GFX/Unit/Descriptor_Unit_2S3M1_Akatsiya_SOV, 2003),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M2HB_NG_US, 2006),
        ($/GFX/Unit/Descriptor_Unit_M109A2_NG_US, 2007),
        ($/GFX/Unit/Descriptor_Unit_M151A2_TOW_NG_US, 2008),
        ($/GFX/Unit/Descriptor_Unit_M1_Abrams_NG_US, 2009),
        ($/GFX/Unit/Descriptor_Unit_M2A1_Bradley_Leader_US, 2010),
        ($/GFX/Unit/Descriptor_Unit_M2_Bradley_IFV_NG_US, 2011),
        ($/GFX/Unit/Descriptor_Unit_M3A2_Bradley_CFV_US, 2012),
        ($/GFX/Unit/Descriptor_Unit_MP_CMD_US, 2014),
        ($/GFX/Unit/Descriptor_Unit_MP_Combat_US, 2015),
        ($/GFX/Unit/Descriptor_Unit_MTLB_Strela10M3_SOV, 2016),
        ($/GFX/Unit/Descriptor_Unit_Rifles_half_Dragon_NG_US, 2017),
        ($/GFX/Unit/Descriptor_Unit_Rifles_half_LAW_NG_US, 2018),
        ($/GFX/Unit/Descriptor_Unit_AH64_Apache_NG_US, 2019),
        ($/GFX/Unit/Descriptor_Unit_Rifles_half_CMD_NG_US, 2020),
        ($/GFX/Unit/Descriptor_Unit_Su_22_AT_SOV, 2021),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M60_NG_US, 2022),
        ($/GFX/Unit/Descriptor_Unit_ATteam_KonkursM_TTsko_SOV, 2023),
        ($/GFX/Unit/Descriptor_Unit_Engineers_CMD_TTsko_SOV, 2024),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Flam_TTsko_SOV, 2025),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Scout_TTsko_SOV, 2026),
        ($/GFX/Unit/Descriptor_Unit_Engineers_TTsko_SOV, 2027),
        ($/GFX/Unit/Descriptor_Unit_FireSupport_TTsko_SOV, 2028),
        ($/GFX/Unit/Descriptor_Unit_HvyScout_TTsko_SOV, 2029),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Igla_TTsko_SOV, 2030),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_BTR_TTsko_SOV, 2031),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_CMD_TTsko_SOV, 2032),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_TTsko_SOV, 2033),
        ($/GFX/Unit/Descriptor_Unit_Scout_TTsko_SOV, 2034),
        ($/GFX/Unit/Descriptor_Unit_DCA_ZU_23_2_TTsko_SOV, 2035),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_AGS17_TTsko_SOV, 2036),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_PKM_TTsko_SOV, 2038),
        ($/GFX/Unit/Descriptor_Unit_Mortier_2S12_120mm_TTsko_SOV, 2039),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_HMG_TTsko_SOV, 2040),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_Metis_TTsko_SOV, 2041),
        ($/GFX/Unit/Descriptor_Unit_BMP_2_reco_SOV, 2042),
        ($/GFX/Unit/Descriptor_Unit_BRDM_2_Konkurs_M_SOV, 2043),
        ($/GFX/Unit/Descriptor_Unit_BTR_80_CMD_SOV, 2044),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Konkurs_TTsko_SOV, 2045),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_NSV_TTsko_SOV, 2046),
        ($/GFX/Unit/Descriptor_Unit_Scout_Spetsnaz_SOV, 2047),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Stinger_NG_US, 2048),
        ($/GFX/Unit/Descriptor_Unit_Scout_NG_US, 2049),
        ($/GFX/Unit/Descriptor_Unit_EH60A_EW_US, 2050),
        ($/GFX/Unit/Descriptor_Unit_ATteam_RCL_SPG9_SOV, 2051),
        ($/GFX/Unit/Descriptor_Unit_MiG_23MLD_SOV, 2052),
        ($/GFX/Unit/Descriptor_Unit_Mortier_240mm_M240_POL, 2053),
        ($/GFX/Unit/Descriptor_Unit_BMD_1K_CMD_SOV, 2054),
        ($/GFX/Unit/Descriptor_Unit_BMD_2_CMD_SOV, 2055),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_RPG22_SOV, 2056),
        ($/GFX/Unit/Descriptor_Unit_Alfa_Group_SOV, 2057),
        ($/GFX/Unit/Descriptor_Unit_F16A_CBU_BEL, 2058),
        ($/GFX/Unit/Descriptor_Unit_Reserve_CMD_DDR, 2070),
        ($/GFX/Unit/Descriptor_Unit_Reserve_HMG_DDR, 2071),
        ($/GFX/Unit/Descriptor_Unit_T54B_CMD_DDR, 2072),
        ($/GFX/Unit/Descriptor_Unit_T54B_DDR, 2073),
        ($/GFX/Unit/Descriptor_Unit_MCV_80_Warrior_CMD_UK, 2074),
        ($/GFX/Unit/Descriptor_Unit_M551A1_ACAV_Sheridan_US, 2080),
        ($/GFX/Unit/Descriptor_Unit_LAV_25_M1047_US_US, 2081),
        ($/GFX/Unit/Descriptor_Unit_M163_CS_US, 2082),
        ($/GFX/Unit/Descriptor_Unit_Ranger_Dragon_US, 2083),
        ($/GFX/Unit/Descriptor_Unit_HvyScout_DShV_SOV, 2084),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TV_Gunship_SOV, 2085),
        ($/GFX/Unit/Descriptor_Unit_AEC_Militant_UK, 2086),
        ($/GFX/Unit/Descriptor_Unit_FV4003_Centurion_AVRE_ROMOR_UK, 2087),
        ($/GFX/Unit/Descriptor_Unit_FV432_MILAN_UK, 2089),
        ($/GFX/Unit/Descriptor_Unit_FV432_SCAT_UK, 2090),
        ($/GFX/Unit/Descriptor_Unit_M113A1B_Radar_BEL, 2091),
        ($/GFX/Unit/Descriptor_Unit_FV4201_Chieftain_Mk6_UK, 2092),
        ($/GFX/Unit/Descriptor_Unit_2S1M_POL, 2093),
        ($/GFX/Unit/Descriptor_Unit_BRDM_1_DShK_POL, 2094),
        ($/GFX/Unit/Descriptor_Unit_BRDM_1_POL, 2095),
        ($/GFX/Unit/Descriptor_Unit_BRDM_1_PSNR1_POL, 2096),
        ($/GFX/Unit/Descriptor_Unit_LUAZ_967M_AGL_SOV, 2097),
        ($/GFX/Unit/Descriptor_Unit_LUAZ_967M_Fagot_SOV, 2098),
        ($/GFX/Unit/Descriptor_Unit_LUAZ_967M_SOV, 2099),
        ($/GFX/Unit/Descriptor_Unit_LUAZ_967M_SPG9_SOV, 2100),
        ($/GFX/Unit/Descriptor_Unit_Unimog_U1350L_BEL, 2102),
        ($/GFX/Unit/Descriptor_Unit_Unimog_U1350L_Para_BEL, 2103),
        ($/GFX/Unit/Descriptor_Unit_ASU_85_POL, 2105),
        ($/GFX/Unit/Descriptor_Unit_Mi_24D_Desant_SOV, 2106),
        ($/GFX/Unit/Descriptor_Unit_MP_Combat_SOV, 2107),
        ($/GFX/Unit/Descriptor_Unit_Scout_KdA_DDR, 2108),
        ($/GFX/Unit/Descriptor_Unit_Mi_8MTPI_SOV, 2109),
        ($/GFX/Unit/Descriptor_Unit_Mi_8PPA_SOV, 2110),
        ($/GFX/Unit/Descriptor_Unit_Puma_HET_FR, 2112),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Airmobile_UK, 2113),
        ($/GFX/Unit/Descriptor_Unit_M270_MLRS_cluster_UK, 2114),
        ($/GFX/Unit/Descriptor_Unit_AT_Group_Gurkhas_UK, 2115),
        ($/GFX/Unit/Descriptor_Unit_CH47_Chinook_UK, 2116),
        ($/GFX/Unit/Descriptor_Unit_A109BA_BEL, 2117),
        ($/GFX/Unit/Descriptor_Unit_Alouette_II_trans_BEL, 2118),
        ($/GFX/Unit/Descriptor_Unit_M113A1G_MILAN_RFA, 2119),
        ($/GFX/Unit/Descriptor_Unit_F16E_AA2_US, 2120),
        ($/GFX/Unit/Descriptor_Unit_MH47D_Super_Chinook_US, 2121),
        ($/GFX/Unit/Descriptor_Unit_MiG_27M_CLU_SOV, 2122),
        ($/GFX/Unit/Descriptor_Unit_Jager_Carl_RFA, 2123),
        ($/GFX/Unit/Descriptor_Unit_Jager_noAT_RFA, 2124),
        ($/GFX/Unit/Descriptor_Unit_Airmobile_Mot_CMD_UK, 2125),
        ($/GFX/Unit/Descriptor_Unit_Airmobile_Mot_UK, 2126),
        ($/GFX/Unit/Descriptor_Unit_MCV_80_Warrior_MILAN_ERA_UK, 2127),
        ($/GFX/Unit/Descriptor_Unit_Lynx_AH_Mk7_Chancellor_UK, 2128),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_NSV_VDV_SOV, 2129),
        ($/GFX/Unit/Descriptor_Unit_ERC_90_Sagaie_CMD_FR, 2130),
        ($/GFX/Unit/Descriptor_Unit_Su_24MP_EW_SOV, 2131),
        ($/GFX/Unit/Descriptor_Unit_Star_266_supply_POL, 2132),
        ($/GFX/Unit/Descriptor_Unit_T72_CMD_DDR, 2133),
        ($/GFX/Unit/Descriptor_Unit_Luftsturmjager_CMD_DDR, 2135),
        ($/GFX/Unit/Descriptor_Unit_Luftsturmjager_DDR, 2136),
        ($/GFX/Unit/Descriptor_Unit_MiG_29_AA3_SOV, 2138),
        ($/GFX/Unit/Descriptor_Unit_Howz_M30_122mm_DDR, 2139),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_Strela_DDR, 2140),
        ($/GFX/Unit/Descriptor_Unit_Rover_101FC_LUX, 2141),
        ($/GFX/Unit/Descriptor_Unit_FOB_POL, 2142),
        ($/GFX/Unit/Descriptor_Unit_M35_trans_DDR, 2143),
        ($/GFX/Unit/Descriptor_Unit_M151_MUTT_trans_DDR, 2144),
        ($/GFX/Unit/Descriptor_Unit_L39ZO_HE1_DDR, 2145),
        ($/GFX/Unit/Descriptor_Unit_L39ZO_CLU_DDR, 2147),
        ($/GFX/Unit/Descriptor_Unit_BM14M_POL, 2148),
        ($/GFX/Unit/Descriptor_Unit_BRDM_1_DDR, 2149),
        ($/GFX/Unit/Descriptor_Unit_DCA_AZP_S60_DDR, 2150),
        ($/GFX/Unit/Descriptor_Unit_MTLB_CMD_DDR, 2151),
        ($/GFX/Unit/Descriptor_Unit_T54B_CMD_POL, 2152),
        ($/GFX/Unit/Descriptor_Unit_T54B_POL, 2153),
        ($/GFX/Unit/Descriptor_Unit_L39ZO_NPLM_SOV, 2154),
        ($/GFX/Unit/Descriptor_Unit_L39ZO_HE1_SOV, 2155),
        ($/GFX/Unit/Descriptor_Unit_MiG_21bis_AA3_DDR, 2156),
        ($/GFX/Unit/Descriptor_Unit_MiG_21bis_CLU_DDR, 2157),
        ($/GFX/Unit/Descriptor_Unit_MiG_21bis_NPLM_DDR, 2158),
        ($/GFX/Unit/Descriptor_Unit_Lynx_AH_Mk1_LBH_UK, 2159),
        ($/GFX/Unit/Descriptor_Unit_Groupe_AT_Wach_DDR, 2160),
        ($/GFX/Unit/Descriptor_Unit_Scout_Wach_DDR, 2161),
        ($/GFX/Unit/Descriptor_Unit_Wachregiment_RPG_DDR, 2162),
        ($/GFX/Unit/Descriptor_Unit_A109BA_TOW_BEL, 2163),
        ($/GFX/Unit/Descriptor_Unit_A109BA_TOW_twin_BEL, 2164),
        ($/GFX/Unit/Descriptor_Unit_JOH_58C_US, 2165),
        ($/GFX/Unit/Descriptor_Unit_2K11_KRUG_DDR, 2166),
        ($/GFX/Unit/Descriptor_Unit_2K11_KRUG_POL, 2167),
        ($/GFX/Unit/Descriptor_Unit_2K11_KRUG_SOV, 2168),
        ($/GFX/Unit/Descriptor_Unit_LO_1800_ZPU_2_POL, 2170),
        ($/GFX/Unit/Descriptor_Unit_MTLB_TRI_Hors_POL, 2171),
        ($/GFX/Unit/Descriptor_Unit_Rifles_Berlin_UK, 2174),
        ($/GFX/Unit/Descriptor_Unit_Rifles_DMR_FR, 2175),
        ($/GFX/Unit/Descriptor_Unit_BTR_60_reco_DDR, 2176),
        ($/GFX/Unit/Descriptor_Unit_BTR_60_reco_SOV, 2177),
        ($/GFX/Unit/Descriptor_Unit_KrAZ_255B_supply_DDR, 2178),
        ($/GFX/Unit/Descriptor_Unit_M60A3_Patton_NG_US, 2179),
        ($/GFX/Unit/Descriptor_Unit_Sniper_M82_US, 2180),
        ($/GFX/Unit/Descriptor_Unit_MotRifles_RPG27_DDR, 2181),
        ($/GFX/Unit/Descriptor_Unit_F5A_FreedomFighter_AA_NL, 2182),
        ($/GFX/Unit/Descriptor_Unit_F16A_AA2_NL, 2183),
        ($/GFX/Unit/Descriptor_Unit_F5A_FreedomFighter_RKT_NL, 2184),
        ($/GFX/Unit/Descriptor_Unit_Epervier_BEL, 2185),
        ($/GFX/Unit/Descriptor_Unit_FV601_Saladin_UK, 2186),
        ($/GFX/Unit/Descriptor_Unit_FV603_Saracen_UK, 2187),
        ($/GFX/Unit/Descriptor_Unit_Luftsturmjager_Metis_DDR, 2188),
        ($/GFX/Unit/Descriptor_Unit_AH6G_Little_Bird_US, 2189),
        ($/GFX/Unit/Descriptor_Unit_F4_Phantom_GR2_NPLM_UK, 2190),
        ($/GFX/Unit/Descriptor_Unit_DAF_YA_4400_supply_NL, 2191),
        ($/GFX/Unit/Descriptor_Unit_Security_NL, 2192),
        ($/GFX/Unit/Descriptor_Unit_MP_AT_NL, 2193),
        ($/GFX/Unit/Descriptor_Unit_MP_CMD_NL, 2194),
        ($/GFX/Unit/Descriptor_Unit_ATteam_TOW2_NL, 2195),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Mistral_para_BEL, 2196),
        ($/GFX/Unit/Descriptor_Unit_Paratroopers_Engineers_CarlG_UK, 2197),
        ($/GFX/Unit/Descriptor_Unit_Unimog_U1350L_supply_BEL, 2198),
        ($/GFX/Unit/Descriptor_Unit_FV103_Spartan_para_BEL, 2200),
        ($/GFX/Unit/Descriptor_Unit_FV105_Sultan_para_BEL, 2201),
        ($/GFX/Unit/Descriptor_Unit_Fallschirm_Engineers_RFA, 2202),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_1_para_BEL, 2203),
        ($/GFX/Unit/Descriptor_Unit_Groupe_AT_Marines_NL, 2204),
        ($/GFX/Unit/Descriptor_Unit_Mortier_107mm_NL, 2205),
        ($/GFX/Unit/Descriptor_Unit_Commando_CMD_NL, 2207),
        ($/GFX/Unit/Descriptor_Unit_Commando_NL, 2208),
        ($/GFX/Unit/Descriptor_Unit_Atteam_Dragon_Marines_NL, 2209),
        ($/GFX/Unit/Descriptor_Unit_Mortier_M29_81mm_Marines_NL, 2210),
        ($/GFX/Unit/Descriptor_Unit_Security_Mobile_NL, 2211),
        ($/GFX/Unit/Descriptor_Unit_DAF_YHZ_2300_trans_NL, 2212),
        ($/GFX/Unit/Descriptor_Unit_M270_MLRS_cluster_NL, 2213),
        ($/GFX/Unit/Descriptor_Unit_Rifles_Mot_UK, 2214),
        ($/GFX/Unit/Descriptor_Unit_Pioneer_UK, 2215),
        ($/GFX/Unit/Descriptor_Unit_ASU_85_CMD_POL, 2216),
        ($/GFX/Unit/Descriptor_Unit_BAV_485_POL, 2217),
        ($/GFX/Unit/Descriptor_Unit_GAZ_66B_supply_POL, 2218),
        ($/GFX/Unit/Descriptor_Unit_GAZ_66B_supply_SOV, 2219),
        ($/GFX/Unit/Descriptor_Unit_Mortier_M43_82mm_Para_POL, 2220),
        ($/GFX/Unit/Descriptor_Unit_T64A_CMD_SOV, 2221),
        ($/GFX/Unit/Descriptor_Unit_Mi_6_SOV, 2222),
        ($/GFX/Unit/Descriptor_Unit_Commandos_Para_CMD_POL, 2223),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Scout_POL, 2224),
        ($/GFX/Unit/Descriptor_Unit_Groupe_AT_Para_POL, 2225),
        ($/GFX/Unit/Descriptor_Unit_Security_SOV, 2226),
        ($/GFX/Unit/Descriptor_Unit_Para_Marine_FR, 2227),
        ($/GFX/Unit/Descriptor_Unit_UAZ_469_AGL_Grenzer_DDR, 2228),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M2HB_UK, 2229),
        ($/GFX/Unit/Descriptor_Unit_BGS_hvy_RFA, 2230),
        ($/GFX/Unit/Descriptor_Unit_MiG_23BN_KMGU_DDR, 2232),
        ($/GFX/Unit/Descriptor_Unit_Reserve_CMD_POL, 2233),
        ($/GFX/Unit/Descriptor_Unit_Reserve_POL, 2234),
        ($/GFX/Unit/Descriptor_Unit_T34_85M_CMD_POL, 2235),
        ($/GFX/Unit/Descriptor_Unit_T34_85M_POL, 2236),
        ($/GFX/Unit/Descriptor_Unit_AT_D48_85mm_POL, 2237),
        ($/GFX/Unit/Descriptor_Unit_DCA_ZU_23_2_nonPara_SOV, 2238),
        ($/GFX/Unit/Descriptor_Unit_T64AM_SOV, 2239),
        ($/GFX/Unit/Descriptor_Unit_Mi_24D_s5_AT_SOV, 2240),
        ($/GFX/Unit/Descriptor_Unit_Mi_24D_s8_AT_SOV, 2241),
        ($/GFX/Unit/Descriptor_Unit_CL_289_RFA, 2242),
        ($/GFX/Unit/Descriptor_Unit_MiG_27K_AT1_SOV, 2248),
        ($/GFX/Unit/Descriptor_Unit_MiG_27K_AT2_SOV, 2249),
        ($/GFX/Unit/Descriptor_Unit_BTR_60_CHAIKA_CMD_DDR, 2250),
        ($/GFX/Unit/Descriptor_Unit_BTR_60_CHAIKA_CMD_SOV, 2251),
        ($/GFX/Unit/Descriptor_Unit_BTR_70D_SOV, 2252),
        ($/GFX/Unit/Descriptor_Unit_BTR_70_AGS_SOV, 2253),
        ($/GFX/Unit/Descriptor_Unit_BTR_70_MP_SOV, 2254),
        ($/GFX/Unit/Descriptor_Unit_BTR_70_Rys_SOV, 2255),
        ($/GFX/Unit/Descriptor_Unit_BTR_70_S5_SOV, 2256),
        ($/GFX/Unit/Descriptor_Unit_BTR_70_S8_SOV, 2257),
        ($/GFX/Unit/Descriptor_Unit_GTMU_1D_AGS_SOV, 2258),
        ($/GFX/Unit/Descriptor_Unit_GTMU_1D_SOV, 2259),
        ($/GFX/Unit/Descriptor_Unit_GTMU_1D_SPG9_SOV, 2260),
        ($/GFX/Unit/Descriptor_Unit_GTMU_1D_ZU_SOV, 2261),
        ($/GFX/Unit/Descriptor_Unit_Gama_Goat_supply_US, 2262),
        ($/GFX/Unit/Descriptor_Unit_Gama_Goat_trans_US, 2263),
        ($/GFX/Unit/Descriptor_Unit_Ural_4320_Metla_SOV, 2264),
        ($/GFX/Unit/Descriptor_Unit_Ural_4320_ZPU_SOV, 2265),
        ($/GFX/Unit/Descriptor_Unit_Ural_4320_ZU_SOV, 2266),
        ($/GFX/Unit/Descriptor_Unit_ZSU_23_Shilka_reco_SOV, 2267),
        ($/GFX/Unit/Descriptor_Unit_DShV_Hvy_SOV, 2268),
        ($/GFX/Unit/Descriptor_Unit_DShV_RPG16_SOV, 2269),
        ($/GFX/Unit/Descriptor_Unit_T62MD_SOV, 2270),
        ($/GFX/Unit/Descriptor_Unit_T62MD1_SOV, 2271),
        ($/GFX/Unit/Descriptor_Unit_T62MD_CMD_SOV, 2272),
        ($/GFX/Unit/Descriptor_Unit_BMP_2D_reco_SOV, 2273),
        ($/GFX/Unit/Descriptor_Unit_Mortier_2B14_82mm_SOV, 2274),
        ($/GFX/Unit/Descriptor_Unit_ATteam_RCL_SPG9_DShV_SOV, 2275),
        ($/GFX/Unit/Descriptor_Unit_Atteam_Fagot_DShV_SOV, 2276),
        ($/GFX/Unit/Descriptor_Unit_Atteam_Konkurs_DShV_SOV, 2277),
        ($/GFX/Unit/Descriptor_Unit_DShV_Afgantsy_SOV, 2278),
        ($/GFX/Unit/Descriptor_Unit_DShV_HMG_SOV, 2279),
        ($/GFX/Unit/Descriptor_Unit_DShV_Metis_SOV, 2280),
        ($/GFX/Unit/Descriptor_Unit_Engineers_CMD_DShV_SOV, 2281),
        ($/GFX/Unit/Descriptor_Unit_Engineers_DShV_SOV, 2282),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Flam_DShV_SOV, 2283),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_AGS17_DShV_SOV, 2284),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_NSV_DShV_SOV, 2285),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_PKM_DShV_SOV, 2286),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Igla_DShV_SOV, 2287),
        ($/GFX/Unit/Descriptor_Unit_Mortier_2S12_120mm_DShV_SOV, 2288),
        ($/GFX/Unit/Descriptor_Unit_Scout_DShV_SOV, 2289),
        ($/GFX/Unit/Descriptor_Unit_Mortier_2B14_82mm_DShV_SOV, 2290),
        ($/GFX/Unit/Descriptor_Unit_Scout_SpetsnazGRU_Stinger_SOV, 2291),
        ($/GFX/Unit/Descriptor_Unit_Mi_24P_AA_SOV, 2292),
        ($/GFX/Unit/Descriptor_Unit_AH64_Apache_ATAS_US, 2293),
        ($/GFX/Unit/Descriptor_Unit_AeroEngineer_CMD_US, 2294),
        ($/GFX/Unit/Descriptor_Unit_AeroEngineers_US, 2296),
        ($/GFX/Unit/Descriptor_Unit_AeroRifles_Dragon_US, 2299),
        ($/GFX/Unit/Descriptor_Unit_Aero_half_AT4_US, 2302),
        ($/GFX/Unit/Descriptor_Unit_Aero_half_Dragon_US, 2303),
        ($/GFX/Unit/Descriptor_Unit_Mortier_107mm_Aero_US, 2304),
        ($/GFX/Unit/Descriptor_Unit_AeroRifles_AT4_US, 2305),
        ($/GFX/Unit/Descriptor_Unit_Aero_half_CMD_US, 2306),
        ($/GFX/Unit/Descriptor_Unit_DeltaForce_US, 2307),
        ($/GFX/Unit/Descriptor_Unit_LRRP_Aero_US, 2308),
        ($/GFX/Unit/Descriptor_Unit_Scout_Aero_US, 2309),
        ($/GFX/Unit/Descriptor_Unit_ATteam_TOW2_Aero_US, 2310),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M60_Aero_US, 2312),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Stinger_C_Aero_US, 2313),
        ($/GFX/Unit/Descriptor_Unit_M998_Humvee_Delta_US, 2314),
        ($/GFX/Unit/Descriptor_Unit_81mm_mortar_Aero_US, 2315),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M2HB_Aero_US, 2316),
        ($/GFX/Unit/Descriptor_Unit_DCA_M167A2_Vulcan_20mm_Aero_US, 2317),
        ($/GFX/Unit/Descriptor_Unit_M274_Mule_ITOW_US, 2318),
        ($/GFX/Unit/Descriptor_Unit_M274_Mule_M2HB_US, 2319),
        ($/GFX/Unit/Descriptor_Unit_M274_Mule_RCL_US, 2320),
        ($/GFX/Unit/Descriptor_Unit_M274_Mule_supply_US, 2321),
        ($/GFX/Unit/Descriptor_Unit_Mortier_240mm_M240_Cluster_SOV, 2322),
        ($/GFX/Unit/Descriptor_Unit_BTR_152A_DDR, 2326),
        ($/GFX/Unit/Descriptor_Unit_DCA_KS19_100mm_DDR, 2327),
        ($/GFX/Unit/Descriptor_Unit_M113A2_TOW_US, 2328),
        ($/GFX/Unit/Descriptor_Unit_M42_Duster_US, 2329),
        ($/GFX/Unit/Descriptor_Unit_M60A1_RISE_Passive_CMD_US, 2330),
        ($/GFX/Unit/Descriptor_Unit_M60A1_RISE_Passive_US, 2331),
        ($/GFX/Unit/Descriptor_Unit_M60A3_ERA_Patton_US, 2332),
        ($/GFX/Unit/Descriptor_Unit_M812_supply_US, 2333),
        ($/GFX/Unit/Descriptor_Unit_AT_vz52_85mm_DDR, 2334),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TB_reco_Marine_DDR, 2336),
        ($/GFX/Unit/Descriptor_Unit_KSK18_DDR, 2337),
        ($/GFX/Unit/Descriptor_Unit_Mi_14PL_AT_DDR, 2338),
        ($/GFX/Unit/Descriptor_Unit_Mi_14PL_recon_DDR, 2339),
        ($/GFX/Unit/Descriptor_Unit_PTS_M_supply_DDR, 2340),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Naval_CMD_DDR, 2341),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Naval_DDR, 2342),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Naval_Flam_DDR, 2343),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Naval_Scout_DDR, 2344),
        ($/GFX/Unit/Descriptor_Unit_HvyScout_NG_US, 2345),
        ($/GFX/Unit/Descriptor_Unit_M60A1_RISE_Passive_reco_US, 2346),
        ($/GFX/Unit/Descriptor_Unit_ATteam_RCL_M40A1_NG_US, 2347),
        ($/GFX/Unit/Descriptor_Unit_M113A1_NG_US, 2348),
        ($/GFX/Unit/Descriptor_Unit_HvyScout_NG_Dragon_US, 2349),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Redeye_US, 2350),
        ($/GFX/Unit/Descriptor_Unit_NatGuard_M67_US, 2351),
        ($/GFX/Unit/Descriptor_Unit_M110A2_Howz_NG_US, 2352),
        ($/GFX/Unit/Descriptor_Unit_M125_HOWZ_NG_US, 2353),
        ($/GFX/Unit/Descriptor_Unit_M728_CEV_NG_US, 2354),
        ($/GFX/Unit/Descriptor_Unit_Mortier_107mm_NG_US, 2355),
        ($/GFX/Unit/Descriptor_Unit_81mm_mortar_NG_US, 2356),
        ($/GFX/Unit/Descriptor_Unit_M113_ACAV_NG_US, 2358),
        ($/GFX/Unit/Descriptor_Unit_LRRP_CEWI_US, 2359),
        ($/GFX/Unit/Descriptor_Unit_NatGuard_Engineers_CMD_US, 2360),
        ($/GFX/Unit/Descriptor_Unit_NatGuard_Engineers_M67_US, 2361),
        ($/GFX/Unit/Descriptor_Unit_NatGuard_Engineers_US, 2362),
        ($/GFX/Unit/Descriptor_Unit_ATteam_ITOW_NG_US, 2363),
        ($/GFX/Unit/Descriptor_Unit_M106A2_Howz_NG_US, 2364),
        ($/GFX/Unit/Descriptor_Unit_NatGuard_Engineers_Flam_US, 2365),
        ($/GFX/Unit/Descriptor_Unit_EBR_90mm_FR, 2373),
        ($/GFX/Unit/Descriptor_Unit_M201_CMD_FR, 2376),
        ($/GFX/Unit/Descriptor_Unit_M201_FR, 2377),
        ($/GFX/Unit/Descriptor_Unit_M201_MG_FR, 2378),
        ($/GFX/Unit/Descriptor_Unit_M201_MILAN_FR, 2379),
        ($/GFX/Unit/Descriptor_Unit_Commandos_Air_FR, 2390),
        ($/GFX/Unit/Descriptor_Unit_FAV_trans_US, 2394),
        ($/GFX/Unit/Descriptor_Unit_LRRP_FOLT_US, 2395),
        ($/GFX/Unit/Descriptor_Unit_GreenBerets_ODA_US, 2396),
        ($/GFX/Unit/Descriptor_Unit_AeroRifles_AB_US, 2397),
        ($/GFX/Unit/Descriptor_Unit_M151C_RCL_NG_US, 2407),
        ($/GFX/Unit/Descriptor_Unit_M60A1_AVLM_US, 2409),
        ($/GFX/Unit/Descriptor_Unit_M1025_Humvee_GVLLD_US, 2410),
        ($/GFX/Unit/Descriptor_Unit_OH58A_reco_NG_US, 2411),
        ($/GFX/Unit/Descriptor_Unit_Mi_8TB_SOV, 2413),
        ($/GFX/Unit/Descriptor_Unit_UH1M_gunship_US, 2414),
        ($/GFX/Unit/Descriptor_Unit_A37B_Dragonfly_US, 2415),
        ($/GFX/Unit/Descriptor_Unit_AH1F_Cobra_NG_US, 2416),
        ($/GFX/Unit/Descriptor_Unit_A37B_Dragonfly_HE_US, 2417),
        ($/GFX/Unit/Descriptor_Unit_A37B_Dragonfly_NPLM_US, 2418),
        ($/GFX/Unit/Descriptor_Unit_Scout_SF_POL, 2419),
        ($/GFX/Unit/Descriptor_Unit_Scout_Reserve_DDR, 2420),
        ($/GFX/Unit/Descriptor_Unit_Su_22_AT2_DDR, 2421),
        ($/GFX/Unit/Descriptor_Unit_M1025_Humvee_MP_US, 2422),
        ($/GFX/Unit/Descriptor_Unit_F4E_Phantom_II_AT_US, 2423),
        ($/GFX/Unit/Descriptor_Unit_Su_24M_LGB2_SOV, 2425),
        ($/GFX/Unit/Descriptor_Unit_Su_17_cluster_POL, 2427),
        ($/GFX/Unit/Descriptor_Unit_Su_22_HE2_DDR, 2428),
        ($/GFX/Unit/Descriptor_Unit_PTS_M_supply_POL, 2429),
        ($/GFX/Unit/Descriptor_Unit_Howz_D1_152mm_POL, 2430),
        ($/GFX/Unit/Descriptor_Unit_LightRifles_Dragon_US, 2431),
        ($/GFX/Unit/Descriptor_Unit_F16E_TER_HE_US, 2432),
        ($/GFX/Unit/Descriptor_Unit_AML_90_CMD_FR, 2433),
        ($/GFX/Unit/Descriptor_Unit_AML_90_Reserve_FR, 2434),
        ($/GFX/Unit/Descriptor_Unit_BMD_3_SOV, 2435),
        ($/GFX/Unit/Descriptor_Unit_BMD_3_reco_SOV, 2436),
        ($/GFX/Unit/Descriptor_Unit_DCA_76T2_20mm_CPA_FR, 2437),
        ($/GFX/Unit/Descriptor_Unit_LUAZ_967M_AGL_VDV_SOV, 2438),
        ($/GFX/Unit/Descriptor_Unit_LUAZ_967M_CMD_VDV_SOV, 2439),
        ($/GFX/Unit/Descriptor_Unit_LUAZ_967M_Fagot_VDV_SOV, 2440),
        ($/GFX/Unit/Descriptor_Unit_LUAZ_967M_SPG9_VDV_SOV, 2441),
        ($/GFX/Unit/Descriptor_Unit_LUAZ_967M_VDV_SOV, 2442),
        ($/GFX/Unit/Descriptor_Unit_Mortier_2B14_82mm_VDV_SOV, 2443),
        ($/GFX/Unit/Descriptor_Unit_Mortier_81mm_FR, 2444),
        ($/GFX/Unit/Descriptor_Unit_VIB_FR, 2445),
        ($/GFX/Unit/Descriptor_Unit_Gendarmerie_Air_FR, 2446),
        ($/GFX/Unit/Descriptor_Unit_Alouette_III_SS11_FR, 2447),
        ($/GFX/Unit/Descriptor_Unit_Alouette_II_CMD_FR, 2448),
        ($/GFX/Unit/Descriptor_Unit_Alouette_II_trans_FR, 2449),
        ($/GFX/Unit/Descriptor_Unit_Ecureuil_20mm_FR, 2450),
        ($/GFX/Unit/Descriptor_Unit_Ecureuil_reco_FR, 2451),
        ($/GFX/Unit/Descriptor_Unit_CM170_Magister_FR, 2452),
        ($/GFX/Unit/Descriptor_Unit_DCA_Bofors_L60_FR, 2453),
        ($/GFX/Unit/Descriptor_Unit_Su_24M_AT1_SOV, 2454),
        ($/GFX/Unit/Descriptor_Unit_Su_24M_AT2_SOV, 2455),
        ($/GFX/Unit/Descriptor_Unit_LuAZ_967M_AA_VDV_SOV, 2460),
        ($/GFX/Unit/Descriptor_Unit_Scout_SIGINT_SOV, 2465),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_NSV_6U6_VDV_SOV, 2468),
        ($/GFX/Unit/Descriptor_Unit_LUAZ_967M_supply_SOV, 2469),
        ($/GFX/Unit/Descriptor_Unit_Su_24M_thermo_SOV, 2470),
        ($/GFX/Unit/Descriptor_Unit_MiG_31_AA1_SOV, 2471),
        ($/GFX/Unit/Descriptor_Unit_MiG_31_AA2_SOV, 2472),
        ($/GFX/Unit/Descriptor_Unit_MiG_27K_SEAD_SOV, 2475),
        ($/GFX/Unit/Descriptor_Unit_Leopard_2A1_NL, 2476),
        ($/GFX/Unit/Descriptor_Unit_M998_Humvee_HMG_US, 2477),
        ($/GFX/Unit/Descriptor_Unit_Mi_24V_RKT2_SOV, 2478),
        ($/GFX/Unit/Descriptor_Unit_BMP_1P_Konkurs_DDR, 2479),
        ($/GFX/Unit/Descriptor_Unit_BMP_1P_Konkurs_SOV, 2480),
        ($/GFX/Unit/Descriptor_Unit_T64B1_reco_SOV, 2481),
        ($/GFX/Unit/Descriptor_Unit_MTLB_Shturm_DDR, 2482),
        ($/GFX/Unit/Descriptor_Unit_Leopard_2A4B_CMD_NL, 2483),
        ($/GFX/Unit/Descriptor_Unit_Leopard_2A4B_NL, 2484),
        ($/GFX/Unit/Descriptor_Unit_Alpha_Jet_E_NPLM_FR, 2485),
        ($/GFX/Unit/Descriptor_Unit_CM170_Magister_SS11_FR, 2486),
        ($/GFX/Unit/Descriptor_Unit_Super_Etendard_AT_FR, 2487),
        ($/GFX/Unit/Descriptor_Unit_Super_Etendard_CLU_FR, 2488),
        ($/GFX/Unit/Descriptor_Unit_LUAZ_967M_FAO_SOV, 2489),
        ($/GFX/Unit/Descriptor_Unit_F4_Phantom_GR2_HE_UK, 2490),
        ($/GFX/Unit/Descriptor_Unit_Scout_Motorized_UK, 2491),
        ($/GFX/Unit/Descriptor_Unit_Scout_RIMa_FR, 2492),
        ($/GFX/Unit/Descriptor_Unit_Sniper_RIMa_FR, 2493),
        ($/GFX/Unit/Descriptor_Unit_Super_Etendard_HE_FR, 2494),
        ($/GFX/Unit/Descriptor_Unit_Reserviste_FAMAS_FR, 2495),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_AANF1_Reserve_FR, 2496),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M2HB_RIMa_FR, 2497),
        ($/GFX/Unit/Descriptor_Unit_Spetsnaz_Vympel_SOV, 2500),
        ($/GFX/Unit/Descriptor_Unit_VDV_Combine_SOV, 2501),
        ($/GFX/Unit/Descriptor_Unit_LandRover_WOMBAT_Gurkhas_UK, 2502),
        ($/GFX/Unit/Descriptor_Unit_F16E_TER_CLU_US, 2503),
        ($/GFX/Unit/Descriptor_Unit_Su_24MP_SEAD2_SOV, 2505),
        ($/GFX/Unit/Descriptor_Unit_BTR_D_reco_SOV, 2506),
        ($/GFX/Unit/Descriptor_Unit_Scout_Spetsnaz_VDV_SOV, 2507),
        ($/GFX/Unit/Descriptor_Unit_VAB_Reserve_FR, 2509),
        ($/GFX/Unit/Descriptor_Unit_Su_25_rkt2_SOV, 2510),
        ($/GFX/Unit/Descriptor_Unit_F8P_Crusader_AA2_FR, 2511),
        ($/GFX/Unit/Descriptor_Unit_FV432_supply_UK, 2512),
        ($/GFX/Unit/Descriptor_Unit_BMP_1_SP2_reco_POL, 2513),
        ($/GFX/Unit/Descriptor_Unit_81mm_mortar_CLU_UK, 2514),
        ($/GFX/Unit/Descriptor_Unit_BRDM_2_CMD_R5_POL, 2515),
        ($/GFX/Unit/Descriptor_Unit_MiG_29_AA2_POL, 2516),
        ($/GFX/Unit/Descriptor_Unit_CUCV_MP_US, 2538),
        ($/GFX/Unit/Descriptor_Unit_CUCV_trans_US, 2540),
        ($/GFX/Unit/Descriptor_Unit_Mortier_2S12_120mm_DDR, 2596),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_PKM_Naval_POL, 2597),
        ($/GFX/Unit/Descriptor_Unit_PT76B_CMD_Naval_POL, 2598),
        ($/GFX/Unit/Descriptor_Unit_PT76B_tank_Naval_POL, 2599),
        ($/GFX/Unit/Descriptor_Unit_BRDM_2_Malyu_P_SOV, 2673),
        ($/GFX/Unit/Descriptor_Unit_CGage_Peacekeeper_US, 2678),
        ($/GFX/Unit/Descriptor_Unit_CGage_V150_Commando_US, 2679),
        ($/GFX/Unit/Descriptor_Unit_CUCV_AGL_US, 2680),
        ($/GFX/Unit/Descriptor_Unit_Dragoon_300_US, 2681),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_M2HB_M63_US, 2682),
        ($/GFX/Unit/Descriptor_Unit_M113A1_ACAV_NG_US, 2683),
        ($/GFX/Unit/Descriptor_Unit_M48A5_reco_NG_US, 2684),
        ($/GFX/Unit/Descriptor_Unit_Howz_M101_105mm_US, 2685),
        ($/GFX/Unit/Descriptor_Unit_M901_TOW_NG_US, 2686),
        ($/GFX/Unit/Descriptor_Unit_Mortier_M29_81mm_US, 2687),
        ($/GFX/Unit/Descriptor_Unit_Alouette_III_SOV, 2688),
        ($/GFX/Unit/Descriptor_Unit_CH54B_Tarhe_supply_US, 2689),
        ($/GFX/Unit/Descriptor_Unit_CH54B_Tarhe_trans_US, 2690),
        ($/GFX/Unit/Descriptor_Unit_OA10A_US, 2694),
        ($/GFX/Unit/Descriptor_Unit_Su_15TM_AA_SOV, 2695),
        ($/GFX/Unit/Descriptor_Unit_Su_27K_SOV, 2696),
        ($/GFX/Unit/Descriptor_Unit_A7D_Corsair_II_US, 2697),
        ($/GFX/Unit/Descriptor_Unit_A6E_Intruder_US, 2698),
        ($/GFX/Unit/Descriptor_Unit_EA6B_Prowler_US, 2699),
        ($/GFX/Unit/Descriptor_Unit_M998_Humvee_AGL_US, 2700),
        ($/GFX/Unit/Descriptor_Unit_LightRifles_AT4_US, 2701),
        ($/GFX/Unit/Descriptor_Unit_LightRifles_Viper_US, 2702),
        ($/GFX/Unit/Descriptor_Unit_Security_USMC_US, 2704),
        ($/GFX/Unit/Descriptor_Unit_HvyScout_NG_Alaska_US, 2705),
        ($/GFX/Unit/Descriptor_Unit_Scout_NG_Alaska_US, 2706),
        ($/GFX/Unit/Descriptor_Unit_Sniper_NG_Alaska_US, 2707),
        ($/GFX/Unit/Descriptor_Unit_MP_Combat_USAF_US, 2708),
        ($/GFX/Unit/Descriptor_Unit_MP_Patrol_USAF_US, 2709),
        ($/GFX/Unit/Descriptor_Unit_Pathfinder_NG_US, 2710),
        ($/GFX/Unit/Descriptor_Unit_A222_Bereg_SOV, 2711),
        ($/GFX/Unit/Descriptor_Unit_AT_KSM65_100mm_SOV, 2712),
        ($/GFX/Unit/Descriptor_Unit_BM24M_SOV, 2713),
        ($/GFX/Unit/Descriptor_Unit_BTR_152A_SOV, 2714),
        ($/GFX/Unit/Descriptor_Unit_BTR_152K_SOV, 2715),
        ($/GFX/Unit/Descriptor_Unit_BTR_152S_CMD_SOV, 2716),
        ($/GFX/Unit/Descriptor_Unit_PT76B_Naval_POL, 2717),
        ($/GFX/Unit/Descriptor_Unit_BTR_40A_SOV, 2718),
        ($/GFX/Unit/Descriptor_Unit_BTR_40B_CMD_SOV, 2719),
        ($/GFX/Unit/Descriptor_Unit_BTR_40_SOV, 2720),
        ($/GFX/Unit/Descriptor_Unit_DCA_KS30_130mm_SOV, 2721),
        ($/GFX/Unit/Descriptor_Unit_Howz_BS3_100mm_SOV, 2722),
        ($/GFX/Unit/Descriptor_Unit_Howz_D1_152mm_SOV, 2724),
        ($/GFX/Unit/Descriptor_Unit_T54B_SOV, 2726),
        ($/GFX/Unit/Descriptor_Unit_T55A_obr81_SOV, 2730),
        ($/GFX/Unit/Descriptor_Unit_IS2M_SOV, 2731),
        ($/GFX/Unit/Descriptor_Unit_Su_15TM_AA2_SOV, 2732),
        ($/GFX/Unit/Descriptor_Unit_Navy_SEAL_US, 2733),
        ($/GFX/Unit/Descriptor_Unit_Partisan_SOV, 2734),
        ($/GFX/Unit/Descriptor_Unit_Reserve_CMD_SOV, 2735),
        ($/GFX/Unit/Descriptor_Unit_AH1S_Cobra_US, 2736),
        ($/GFX/Unit/Descriptor_Unit_Howz_M119_105mm_US, 2737),
        ($/GFX/Unit/Descriptor_Unit_T55AM_1_CMD_SOV, 2738),
        ($/GFX/Unit/Descriptor_Unit_T55AM_1_SOV, 2739),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_Maxim_Reserve_SOV, 2740),
        ($/GFX/Unit/Descriptor_Unit_Howz_Br5M_280mm_SOV, 2741),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Flam_Reserve_SOV, 2742),
        ($/GFX/Unit/Descriptor_Unit_Engineers_Reserve_SOV, 2743),
        ($/GFX/Unit/Descriptor_Unit_HvyScout_Reserve_SOV, 2744),
        ($/GFX/Unit/Descriptor_Unit_Security_Reserve_SOV, 2746),
        ($/GFX/Unit/Descriptor_Unit_A6E_Intruder_SEAD_US, 2747),
        ($/GFX/Unit/Descriptor_Unit_A7D_Corsair_II_AT_US, 2748),
        ($/GFX/Unit/Descriptor_Unit_A7D_Corsair_II_CLU_US, 2749),
        ($/GFX/Unit/Descriptor_Unit_A7D_Corsair_II_RKT_US, 2750),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Strela_3_SOV, 2751),
        ($/GFX/Unit/Descriptor_Unit_ATteam_TOW2A_US, 2765),
        ($/GFX/Unit/Descriptor_Unit_Security_Naval_SOV, 2766),
        ($/GFX/Unit/Descriptor_Unit_ATteam_RCL_B11_Reserve_SOV, 2767),
        ($/GFX/Unit/Descriptor_Unit_M48A5E_CMD_ESP, 2782),
        ($/GFX/Unit/Descriptor_Unit_M48A5E_ESP, 2783),
        ($/GFX/Unit/Descriptor_Unit_UH1H_CMD_ESP, 2790),
        ($/GFX/Unit/Descriptor_Unit_Iltis_MILAN_2_RFA, 2803),
        ($/GFX/Unit/Descriptor_Unit_Marder_1A1_RFA, 2805),
        ($/GFX/Unit/Descriptor_Unit_MANPAD_Strela_2M_Naval_POL, 2839),
        ($/GFX/Unit/Descriptor_Unit_Marder_1A1_MILAN_RFA, 2840),
        ($/GFX/Unit/Descriptor_Unit_DCA_AZP_S60_SOV, 2866),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_DShK_AA_SOV, 2876),
        ($/GFX/Unit/Descriptor_Unit_HMGteam_DShK_SOV, 2877),
        ($/GFX/Unit/Descriptor_Unit_Mortier_PM43_120mm_SOV, 2878),
        ($/GFX/Unit/Descriptor_Unit_BMP_1_SP2_SOV, 2879),
        ($/GFX/Unit/Descriptor_Unit_Reserve_HMG_SOV, 2880),
        ($/GFX/Unit/Descriptor_Unit_M113A1_Dragon_NG_US, 2881),
        ($/GFX/Unit/Descriptor_Unit_CUCV_HMG_US, 2882),
        ($/GFX/Unit/Descriptor_Unit_Gvardeitsy_BTR_SOV, 2922),
        ($/GFX/Unit/Descriptor_Unit_Gvardeitsy_CMD_SOV, 2923),
        ($/GFX/Unit/Descriptor_Unit_Gvardeitsy_HMG_SOV, 2924),
        ($/GFX/Unit/Descriptor_Unit_Gvardeitsy_Metis_SOV, 2925),
        ($/GFX/Unit/Descriptor_Unit_Gvardeitsy_SOV, 2926),
        ($/GFX/Unit/Descriptor_Unit_Gvardeitsy_SVD_SOV, 2927),
        ($/GFX/Unit/Descriptor_Unit_MiG_23MLD_AA1_SOV, 2928),
        ($/GFX/Unit/Descriptor_Unit_ATteam_Milan_2_RIMa_FR, 2941),
        ($/GFX/Unit/Descriptor_Unit_Groupe_AT_US, 2942),
        ($/GFX/Unit/Descriptor_Unit_ATteam_RCL_M40A1_NL, 2946),
    ]
)

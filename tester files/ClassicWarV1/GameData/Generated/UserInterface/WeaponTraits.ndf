// Ne pas éditer, ce fichier est généré par WeaponTraitsFileWriter

WeaponTraits is TWeaponTraitsContainer
(
    Descriptors = MAP
    [

        (
            "CAC",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_cac"
                TraitHintTitleToken    = "HUETOQPSPZ"
                TraitHintBodyToken     = "BFSZZVXMDT"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "CLGP",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_indirect_CLGP"
                TraitHintTitleToken    = "YLOZVWCYJF"
                TraitHintBodyToken     = "DYTVXJJXIQ"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "F&F",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_fireAndForget"
                TraitHintTitleToken    = "INEEJCQGOC"
                TraitHintBodyToken     = "SPZEYDYYBR"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "HE",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_he"
                TraitHintTitleToken    = "QTKHHMBUHB"
                TraitHintBodyToken     = "PDRZVSAGPW"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "HEAT",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_heat"
                TraitHintTitleToken    = "VDBPKKZLTY"
                TraitHintBodyToken     = "LFQJFSOQGY"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "IND",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_indirect"
                TraitHintTitleToken    = "KKWEDNOPPT"
                TraitHintBodyToken     = "JFFKFKSMEO"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "KINETIC",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_ke"
                TraitHintTitleToken    = "EAAVVQBQBO"
                TraitHintBodyToken     = "LZXIDHDYLP"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "MOTION",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_motion"
                TraitHintTitleToken    = "PALYIYEIFQ"
                TraitHintBodyToken     = "QCTXUKECOO"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "OTA",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_OTA"
                TraitHintTitleToken    = "CVSHZKRIKC"
                TraitHintBodyToken     = "BYJNUVZTLL"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "RADAR",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_radar"
                TraitHintTitleToken    = "SCISFGDUFY"
                TraitHintBodyToken     = "RRIVLUVFLL"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "SACLOS",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_manual"
                TraitHintTitleToken    = "AANYVFLPTZ"
                TraitHintBodyToken     = "CFNPWFOSTI"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "STAT",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_static"
                TraitHintTitleToken    = "FBXRVZBVFG"
                TraitHintBodyToken     = "LSWXGNQREO"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "TANDEM",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_tandem"
                TraitHintTitleToken    = "GIQBDXUCJM"
                TraitHintBodyToken     = "FVZWPYWRWX"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "TIRET",
            TWeaponTraitDescriptor
            (
                TraitHintTitleToken    = "GVIOFFUZMV"
                ShowAsFilterInShowroom = False
            )
        ),
        (
            "Unarmed",
            TWeaponTraitDescriptor
            (
                TraitHintTitleToken    = "LKUBBCAHPR"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "antiair",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_antiair"
                TraitHintTitleToken    = "QQRLFIVQTW"
                TraitHintBodyToken     = "SFTLATXUCE"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "autoloader",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_autoloader"
                TraitHintTitleToken    = "WJTUTHRZHE"
                TraitHintBodyToken     = "FWYEZTDXZK"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "cluster",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_cluster"
                TraitHintTitleToken    = "FKERNZHZHY"
                TraitHintBodyToken     = "ZOWOQJMNHC"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "coax",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_coax"
                TraitHintTitleToken    = "WLGQMWJJOA"
                TraitHintBodyToken     = "TPZSXIKNCT"
                ShowAsFilterInShowroom = False
            )
        ),
        (
            "manual",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_manual"
                TraitHintTitleToken    = "BPNVWTJFTS"
                TraitHintBodyToken     = "UMKYYORNLJ"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "napalm",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_napalm"
                TraitHintTitleToken    = "VXXGPWBBFD"
                TraitHintBodyToken     = "NDZBSGPUCF"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "remote",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_remote"
                TraitHintTitleToken    = "UOHAVAKVWG"
                TraitHintBodyToken     = "KBXTNDMOYQ"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "sead",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_sead2"
                TraitHintTitleToken    = "JQXETMKFMA"
                TraitHintBodyToken     = "PXYUWPBJKW"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "semiAuto",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_semiAuto"
                TraitHintTitleToken    = "OMTVAWUGGA"
                TraitHintBodyToken     = "RCAGNLPFSQ"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "smoke",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_smoke"
                TraitHintTitleToken    = "YTHWCIZAJB"
                TraitHintBodyToken     = "TAZBZWNUJQ"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "thermobaric",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_thermobaric"
                TraitHintTitleToken    = "PESOLAOLMA"
                TraitHintBodyToken     = "YUBPNRZZTE"
                ShowAsFilterInShowroom = True
            )
        ),
        (
            "tripod",
            TWeaponTraitDescriptor
            (
                TraitTextureName       = "Texture_Trait_Icon_tripod"
                TraitHintTitleToken    = "MCNEDZAYTO"
                TraitHintBodyToken     = "NXIZYXLVUH"
                ShowAsFilterInShowroom = True
            )
        ),
    ]
)

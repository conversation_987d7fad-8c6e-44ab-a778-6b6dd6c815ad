// Ne pas éditer, ce fichier est généré par AbstractButtonTextureFileWriter



PawnButtonTextureAdditionalBank is TBUCKToolAdditionalTextureBank
(
    Textures = MAP
    [

        ("Texture_Button_Pawn_pion_BEL_10Wing_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_11Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1Wing_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_23Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_2Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_2Wing_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_31Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_349Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_350Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_3Wing_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_42Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_7Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_8Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_9Wing_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_BAF_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_Grp_Eindhoven", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_Grp_Leeuwarden", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_Grp_Twenthe", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_Grp_Volkel", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_KLu_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_3DLMB_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_PLMB40", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_PLMB8", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1LVD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_3LVD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_JBG28", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_JBG37", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_JBG77", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_JG1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_JG2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_JG3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_JG7", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_JG8", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_JG9", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2LwDiv_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_JBG33", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_JBG33_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_JBG34", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_JBG34_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_JBG35", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_JBG35_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_JG74", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_JG74_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_105ADIB_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11ORAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_125ADIB_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_126IAD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GvIAD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16VA_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_19GAPIB", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GAPIB", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_294ORAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_296APIB", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_31IGAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_33IAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_357OCHAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_35IAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_368OCHAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_559APIB", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_6GvIAD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_730APIB", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_73IAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_773IAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_787IAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_833IAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_85IGAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_911APIB", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_931ORAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_968IAP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_12Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_16_20Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_17_31Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_19_92Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2ATAF_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2_15Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_3_4Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_UK_3_4Sq.png"))]),
        ("Texture_Button_Pawn_pion_UK_9_14Sq", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_RAFG_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_RAF_Bruggen_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_RAF_Gutersloh_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_RAF_Laarbruch_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_RAF_Wildenrath_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_10TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_10TFW_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_17AF_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_20TFW_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_22TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_23TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_313TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_32TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_36TFW_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AF_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_4450TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_480TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_48TFW_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_492TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_493TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_494TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_495TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_496TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_4ATAF_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_509TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_50TFW_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_510TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_511TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_512TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_525TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_526TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_527Aggress", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_52TFW_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_53TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_55TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_77TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_78TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_79TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_81TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_81TFW", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_81TFW_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_86TFW_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_91TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_92TFS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_test_avion_alex", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_test_avion_cas", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),

        ("Texture_Button_Pawn_pion_BEL_10PtserBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_12BrigInf_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_14Linie", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_16PtserDiv", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_BEL_16PtserDiv.png"))]),
        ("Texture_Button_Pawn_pion_BEL_16PtserDiv_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_17ArtCheval", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_BEL_17ArtCheval.png"))]),
        ("Texture_Button_Pawn_pion_BEL_17BrigBlind_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_18Artillerie", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_19ArtCheval", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1BEC", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_BEL_1BEC.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1BEC_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1CaC", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DI_12Linie", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DI_15Artillerie", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DI_1Ardennais", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DI_1Artillerie", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DI_1DivInf", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DI_1Karabiniers", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DI_1Lanciers", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_BEL_1DI_1Lanciers.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DI_2Ardennais", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DI_2Artillerie", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DI_2Lanciers", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_BEL_1DI_2Lanciers.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DI_3Carabiniers", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DI_3Lanciers", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_BEL_1DI_3Lanciers.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DI_Bevrijding", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1DivInfanterie_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1Grenadiers", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1Guides", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_BEL_1Guides.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1Karabiniers", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_1PtserInfBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_20Artillerie", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_2BrigMNAD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_2CaC", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_BEL_2CaC.png"))]),
        ("Texture_Button_Pawn_pion_BEL_2Carabiniers", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_2Guides", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_2Karabiniers", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_BEL_2Karabiniers.png"))]),
        ("Texture_Button_Pawn_pion_BEL_3Linie", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_4CaC", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_4Lanciers", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_4Linie", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_4PtserBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_5Linie", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_6Artillerie", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_74Artillerie", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_7BrigInfBlind_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_8Lanciers", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_Artilleriebataljon_43", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_Artilleriebataljon_62", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_COMRECCE_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_EngGroup", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_MNAD_EscBlinde", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_MNAD_ParaCdo_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_MNAD_ParaCdo_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_BEL_MNAD_ParaCdo_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1CorpsArt_134Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1CorpsArt_144Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1CorpsArt_154Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1CorpsArt_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_NL_1Div.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_101Art_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_101TkBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_102Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_11Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_11PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_NL_1Div_11PaInfBat.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_11PainfBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_11TkBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_124Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_12Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_12PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_NL_1Div_12PaInfBat.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_12PainfBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_13PaBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_13PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_14Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_17PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_NL_1Div_17PaInfBat.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_244Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_48PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_49TkBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_59TkBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1Div_Hvy101Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_1LK_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_3_GWG_SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_NL_4Div.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_102Art_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_103Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_104Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_114Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_41Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_41PaBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_41PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_NL_4Div_41PaInfBat.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_41TkBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_42Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_42PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_42PainfBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_42TkBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_43Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_43PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_NL_4Div_43PaInfBat.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_43PainfBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_43TkBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_45PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_47PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_57TkBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_4Div_Hvy102Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_104Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_15PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_16PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_34Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_41PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_41TkBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_42TkBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_43Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_43TkBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_44Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_44PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_47PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_51Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_51PaBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_52PainfBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_52TkBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_53PainfBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5Div_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_5_GWG_SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA_101AARgt_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA_101InfBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA_102InfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA_115AA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA_125AA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA_132InfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA_142PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA_45AA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA_54Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA_55PaInfBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA_Security1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_NL_CLKA_Security2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_10MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_10MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_10MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_18SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_25MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_25MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_25MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_39Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_39Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_40MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_59MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_59MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_59MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_7Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_10MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_11SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_29TK_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_29TK_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_29TK_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_33Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_33Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_3TK_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_3TK_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_3TK_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_42MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_42MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_42MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_43MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_8TK_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_8TK_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_8TK_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_9Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_11TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_124SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_16Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_21MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_25TK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_2Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_2Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_2Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_41MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_41MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_41MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_5MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_5MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_5MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_9MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_9MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_9MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_12MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_14AT", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_12Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_35TK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_37MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_37MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_37MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_45MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_46SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_50MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_50MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_50MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_75MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_75MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_75MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_9Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_9Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_9Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_15MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_100MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_100MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_100MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_13SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_16Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_16Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_17Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_46MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_51MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_51MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_51MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_55MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_55MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_55MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_16MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1Armia", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1Armia_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1AssaultBrig", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1SF", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1eBrig10MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1eBrig11TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1eBrig12MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1eBrig15MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1eBrig16MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1eBrig20TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1eBrig2MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1eBrig4MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1eBrig5TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_1eBrig8MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20AT", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_24TK_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_24TK_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_24TK_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_26MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_28TK_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_28TK_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_28TK_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_36Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_36Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_49MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_49MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_49MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_68TK_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_68TK_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_68TK_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_75SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_8Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_20TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_23Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_23Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_23Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_23Art_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_24Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_24Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_24Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_24Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_24Art_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2Armia", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2Armia_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_10Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_15TK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_27MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_27MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_27MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_2MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_33MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_33MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_33MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_37Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_37Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_37Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_6MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_6MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_6MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_99SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2eBrig10MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2eBrig11TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2eBrig12MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2eBrig15MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2eBrig16MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2eBrig20TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2eBrig2MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2eBrig4MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2eBrig5TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_2eBrig8MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_30RMD_32Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_30RMD_76TKSR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_30RMD_77Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_30RMD_83MSR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_30RMD_84MSR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_30RMD_85MSR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_30RMD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_3eBrig10MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_3eBrig11TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_3eBrig12MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_3eBrig15MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_3eBrig16MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_3eBrig20TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_3eBrig2MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_3eBrig4MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_3eBrig5TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_3eBrig8MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_49AtckHelo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4AssaultBrig", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_11MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_11MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_11MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_128SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_12MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_12MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_12MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_12MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_17MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_17MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_17MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_18TK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_22Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_22Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_22Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_25Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4eBrig11TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4eBrig20TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_4eBrig5TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_56AtckHelo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5Art_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5Art_6", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5Art_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5AssaultBrig", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_113Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_113Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_13MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_13MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_13MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_23TK_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_23TK_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_23TK_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_25MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_27TK_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_27TK_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_27TK_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_2Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_5SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_73TK_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_73TK_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_73TK_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_5TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_61SAMBrig_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_61SAMBrig_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_61SAMBrig_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_61SAMBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_6Airborne_10Para", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_6Airborne_16Para", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_6Airborne_18Para", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_6Airborne_6Para", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_6Airborne_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_6Airborne_HQ", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_6Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_6Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_6Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_6Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_6Art_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_6Art_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Art_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Art_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Marine_12Tkb", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Marine_20MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Marine_34MSP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Marine_35MSP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Marine_41Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Marine_4MSP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Marine_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_7Marine_HQ", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8Art_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8Art_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_16TK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_28MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_28MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_28MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_32MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_32MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_32MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_36MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_36MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_36MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_47MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_4Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_4Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_4Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_5Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_83SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_8MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_91AT", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_LWP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_LWP_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_ZgrDes", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_POL_ZgrDes_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_10Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_10Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_10Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_10Aufk", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_10MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_10PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_10PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_10PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_10SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_14MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_14MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_14MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_15MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_15MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_15MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_16MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_16MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_16MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_10MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_11Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_11Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_11Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_11Aufk", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_11MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_11PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_11PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_11PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_11SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_16MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_16MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_16MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_17MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_17MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_17MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_18MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_18MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_18MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_11MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_15ResPzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_17Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_17Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_17Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_17Aufk", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_17MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_17PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_17PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_17PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_17SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_41MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_41MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_41MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_42MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_42MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_42MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_43MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_43MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_43MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_17MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_19MSD.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_19Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_19Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_19Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_19Aufk", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_19Flak", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_19MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_19PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_19MSD_19PzR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_19PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_19PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_30MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_30MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_30MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_31MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_19MSD_31MSR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_31MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_31MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_32MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_19MSD_32MSR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_32MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_32MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_19MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_1Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_1Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_1Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_1Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_1Aufk", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_1MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_1MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_1MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_1MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_1PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_1PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_1PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_2MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_2MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_2MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_3MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_3MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_3MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1eBrig10MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1eBrig11MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1eBrig17MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1eBrig19MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1eBrig1MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1eBrig20MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1eBrig4MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1eBrig7PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1eBrig8MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_1eBrig9PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_20Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_20Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_20Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_20Aufk", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_20MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_20PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_20PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_20PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_20SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_33MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_33MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_33MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_34MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_34MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_34MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_35MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_35MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_35MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_20MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_25MSR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_25MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_25MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_25MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_25MSR_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_25MSR_SAM_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_25MSR_SAM_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_2eBrig10MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_2eBrig11MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_2eBrig17MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_2eBrig19MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_2eBrig1MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_2eBrig20MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_2eBrig4MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_2eBrig7PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_2eBrig8MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_2eBrig9PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_3eBrig10MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_3eBrig11MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_3eBrig17MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_3eBrig19MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_3eBrig1MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_3eBrig20MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_3eBrig4MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_3eBrig7PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_3eBrig8MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_3eBrig9PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_22MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_22MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_22MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_23MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_23MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_23MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_24MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_24MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_24MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_4Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_4Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_4Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_4Aufk", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_4MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_4PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_4PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_4PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_4SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4eBrig10MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4eBrig11MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4eBrig17MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4eBrig19MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4eBrig1MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4eBrig20MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4eBrig4MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4eBrig7PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4eBrig8MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_4eBrig9PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_5ArtBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_5GEWR_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_7PzD.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_14PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_7PzD_14PzR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_14PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_14PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_15PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_7PzD_15PzR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_15PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_15PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_16PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_7PzD_16PzR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_16PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_16PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_7Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_7Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_7Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_7Aufk", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_7MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_7MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_7PzD_7MSR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_7MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_7MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_7SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_7PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8GA_MBIII_3ArtBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8GA_MBIII_3Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8GA_MBIII_3Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8GA_MBIII_3Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8GA_MBIII_3Art_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_8MSD.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_27MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_8MSD_27MSR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_27MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_27MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_28MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_8MSD_28MSR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_28MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_28MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_29MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_8MSD_29MSR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_29MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_29MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_8Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_8Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_8Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_8Aufk", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_8MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_8PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_8MSD_8PzR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_8PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_8PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_8SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_8MSD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_9PzD.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_21PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_9PzD_21PzR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_21PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_21PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_22PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_9PzD_22PzR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_22PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_22PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_23PzR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_9PzD_23PzR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_23PzR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_23PzR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_9Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_9Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_9Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_9Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_9Art_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_9Aufk", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_9MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_9MSR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_9PzD_9MSR_1.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_9MSR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_9MSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_9SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_9PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_13ResMSB", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_23ResMSB", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_2Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_33ResMSB", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_3AT", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_3Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_40ArtBrig_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_40ArtBrig_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_40ArtBrig_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_40ArtBrig_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_40ArtBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_40ArtBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_43ResMSB", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_57AtckHelo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_FJB40", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_RDA_MBIII_FJB40.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_FJB41", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBIII_LStR40_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBV_5AT", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBV_5Art_KG1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBV_5Art_KG2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBV_5AtckHelo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBV_5Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBV_5GEWR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBV_5GEWR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBV_5GEWR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBV_5SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RDA_MBV_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_111Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_112MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_116Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_117Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_118Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_11Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_311Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_312PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_313PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_314Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_315Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_321Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_322PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_323PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_324Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_325Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_331MxPz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_332PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_333Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_334Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_335Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_11PzGrD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_121Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_122MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_126Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_127Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_RFA_12PzD_127Jag.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_128Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_12Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_12Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_341MxPz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_342PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_343Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_344Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_345Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_351Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_351MxPzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_352PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_353PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_354Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_3555Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_361MxPz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_362PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_363Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_364Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_365Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_12PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1Brig11PzGr_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1Brig12Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1Brig1Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1Brig2PzGr_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1Brig3Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1Brig5Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1Brig7Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1K", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1K_110Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1K_120Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1K_1EngBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1K_Flieger16", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1K_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_11Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_11MxPzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_12MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_12PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_13PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_14Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_15Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_16Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_17Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_18Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_1Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_1Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_21MxPz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_22PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_23Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_24Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_25Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_31MxPz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_32PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_33Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_34Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_35Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_1PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_26AB_261", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_RFA_26AB_261.png"))]),
        ("Texture_Button_Pawn_pion_RFA_26AB_262", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_26AB_263", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_26AB_264", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_26LuftBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2Brig11PzGr_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2Brig12Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2Brig1Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2Brig2PzGr_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2Brig3Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2Brig5Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2Brig7Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_21Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_22MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_26Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_27Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_28Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_2Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_2Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_41Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_42PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_43PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_44Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_45Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_51MxPzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_52PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_53PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_54Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_55Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_61MxPz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_62PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_63Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_64Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_RFA_2PzGrD_64Pz.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_65Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_2PzGrD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3Brig11PzGr_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3Brig12Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3Brig1Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3Brig2PzGr_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3Brig3Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3Brig5Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3Brig7Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3BrigMNAD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_300Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_310Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_320Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_340Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_350Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_3EngBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_3FlakBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_3HFG_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_FlaK300", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_FlaK330", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_FlaK340", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_RFA_3K_FlaK340.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_Flieger30", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_Flieger36", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3K_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_31Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_32MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_36Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_37Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_38Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_3Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_3Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_71MxPzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_72PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_73PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_74Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_75Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_81MxPz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_82PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_83Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_84Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_85Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_91MxPz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_92PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_93Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_94Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_95Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_3PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_131MxPzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_RFA_5PzD_131MxPzG.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_132PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_133PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_134Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_135Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_141MxPz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_RFA_5PzD_141MxPz.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_142PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_143Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_144Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_145Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_151MxPz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_152PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_153Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_154Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_155Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_51Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_52MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_56Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_57Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_58Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_5Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_5Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_5PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_191MxPzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_192PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_193PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_194Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_195Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_201MxPz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_202PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_203Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_204Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_205Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_211MxPz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_212PzG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_213Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_214Pz", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_215Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_71Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_72MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_76Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_77Jag", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_78Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_7Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_7Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_7PzD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_ArtBrig11PzGr_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_ArtBrig12Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_ArtBrig1Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_ArtBrig2PzGr_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_ArtBrig3Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_ArtBrig5Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_ArtBrig7Pz_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_CENT_FlaRak31", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_CENT_FlaRak32", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_CENT_FlaRak33", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_CENT_FlaRak34", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_CENT_FlaRak35", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_CENT_FlaRak36", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_CENT_FlaRak37", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_CENT_FlaRak38", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_CENT_FlaRak39", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_CENT_FlaRak41", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_CENT_FlaRak42", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_CENT_FlaRak43", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_CENT_Luftwaffe", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_FlaKdo1_FlaK100", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_FlaKdo1_FlaK130", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_FlaKdo1_FlaK140", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_FlaKdo1_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_FlakRakRgt14_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_FlakRakRgt3_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_FlakRakRgt4_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_FlakRakRgt6_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_Luftwaffe_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_MNAD_Luft261", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_MNAD_Luft262", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_MNAD_Luft263", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_52Brig_Art525", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_52Brig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_52Brig_Jag521", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_52Brig_Jag522", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_52Brig_Pz523", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_52Brig_Pz524", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_53Brig_Art535", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_53Brig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_53Brig_Jag531", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_53Brig_Jag532", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_53Brig_Pz533", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_53Brig_Pz534", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_62Brig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_62Brig_Jag621", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_62Brig_Jag622", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_62Brig_Pz623", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_63Brig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_72EngRgt", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_73Brig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_73EngRgt", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_Art635", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_Jag631", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_Jag632", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_Jag731", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_Jag732", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_Jag733", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_Pz633", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK20", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK22_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK22_VKK221", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK22_VKK222", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK24_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK24_VKK241", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK24_VKK242", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK24_VKK243", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK24_VKK244", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK24_VKK245", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK25_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK25_VKK251", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK25_VKK253", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK25_VKK254", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK31_4301Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK31_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK31_VKK311", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK31_VKK312", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK31_VKK313", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK31_VKK314", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK32_4321Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK32_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK32_VKK321", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK32_VKK322", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK32_VKK323", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK32_VKK324", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK33_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK33_VKK331", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK33_VKK333", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK33_VKK334", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK34_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK34_VKK342", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK34_VKK343", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK34_VKK344", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK34_VKK345", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK35_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK35_VKK352", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK35_VKK353", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK35_VKK354", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK_III_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_VBK_II_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_WBKIII_83Rgt", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_WBKIII_93Rgt", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_WBKII_72Rgt", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKNord_WBKII_82Rgt", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_441Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_4431Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_4432Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_4433Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_4451Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_4452Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_4453Sich", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_54Brig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_64Brig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_740Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_740MP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_741Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_741MP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_742Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_742MP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_743MP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_74PioRgt_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Art545", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Art645", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Heimat71_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Heimat84_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Heimat94_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Jag541", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_RFA_TKSud_Jag541.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Jag542", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Jag641", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_RFA_TKSud_Jag641.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Jag642", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Jag711", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Jag712", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Jag713", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Jag841", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Jag842", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Jag843", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Jag941", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Jag942", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Jag943", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Pz543", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Pz544", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_Pz643", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VBK41_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VBK42_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VBK43_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VBK44_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VBK45_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VBK46_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VBK47_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK411", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK412", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK413", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK421", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK422", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK431", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK432", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK441", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK442", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK451", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK452", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK453", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK461", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_RFA_TKSud_VKK471", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_112Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_248GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_248GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_248MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_359SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_61GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_61GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_61GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_62GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_62GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_62GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_63GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_63GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_63GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_744Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_744Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_744Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_10GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_115TkInde_7Tank", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_115TkInde_81Tank", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_115TkInde_9Tank", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_115TkInde_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_1018AAR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_249GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_11GTkD_249GMSP_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_249GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_249GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_40Tk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_40Tk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_40Tk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_44GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_11GTkD_44GTk_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_44GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_44GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_7GTkD_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_11GTkD_7GTkD_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_7GTkD_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_7GTkD_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_841Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_841Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_841Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_9Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_11GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_117Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_117Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_117Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_117Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_18Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_200GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_12GTkD_200GMSP_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_200GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_332GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_12GTkD_332GTk_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_332GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_332GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_353GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_12GTkD_353GTk_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_353GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_353GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_48GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_12GTkD_48GTk_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_48GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_48GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_933SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_12GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_138TkInde_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_138TkInde_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_138TkInde_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_138TkInde_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_145TkInde_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_145TkInde_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_145TkInde_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_145TkInde_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_17GReco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_47GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_47GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_47GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_60MSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_60MSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_65GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_65GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_65GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_66SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_67GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_67GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_67GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_724GArt_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_724GArt_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_724GArt_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_724GArt_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_16GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_1GTA.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_225Helo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_234Security", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_308ArtBr_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_308ArtBr_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_308ArtBr_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_308ArtBr_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_308ArtBr_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_308ArtBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_443Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_485Helo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_595Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_677SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_679SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_1GTA_679SAM.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_682SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_923SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_SAM_Brig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_SpecialHelo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1GTA_Tauroggen", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig10GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig11GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig12GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig16GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig207MRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig20GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig21MRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig27GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig39GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig47GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig57GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig79GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig7GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig94GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_1eBrig9TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_16GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_16GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_16GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_32TkInde", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_33MSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_33MSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_3MSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_400MSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_400MSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_400MSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_41MSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_41MSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_41MSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_693Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_693Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_693Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_693Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_6Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_75GSAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_207MRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_20TkInde", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_241GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_241GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_241GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_29GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_29GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_29GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_358GAAR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_67GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_67GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_67GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_68Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_944GArt_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_944GArt_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_944GArt_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_944GArt_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_95Tk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_95Tk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_95Tk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_20GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_1054Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_1054Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_1054Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_1054Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_1079SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_18TkInde", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_239MSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_239MSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_239MSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_240MSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_240MSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_240MSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_283GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_283GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_283GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_33Tk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_33Tk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_33Tk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_34Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_21MRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_221TkInde_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_221TkInde_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_221TkInde_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_221TkInde_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_221TkInde_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_27GMRD.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_243GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_27GMRD_243GMSP_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_243GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_243GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_244GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_27GMRD_244GMSP_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_244GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_244GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_286AAR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_28GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_27GMRD_28GTk_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_28GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_28GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_54Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_54Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_54Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_54Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_5Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_68GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_27GMRD_68GMSP_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_68GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_68GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_MSBInde", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_27GMRD_TkInde", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_290ArtBrig_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_290ArtBrig_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_290ArtBrig_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_290ArtBrig_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2GTA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2GTA_1185DShB", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2GTA_172AtckHelo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2GTA_240Security", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2GTA_290ArtBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2GTA_439AtckHelo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2GTA_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig10GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig11GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig12GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig16GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig207MRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig20GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig21MRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig27GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig39GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig47GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig57GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig79GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig7GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig94GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_2eBrig9TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_385ArtBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_117GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_117GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_117GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_11Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_120GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_120GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_120GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_15GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_15GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_15GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_172GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_172GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_172GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_23TkInde", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_87Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_87Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_87Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_87Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_915AAR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_39GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_1309SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_178AHelo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_232Security", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_385Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_385Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_385Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_385Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_440Helo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_451AT", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_658SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_667SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_673SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_899DShB", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3A_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig10GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig11GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig12GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig16GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig207MRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig20GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig21MRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig27GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig39GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig47GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig57GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig79GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig7GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig94GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_3eBrig9TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_46SAMBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_1069SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_153Tk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_197GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_197GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_197GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_245GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_245GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_26Tk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_26Tk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_26Tk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_56GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_56GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_7Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_99Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_99Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_99Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_99Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_47GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig10GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig11GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig12GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig16GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig207MRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig20GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig21MRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig27GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig39GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig47GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig57GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig79GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig7GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig94GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_4eBrig9TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_113Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_128Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_128Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_128Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_128Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_170GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_170GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_170GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_174GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_174GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_174GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_241GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_57GMRD_241GMSP_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_241GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_241GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_29TkInde", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_51GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_57GMRD_51GTk_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_51GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_51GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_901AAR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_57GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_61SAMBrig_1089SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_61SAMBrig_412SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_61SAMBrig_413SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_61SAMBrig_422SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_61SAMBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_1075AAR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_10Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_172Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_172Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_172Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_17GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_17GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_17GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_211GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_211GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_211GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_247GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_247GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_247GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_45GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_45GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_45GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_79GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_287SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_40GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_40GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_440MLRS", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_4Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_55GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_55GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_55GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_56GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_56GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_56GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_670Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_670Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_670Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_79GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_79GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_79GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_7GTkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_119TkInde_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_119TkInde_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_119TkInde_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_119TkInde_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_134Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_227Security", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_336Helo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_34ArtDiv_4_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_34ArtDiv_4_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_34ArtDiv_4_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_34ArtDiv_4_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_34ArtDiv_6_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_34ArtDiv_6_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_34ArtDiv_6_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_34ArtDiv_6_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_34ArtDiv_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_35AirAsltBrig_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_35AirAsltBrig_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_8GA_35AirAsltBrig_2.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_35AirAsltBrig_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_35AirAsltBrig_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_8GA_35AirAsltBrig_4.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_35AirAsltBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_390ArtBr_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_390ArtBr_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_390ArtBr_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_390ArtBr_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_390ArtBr_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_390ArtBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_390ArtBrigade_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_486Helo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_519SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_520SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_526SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_900AirAsltBat", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_923SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_Flam21", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_MLRS_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_8GA_SAM_Brig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_12Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_199Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_199Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_199Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_199Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_204GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_204GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_204GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_286GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_286GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_286GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_288GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_288GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_288GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_28TkInde", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_74GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_74GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_74GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_896SAM", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_94GMRD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_9TkD.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_13Reco", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_1GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_9TkD_1GTk_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_1GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_1GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_216AAR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_23Tk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_9TkD_23Tk_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_23Tk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_23Tk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_302GMSP_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_302GMSP_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_302GMSP_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_70GTk_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_9TkD_70GTk_1.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_70GTk_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_70GTk_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_96Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_96Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_96Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_9TkD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_NGF", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_WGF", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_WGF_34ArtDiv_286ArtBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/pion_SOV_WGF_34ArtDiv_286ArtBrigade.png"))]),
        ("Texture_Button_Pawn_pion_SOV_WGF_34ArtDiv_288ArtBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_WGF_34ArtDiv_303ArtBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_SOV_WGF_34ArtDiv_307ArtBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/PACT/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_11ArmBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_12ArmBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_14_20_Hussars", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_UK_14_20_Hussars.png"))]),
        ("Texture_Button_Pawn_pion_UK_15InfBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_17_21_Lancers", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_19ArmBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1AAC", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_1Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_1RTR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_21Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_2RTR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_40Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_4Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_4RTR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_UK_1ArmDiv_4RTR.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_Para10", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_Para15", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_Para4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_UK_1ArmDiv_Para4.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_QOH", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_RAR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_RGJ_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_RGJ_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_RIR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_RSDG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_Scots_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1ArmDiv_Stafford_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1BrC", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1BrC_16_5QRL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1BrC_16_ADR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1BrC_1QDG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1BrC_22_ADR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1BrC_45Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1BrC_5Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1BrC_9_12RL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1BrC_BAOREng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_UK_1BrC_BAOREng.png"))]),
        ("Texture_Button_Pawn_pion_UK_1BrC_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1BrC_RAF_Gutersloh", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1BrC_RGJ_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_1BrigMNAD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_20ArmBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_22ArmBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_24AirmobfBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_UK_2InfDiv.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_100Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_103Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_27Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_29EngBrig", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_9AAC", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_PWORY_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_QOYeo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_UK_2InfDiv_QOYeo.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_RAR_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_RAR_7", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_RRF_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_RRF_6", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_RoyalYeo", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_UK_2InfDiv_RoyalYeo.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_TGH_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_UK_2InfDiv_TGH_1.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_TLI_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_TLI_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_TLI_7", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_TLI_8", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_TSR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_TYV_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_2InfDiv_TYV_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_33ArmBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_3AAC", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_3ArmDiv", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_UK_3ArmDiv.png"))]),
        ("Texture_Button_Pawn_pion_UK_3ArmDiv_26Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_3ArmDiv_39HvyArt", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_3ArmDiv_9_12Lancers", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_3ArmDiv_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_3Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_3RTR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_UK_3RTR.png"))]),
        ("Texture_Button_Pawn_pion_UK_49Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_49InfBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_15_19KRH", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_19Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_26Art", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_35Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_4AAC", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_4_7RDG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_51Highland_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_5RDG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_ASH_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_KORB_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_Queen_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_Queen_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_RAR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_RAR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_RIR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_4ArmDiv_RoyalHussars", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_6ArmBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_7ArmBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_BlueRoyals", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_GrenadierGds", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_MNAD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_MNAD_5AB_2Gurk", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_MNAD_5AB_7ArtRHA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_MNAD_5AB_Para1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_MNAD_5AB_Para2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_MNAD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_NORTHAG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_NORTHAG_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_ParaRgtGrp_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_QLR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_QOH_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_RRF_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_UK_RScots_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_11ACR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_11ACR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_11ACR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_11ACR_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_11ACR_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_17C", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_17C_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_18EngBr_249Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_18EngBr_293Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_18EngBr_79Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_18EngBr_94Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_18_EngBri_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_194ArmBr_10Arm_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_194ArmBr_10Arm_1.png"))]),
        ("Texture_Button_Pawn_pion_US_194ArmBr_10Arm_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_194ArmBr_15Mech_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_194ArmBr_77Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_194ArmBr_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_197ArmBr_18Mech_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_197ArmBr_41Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_197ArmBr_58Mech_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_197ArmBr_69Arm_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_197ArmBr_69Arm_2.png"))]),
        ("Texture_Button_Pawn_pion_US_197MechBr_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_1AD.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_13Arm_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_1AD_13Arm_1.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_16Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_1Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_1Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_1Art_6", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_1Cav_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_1Helo_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_1Helo_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_35Arm_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_1AD_35Arm_1.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_35Arm_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_1AD_35Arm_3.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_37Arm_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_54Mech_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_6Mech_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_6Mech_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_6Mech_7", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_70Arm_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_81Arm_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_AviaBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1Brig1AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1Brig2AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1Brig3AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1Brig3ID_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1Brig82AB_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_1Brig8ID_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2ACR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2ACR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2ACR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2ACR_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2ACR_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_17Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_1Cav_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_3Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_3Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_3Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_3Helo_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_41Inf_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_41Inf_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_41Inf_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_41Inf_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_66Arm_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_66Arm_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_66Arm_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_67Arm_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_67Arm_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_FwdHQ", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2Brig1AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2Brig2AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2Brig3AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2Brig3ID_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2Brig82AB_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_2Brig8ID_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_32ADC_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_3AD.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_18Mech_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_227Helo_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_227Helo_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_23Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_32Arm_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_32Arm_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_36Mech_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_3Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_5Mech_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_5Mech_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_67Arm_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_3AD_67Arm_2.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_67Arm_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_7Cav_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_3AD_7Cav_4.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_82Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_82Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_8Arm_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_3AD_8Arm_3.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_8Arm_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_AviaBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3Brig1AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3Brig2AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3Brig3AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3Brig3ID_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3Brig82AB_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3Brig8ID_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3C", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3C_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_10Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_15Mech_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_30Mech_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_30Mech_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_3Helo_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_3Helo_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_41Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_41Art_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_41Art_6", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_4Cav_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_63Arm_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_64Arm_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_64Arm_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_68Arm_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_69Arm_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_7Mech_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_7Mech_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_AviaBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_3ID_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_4Brig1AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_4Brig2AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_4Brig3AD_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_4Brig3ID_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_4Brig82AB_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_4Brig8ID_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_111Helo_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_12AviaBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_12AviaBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_130EngBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_130Eng_317Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_130Eng_547Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_130Eng_54Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_5C_130Eng_54Eng.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_158Helo_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_18Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_18MPBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_20Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_20Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_229Helo_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_27Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_3Art_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_41ArtBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_41ArtBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_42ArtBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_42ArtBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_6Helo_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_709MP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_77Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_7Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_93MP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_5C_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_11AviaBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_11AviaBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_12Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_12Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_14Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_14Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_14MPBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_151Helo_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_159Helo_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_17ArtBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_17ArtBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_17Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_17Art_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_18Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_210ArtBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_210ArtBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_229Helo_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_27Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_35Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_36Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_385MP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_5Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_6Helo_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_72ArtBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_72ArtBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_77Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_793MP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7C_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7EngBrig_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7Eng_237Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7Eng_78Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_7Eng_9Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_82AB.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_17Cav_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_319Art_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_319Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_319Art_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_325PIR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_82AB_325PIR_1.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_325PIR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_325PIR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_504PIR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_82AB_504PIR_1.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_504PIR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_504PIR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_505PIR_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_82AB_505PIR_1.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_505PIR_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_505PIR_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_73Arm_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_82Helo_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_AviaBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_82AB_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_12Eng", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_13Mech_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_29Art_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_29Art_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_29Art_6", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_39Mech_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_4Helo_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_4Helo_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_68Arm_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_68Arm_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_68Arm_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_69Arm_2", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_77Arm_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_7Cav_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_8Mech_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_8Mech_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_8Mech_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_AviaBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_8ID_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_CENTAG", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_CENTAG_GROUPE", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_CENT_10SFG_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_CENT_1ADA_1", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_CENT_1ADA_4", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_CENT_44ADA_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_CENT_44ADA_5", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_CENT_52ADA_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_CENT_52ADA_6", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_CENT_60ADA_3", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_pion_US_NG_AviaBrigade", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/pion_US_NG_AviaBrigade.png"))]),

        ("Texture_Button_Pawn_TestAirport", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
        ("Texture_Button_Pawn_TestDepotRavitaillement", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/PawnsIcons/NATO/default.png"))]),
    ]
)

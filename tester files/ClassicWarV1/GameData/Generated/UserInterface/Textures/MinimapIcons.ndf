// Ne pas éditer, ce fichier est généré par MinimapIconFileWriter





Texture_Minimap_Unit_unit is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/UseInGame/Minimap/unit.png"
)
Texture_Minimap_Unit_inf is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/UseInGame/Minimap/inf.png"
)
Texture_Minimap_Unit_helico is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/UseInGame/Minimap/helico.png"
)
Texture_Minimap_Unit_avion is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/UseInGame/Minimap/avion.png"
)

MinimapIconAdditionalTextureBank is TBUCKToolAdditionalTextureBank
(
    Textures = MAP
    [
        ("Texture_Minimap_Unit_unit", MAP [(~/ComponentState/Normal, ~/Texture_Minimap_Unit_unit)]),
        ("Texture_Minimap_Unit_inf", MAP [(~/ComponentState/Normal, ~/Texture_Minimap_Unit_inf)]),
        ("Texture_Minimap_Unit_helico", MAP [(~/ComponentState/Normal, ~/Texture_Minimap_Unit_helico)]),
        ("Texture_Minimap_Unit_avion", MAP [(~/ComponentState/Normal, ~/Texture_Minimap_Unit_avion)]),
    ]
)

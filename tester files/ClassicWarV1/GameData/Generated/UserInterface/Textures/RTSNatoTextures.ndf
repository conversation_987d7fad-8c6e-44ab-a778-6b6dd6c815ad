// Ne pas éditer, ce fichier est généré par RTSNatoTextureFileWriter

Texture_AA is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/AA.png'
)

Texture_veh_nonIdentifie is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/veh_nonIdentifie.png'
)

Texture_RTS_H_AA is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/AA.png'
)

Texture_RTS_H_veh_nonIdentifie is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/veh_nonIdentifie.png'
)

Texture_howitzer is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/howitzer.png'
)

Texture_RTS_H_howitzer is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/howitzer.png'
)

Texture_mortar is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/mortar.png'
)

Texture_RTS_H_mortar is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/mortar.png'
)

Texture_infantry_nonIdentifie is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/infantry_nonIdentifie.png'
)

Texture_RTS_H_infantry_nonIdentifie is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/infantry_nonIdentifie.png'
)

Texture_supply is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/supply.png'
)

Texture_RTS_H_supply is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/supply.png'
)

Texture_appui is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/appui.png'
)

Texture_RTS_H_appui is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/appui.png'
)

Texture_ifv is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/ifv.png'
)

Texture_RTS_H_ifv is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/ifv.png'
)

Texture_CMD_veh is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/CMD_veh.png'
)

Texture_RTS_H_CMD_veh is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/CMD_veh.png'
)

Texture_reco is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/reco.png'
)

Texture_RTS_H_reco is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/reco.png'
)

Texture_AT_veh is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/AT_veh.png'
)

Texture_RTS_H_AT_veh is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/AT_veh.png'
)

Texture_RECO_veh is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/RECO_veh.png'
)

Texture_RTS_H_RECO_veh is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/RECO_veh.png'
)

Texture_CMD_tank is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/CMD_tank.png'
)

Texture_RTS_H_CMD_tank is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/CMD_tank.png'
)

Texture_Armor is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/Armor.png'
)

Texture_RTS_H_Armor is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/Armor.png'
)

Texture_AA_veh is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/AA_veh.png'
)

Texture_RTS_H_AA_veh is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/AA_veh.png'
)

Texture_RTS_H_ATGun is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/ATGun.png'
)

Texture_Infantry is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/Infantry.png'
)

Texture_RTS_H_Infantry is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/Infantry.png'
)

Texture_AT is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/AT.png'
)

Texture_RTS_H_AT is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/AT.png'
)

Texture_FireSupport is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/FireSupport.png'
)

Texture_CMD_inf is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/CMD_inf.png'
)

Texture_RTS_H_CMD_inf is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/CMD_inf.png'
)

Texture_assault is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/assault.png'
)

Texture_RTS_H_assault is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/assault.png'
)

Texture_RECO_inf is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/RECO_inf.png'
)

Texture_RTS_H_RECO_inf is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/RECO_inf.png'
)

Texture_sf is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/sf.png'
)

Texture_RTS_H_RECO_inf_sf is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/RECO_inf_sf.png'
)

Texture_transport_noweapon is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/transport_noweapon.png'
)

Texture_RTS_H_transport_noweapon is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/transport_noweapon.png'
)

Texture_mlrs is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/mlrs.png'
)

Texture_RTS_H_mlrs is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/mlrs.png'
)

Texture_transport_small is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/transport_small.png'
)

Texture_RTS_H_transport_small is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/transport_small.png'
)

Texture_RTS_H_Armor_heavy is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/Armor_heavy.png'
)

Texture_RTS_H_CMD_inf_sf is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/CMD_inf_sf.png'
)

Texture_RTS_H_Infantry_sf is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/Infantry_sf.png'
)

Texture_HMG is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/HMG.png'
)

Texture_RTS_H_HMG is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/HMG.png'
)

Texture_manpad is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/manpad.png'
)

Texture_RTS_H_manpad is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/manpad.png'
)

Texture_hel is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/hel.png'
)

Texture_hel_nonIdentifie is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/hel_nonIdentifie.png'
)

Texture_RTS_H_hel is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/hel.png'
)

Texture_RTS_H_hel_nonIdentifie is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/hel_nonIdentifie.png'
)

Texture_ATGM_hel is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/ATGM_hel.png'
)

Texture_RTS_H_ATGM_hel is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/ATGM_hel.png'
)

Texture_Support_hel is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/Support_hel.png'
)

Texture_RTS_H_Support_hel is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/Support_hel.png'
)

Texture_RECO_hel is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/RECO_hel.png'
)

Texture_RTS_H_RECO_hel is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/RECO_hel.png'
)

Texture_CMD_hel is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/CMD_hel.png'
)

Texture_RTS_H_CMD_hel is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/CMD_hel.png'
)

Texture_hel_supply is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/hel_supply.png'
)

Texture_RTS_H_hel_supply is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/hel_supply.png'
)

Texture_AA_hel is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/AA_hel.png'
)

Texture_RTS_H_AA_hel is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/AA_hel.png'
)

Texture_ATGM_air is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/ATGM_air.png'
)

Texture_RTS_H_ATGM_air is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/ATGM_air.png'
)

Texture_RTS_H_avion_nonIdentifie is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/avion_nonIdentifie.png'
)

Texture_Support_air is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/Support_air.png'
)

Texture_RTS_H_Support_air is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/Support_air.png'
)

Texture_SEAD_air is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/SEAD_air.png'
)

Texture_RTS_H_SEAD_air is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/SEAD_air.png'
)

Texture_uav is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/uav.png'
)

Texture_RTS_H_uav is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/uav.png'
)

Texture_AA_air is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/AA_air.png'
)

Texture_RTS_H_AA_air is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/AA_air.png'
)

Texture_fob is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/NATO/fob.png'
)

Texture_RTS_H_fob is TUIResourceTexture_Common
(
    FileName      = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/fob.png'
)


NATORTSAdditionalTextureBank is TBUCKToolAdditionalTextureBank
(
    Textures = MAP
    [
        ("Texture_AA", MAP [(~/ComponentState/Normal, ~/Texture_AA)]),
        ("Texture_veh_nonIdentifie", MAP [(~/ComponentState/Normal, ~/Texture_veh_nonIdentifie)]),
        ("Texture_RTS_H_AA", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_AA)]),
        ("Texture_RTS_H_veh_nonIdentifie", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_veh_nonIdentifie)]),
        ("Texture_howitzer", MAP [(~/ComponentState/Normal, ~/Texture_howitzer)]),
        ("Texture_RTS_H_howitzer", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_howitzer)]),
        ("Texture_mortar", MAP [(~/ComponentState/Normal, ~/Texture_mortar)]),
        ("Texture_RTS_H_mortar", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_mortar)]),
        ("Texture_infantry_nonIdentifie", MAP [(~/ComponentState/Normal, ~/Texture_infantry_nonIdentifie)]),
        ("Texture_RTS_H_infantry_nonIdentifie", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_infantry_nonIdentifie)]),
        ("Texture_supply", MAP [(~/ComponentState/Normal, ~/Texture_supply)]),
        ("Texture_RTS_H_supply", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_supply)]),
        ("Texture_appui", MAP [(~/ComponentState/Normal, ~/Texture_appui)]),
        ("Texture_RTS_H_appui", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_appui)]),
        ("Texture_ifv", MAP [(~/ComponentState/Normal, ~/Texture_ifv)]),
        ("Texture_RTS_H_ifv", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_ifv)]),
        ("Texture_CMD_veh", MAP [(~/ComponentState/Normal, ~/Texture_CMD_veh)]),
        ("Texture_RTS_H_CMD_veh", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_CMD_veh)]),
        ("Texture_reco", MAP [(~/ComponentState/Normal, ~/Texture_reco)]),
        ("Texture_RTS_H_reco", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_reco)]),
        ("Texture_AT_veh", MAP [(~/ComponentState/Normal, ~/Texture_AT_veh)]),
        ("Texture_RTS_H_AT_veh", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_AT_veh)]),
        ("Texture_RECO_veh", MAP [(~/ComponentState/Normal, ~/Texture_RECO_veh)]),
        ("Texture_RTS_H_RECO_veh", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_RECO_veh)]),
        ("Texture_CMD_tank", MAP [(~/ComponentState/Normal, ~/Texture_CMD_tank)]),
        ("Texture_RTS_H_CMD_tank", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_CMD_tank)]),
        ("Texture_Armor", MAP [(~/ComponentState/Normal, ~/Texture_Armor)]),
        ("Texture_RTS_H_Armor", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_Armor)]),
        ("Texture_AA_veh", MAP [(~/ComponentState/Normal, ~/Texture_AA_veh)]),
        ("Texture_RTS_H_AA_veh", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_AA_veh)]),
        ("Texture_RTS_H_ATGun", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_ATGun)]),
        ("Texture_Infantry", MAP [(~/ComponentState/Normal, ~/Texture_Infantry)]),
        ("Texture_RTS_H_Infantry", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_Infantry)]),
        ("Texture_AT", MAP [(~/ComponentState/Normal, ~/Texture_AT)]),
        ("Texture_RTS_H_AT", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_AT)]),
        ("Texture_FireSupport", MAP [(~/ComponentState/Normal, ~/Texture_FireSupport)]),
        ("Texture_CMD_inf", MAP [(~/ComponentState/Normal, ~/Texture_CMD_inf)]),
        ("Texture_RTS_H_CMD_inf", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_CMD_inf)]),
        ("Texture_assault", MAP [(~/ComponentState/Normal, ~/Texture_assault)]),
        ("Texture_RTS_H_assault", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_assault)]),
        ("Texture_RECO_inf", MAP [(~/ComponentState/Normal, ~/Texture_RECO_inf)]),
        ("Texture_RTS_H_RECO_inf", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_RECO_inf)]),
        ("Texture_sf", MAP [(~/ComponentState/Normal, ~/Texture_sf)]),
        ("Texture_RTS_H_RECO_inf_sf", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_RECO_inf_sf)]),
        ("Texture_transport_noweapon", MAP [(~/ComponentState/Normal, ~/Texture_transport_noweapon)]),
        ("Texture_RTS_H_transport_noweapon", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_transport_noweapon)]),
        ("Texture_mlrs", MAP [(~/ComponentState/Normal, ~/Texture_mlrs)]),
        ("Texture_RTS_H_mlrs", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_mlrs)]),
        ("Texture_transport_small", MAP [(~/ComponentState/Normal, ~/Texture_transport_small)]),
        ("Texture_RTS_H_transport_small", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_transport_small)]),
        ("Texture_RTS_H_Armor_heavy", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_Armor_heavy)]),
        ("Texture_RTS_H_CMD_inf_sf", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_CMD_inf_sf)]),
        ("Texture_RTS_H_Infantry_sf", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_Infantry_sf)]),
        ("Texture_HMG", MAP [(~/ComponentState/Normal, ~/Texture_HMG)]),
        ("Texture_RTS_H_HMG", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_HMG)]),
        ("Texture_manpad", MAP [(~/ComponentState/Normal, ~/Texture_manpad)]),
        ("Texture_RTS_H_manpad", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_manpad)]),
        ("Texture_hel", MAP [(~/ComponentState/Normal, ~/Texture_hel)]),
        ("Texture_hel_nonIdentifie", MAP [(~/ComponentState/Normal, ~/Texture_hel_nonIdentifie)]),
        ("Texture_RTS_H_hel", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_hel)]),
        ("Texture_RTS_H_hel_nonIdentifie", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_hel_nonIdentifie)]),
        ("Texture_ATGM_hel", MAP [(~/ComponentState/Normal, ~/Texture_ATGM_hel)]),
        ("Texture_RTS_H_ATGM_hel", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_ATGM_hel)]),
        ("Texture_Support_hel", MAP [(~/ComponentState/Normal, ~/Texture_Support_hel)]),
        ("Texture_RTS_H_Support_hel", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_Support_hel)]),
        ("Texture_RECO_hel", MAP [(~/ComponentState/Normal, ~/Texture_RECO_hel)]),
        ("Texture_RTS_H_RECO_hel", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_RECO_hel)]),
        ("Texture_CMD_hel", MAP [(~/ComponentState/Normal, ~/Texture_CMD_hel)]),
        ("Texture_RTS_H_CMD_hel", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_CMD_hel)]),
        ("Texture_hel_supply", MAP [(~/ComponentState/Normal, ~/Texture_hel_supply)]),
        ("Texture_RTS_H_hel_supply", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_hel_supply)]),
        ("Texture_AA_hel", MAP [(~/ComponentState/Normal, ~/Texture_AA_hel)]),
        ("Texture_RTS_H_AA_hel", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_AA_hel)]),
        ("Texture_ATGM_air", MAP [(~/ComponentState/Normal, ~/Texture_ATGM_air)]),
        ("Texture_RTS_H_ATGM_air", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_ATGM_air)]),
        ("Texture_RTS_H_avion_nonIdentifie", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_avion_nonIdentifie)]),
        ("Texture_Support_air", MAP [(~/ComponentState/Normal, ~/Texture_Support_air)]),
        ("Texture_RTS_H_Support_air", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_Support_air)]),
        ("Texture_SEAD_air", MAP [(~/ComponentState/Normal, ~/Texture_SEAD_air)]),
        ("Texture_RTS_H_SEAD_air", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_SEAD_air)]),
        ("Texture_uav", MAP [(~/ComponentState/Normal, ~/Texture_uav)]),
        ("Texture_RTS_H_uav", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_uav)]),
        ("Texture_AA_air", MAP [(~/ComponentState/Normal, ~/Texture_AA_air)]),
        ("Texture_RTS_H_AA_air", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_AA_air)]),
        ("Texture_fob", MAP [(~/ComponentState/Normal, ~/Texture_fob)]),
        ("Texture_RTS_H_fob", MAP [(~/ComponentState/Normal, ~/Texture_RTS_H_fob)]),
    ]
)


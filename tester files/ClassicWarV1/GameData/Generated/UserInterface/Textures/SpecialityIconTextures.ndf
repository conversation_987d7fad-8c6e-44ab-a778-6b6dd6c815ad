// Ne pas éditer, ce fichier est généré par SpecialityIconTextureFileWriter_Specific


Texture_Speciality_Icon_AA is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/AA.png"
)
Texture_Speciality_Icon_AT is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/AT.png"
)
Texture_Speciality_Icon_infantry_half is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/infantry_half.png"
)
Texture_Speciality_Icon_airlift is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/airlift.png"
)
Texture_Speciality_Icon_amphibie is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/amphibie.png"
)
Texture_Speciality_Icon_canBeAirlifted is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/canBeAirlifted.png"
)
Texture_Speciality_Icon_shock is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/shock.png"
)
Texture_Speciality_Icon_electronic_warfare is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/electronic_warfare.png"
)
Texture_Speciality_Icon_eo_dazzler is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/eo_dazzler.png"
)
Texture_Speciality_Icon_era is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/era.png"
)
Texture_Speciality_Icon_falseflag is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/falseflag.png"
)
Texture_Speciality_Icon_fireDirection is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/fireDirection.png"
)
Texture_Speciality_Icon_gsr is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/gsr.png"
)
Texture_Speciality_Icon_ifv_2 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/ifv_2.png"
)
Texture_Speciality_Icon_jammer is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/jammer.png"
)
Texture_Speciality_Icon_cmd is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/cmd.png"
)
Texture_Speciality_Icon_mp is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/mp.png"
)
Texture_Speciality_Icon_para is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/para.png"
)
Texture_Speciality_Icon_reco_deploy is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/reco_deploy.png"
)
Texture_Speciality_Icon_reserviste is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/reserviste.png"
)
Texture_Speciality_Icon_resolute is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/resolute.png"
)
Texture_Speciality_Icon_security is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/security.png"
)
Texture_Speciality_Icon_sf is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/sf.png"
)
Texture_Speciality_Icon_singint is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/singint.png"
)
Texture_Speciality_Icon_smoke_launcher is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/smoke_launcher.png"
)
Texture_Speciality_Icon_sniper is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/sniper.png"
)
Texture_Speciality_Icon_transport1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/transport1.png"
)
Texture_Speciality_Icon_transport2 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/transport2.png"
)
Texture_Speciality_Icon_appui is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/appui.png"
)
Texture_Speciality_Icon_armor is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/armor.png"
)
Texture_Speciality_Icon_assault is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/assault.png"
)
Texture_Speciality_Icon_assault_half is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/assault_half.png"
)
Texture_Speciality_Icon_bomb is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/bomb.png"
)
Texture_Speciality_Icon_engineer is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/engineer.png"
)
Texture_Speciality_Icon_flak is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/flak.png"
)
Texture_Speciality_Icon_gunship is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/gunship.png"
)
Texture_Speciality_Icon_half is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/half.png"
)
Texture_Speciality_Icon_howitzer is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/howitzer.png"
)
Texture_Speciality_Icon_hq is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/hq.png"
)
Texture_Speciality_Icon_ifv is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/ifv.png"
)
Texture_Speciality_Icon_infantry is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/infantry.png"
)
Texture_Speciality_Icon_mlrs is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/mlrs.png"
)
Texture_Speciality_Icon_mortar is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/mortar.png"
)
Texture_Speciality_Icon_reco is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/reco.png"
)
Texture_Speciality_Icon_sead is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/sead.png"
)
Texture_Speciality_Icon_special is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/special.png"
)
Texture_Speciality_Icon_supply is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/supply.png"
)
Texture_Speciality_Icon_A is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/A.png"
)
Texture_Speciality_Icon_B is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/B.png"
)
Texture_Speciality_Icon_C is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/C.png"
)
Texture_Speciality_Icon_D is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/D.png"
)
Texture_Speciality_Icon_team is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/team.png"
)
Texture_Speciality_Icon_transport is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/transport.png"
)
Texture_Speciality_Icon_uav is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/uav.png"
)
Texture_Speciality_Icon_veh is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Specialties/veh.png"
)

UnitSpecialityAdditionalTextureBank is TBUCKToolAdditionalTextureBank
(
    Textures = MAP
    [
        ("Texture_Speciality_Icon_AA", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_AA)]),
        ("Texture_Speciality_Icon_AT", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_AT)]),
        ("Texture_Speciality_Icon_infantry_half", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_infantry_half)]),
        ("Texture_Speciality_Icon_airlift", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_airlift)]),
        ("Texture_Speciality_Icon_amphibie", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_amphibie)]),
        ("Texture_Speciality_Icon_canBeAirlifted", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_canBeAirlifted)]),
        ("Texture_Speciality_Icon_shock", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_shock)]),
        ("Texture_Speciality_Icon_electronic_warfare", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_electronic_warfare)]),
        ("Texture_Speciality_Icon_eo_dazzler", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_eo_dazzler)]),
        ("Texture_Speciality_Icon_era", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_era)]),
        ("Texture_Speciality_Icon_falseflag", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_falseflag)]),
        ("Texture_Speciality_Icon_fireDirection", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_fireDirection)]),
        ("Texture_Speciality_Icon_gsr", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_gsr)]),
        ("Texture_Speciality_Icon_ifv_2", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_ifv_2)]),
        ("Texture_Speciality_Icon_jammer", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_jammer)]),
        ("Texture_Speciality_Icon_cmd", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_cmd)]),
        ("Texture_Speciality_Icon_mp", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_mp)]),
        ("Texture_Speciality_Icon_para", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_para)]),
        ("Texture_Speciality_Icon_reco_deploy", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_reco_deploy)]),
        ("Texture_Speciality_Icon_reserviste", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_reserviste)]),
        ("Texture_Speciality_Icon_resolute", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_resolute)]),
        ("Texture_Speciality_Icon_security", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_security)]),
        ("Texture_Speciality_Icon_sf", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_sf)]),
        ("Texture_Speciality_Icon_singint", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_singint)]),
        ("Texture_Speciality_Icon_smoke_launcher", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_smoke_launcher)]),
        ("Texture_Speciality_Icon_sniper", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_sniper)]),
        ("Texture_Speciality_Icon_transport1", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_transport1)]),
        ("Texture_Speciality_Icon_transport2", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_transport2)]),
        ("Texture_Speciality_Icon_appui", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_appui)]),
        ("Texture_Speciality_Icon_armor", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_armor)]),
        ("Texture_Speciality_Icon_assault", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_assault)]),
        ("Texture_Speciality_Icon_assault_half", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_assault_half)]),
        ("Texture_Speciality_Icon_bomb", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_bomb)]),
        ("Texture_Speciality_Icon_engineer", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_engineer)]),
        ("Texture_Speciality_Icon_flak", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_flak)]),
        ("Texture_Speciality_Icon_gunship", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_gunship)]),
        ("Texture_Speciality_Icon_half", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_half)]),
        ("Texture_Speciality_Icon_howitzer", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_howitzer)]),
        ("Texture_Speciality_Icon_hq", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_hq)]),
        ("Texture_Speciality_Icon_ifv", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_ifv)]),
        ("Texture_Speciality_Icon_infantry", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_infantry)]),
        ("Texture_Speciality_Icon_mlrs", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_mlrs)]),
        ("Texture_Speciality_Icon_mortar", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_mortar)]),
        ("Texture_Speciality_Icon_reco", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_reco)]),
        ("Texture_Speciality_Icon_sead", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_sead)]),
        ("Texture_Speciality_Icon_special", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_special)]),
        ("Texture_Speciality_Icon_supply", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_supply)]),
        ("Texture_Speciality_Icon_A", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_A)]),
        ("Texture_Speciality_Icon_B", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_B)]),
        ("Texture_Speciality_Icon_C", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_C)]),
        ("Texture_Speciality_Icon_D", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_D)]),
        ("Texture_Speciality_Icon_team", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_team)]),
        ("Texture_Speciality_Icon_transport", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_transport)]),
        ("Texture_Speciality_Icon_uav", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_uav)]),
        ("Texture_Speciality_Icon_veh", MAP [(~/ComponentState/Normal, ~/Texture_Speciality_Icon_veh)]),
    ]
)

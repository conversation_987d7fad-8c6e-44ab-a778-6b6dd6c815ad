// Ne pas éditer, ce fichier est généré par TurretSoundFileWriter


export DepictionOperator_TurretSound_ST_AMX_10RC is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_AMX_10RC.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_AMX10RC.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_AMX10RC.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_AMX_AuF1 is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_AMX_AuF1.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_MLRS.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_MLRS.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_Artillerie_US is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_Artillerie.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_Artillerie.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_BMP is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_BMP.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_BMP.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_BMP.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_BUK_SOVIET is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_BUK_SOVIET.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_BUK_SOVIET.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_BUK_SOVIET.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_Bradley is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_Bradley.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_Bradley.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_Bradley.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_Challenger is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_Challenger.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_Challenger.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_Challenger.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_LARS is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_LARS.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_LARS.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_LARS.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_Leopard is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_Leopard.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_Leopard.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_Leopard.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_Luchs is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_Luchs.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_Luchs.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_Luchs.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_M1 is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_M1_Abrams.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_M1_Abrams.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_M1_Abrams.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_M60_Patton is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_M60_Patton.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_M60.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_MLRS_US is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_MLRS.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_MLRS.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_MLRS.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_SAM_SOVIET is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_SAM_SOVIET.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_SAM_SOVIET.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_SAM_SOVIET.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_SAM_US is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_SAM_US.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_SAM_US.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_SAM_US.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_T64_T72_T80 is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_T64_T72_T80.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_T80.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_T80.wav"
        )
    )
    TurretAxisName = "tourelle1"
)
export DepictionOperator_TurretSound_ST_TOS is TCosmeticTurretSoundOperatorDesc
(
    OperatorId = "turretSound"
    RotateSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_TOS.wav"
        )
    )
    StartSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_START_T80.wav"
        )
    )
    StopSound = TSoundDescriptor
    (
        SoundSettings = $/SoundSettings/SoundSettings_Tourelles
        TheSoundStream = TSoundStream
        (
            FileName = "GameData:/Assets/Sounds/SFX/Tourelles/Tourelle_Rotation_STOP_T80.wav"
        )
    )
    TurretAxisName = "tourelle1"
)

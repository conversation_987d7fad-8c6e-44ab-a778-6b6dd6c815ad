// Ne pas éditer, ce fichier est auto-généré !

//----------------------------------------------------------------------------------
// TAcknowUnitType
//----------------------------------------------------------------------------------
unnamed TAcknowUnitType
(
    Values = [
        "TAcknowUnitType_ABInf",
        "TAcknowUnitType_ATGun",
        "TAcknowUnitType_ATInf",
        "TAcknowUnitType_ATLVF",
        "TAcknowUnitType_AirRecon",
        "TAcknowUnitType_AirSup",
        "TAcknowUnitType_AirSupNN",
        "TAcknowUnitType_Air_CAS",
        "TAcknowUnitType_ArtObs",
        "TAcknowUnitType_ArtRckt",
        "TAcknowUnitType_ArtRcktVehicle",
        "TAcknowUnitType_ArtShell",
        "TAcknowUnitType_ArtShellLVF",
        "TAcknowUnitType_ArtShellVehicle",
        "TAcknowUnitType_AttackHeli",
        "TAcknowUnitType_Bersa",
        "TAcknowUnitType_CanonAA",
        "TAcknowUnitType_CanonAAVehicle",
        "TAcknowUnitType_CdoInf",
        "TAcknowUnitType_Command",
        "TAcknowUnitType_CommandVehicle",
        "TAcknowUnitType_Command_Infantry",
        "TAcknowUnitType_Engineer",
        "TAcknowUnitType_Engineers",
        "TAcknowUnitType_FFI",
        "TAcknowUnitType_Flame",
        "TAcknowUnitType_FlameVehicle",
        "TAcknowUnitType_GroundAtk",
        "TAcknowUnitType_GunArtillery",
        "TAcknowUnitType_HMG",
        "TAcknowUnitType_HMGLVF",
        "TAcknowUnitType_HeliAttack",
        "TAcknowUnitType_HeliTransport",
        "TAcknowUnitType_Huszar",
        "TAcknowUnitType_Inf",
        "TAcknowUnitType_Inf2",
        "TAcknowUnitType_InfLVF",
        "TAcknowUnitType_Inf_Elite",
        "TAcknowUnitType_Inf_Legion",
        "TAcknowUnitType_Inf_Militia",
        "TAcknowUnitType_KaJaPa",
        "TAcknowUnitType_LargeBoat",
        "TAcknowUnitType_LiInf",
        "TAcknowUnitType_Logistic",
        "TAcknowUnitType_MLRS",
        "TAcknowUnitType_MediumBoat",
        "TAcknowUnitType_MissileAA",
        "TAcknowUnitType_Multirole",
        "TAcknowUnitType_MultiroleNN",
        "TAcknowUnitType_NIrishReco",
        "TAcknowUnitType_Nueve",
        "TAcknowUnitType_OffMap",
        "TAcknowUnitType_Officer",
        "TAcknowUnitType_OfficerLVF",
        "TAcknowUnitType_Ost",
        "TAcknowUnitType_Osttruppen",
        "TAcknowUnitType_Partisan",
        "TAcknowUnitType_Rallies",
        "TAcknowUnitType_Reco",
        "TAcknowUnitType_RecoLVF",
        "TAcknowUnitType_RecoVehicle",
        "TAcknowUnitType_ReconInfantry",
        "TAcknowUnitType_Recon_INF",
        "TAcknowUnitType_Recon_Vehicle",
        "TAcknowUnitType_SAM",
        "TAcknowUnitType_SF",
        "TAcknowUnitType_SFInf",
        "TAcknowUnitType_SmallBoat",
        "TAcknowUnitType_Sniper",
        "TAcknowUnitType_Tank",
        "TAcknowUnitType_TankDestroyer",
        "TAcknowUnitType_TankDestroyerMissile",
        "TAcknowUnitType_Transport",
        "TAcknowUnitType_Vehicle",
        "TAcknowUnitType_Witches",
    ]
)

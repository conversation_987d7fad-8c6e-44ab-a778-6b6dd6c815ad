// Ne pas éditer, ce fichier est généré par ConstantMovementSoundFileWriter


export DepictionOperator_MovementSound_SM_SD_avion_A10 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/A10_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_Albatros is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Albatros_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_AlphaJet is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/AlphaJet_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_Aquila_MQM_105 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 0.5
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Helico
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/MQM_105_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_CL_289 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 0.5
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Helico
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/CL_289_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_Crusader is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Crusader_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_Dragonfly is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Dragonfly_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_Epervier is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Helico
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Epervier_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_F104 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/F104_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_Fouga is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Fouga_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_Mirage2000 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Mirage2000_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_MirageF1 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/MirageIII_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_MirageIII is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/MirageIII_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_MirageIV is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/MirageIV_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_Pchela_1T is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 0.5
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Helico
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/MQM_105_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_SU25 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/SU25_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_SU_15 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Su24_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_Super_Etendard is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Super_Etendard_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_T33_Shooting_Star is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/T33_Shooting_Star_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_bombardier is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/A10_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_bombardier_A6_Intruder is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/A6_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_bombardier_Buccaneer is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Buccaneer_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_bombardier_F111 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/F111_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_bombardier_G91 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/G91_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_bombardier_Harrier is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Harrier_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_bombardier_Jaguar is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Jaguar_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_bombardier_SU22 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Su22_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_bombardier_SU24 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Su24_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_bombardier_Tornado is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Tornado_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_intercepteur_MiG31 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/MiG29_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_multirole_F117 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/F117_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_multirole_F15 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/F15_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_multirole_F16 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/F16_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_multirole_F18 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/F18_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_multirole_F4 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/F4_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_multirole_F5 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/F5_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_multirole_MiG17 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Mig17_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_multirole_MiG21 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/MiG21_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_multirole_MiG23 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/MiG23_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_multirole_MiG29 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/MiG29_Engine.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_SD_avion_multirole_Su27 is TCosmeticConstantMovementSoundOperatorDesc
(
    FadeInDuration = 0.5
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.1
    OperatorId = "constantSound"
    Sounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Avions/Su27_Engine.wav"
            )
        ),
    ]
)


private FlaresButtonDims is [25.0, 25.0]
private dimensionBoutonFlare is 24

//-------------------------------------------------------------------------------------
template FlareButtonSupport
[
IsFromStrategic : bool = false
]
 is BoutonFulda_AvecIcone
(
    ElementName = 'FlareFireSupport'
    IsFromStrategic = <IsFromStrategic>
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [dimensionBoutonFlare, 0.0]
        RelativeWidthHeight = [0.0, 1.0]
    )
    TextureToken = 'textureFlareFireSupport'
    HintTitleToken = "HFL_SuppT"
    HintBodyToken = "HFL_SuppB"

    Mapping = $/KeyboardOption/Mapping_FlareFireSupport
    IsTogglable = true
)
//-------------------------------------------------------------------------------------
template FlareButtonNeedHelp
[
IsFromStrategic : bool = false
]
 is BoutonFulda_AvecIcone

(
    ElementName = 'FlareHelp'
    IsFromStrategic = <IsFromStrategic>
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [dimensionBoutonFlare, 0.0]
        RelativeWidthHeight = [0.0, 1.0]
    )

    TextureToken = 'textureFlareHelp'
    HintTitleToken = "HFL_HELPT"
    HintBodyToken = "HFL_HELPB"
    HintExtendedToken = "HFL_HELPE"

    Mapping = $/KeyboardOption/Mapping_FlareHelp
    IsTogglable = true
)
//-------------------------------------------------------------------------------------
template FlareButtonEnemySpotted
[
IsFromStrategic : bool = false
]
 is BoutonFulda_AvecIcone
(
    ElementName = 'FlareEnemySpotted'
    IsFromStrategic = <IsFromStrategic>
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [dimensionBoutonFlare, 0.0]
        RelativeWidthHeight = [0.0, 1.0]
    )
    TextureToken = 'textureFlareEnemySpotted'
    HintTitleToken = "HFL_recoT"
    HintBodyToken = "HFL_recoB"

    Mapping = $/KeyboardOption/Mapping_FlareEnemySpotted
    IsTogglable = true
)

//-------------------------------------------------------------------------------------
template FlareButtonStrategyAttack
[
IsFromStrategic : bool = false
]
 is BoutonFulda_AvecIcone
(
    ElementName = 'FlareAttack'
    IsFromStrategic = <IsFromStrategic>
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [dimensionBoutonFlare, 0.0]
        RelativeWidthHeight = [0.0, 1.0]
    )
    Mapping = $/KeyboardOption/Mapping_FlareAttack
    IsTogglable = true
    TextureToken = 'textureFlareAttack'
    HintTitleToken = "HFL_ATTACT"
    HintBodyToken = "HFL_ATTACB"
    HintExtendedToken = "HFL_ATTACE"
)
//-------------------------------------------------------------------------------------
template FlareButtonStrategyDefend
[
IsFromStrategic : bool = false
]
 is BoutonFulda_AvecIcone
(
    ElementName = 'FlareDefend'
    IsFromStrategic = <IsFromStrategic>
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [dimensionBoutonFlare, 0.0]
        RelativeWidthHeight = [0.0, 1.0]
    )
    Mapping = $/KeyboardOption/Mapping_FlareDefend
    IsTogglable = true
    TextureToken = 'textureFlareDefense'
    HintTitleToken = "HFL_DEFENT"
    HintBodyToken = "HFL_DEFENB"
    HintExtendedToken = "HFL_DEFENE"
)
//-------------------------------------------------------------------------------------
template FlareButtonCustom
[
IsFromStrategic : bool = false
]
 is BoutonFulda_AvecIcone
(
    ElementName = 'FlareCustom'
    IsFromStrategic = <IsFromStrategic>
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [dimensionBoutonFlare, 0.0]
        RelativeWidthHeight = [0.0, 1.0]
    )

    Mapping = $/KeyboardOption/Mapping_FlareCustom
    IsTogglable = true
    TextureToken = 'textureFlareCustom'
    HintTitleToken = "HFL_CUSMET"
    HintBodyToken = "HFL_CUSMEB"
    HintExtendedToken = "HFL_CUSMEE"
)
//-------------------------------------------------------------------------------------
template FlareCustomEditableText
[
    IsFromStrategic : bool = false
] is BUCKEditableTextDescriptor
(
    ElementName = "FlareCustomEditableText"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [240.0, 24.0]
        AlignementToAnchor = [0.5, 1.0]
        AlignementToFather = [0.5, 0.0]
        MagnifiableOffset = [0.0, -4.0]
    )

    HasBackground = true
    BackgroundBlockColorToken = (<IsFromStrategic> == true ? "SM_RifleGreen_75" : "CustomFlareText")

    ClippingContainerFrameProperty = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    TextSizeToken = "FlaresPanel/CustomFlare/EditableText"
    TypefaceToken = "IBM"
    TextColorToken = (<IsFromStrategic> == true ? "SM_paleSilver" : "Blanc")
    SelectionColorToken = "FlaresPanel/CustomFlare/EditableText/Selected"

    MaxChars = 100
)

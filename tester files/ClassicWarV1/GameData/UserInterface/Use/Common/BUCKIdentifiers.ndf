// Recense tous les composants uniques décrits par un BUCK
// Doit être symétrique à InterfaceActOfRuse/UIWargameBUCKIdentifiers.h

BUCKIdentifiers                     is TBaseClass
(
IngameCubeActionContainer           is "IngameCubeActionContainer"
IngameCubeActionButtonContainer1    is "IngameCubeActionButtonContainer1"
IngameCubeActionButtonContainer2    is "IngameCubeActionButtonContainer2"
IngameCubeActionButtonContainer3    is "IngameCubeActionButtonContainer3"
IngameCubeActionButtonContainer4    is "IngameCubeActionButtonContainer4"
IngameCubeActionButtonContainer5    is "IngameCubeActionButtonContainer5"
IngameCubeActionButtonContainer6    is "IngameCubeActionButtonContainer6"
IngameCubeActionButtonContainer7    is "IngameCubeActionButtonContainer7"
IngameCubeActionButtonContainer8    is "IngameCubeActionButtonContainer8"
IngameCubeActionButtonContainer9    is "IngameCubeActionButtonContainer9"
IngameCubeActionButtonContainer10   is "IngameCubeActionButtonContainer10"
IngameCubeActionButtonContainer11   is "IngameCubeActionButtonContainer11"
IngameCubeActionButtonContainer12   is "IngameCubeActionButtonContainer12"

IngameCubeActionButtonContainer21   is "IngameCubeActionButtonContainer21"
IngameCubeActionButtonContainer22   is "IngameCubeActionButtonContainer22"
IngameCubeActionButtonContainer23   is "IngameCubeActionButtonContainer23"
IngameCubeActionButtonContainer24   is "IngameCubeActionButtonContainer24"
IngameCubeActionButtonContainer25   is "IngameCubeActionButtonContainer25"
IngameCubeActionButtonContainer26   is "IngameCubeActionButtonContainer26"

IngameCubeActionButtonContainer31   is "IngameCubeActionButtonContainer31"
IngameCubeActionButtonContainer32   is "IngameCubeActionButtonContainer32"
IngameCubeActionButtonContainer33   is "IngameCubeActionButtonContainer33"

)

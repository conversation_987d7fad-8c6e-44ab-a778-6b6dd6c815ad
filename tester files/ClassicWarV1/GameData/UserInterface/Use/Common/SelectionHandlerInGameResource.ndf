SelectionResources is TSelectionHandlerInGameResources
(
    Renderer = ~/SelectionRenderer
    DrawingParameters           = $/M3D/Input/SelectionHandlerDrawingParameters

    UserInputLayer                            = $/M3D/Input/UserInputLayerHandler/InputLayer_SelectionInGame
    UserInputLayer_SquareSelection            = $/M3D/Input/UserInputLayerHandler/InputLayer_SquareSelection

    Camera                                    = $/M3D/Misc/CameraPrincipale
    World                                     = $/M3D/Scene/DefaultWorld

    SceneReferenceForSelection                = $/M3D/Scene/Scene_2D_Interface

    UpdateDynamicValuesCommandName            = CommandName/OptionsProfileSelection
    UseStickySelection                        = MAP
    [
        (
            'Tactic',
            $/GameplayOption/StickySelectionValue
        ),
        (
            'Strategic',
            $/GameplayOption/StickySelectionStrategicValue
        ),
    ]
    UseInvertedHotkeysForUniformSelection  = $/GameplayOption/UseInvertedHotkeysForUniformSelection
    KeyboardPresetUpdater = $/KeyboardPreset/KeyboardPresetUpdater
    SquareSelectionActive = true
)

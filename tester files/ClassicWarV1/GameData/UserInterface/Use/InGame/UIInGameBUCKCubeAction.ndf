private CubeActionWargameButtonDims is [117.0, 38.0]
private CubeActionWargameSmartOrdersButtonDims is [117.0, 38.0]
private CubeActionWargameButtonSpacing is [1.0, 1.0]
private CubeActionWargameWidth is 383.0 // (CubeActionWargameButtonDims[0] + CubeActionWargameButtonSpacing[0]) * nombre de colonnes
private CubeActionWargameTitleHeight is 30.0
private CubeActionWargameContentHeight is 130.0 // (CubeActionWargameButtonDims[1] + CubeActionWargameButtonSpacing[1]) * nombre de lignes
private CubeActionWargameSubActionPanel is 125.0

//-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+- //
//                   Cube Action                        //
//-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+- //
template CubeActionButton
[
    BigLineAction : int,
    TextColor : string = "ButtonHUD/Text2",
    TextSize : string = "18",
    HasBorder : bool = false,
    BackgroundBlockColorToken : string = "BoutonTemps_Background",
    BorderLineColorToken : string = "BoutonTemps_Line",
    RoundedVertexes : LIST< bool > = [true, true, true, true], // le 1er est en bas à gauche

    HintableAreaComponent : TBUCKHintableAreaDescriptor = BUCKSpecificHintableArea
    (
        ElementName = "CubeActionHintableArea"
        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
        HintTitleToken = "HUD_UNKN"
        HintBodyToken = "HUD_UNKN"
        HintExtendedToken = "HUD_UNKN"
    ),

] is TCommonInGameCubeActionButtonDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    IsClippable = true
    UniformDrawer = $/UserInterface/UIUniformDrawer

    MaskEvents = true

    Components =
    [
        PanelRoundedCorner
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                MagnifiableWidthHeight = [-4.0, -4.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            Radius = 3
            BackgroundBlockColorToken = <BackgroundBlockColorToken>
            BorderLineColorToken = <BorderLineColorToken>
            RoundedVertexes = <RoundedVertexes>
        ),
        BUCKTextDescriptor
        (
            ElementName = "CubeActionButtonText"

            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.9, 0.9]
                MagnifiableWidthHeight = [-6.0, -6.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
                InterLine = -0.2

            )

            HasBorder = <HasBorder>
            BorderLineColorToken = 'ButtonHUD/Text2'
            BorderThicknessToken = '1'
            HasBackground = true
            BackgroundBlockColorToken = 'bouton_cubeAction'

            TextStyle = "Default"

            HorizontalFitStyle = ~/FitStyle/UserDefined
            VerticalFitStyle = ~/FitStyle/UserDefined

            BigLineAction   = <BigLineAction>
            TextColor       = <TextColor>
            TextSize        = <TextSize>
            TextDico        = ~/LocalisationConstantes/dico_interface_outgame
            TypefaceToken   = "Liberator"
        ),
    ] +

    (<HintableAreaComponent> != nil ? [<HintableAreaComponent>] : [])
)

//------------------------------------------------------------------------

template CubeActionToggleButton
[
    BigLineAction : int,
    TextSize : string = '12',
] is TCommonInGameCubeActionToggleButtonDescriptor
(
    IsTogglable = true

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    IsClippable = true

    UniformDrawer = $/UserInterface/UIUniformDrawer

    HasBackground = false
    BackgroundBlockColorToken = "Fulda_VertBleu"

    HasBorder = false
    BorderThicknessToken = "1"
    BorderLineColorToken = "Fulda_LigneVerte"

    MaskEvents = true

    Components =
    [
        PanelRoundedCorner
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                MagnifiableWidthHeight = [-4.0, -4.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )
            Radius = 3
            BackgroundBlockColorToken = "BoutonTemps_Background"
            BorderLineColorToken = "BoutonTemps_Line"
        ),

        BUCKTextDescriptor
        (
            ElementName = "CubeActionButtonText"

            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                MagnifiableWidthHeight = [-6.0, -6.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
            )

            TextStyle = "Default"

            HorizontalFitStyle = ~/FitStyle/UserDefined
            VerticalFitStyle = ~/FitStyle/UserDefined

            BigLineAction   = <BigLineAction>
            TextColor       = "ButtonHUD/Text2"
            TextSize        = <TextSize>
            TextDico        = ~/LocalisationConstantes/dico_interface_outgame

            TypefaceToken   = "Liberator"
        ),

        BUCKSpecificHintableArea
        (
            ElementName = "CubeActionHintableArea"
            DicoToken = ~/LocalisationConstantes/dico_interface_ingame
            HintTitleToken = "HUD_UNKN"
            HintBodyToken = "HUD_UNKN"
            HintExtendedToken = "HUD_UNKN"
        ),

        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
                PixelWidthHeight = [3.0, 0.0]
                PixelOffset = [1.0, 0.0]
                AlignementToAnchor = [0.0, 0.5]
                AlignementToFather = [0.0, 0.5]
            )

            HasBackground = false
            BackgroundBlockColorToken = "ButtonHUD/BigBorder2"
        ),
    ]
)

//------------------------------------------------------------------------

CubeActionEmptyButton is TCommonInGameCubeActionButtonDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    IsClippable = true

    UniformDrawer = $/UserInterface/UIUniformDrawer

    HasBackground = false
    BackgroundBlockColorToken = "Fulda_Gris"

    HasBorder = false
    BordersToDraw = ~/TBorderSide/All
    BorderThicknessToken = "Normal"
    BorderLineColorToken = "ButtonHUD"

    MaskEvents = true

    Components =
    [
        BUCKTextDescriptor
        (
            ElementName = "CubeActionButtonText"

            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                MagnifiableWidthHeight = [-6.0, -6.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
            )

            HasBackground = false
            BackgroundBlockColorToken = "Fulda2_Orange100"

            TextStyle = "Default"

            HorizontalFitStyle = ~/FitStyle/UserDefined
            VerticalFitStyle = ~/FitStyle/UserDefined

            BigLineAction   = ~/BigLineAction/BalancedMultiline
            TextColor       = "Fulda2_Orange100"
            TextSize        = "11"
            TextDico        = ~/LocalisationConstantes/dico_interface_outgame

            TypefaceToken   = "UIMainFont"
        ),

        BUCKSpecificHintableArea
        (
            ElementName = "CubeActionHintableArea"
            DicoToken = ~/LocalisationConstantes/dico_interface_ingame
            HintTitleToken = "HUD_UNKN"
            HintBodyToken = "HUD_UNKN"
            HintExtendedToken = "HUD_UNKN"
        ),

        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
                PixelWidthHeight = [3.0, 0.0]
                PixelOffset = [1.0, 0.0]
                AlignementToAnchor = [0.0, 0.5]
                AlignementToFather = [0.0, 0.5]
            )

            HasBackground = false
            BackgroundBlockColorToken = "ButtonHUD/BigBorder"
        ),
    ]
)

//-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+- //

template InGameCubeActionWargameButtonContainer
[
    UniqueName : string = '',
    Dimensions = CubeActionWargameButtonDims
]
is BUCKContainerDescriptor
(
    UniqueName = <UniqueName>
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = <Dimensions>
    )
)

//-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+- //

InGameCubeActionWargame is BUCKListDescriptor
(
    ElementName = "InGameCubeActionWargame"

    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToFather = [ 1.0, 1.0 ]
        AlignementToAnchor = [ 1.0, 1.0 ]
    )

    Axis = ~/ListAxis/Vertical
    InterItemMargin = TRTTILength(Magnifiable = 5.0)
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                UniqueName = "UISpecificOpticalFieldViewDescriptor"
                ComponentFrame = TUIFramePropertyRTTI()
                FitStyle = ~/ContainerFitStyle/FitToContent
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                UniqueName = "UISpecificTacticEngagementRulesViewDescriptor"
                ComponentFrame = TUIFramePropertyRTTI()
                FitStyle = ~/ContainerFitStyle/FitToContent
            )
        ),

        // Smart orders Cube action
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/PanelCubeAction_SmartOrders
        ),

        // Standard orders Cube action
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/PanelCubeAction_Orders
        ),
    ]
)

//-------------------------------------------------------------------------------------

private template PanelCubeAction_Title
[
    TextToken : string,
] is BUCKTextDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [~/CubeActionWargameButtonDims[0] , 25.0]
    )

    TextPadding = TRTTILength4(Magnifiable = [6.0, 0.0, 6.0, 0.0])

    ParagraphStyle = ~/paragraphStyleTextCenter

    TextStyle = "Default"

    HorizontalFitStyle = ~/FitStyle/UserDefined
    VerticalFitStyle = ~/FitStyle/UserDefined

    TextColor       = "BlancEquipe"
    TextSize        = "16"
    TextToken       = <TextToken>
    TextDico        = ~/LocalisationConstantes/dico_interface_ingame

    TypefaceToken   = "UIMainFont"
)

//-------------------------------------------------------------------------------------

private PanelCubeAction_Orders is BUCKListDescriptor
(
    ElementName = "StandardOrderCubeActionButtonList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [~/CubeActionWargameWidth, 0.0]
        AlignementToFather = [1.0, 0.0]
        AlignementToAnchor = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical
    LastMargin = TRTTILength(Magnifiable = 10.0)

    Elements =
    [
        // Titles
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ElementName = "StandardOrderCubeActionTitles"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [0.0, 25.0]
                )

                Axis = ~/ListAxis/Horizontal

                FirstMargin = TRTTILength(Magnifiable = 5.0)
                InterItemMargin = TRTTILength(Magnifiable = 11.0)
                LastMargin = TRTTILength(Magnifiable = 7.0)

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = PanelCubeAction_Title(TextToken = "CA_mvt")
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = PanelCubeAction_Title(TextToken = "CA_cbt")
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = PanelCubeAction_Title(TextToken = "CA_spe")
                    ),
                ]
            )
        ),

        // Buttons
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKGridDescriptor
            (
                ElementName = "StandardOrderCubeActionGrid"

                FirstElementMargin = TRTTILength2( Magnifiable = [5.0, 0.0] )
                InterElementMargin = TRTTILength2( Magnifiable = [11.0, 0.0] )
                LastElementMargin  = TRTTILength2( Magnifiable = [7.0, 0.0] )

                GridElements = MAP
                [
                    ( [0, 0], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer1 ) ),
                    ( [0, 1], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer2 ) ),
                    ( [0, 2], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer3 ) ),

                    ( [1, 0], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer4 ) ),
                    ( [1, 1], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer5 ) ),
                    ( [1, 2], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer6 ) ),

                    ( [2, 0], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer7 ) ),
                    ( [2, 1], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer8 ) ),
                    ( [2, 2], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer9 ) ),

                    ( [3, 0], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer10 ) ),
                    ( [3, 1], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer11 ) ),
                    ( [3, 2], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer12 ) ),
                ]
            )
        ),
    ]

    BackgroundComponents =
    [
        PanelRoundedCorner
        (
            Components =
            [
                BUCKListDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [0.0, 1.0]
                        AlignementToFather = [0.5, 1.0]
                        AlignementToAnchor = [0.5, 1.0]
                    )

                    Axis = ~/ListAxis/Horizontal
                    InterItemMargin = TRTTILength( Magnifiable = 3.0 )
                    LastMargin = TRTTILength( Magnifiable = 0.0 )

                    Elements =
                    [
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = PanelRoundedCorner
                            (
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [0.0, 1.0]
                                    MagnifiableWidthHeight = [~/CubeActionWargameSubActionPanel, 0.0]
                                )
                            )
                        ),

                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = PanelRoundedCorner
                            (
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [0.0, 1.0]
                                    MagnifiableWidthHeight = [~/CubeActionWargameSubActionPanel, 0.0]
                                )
                            )
                        ),

                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = PanelRoundedCorner
                            (
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [0.0, 1.0]
                                    MagnifiableWidthHeight = [~/CubeActionWargameSubActionPanel, 0.0]
                                )
                            )
                        ),
                    ]
                )
            ]
        ),

    ]
)

//-------------------------------------------------------------------------------------

private PanelCubeAction_SmartOrders is BUCKListDescriptor
(
    ElementName = "SmartOrderCubeActionButtonList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [~/CubeActionWargameWidth, 0.0]
        AlignementToFather = [1.0, 0.0]
        AlignementToAnchor = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical
    LastMargin = TRTTILength(Magnifiable = 10.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [0.0, 25.0]
                )

                Axis = ~/ListAxis/Horizontal
                Elements =
                [
                    // SMART ORDERS
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                MagnifiableWidthHeight = [0.0, 25.0]
                            )

                            TextPadding = TRTTILength4(Magnifiable = [6.0, 0.0, 6.0, 0.0])

                            ParagraphStyle = ~/paragraphStyleTextLeftAlign

                            TextStyle = "Default"

                            HorizontalFitStyle = ~/FitStyle/FitToContent
                            VerticalFitStyle = ~/FitStyle/UserDefined

                            TextColor       = "BlancEquipe"
                            TextSize        = "16"
                            TextToken       = "SCA_TITLE"
                            TextDico        = ~/LocalisationConstantes/dico_interface_ingame

                            TypefaceToken   = "UIMainFont"
                        )
                    ),
                ]
            )
        ),

        // Buttons SmartOrders
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKGridDescriptor
            (
                ElementName = "SmartOrderCubeActionGrid"

                FirstElementMargin = TRTTILength2( Magnifiable = [5.0, 0.0] )
                InterElementMargin = TRTTILength2( Magnifiable = [11.0, 0.0] )
                LastElementMargin  = TRTTILength2( Magnifiable = [7.0, 0.0] )


                GridElements = MAP
                [
                    ( [0, 0], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer21 Dimensions=CubeActionWargameSmartOrdersButtonDims ) ),
                    ( [0, 1], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer22 Dimensions=CubeActionWargameSmartOrdersButtonDims ) ),
                    ( [0, 2], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer23 Dimensions=CubeActionWargameSmartOrdersButtonDims ) ),

                    ( [1, 0], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer24 Dimensions=CubeActionWargameSmartOrdersButtonDims ) ),
                    ( [1, 1], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer25 Dimensions=CubeActionWargameSmartOrdersButtonDims ) ),
                    ( [1, 2], InGameCubeActionWargameButtonContainer( UniqueName=BUCKIdentifiers/IngameCubeActionButtonContainer26 Dimensions=CubeActionWargameSmartOrdersButtonDims ) ),
                ]
            )
        ),
    ]

    BackgroundComponents =
    [
        PanelRoundedCorner()
    ]
)

//-------------------------------------------------------------------------------------
private UISpecificTacticCubeActionViewDescriptor is TUISpecificCubeActionViewDescriptor
(
    MainComponentDescriptor = ~/InGameCubeActionWargame
    MainComponentContainerUniqueName = "UISpecificTacticCubeActionViewMainContainer"

    ButtonTemplateDescriptorMap       = MAP
    [
        (~/TacticCubeActionFunctionType/Orders, CubeActionButton(BigLineAction = ~/BigLineAction/BalancedMultiline)),
        (~/TacticCubeActionFunctionType/TogglableOrders, CubeActionToggleButton(TextSize = '18' BigLineAction = ~/BigLineAction/BalancedMultiline)),
        (~/TacticCubeActionFunctionType/SmartOrders, CubeActionButton(TextSize = '18' BigLineAction = ~/BigLineAction/BalancedMultiline)),
    ]

    EmptyButtonTemplateDescriptor  = ~/CubeActionEmptyButton

    SpotlightDescriptor = BUCKSpotlightDescriptor
    (
        ComponentFrame = TUIFramePropertyRTTI
        (
            RelativeWidthHeight = [1.0, 1.0]
        )
    )
)

//-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+- //
//         Panel HUD (spec. Hud générique)              //
//-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+- //
template GenericPanelHUD [
    UniqueName : string = '',
    PanelRelativeWidth : float = 0.0,
    PanelRelativeHeight : float = 0.0,
    PanelMagnifiableWidth : float = 0.0,

    LastExtendedWeight : bool = false,

    TitleVisible : boolean = true,
    TitleHeight : float = 0.0,
    TitleTextToken : string = '',
    TitleTextColorIndex : int = 0,
    TitleTextSizeIndex : int = 0,
    TitleTypeface : string = "Title",
    TitleMagnifiableOffset : int = 0,
    TitleAfterContent : boolean = false, //Le titre est placé avant le contenu si ce paramètre est faux

    AlignementToFather : float2 = [0.0,0.0],
    AlignementToAnchor : float2 = [0.0,0.0],

    MagnifiableOffset : float2 = [0.0, 0.0],

    ContentUniqueName : string = '',
    ContentVisible : boolean = true,
    ContentHeight : float = 0.0,
    ContentMagnifiableLeftRightMargins : float = 0.0,
    ContentMagnifiableTopBottomMargins : float = 0.0,
    ContentBackgroundBlockColorToken : string = '0',
    ContentComponents : LIST<TBUCKContainerDescriptor>,
    HasBackground,

    ClipContent = false,
]
is BUCKContainerDescriptor
(
    UniqueName = <UniqueName>
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [<PanelRelativeWidth>, <PanelRelativeHeight>]
        MagnifiableWidthHeight = [
            <PanelMagnifiableWidth>,
            (<PanelRelativeHeight> == 0) ?
                ((<TitleVisible> ? <TitleHeight> : 0) + (<ContentVisible> ? <ContentHeight> : 0)) :
                0
        ]
        AlignementToFather = <AlignementToFather>
        AlignementToAnchor = <AlignementToAnchor>
        MagnifiableOffset = <MagnifiableOffset>
    )

    ClipContent = <ClipContent>

    Components = [
        BUCKListDescriptor
        (
            ElementName = "HUDPanelContentList_" + <UniqueName>

            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
            )

            Axis = ~/ListAxis/Vertical

            BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromFrameProperty
            GridAlign = false

            Elements =
            ( !<TitleAfterContent> & <TitleVisible> ? [
                BUCKListElementDescriptor(
                    ComponentDescriptor = GenericPanelHUD_BlockTitle
                    (
                        ElementName = "GenericPanelHUD_FirstBlockTitle_" + <UniqueName>
                        TextToken = <TitleTextToken>

                        RelativeWidthHeight = [<PanelRelativeWidth>, 0]
                        MagnifiableWidthHeight = [<PanelMagnifiableWidth>, <TitleHeight>]

                        TextColorIndex = <TitleTextColorIndex>
                        TextSizeIndex = <TitleTextSizeIndex>
                        TypefaceToken = <TitleTypeface>
                        HorizontalMagnifiableOffset = <TitleMagnifiableOffset>
                    )
                )
            ] : [] ) +
            ( <ContentVisible> ? [
                BUCKListElementDescriptor(
                    ComponentDescriptor = GenericPanelHUD_BlockContent
                    (
                        HasBackground = <HasBackground>
                        UniqueName = <ContentUniqueName>
                        Components = <ContentComponents>
                        Height = <ContentHeight>
                        Width = <PanelMagnifiableWidth>
                        RelativeWidth = <PanelRelativeWidth>
                        TopBottomMargin = <ContentMagnifiableTopBottomMargins>
                        LeftRightMargin = <ContentMagnifiableLeftRightMargins>
                        BackgroundBlockColorToken = <ContentBackgroundBlockColorToken>
                        LastExtendedWeight = <LastExtendedWeight>
                    )
                    ExtendWeight = 1
                )
            ] : []) +
            ( <TitleAfterContent> & <TitleVisible> ? [
                BUCKListElementDescriptor(
                    ComponentDescriptor = GenericPanelHUD_BlockTitle
                    (
                        ElementName = "GenericPanelHUD_SecondBlockTitle_" + <UniqueName>
                        TextToken = <TitleTextToken>

                        RelativeWidthHeight = [<PanelRelativeWidth>, 0]
                        MagnifiableWidthHeight = [<PanelMagnifiableWidth>, <TitleHeight>]

                        BackgroundColorIndex = <TitleBackgroundColorIndex>
                        TextColorIndex = <TitleTextColorIndex>
                        TextSizeIndex = <TitleTextSizeIndex>
                        TypefaceToken = <TitleTypeface>
                        HorizontalMagnifiableOffset = <TitleMagnifiableOffset>
                    )
                )
            ] : [] )
        )
    ]
)

// Descipteur du block de titre du Panel
template GenericPanelHUD_BlockTitle [
    ElementName : string = '',
    UniqueTagTextName: string = '',
    TextToken : string = '',

    RelativeWidthHeight : float2 = [0.0, 0.0],
    MagnifiableWidthHeight : float2 = [0.0, 0.0],

    TextColorIndex : int,
    TextSizeIndex : int,
    TypefaceToken : string,
    HorizontalMagnifiableOffset : int,
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = <RelativeWidthHeight>
        MagnifiableWidthHeight = <MagnifiableWidthHeight>
    )
    HidePointerEvents = true

    Components =
    ( <TextToken> != '' ?
        [
            BUCKTextDescriptor
            (
                ElementName = <ElementName>
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1, 1]
                    MagnifiableWidthHeight = [-2.0*<HorizontalMagnifiableOffset> , 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Left
                    VerticalAlignment = UIText_VerticalCenter
                    InterLine = 0
                )
                TextStyle = "GenericPanelHUD_BlockTitle"

                HorizontalFitStyle = ~/FitStyle/UserDefined

                TypefaceToken = <TypefaceToken>
                BigLineAction = ~/BigLineAction/MultiLine
                TextToken = <TextToken>
                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                HasBackground = true

                TextColorIndex = <TextColorIndex>
                TextSizeIndex = <TextSizeIndex>
            )
        ] : []
    )
)

Component_PanelHUD is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1,1] )
)

// Descipteur du block de contenu du Panel
template GenericPanelHUD_BlockContent [
    UniqueName : string = '',
    Components : LIST<TBUCKContainerDescriptor>,
    Height : float,
    Width : float,
    RelativeWidth : float,
    TopBottomMargin : float = 0,
    LeftRightMargin : float = 0,
    BackgroundBlockColorToken : string = '0',
    LastExtendedWeight : boolean = false,
    HasBackground : boolean = true,
]
is BUCKContainerDescriptor
(
    UniqueName = <UniqueName>
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = (<LastExtendedWeight>) ? [0, 1] : [<RelativeWidth>, 0]
        MagnifiableWidthHeight = [<Width>, <Height>]
    )
    HasBackground = <HasBackground>
    BackgroundBlockColorToken = <BackgroundBlockColorToken>

    Components = [
        ~/Component_PanelHUD,
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                MagnifiableWidthHeight = [-<LeftRightMargin> * 2, -<TopBottomMargin> * 2]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )
            Components = <Components>
        )
    ]

    HidePointerEvents = true
)


GameRulesHelper is TUITacticalGameRulesHelper
(
    TimeStepMessage = "REM_TIME"
    TimeInMinutesStepList = [10,5,4,3,2,1]

    SuddenDeathTokens =
    [
        [ "SUDD_ENEAL", "SUDD_ALLAL", "SUDD_LOCAL", "SUDD_NEUT" ], // sudden death no owned zone
        [ "NOMU_ENEAL", "NOMU_ALLAL", "NOMU_LOCAL", "NOMU_NEUT" ], // sudden death no more production
    ]

    StandardStepMessages = TUITacticalGameRulesHelperStepMessages
    (
        // Message on self score update
        LocalPlayerAllianceScore =
        [
            (50.0, "win50"),
            (75.0, "win75"),
            (90.0, "win90")
        ]

        // Message about other alliance score update
        OtherAllianceScore =
        [
            (50.0, "Ewin50"),
            (75.0, "Ewin75"),
            (90.0, "Ewin90")
        ]
    )

    //Attacking alliance
    AttackingAllianceStepMessages = TUITacticalGameRulesHelperStepMessages
    (
        // Message on self score update
        LocalPlayerAllianceScore =
        [
            (20.0, "BKLOC20"),
            (50.0, "BKLOC50"),
            (75.0, "BKLOC75")
        ]
    )

    // Defending alliance
    DefendingAllianceStepMessages = TUITacticalGameRulesHelperStepMessages
    (
        // Message on self score update
        LocalPlayerAllianceScore =
        [
            (80.0, "BKOTH80"),
            (50.0, "BKOTH50"),
            (25.0, "BKOTH25")
        ]

        AreDecreasingSteps = true
    )

    TimeToShowStepMessageInSecond = 5

    ComponentToUseForSuddenDeathMessage = [ "SuddenDeathVictoryComponent","SuddenDeathWarningComponent","SuddenDeathAlertComponent","SuddenDeathWarningComponent" ]

    ComponentToUseForRemainingTimeMessage = "RemainingTimeAlertComponent"
    ComponentToUseForLocalPlayerAllianceMessage = "ConquestLocalPlayerHasAdvantageComponent"
    ComponentToUseForOtherAllianceMessage = "ConquestOpponentHasAdvantageComponent"
    LDHintTextComponentNameToFill = "Text1"

    LDHintMoralDico = "INGAME"
    LDHintMoralTokenLocalPlayerAlliance = "MOLO_HTS"
    LDHintMoralTokenEnemyAlliance = "MOLO_HTE"
    LDHintMoralDuration = 5.0
    LDHintMoralThreshold = [ 0.8, 0.5, 0.2 ]

    LDHintComponentForLocalPlayerAlliance = "LocalPlayerAllianceMoralComponent"
    LDHintComponentForEnemyAlliance = "EnemyAllianceMoralComponent"
)


UIOrderDisplayResources is TTacticOrderDisplayResources
(
    Camera                      = $/M3D/Misc/CameraPrincipale
    OrderDisplayUserInputLayer  = $/M3D/Input/UserInputLayerHandler/InputLayer_OrderDisplay
    OrderDisplayDrawer          = ~/OrderDisplayDrawer
    AlwaysShowOrderButtonAlsoShowsSmartOrders = true

    // configuration des flèches d'ordres

    StartFadeAlphaAfterTimeInSeconds = 1.0
    AlphaFadeDurationInSeconds = 2.0
    SubSelectionAlphaFadeMultiplier = 0.2

    CameraMoverHelperProxy    = $/M3D/Scene/IngameCameraMoverHelperProxy
    FadeHeightTransitionStart = 4*16000.0//9000.0
    FadeHeightTransitionEnd   = 3*16000.0 //5000.0
)


// -------------------------------------------------------------------------------------------------

OffMapAirplaneComponentDimension is [111.0, 72.0]
OffMapAirplaneRadioButton is TBUCKRadioButtonManager()

BUCKSpecificOffMapAirplaneMainComponentDescriptor is BUCKSpecificOffMapButton
(
    ElementName = "OffMapAirplaneMainComponent"

    ButtonWidthHeight = ~/OffMapAirplaneComponentDimension
    HeightCoefficientToRemoveFromInteriorButtonWidth = 0.4
    MainBackgroundColorToken = 'BoutonTemps_Background'
    BorderLineColorToken = 'BoutonTemps_Line'

    Components =
    [
        BUCKSpecificButton
        (
            ElementName = 'SelectionButton'

            ButtonMagnifiableWidthHeight = ~/OffMapAirplaneComponentDimension
            ButtonAlignementToFather = [0.0, 0.5]
            ButtonAlignementToAnchor = [0.0, 0.5]

            BorderLineColorToken = "BoutonTemps_Line"
            BigBorderBackgroundColorToken = "BoutonTemps_Background"
            BackgroundBlockColorToken  = "BoutonTemps_Background"
            TextColorToken = "ButtonHUD/Text2"

            IsTogglable = true
            HasBorder = true

            Components =
            [
                ~/IconeUnite,
                ~/BlocNom_status_jauge,
                ~/AirplaneXP,
            ]
        ),
        // bouton EVAC
        BUCKButtonDescriptor
        (
            ElementName = 'EvacuateButton'
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [23.0, 23.0]
                AlignementToFather = [1.0, 0.0]
                AlignementToAnchor = [1.0, 0.0]
            )

            IsTogglable = true

            RadioButtonManager = ~/OffMapAirplaneRadioButton

            Components =
            [
                BUCKTextureDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                    )

                    TextureToken = 'icone_evacuation'
                    TextureFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                        AlignementToFather = [0.5, 0.5]
                        AlignementToAnchor = [0.5, 0.5]
                    )
                    TextureColorToken = 'BoutonTemps_Text'
                ),

                BUCKSpecificHintableArea
                (
                    DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    HintTitleToken = "HOM_EVAT"
                    HintBodyToken = "HOM_EVAB"
                    HintExtendedToken = "HOM_EVAE"
                )
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------

AirplaneXP is BUCKTextureDescriptor
(
    ElementName = "XPTexture"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = XPStarMagnifiableWitdhHeight
        AlignementToFather = [1.0, 0.0]
        AlignementToAnchor = [1.0, 0.0]
        MagnifiableOffset = [-3.0, 3.0]
    )

    TextureFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1.0, 1.0] )

    HasBackground = true
    BackgroundBlockColorToken = "bleuNavy_fonce"

    // Hint
    Components =
    [
         BUCKSpecificHintableArea
        (
            DicoToken = ~/LocalisationConstantes/dico_interface_ingame
            HintTitleToken = "HPD_UEXPT"
            HintBodyToken = "HPD_UEXPB"
            HintExtendedToken = "HPD_UEXPE"
        )
    ]
)

//-------------------------------------------------------------------------------------

BlocNom_status_jauge is BUCKListDescriptor
(
    ElementName = 'BlocNom_status_jauge'

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        AlignementToAnchor  = [0.0, 1.0]
        AlignementToFather  = [0.0, 1.0]
    )

    Axis = ~/ListAxis/Vertical

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/StateGauge
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/NomUnite
        ),
    ]
)

//-------------------------------------------------------------------------------------
StatusUnite is BUCKTextDescriptor
(
    ElementName = 'StateGaugeText'

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 12.0]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )

    ParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Center
        VerticalAlignment = UIText_VerticalCenter
        InterLine = 0
    )

    TextStyle = "Default"

    HorizontalFitStyle = ~/FitStyle/UserDefined
    VerticalFitStyle = ~/FitStyle/UserDefined


    TypefaceToken = "IBM"
    BigLineAction = ~/BigLineAction/CutByDots

    HasBackground = false

    TextDico = ~/LocalisationConstantes/dico_interface_ingame

    TextColor = "Blanc"
    TextSize = "12"

    Hint = BUCKSpecificHintableArea
    (
        ElementName = "StateHint"
        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
        HintTitleToken = "HOM_STATT"
        HintExtendedToken = "HOM_STATE"
    )
)

//-------------------------------------------------------------------------------------

NomUnite is BUCKTextDescriptor
(
    ElementName = "UnitName"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 15.0]
    )

    HasBackground = true
    BackgroundBlockColorToken = 'OffMapUnitButtonName'
    TextStyle = "Default"

    ParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Center
        VerticalAlignment = UIText_VerticalCenter
    )

    BigLineAction = ~/BigLineAction/CutByDots

    TextSize = "12"
    TextColor = "BlancEquipe"
    TextToken = ""
    TextDico = ~/LocalisationConstantes/dico_interface_ingame
    TypefaceToken = "UIMainFont"

    HorizontalFitStyle = ~/FitStyle/UserDefined
    VerticalFitStyle = ~/FitStyle/UserDefined

    Hint = BUCKSpecificHintableArea
    (
        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
        HintTitleToken = "HOM_UNNAMT"
        HintBodyToken = "HOM_UNNAMB"
        HintExtendedToken = "HOM_UNNAME"
    )
)

//-------------------------------------------------------------------------------------

IconeUnite is BUCKContainerDescriptor
(
    ElementName = "IconeUnite"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToFather = [0.0, 0.5]
        AlignementToAnchor = [0.0, 0.5]
    )

    Components =
    [
        BUCKTextureDescriptor
        (
            ElementName = 'UnitTexture'
            ComponentFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1.0, 1.0] )
            TextureFrame   = TUIFramePropertyRTTI( RelativeWidthHeight = [1.0, 1.0] )
        ),

        BUCKSpecificHintableArea
        (
            ElementName = 'UnitTextureHint'
            DicoToken = ~/LocalisationConstantes/dico_units
            HintTitleToken = "UNKNOWN"
            HintBodyToken = "UNKNOWN"
            HintExtendedToken = "UNKNOWN"
        ),
    ]
)

//-------------------------------------------------------------------------------------

StateGauge is BUCKGaugeDescriptor
(
    ElementName = 'StateGauge'

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 12.0]
    )

    GaugeComponentNames = [ 'StateGaugeValue' ]
    GaugeValueMinSize = TRTTILength( Pixel = 1.0 )
    DurationForGaugeFullChange = 1
    GaugeMax = 100

    HasBackground = true
    BackgroundBlockColorToken = 'TypeC'

    Components =
    [
        BUCKGaugeValueDescriptor
        (
            ElementName = 'StateGaugeValue'
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
            )

            HasBackground = true
            BackgroundBlockColorToken = "Fulda2_Orange100"
        ),
        ~/StatusUnite,
    ]
)

//-------------------------------------------------------------------------------------

UISpecificOffMapAirplaneViewDescriptor is TUISpecificOffMapAirplaneViewDescriptor
(
    MainComponentDescriptor = ~/BUCKSpecificOffMapAirplaneMainComponentDescriptor
    MainComponentContainerUniqueName = "" // Permet d'indiquer l'endroit ou le composant doit s'insérer

    ExperienceTextureTokens = ["CommonTexture_Chevron_1", "CommonTexture_Chevron_2", "CommonTexture_Chevron_3"]
)

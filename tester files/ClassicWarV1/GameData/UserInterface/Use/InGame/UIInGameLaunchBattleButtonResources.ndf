UILaunchBattleViewDescriptor is TUISpecificLaunchBattleViewDescriptor
(
    MainComponentDescriptor = ~/DeploymentPhasePanel
    MainComponentContainerUniqueName = "SpecificLaunchBattleMainComponentDescriptor"
)

DeploymentPhasePanelWidth is 320.0
DeploymentPhasePanelHeight is 140.0

DeploymentVerticalListWidth is 220

//-------------------------------------------------------------------------------------

DeploymentPhasePanel is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    Components =
    [
        PanelRoundedCorner
        (
            BackgroundBlockColorToken = 'H2_bleu_2'
            BorderLineColorToken = 'H2_bleu_2'
        ),
        //cadre
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [DeploymentPhasePanelWidth - 13.0, DeploymentPhasePanelHeight - 18.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            HasBorder = true
            BorderLineColorToken = 'BoutonTemps_Line'
            BorderThicknessToken = '3'
        ),
        BUCKTextDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [DeploymentPhasePanelWidth - 13.0, 30.0]
                MagnifiableOffset = [0.0, 8.0]
                AlignementToFather = [0.5, 0.0]
                AlignementToAnchor = [0.5, 0.0]
            )

            HorizontalFitStyle = ~/FitStyle/UserDefined
            VerticalFitStyle = ~/FitStyle/UserDefined

            TextStyle = 'Default'
            TextToken = 'DSI_Title2'
            TypefaceToken = "UIMainFont"
            TextDico = ~/LocalisationConstantes/dico_interface_ingame
            TextColor = "BlancTexte"
            TextSize = "28"

            HasBackground = true
            BackgroundBlockColorToken = 'BoutonTemps_Background'

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
            )

            HasBorder = true
            BorderLineColorToken = 'BoutonTemps_Line'
            BorderThicknessToken = '3'
            BordersToDraw = ~/TBorderSide/Bottom
        ),
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset =  [0.0, 40.0]
                MagnifiableWidthHeight = [DeploymentVerticalListWidth, 0.0]
                AlignementToFather = [0.5, 0.0]
                AlignementToAnchor = [0.5, 0.0]
            )

            Axis = ~/ListAxis/Vertical

            FirstMargin = TRTTILength(Magnifiable = 7.0)
            InterItemMargin = TRTTILength(Magnifiable = 7.0)

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKListDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            AlignementToFather = [0.0, 0.0]
                            AlignementToAnchor = [0.0, 0.0]
                        )

                        BreadthComputationMode = BreadthComputationMode/ComputeBreadthFromLargestChild
                        InterItemMargin = TRTTILength(Magnifiable = 7.0)
                        Axis = ~/ListAxis/Horizontal

                        Elements =
                        [
                            BUCKListElementDescriptor
                            (
                                ComponentDescriptor = BUCKContainerDescriptor
                                (
                                    ComponentFrame = TUIFramePropertyRTTI ()
                                    UniqueName = "SpecificAutoDeployMainComponentDescriptor"
                                    FitStyle = ~/ContainerFitStyle/FitToContent
                                )
                            ),
                            BUCKListElementDescriptor
                            (
                                ComponentDescriptor = BUCKContainerDescriptor
                                (
                                    ComponentFrame = TUIFramePropertyRTTI ()
                                    ChildFitToContent = true
                                    UniqueName = "SpecificIdleUnitMainComponentDescriptor"
                                    FitStyle = ~/ContainerFitStyle/FitToContent
                                )
                            )
                        ]
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKContainerDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI ()
                        Components =
                        [
                             ~/LaunchBattleMainComponent
                        ]
                        FitStyle = ~/ContainerFitStyle/FitToContent
                    )
                ),
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------

LaunchButtonCancelDescriptor is BUCKTextDescriptor
(
    ElementName = "LaunchBattleCancelText"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [DeploymentVerticalListWidth, 10.0]
        MagnifiableOffset = [-1.0, -1.0]
        AlignementToFather = [1.0, 1.0]
        AlignementToAnchor = [1.0, 1.0]
    )

    ParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Right
        VerticalAlignment = UIText_VerticalCenter
        InterLine = 0
    )

    TextStyle = "Default"

    HorizontalFitStyle = ~/FitStyle/UserDefined
    VerticalFitStyle = ~/FitStyle/UserDefined

    TypefaceToken = "UIMainFont"
    BigLineAction = ~/BigLineAction/ResizeFont

    TextColor = "DeploymentPhase/CancelTimer"
    TextSize = "12"

    TextDico = ~/LocalisationConstantes/dico_interface_ingame
)

//-------------------------------------------------------------------------------------

SpotLight_StartGame is BUCKSpotlightDescriptor
(
    UniqueName = "Spotlight_StartGame"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToAnchor = [0.0, 0.0]
        AlignementToFather = [0.0, 0.0]
    )
)

//-------------------------------------------------------------------------------------
//Bouton Launch Battle et timer
private LaunchBattleMainComponent is BUCKSpecificButton
(
    UniqueName = "LaunchBattleButton"
    ElementName = 'LaunchBattleButton'

    RoundedCorner = true
    RoundedVertexes = [true, true, false, false]

    ButtonMagnifiableWidthHeight = [~/DeploymentVerticalListWidth, 31.0]
    ButtonAlignementToAnchor = [0.5, 0]
    ButtonAlignementToFather = [0.5, 0]

    HasText = true
    TextParagraphStyle = ~/LaunchBattleParagraphStyleCentered
    TextStyle = "Default"
    TextSizeToken = "SD2_Moyen"
    TextDico = ~/LocalisationConstantes/dico_interface_ingame

    HidePointerEvents = true

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken = "HLB_BUTTOT"
        HintBodyToken = "HLB_BUTTOB"
        HintExtendedToken = "HLB_BUTTOE"
        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
    )

    Components = [~/LaunchButtonCancelDescriptor, ~/SpotLight_StartGame]
)

//-------------------------------------------------------------------------------------

private LaunchBattleParagraphStyleCentered is TParagraphStyle
(
    Alignment = UIText_Center
    VerticalAlignment = UIText_VerticalCenter
    InterLine = 0
)

export InGameHUDResource is TUISpecificInGameHUDResource
(
    MinimapResources            = ~/InGameMinimap

    HUDReplayResource               = ~/HUDReplayResource
    HUDAlertPanelDescriptor         = ~/UISpecificHUDAlertPanelViewDescriptor
    CameraManipulator                   = $/Camera/CameraManipulator

    PerformanceAlertViewDescriptor      = ~/PerformanceAlertViewDescriptor

    ShowUnitInfosMapping       = $/KeyboardOption/Mapping_ShowUnitInfos
    TurnOffWeaponsMapping      = $/KeyboardOption/Mapping_TurnOffWeapons
    DisplayAllOrdersMapping    = $/KeyboardOption/Mapping_DisplayAllOrders

    OrderDisplayResources                   = ~/UIOrderDisplayResources

    SelectionPanelDescriptor                 = ~/UISpecificUnitSelectionPanelViewDescriptor
    SelectionWeaponPanelDescriptor           = ~/UISpecificUnitSelectionWeaponPanelViewDescriptor
    SelectionRoeDescriptor                   = ~/UISpecificUnitSelectionRoeViewDescriptor

    MiniMapInfoViewDescriptor               = ~/UISpecificMiniMapInfoViewDescriptor
    ShortcutsForSelectionViewDescriptor     = ~/UISpecificShortcutsForSelectionViewDescriptor

    PhasePanelElementNames = ~/PhasePanelElementNames
)

UIMinimapTypeFree is 0
UIMinimapTypeBox is 1
UIMinimapTypeRound is 2

//----------------------------------------------------------------------------------
// Enum des pings de minimap
// NOTA : laisser la valeur "None" sur le 0
//----------------------------------------------------------------------------------
export MinimapPing_None                 is 0
export MinimapPing_Beep                 is 1
export MinimapPing_Cross                is 2
export MinimapPing_Alarm                is 3
export MinimapFlareAttack               is 4
export MinimapFlareDefend               is 5
export MinimapFlareHelp                 is 6
export MinimapFlareCustom               is 7
export MinimapPing_Spotted              is 8
export MinimapFlareFireSupport          is 9
export MinimapFlareEnemySpotted         is 10

MinimapPingType_Enum is TMinimapPingType
(
    Values = [
        "MinimapPing_None",
        "MinimapPing_Beep",
        "MinimapPing_Cross",
        "MinimapPing_Alarm",
        "MinimapFlareAttack",
        "MinimapFlareDefend",
        "MinimapFlareHelp",
        "MinimapFlareCustom",
        "MinimapPing_Spotted",
        "MinimapFlareFireSupport",
        "MinimapFlareEnemySpotted",
    ]
)

//----------------------------------------------------------------------------------
// Enum des types d'animation pour les pings de minimap
// NOTA : laisser la valeur "None" sur le 0
//----------------------------------------------------------------------------------
export MinimapPingAnim_None            is 0
export MinimapPingAnim_Fade            is 1
export MinimapPingAnim_Scale           is 2

MinimapPingAnimType_Enum is TMinimapPingAnimType
(
    Values = [
        "MinimapPingAnim_None",
        "MinimapPingAnim_Fade",
        "MinimapPingAnim_Scale",
    ]
)

MinimapPanelDescriptor is TUISpecificBUCKMinimapDescriptor
(
    ElementName = 'MinimapPanel'
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        MagnifiableWidthHeight = [-4.0, -4.0]
        AlignementToFather = [0.5, 0.5]
        AlignementToAnchor = [0.5, 0.5]
    )

    UniformDrawer = $/UserInterface/UIUniformDrawer
    IsClippable = true
)

InGameMinimap is TUISpecificInGameMinimapResources
(
    WorldFloorProxy                     = $/M3D/Scene/WorldFloorForIAProxy
    CameraManipulator             = $/Camera/CameraManipulator
    CameraMoverHelperProxy              = $/M3D/Scene/IngameCameraMoverHelperProxy

    CameraAngleTexture                  = ~/Texture_Minimap_CameraAngle

    UniformAntiAliasedDrawer            = $/UserInterface/UIUniformAntiAliasedDrawer

    TextureDrawer = "ColorMultiply"

    TextureForPingType = MAP[
        ( MinimapPing_Beep, ~/Texture_Minimap_Alert ),
        ( MinimapPing_Cross, ~/Texture_Minimap_Death ),
        ( MinimapPing_Alarm, ~/Texture_Minimap_Alarm ),
        ( MinimapFlareAttack, ~/Texture_MinimapFlareAttack ),
        ( MinimapFlareDefend, ~/Texture_MinimapFlareDefend ),
        ( MinimapFlareHelp, ~/Texture_MinimapFlareHelp ),
        ( MinimapFlareCustom, ~/Texture_MinimapFlareCustom ),
        ( MinimapPing_Spotted, ~/Texture_MinimapPing_Spotted ),
        ( MinimapFlareFireSupport, ~/Texture_MinimapFlareFireSupport ),
        ( MinimapFlareEnemySpotted, ~/Texture_MinimapFlareEnemySpotted ),
    ]

    UnitAppearingColor                  = RGBA[255, 255, 255, 255]
    UnitSelectedColor                   = RGBA[255, 255, 255, 255]
    UnitTakingDamageColor               = RGBA[255, 0, 0, 255]

    CameraTrapezoidOriginColor          = RGBA[255, 255, 255, 120]

    ScaleUnitInsideMinimap              = 1.0
    ScalePingInsideMinimap              = 0.65

    CommandZoneBorderWidth              = 0.5

    // Les layers sont répartis en 5 couches de 4 layers, soit 20 au total. Le layer 0 est tout au fond et le 19 est au sommet, le plus visible.
    // 'SortOrderList' définit la couche. C'est la catégorie de trucs qu'on affiche, histoire de dire "les flares c'est au-dessus des unités"
    // 'SortOrderInsideLayer' définit le sous-niveau, à l'intérieur d'une couche. Là c'est plutôt "Parmi tel type d'infos, on affiche mes trucs par-dessus ceux des autres"
    // Avec les valeurs actuelles, par exemple un Supply ennemi appartiendra à la couche 1, sous-couche 2, soit layer 6 (1x4 + 2)

    // specifier Layer pour CommandZone,Unit,Fob,Command,Supply,Alert,Mort,Flare
    // 0 en dessous 5 au dessus et 7 valeurs OBLIGATOIREMENT
    SortOrderList                       = [0,0,1,1,1,3,4,2]
    // specifier Ordre a l'interieur d'un layer pour Player,Allied,Enemy
    // valeur de 0 a 3, et 4 valeurs OBLIGATOIREMENT
    SortOrderInsideLayer                = [3,1,2,0]

    EventDuration                       = 0.80
    Camera                              = $/M3D/Misc/CameraPrincipale

    SeuilRotateCameraInDegree           = 20

    DistrictColor                       = RGBA[255, 252,  58, 255]
)

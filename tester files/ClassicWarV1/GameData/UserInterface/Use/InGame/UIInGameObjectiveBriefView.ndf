
private ObjectiveBriefMainComponentWidth is 320.0

BUCKSpecificInGameObjectiveBriefMainComponentDescriptor is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1,0]
    )

    FirstMargin  = TRTTILength (Magnifiable = 8.0)
    InterItemMargin = TRTTILength(Magnifiable = 8.0)
    LastMargin  = TRTTILength (Magnifiable = 8.0)

    Axis = ~/ListAxis/Vertical

    HidePointerEvents = true

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight = [0,24]
                )

                ParagraphStyle = ~/LDHintParagraphStyleLeftAlignedExact
                HorizontalFitStyle = ~/FitStyle/MinBetweenUserDefinedAndContent
                VerticalFitStyle = ~/FitStyle/UserDefined

                TextPadding = TRTTILength4(Magnifiable = [10.0, 0.0, 4.0, 0.0])

                TextStyle = 'Default'
                TypefaceToken = "Eurostyle_Medium"

                TextToken = 'Obj_ti2'
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                TextSize = '16'
                TextColor = 'BlancEquipe'
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKRackDescriptor
            (
                ElementName = "ObjectiveListRack"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                )

                Axis = ~/ListAxis/Vertical

                InterItemMargin = TRTTILength (Magnifiable = 6.0)

                BladeDescriptor = ~/ObjectivePanelOneObjectiveContent
            )
        ),
    ]
    BackgroundComponents =
    [
        PanelRoundedCorner()
    ]
)

private OneObjectiveWidthMarginLeft is 15.0
private OneObjectiveWidthMarginRight is 4.0
private OneObjectiveContentWidth is ~/ObjectiveBriefMainComponentWidth - ~/OneObjectiveWidthMarginLeft - ~/OneObjectiveWidthMarginRight
private OneObjectiveCheckboxSize is 16.0
private OneObjectiveSpaceBetwenCheckboxAndText is 10.0
private OneObjectiveTextContentWidth is ~/OneObjectiveContentWidth - ~/OneObjectiveCheckboxSize - ~/OneObjectiveSpaceBetwenCheckboxAndText

private ObjectivePanelOneObjectiveContent is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 0.0]
    )

    FirstMargin = TRTTILength(Magnifiable = ~/OneObjectiveWidthMarginLeft)
    InterItemMargin = TRTTILength(Magnifiable = ~/OneObjectiveSpaceBetwenCheckboxAndText)
    LastMargin = TRTTILength(Magnifiable = ~/OneObjectiveWidthMarginRight)
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    Axis = ~/ListAxis/Horizontal

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKSpecificCheckBoxDescriptor
            (
                ElementName = "ObjectiveCompletedCheckbox"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [~/OneObjectiveCheckboxSize, ~/OneObjectiveCheckboxSize]
                )
                BorderLineColorToken = "Objective/CheckboxBorder"
                TextureColorToken = "Objective/CheckboxBorder"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [~/OneObjectiveTextContentWidth, 0.0]
                )

                FirstMargin = TRTTILength(Magnifiable = 2)
                InterItemMargin = TRTTILength(Magnifiable = 2.0)

                Axis = ~/ListAxis/Vertical

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ElementName = "ObjectiveTitle"

                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                            )

                            ParagraphStyle = ~/LDHintParagraphStyleLeftAlignedExact
                            HorizontalFitStyle = ~/FitStyle/UserDefined
                            VerticalFitStyle = ~/FitStyle/FitToContent

                            TextStyle = 'Default'
                            TypefaceToken = "Eurostyle_Medium"

                            TextSize = '14'
                            TextColor = 'SM_paleSilver'
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ElementName = "ObjectiveDescription"

                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                            )

                            ParagraphStyle = ~/LDHintParagraphStyleLeftAlignedExact
                            HorizontalFitStyle = ~/FitStyle/UserDefined
                            VerticalFitStyle = ~/FitStyle/FitToContent
                            BigLineAction = ~/BigLineAction/MultiLine
                            TextStyle = 'Default'
                            TypefaceToken = "Eurostyle_Italic"

                            TextSize = '14'
                            TextColor = 'SM_paleSilver'
                        )
                    ),
                ]
            )
        ),
    ]
)

UISpecificInGameObjectiveBriefViewDescriptor is TUISpecificObjectiveBriefViewDescriptor
(
    MainComponentDescriptor = ~/BUCKSpecificInGameObjectiveBriefMainComponentDescriptor
    MainComponentContainerUniqueName = "InGameGlobalObjectivesContainer" // Permet d'indiquer l'endroit ou le composant doit s'insérer

    TextColorByObjectiveState = MAP
    [
        (
            EStateObjective/Incomplete,
            "SM_paleSilver"
        ),
        (
            EStateObjective/Failed,
            "score_05"
        ),
        (
            EStateObjective/Succes,
            "score_02"
        ),
        (
            EStateObjective/Ended,
            "Objective/Failed"
        ),
    ]
)

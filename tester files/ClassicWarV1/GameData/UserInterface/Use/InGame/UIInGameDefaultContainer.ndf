DistanceBetweenExternalAndInternalBorder is 3

template TContainerDarkStyle
[
    ComponentFrame : TUIFramePropertyRTTI,
    Components : LIST<TBUCKContainerDescriptor> = [],
    ElementName : string = '',
    UniqueName : string = '',
    ShowLightTexture : bool = true,
    HidePointerEvents : bool = true,
    HasInternalBorders : bool = false,
    BorderLineColorToken : string = "ContainerDarkStyle/BordureExterne",
    BackgroundBlockColorToken : string = "Noir80"
]
is BUCKContainerDescriptor
(
    ElementName = <ElementName>
    UniqueName = <UniqueName>

    ComponentFrame = <ComponentFrame>

    HasBackground = true
    BackgroundBlockColorToken = <BackgroundBlockColorToken>

    HasBorder = true
    BorderLineColorToken = <BorderLineColorToken>
    BorderThicknessToken = "1"
    BordersToDraw = ~/TBorderSide/All

    HidePointerEvents = <HidePointerEvents>

    Components =
    (<ShowLightTexture> ?
        [
            BUCKTextureDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                   RelativeWidthHeight = [0.17, 0.9]
                )

                TextureToken = "StyleCockpitTexture_Light_BackgroundDarkStyle"
                TextureFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1, 1]
                )
            ),
        ]
        :
        []
    ) +
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1, 1]
                PixelWidthHeight = [-2 * DistanceBetweenExternalAndInternalBorder, -2 * DistanceBetweenExternalAndInternalBorder]
                MagnifiableOffset = [0.0, 0.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            HasBorder = <HasInternalBorders>
            BorderLineColorToken = <BorderLineColorToken>
            BorderThicknessToken = "1"
            BordersToDraw = ~/TBorderSide/All

            Components = <Components>
        )
    ]
)

template OmbrePanel
[
    ElementName : string = "",
] is AuraDescriptor
(
    ElementName = <ElementName>
    TextureDrawer = "ColorMultiply"
    AuraBorderSize = 12.0
    TopTextureToken = "CommonTexture_Ombre_TopTileTexture"
    BottomTextureToken = "CommonTexture_Ombre_BottomTileTexture"
    LeftTextureToken = "CommonTexture_Ombre_LeftTileTexture"
    RightTextureToken = "CommonTexture_Ombre_RightTileTexture"
    TopLeftTexture = "CommonTexture_Ombre_CornerTexture_TopLeft"
    TopRightTexture = "CommonTexture_Ombre_CornerTexture_TopRight"
    BottomRightTexture = "CommonTexture_Ombre_CornerTexture_BottomRight"
    BottomLeftTexture = "CommonTexture_Ombre_CornerTexture_BottomLeft"
)

template SmallOmbrePanel
[
    ElementName : string = "",
] is AuraDescriptor
(
    ElementName = <ElementName>
    TextureDrawer = "ColorMultiply"
    AuraBorderSize = 4.0
    TopTextureToken = "CommonTexture_Ombre_TopTileTexture"
    BottomTextureToken = "CommonTexture_Ombre_BottomTileTexture"
    LeftTextureToken = "CommonTexture_Ombre_LeftTileTexture"
    RightTextureToken = "CommonTexture_Ombre_RightTileTexture"
    TopLeftTexture = "CommonTexture_Ombre_CornerTexture_TopLeft"
    TopRightTexture = "CommonTexture_Ombre_CornerTexture_TopRight"
    BottomRightTexture = "CommonTexture_Ombre_CornerTexture_BottomRight"
    BottomLeftTexture = "CommonTexture_Ombre_CornerTexture_BottomLeft"
)

template PanelRoundedCorner
[
    // ++ BUCKContainerDescriptor
    ElementName : string = "",
    UniqueName : string = "",
    ComponentFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI ( RelativeWidthHeight = [1.0, 1.0] ),

    MagnifierMultiplication : float = 0.0,

    FitStyle : int = ~/ContainerFitStyle/None,
    ChildFitToContent : bool = false,

    ClipContent : bool = false,
    IsClippable : bool = true,

    HidePointerEvents : bool = false,

    HasBackground : bool = true,
    BackgroundBlockColorToken : string = "H2_bleu_2",
    HasBorder : bool = true,
    BorderThicknessToken : string = '1',
    BorderLineColorToken :string = "H2_bleu_2",

    BackgroundLocalRenderLayer : int = 0,
    BorderLocalRenderLayer : int = 0,

    Components = [],
    // -- BUCKContainerDescriptor

    Radius : int = 5,
    RoundedVertexes : LIST< bool > = [true, true, true, true], // le 1er est en bas à gauche
    NbOfPoints = 5,
]
is BUCKPolygonDescriptor
(
    MagnifierMultiplication = <MagnifierMultiplication>
    ElementName = <ElementName>
    UniqueName = <UniqueName>
    ComponentFrame = <ComponentFrame>


    PolygonShape = PolygonShapeRoundedContainer
    (
        Radius = TRTTILength( Magnifiable = <Radius>)
        NbOfPoints = <NbOfPoints>
        RoundedVertexes = <RoundedVertexes>
    )

    FitStyle = <FitStyle>
    ChildFitToContent = <ChildFitToContent>

    ClipContent = <ClipContent>
    IsClippable = <IsClippable>

    HidePointerEvents = <HidePointerEvents>

    HasBackground = <HasBackground>
    BackgroundBlockColorToken = <BackgroundBlockColorToken>

    HasBorder = <HasBorder>
    BorderThicknessToken = <BorderThicknessToken>
    BorderLineColorToken = <BorderLineColorToken>

    BackgroundLocalRenderLayer = <BackgroundLocalRenderLayer>
    BorderLocalRenderLayer = <BorderLocalRenderLayer>

    Components = <Components>
)

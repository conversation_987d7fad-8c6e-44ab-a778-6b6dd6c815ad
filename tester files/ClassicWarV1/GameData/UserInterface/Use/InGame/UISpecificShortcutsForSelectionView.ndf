//------------------------------------------------------------------------------

template ShortcutButton
[
    ElementName : string = "",
    UniqueName : string = "",
    SpotlightUniqueName : string = "",
    TextureToken : string = "",
    HintableAreaComponent : TBUCKHintableAreaDescriptor = nil,
    MagnifiableWidthHeightTexture : float2 = [30.0, 30.0],
    BackgroundBlockColorToken : string = "BoutonTemps_Background",
    BorderLineColorToken : string = "CouleurBordure_boutonShortcuts",
    TextureColorToken : string,
    LeftClickSound : TSoundDescriptor = nil,
    Mapping : TEugBMutablePBaseClass = nil,
    Components : LIST<TBUCKContainerDescriptor> = [],
    IsTogglable : bool = true
] is BUCKButtonDescriptor
(
    ElementName = <ElementName>
    UniqueName = <UniqueName>
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [52.0, 32.0]
    )

    IsTogglable = <IsTogglable>
    DefaultToggleValue = false
    HasBorder = false
    LeftClickSound = <LeftClickSound>
    Mapping = <Mapping>

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                MagnifiableWidthHeight = [-4.0, -4.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            Components =
            [
                PanelRoundedCorner
                (
                    BackgroundBlockColorToken = <BackgroundBlockColorToken>
                    BorderLineColorToken = <BorderLineColorToken>
                )
            ]
        ),

        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
               MagnifiableWidthHeight = <MagnifiableWidthHeightTexture>
               AlignementToFather = [0.5, 0.5]
               AlignementToAnchor = [0.5, 0.5]
            )

            TextureToken = <TextureToken>
            TextureColorToken = <TextureColorToken>

            Components = (<HintableAreaComponent> != nil ? [<HintableAreaComponent>] : [])
        ),

    ] +
    <Components>
    + ( <SpotlightUniqueName> == "" ? []
        : [
            BUCKSpotlightDescriptor
            (
                UniqueName = <SpotlightUniqueName>
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )
            )
        ]
    )
)

template ShortcutButtonWithBaseHint
[
    ElementName : string = "",
    TextureToken : string = "",
    HintTitleToken : string = "",
    HintBodyToken : string = "",
    HintExtendedToken : string = "",
    MagnifiableWidthHeightTexture : float2 = [30.0, 30.0],
] is ShortcutButton
(
    ElementName = <ElementName>
    MagnifiableWidthHeightTexture = <MagnifiableWidthHeightTexture>
    TextureToken = <TextureToken>
    TextureColorToken = "CouleurTexture_boutonShortcuts"

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken = <HintTitleToken>
        HintBodyToken = <HintBodyToken>
        HintExtendedToken = <HintExtendedToken>
        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
    )
)

template ShortcutButtonWithStrategicHint
[
    ElementName : string = "",
    UniqueName : string = "",
    SpotlightUniqueName : string = "",
    TextureToken : string = "",
    TextureColorToken : string = "CouleurTexture_boutonShortcutsStrategic",
    HintTitleToken : string = "",
    HintBodyToken : string = "",
    BackgroundBlockColorToken : string = "BoutonTemps_Background",
    BorderLineColorToken : string = "CouleurBordure_boutonShortcuts",
    LeftClickSound : TSoundDescriptor = nil,
    MagnifiableWidthHeightTexture : float2 = [30.0, 30.0],
    Mapping : TEugBMutablePBaseClass = nil,
    Components : LIST<TBUCKContainerDescriptor> = [],
    IsTogglable : bool = true
] is ShortcutButton
(
    ElementName = <ElementName>
    UniqueName = <UniqueName>
    SpotlightUniqueName = <SpotlightUniqueName>
    TextureToken = <TextureToken>
    BackgroundBlockColorToken = <BackgroundBlockColorToken>
    BorderLineColorToken = <BorderLineColorToken>
    LeftClickSound = <LeftClickSound>
    MagnifiableWidthHeightTexture = <MagnifiableWidthHeightTexture>
    TextureColorToken = <TextureColorToken>
    Mapping = <Mapping>

    HintableAreaComponent = BUCKSpecificStrategicHintableArea
    (
        HintTitleToken = <HintTitleToken>
        HintBodyToken = <HintBodyToken>
        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
    )
    Components = <Components>
    IsTogglable = <IsTogglable>
)

//------------------------------------------------------------------------------

OrderPanelPanelButton is ShortcutButtonWithBaseHint
(
    ElementName = 'OrdersFeedBackButton'
    TextureToken = "textureOrders"
    HintTitleToken = "HSL_ORDERT"
    HintBodyToken = "HSL_ORDERB"
)

//------------------------------------------------------------------------------

LoSPanelButton is ShortcutButtonWithBaseHint
(
    ElementName = 'LoSFeedBackButton'
    TextureToken = "icone_los"
    HintTitleToken = "HSL_ULOSBT"
    HintBodyToken = "HSL_ULOSBB"
    HintExtendedToken = "HSL_ULOSBE"
    MagnifiableWidthHeightTexture = [52.0, 32.0]

)

//------------------------------------------------------------------------------

ChatShortcutButton is ShortcutButtonWithBaseHint
(
    ElementName = 'ChatButton'
    TextureToken = "icone_chat"
    MagnifiableWidthHeightTexture = [52.0, 32.0]
    HintTitleToken = "HSL_chatt"
    HintBodyToken = "HSL_chatb"
)

//------------------------------------------------------------------------------

HelpButton is ShortcutButtonWithBaseHint
(
    TextureToken = "textureOrders "
)

//------------------------------------------------------------------------------

EngagementRulesButton is ShortcutButtonWithBaseHint
(
    ElementName = 'EngagementRulesButton'
    TextureToken = "icone_roe"
    MagnifiableWidthHeightTexture = [52.0, 32.0]
    HintTitleToken = "HSL_Roet"
    HintBodyToken = "HSL_Roeb"
)

//------------------------------------------------------------------------------

DisplayStartingInformationButton is ShortcutButtonWithBaseHint
(
    ElementName = 'StartingInformationButton'
    TextureToken = "icone_starting_information"
    HintTitleToken = "HSL_wrnt"
    HintBodyToken = "HSL_wrnb"
)

UnusedButtonTextContainer is BUCKContainerDescriptor
(
    ElementName = "IdleUnitNumberTextButton"
    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToFather = [1.0, 0.0]
        AlignementToAnchor = [0.5, 0.5]
        MagnifiableWidthHeight = [24, 24]
        MagnifiableOffset = [-2, 8]
    )

    Components =
    [
        PanelRoundedCorner
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                AlignementToFather = [0.0, 0.0]
                AlignementToAnchor = [0.0, 0.0]
                MagnifiableWidthHeight = [24, 24]
                MagnifiableOffset = [0, 0]
            )
            BackgroundBlockColorToken = "SM_Feldgrau_NoToggle"
            BorderLineColorToken = "SM_Grullo_NoToggle"
            Radius = 12.0
        ),
        BUCKTextDescriptor
        (
            ElementName = "IdleUnitNumberText"
            ComponentFrame = TUIFramePropertyRTTI
            (
                AlignementToFather =  [0.5, 0.5]
                AlignementToAnchor  = [0.5, 0.5]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Right
                VerticalAlignment = UIText_VerticalCenter
                InterLine = 0
            )

            TextStyle = "Default"

            HorizontalFitStyle = ~/FitStyle/FitToContent
            VerticalFitStyle = ~/FitStyle/FitToContent

            TypefaceToken = "Eurostyle"
            BigLineAction = ~/BigLineAction/ResizeFont

            TextDico = ~/LocalisationConstantes/dico_interface_ingame
            TextToken = "HPROD_CMDP"

            TextColor = "CouleurTexture_boutonShortcutsStrategic_NoToggle"
            TextSize = "12"
        )
    ]
)

UnusedUnitButton is ShortcutButtonWithStrategicHint
(
    ElementName = "NextIdleUnitButton"
    UniqueName = 'StrategicHeader_BtnUnusedUnit'
    SpotlightUniqueName = 'SpotLight_UnusedUnit'
    TextureToken = "icone_idle_unit"
    TextureColorToken = "CouleurTexture_boutonShortcutsStrategic_NoToggle"
    HintTitleToken = "HNT_NEXTUT"
    HintBodyToken = "HNT_NEXTUB"
    BackgroundBlockColorToken = "SM_Feldgrau_NoToggle"
    BorderLineColorToken = "SM_Grullo_NoToggle"
    LeftClickSound = SoundEvent_SteelmanCampaignStatus
    Mapping = $/KeyboardOption/Mapping_NextIdleUnit
    Components = [~/UnusedButtonTextContainer]
    IsTogglable = false
)

UnusedUnitContainer is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI ( MagnifiableWidthHeight = [52.0, 32.0])
    Components = [ ~/UnusedUnitButton, ]
)
//------------------------------------------------------------------------------
// pour Steelman
//------------------------------------------------------------------------------
BoutonAffichageStrategicSituation is ShortcutButtonWithStrategicHint
(
    ElementName = 'BtnStrategicSituation'
    UniqueName = 'StrategicHeader_BtnStrategicSituation'
    SpotlightUniqueName = 'SpotLight_SituationBtn'
    TextureToken = "UseStrategic_BoutonMission"
    HintTitleToken = "SSC_TITLE"
    HintBodyToken = "head_Osb"
    BackgroundBlockColorToken = "SM_Feldgrau"
    BorderLineColorToken = "SM_Grullo"
    LeftClickSound = SoundEvent_SteelmanCampaignStatus
)

BoutonAffichageOrdreDeBataille is ShortcutButtonWithStrategicHint
(
    UniqueName = 'StrategicHeader_OrganisationBtn'
    ElementName = 'BtnOrganisation'
    SpotlightUniqueName = 'SpotLight_OrganisationBtn'
    TextureToken = "UseStrategic_BoutonOrganisation"
    HintTitleToken = "SBO_TITL"
    HintBodyToken = "head_Obb"
    BackgroundBlockColorToken = "SM_Feldgrau"
    BorderLineColorToken = "SM_Grullo"
    LeftClickSound = SoundEvent_SteelmanOrderOfBattle
)


BoutonAffichagePertes is ShortcutButtonWithStrategicHint
(
    UniqueName = 'StrategicHeader_BtnLossesState'
    ElementName = 'BtnLossesState'
    SpotlightUniqueName = 'SpotLight_CasualtiesBtn'
    TextureToken = "UseStrategic_BoutonPertes"
    HintTitleToken = "SH_LOSS"
    HintBodyToken = "head_Lsb"
    BackgroundBlockColorToken = "SM_Feldgrau"
    BorderLineColorToken = "SM_Grullo"
    LeftClickSound = SoundEvent_SteelmanCasualtiesList
)

BoutonAccelerationDuTemps is ShortcutButtonWithStrategicHint
(
    ElementName = 'BtnGameSpeed'
    UniqueName = ''
    SpotlightUniqueName = 'SpotLight_GameSpeedBtn'
    TextureToken = "vitesse03Strat"
    HintTitleToken = "sp_t"
    HintBodyToken = "sp_c"
    BackgroundBlockColorToken = "SM_Feldgrau"
    BorderLineColorToken = "SM_Grullo"
    LeftClickSound = SoundEvent_SteelmanGameSpeed
)

BoutonChat is ShortcutButtonWithStrategicHint
(
    ElementName = 'ChatButton'
    MagnifiableWidthHeightTexture = [52.0, 32.0]
    UniqueName = ''
    SpotlightUniqueName = 'SpotLight_GameChat'
    TextureToken = "icone_chat"
    HintTitleToken = "HSL_chatt"
    HintBodyToken = "HSL_chatb"
    BackgroundBlockColorToken = "SM_Feldgrau"
    BorderLineColorToken = "SM_Grullo"
    LeftClickSound = SoundEvent_SteelmanChat
)

//------------------------------------------------------------------------------
// Spotlights Steelman
//------------------------------------------------------------------------------

SpotLight_SituationBtn is BUCKSpotlightDescriptor
(
    UniqueName = "SpotLight_SituationBtn"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToAnchor = [0.0, 0.0]
        AlignementToFather = [0.0, 0.0]
    )
)

SpotLight_OrganisationBtn is BUCKSpotlightDescriptor
(
    UniqueName = "SpotLight_OrganisationBtn"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToAnchor = [0.0, 0.0]
        AlignementToFather = [0.0, 0.0]
    )
)

SpotLight_CasualtiesBtn is BUCKSpotlightDescriptor
(
    UniqueName = "SpotLight_CasualtiesBtn"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToAnchor = [0.0, 0.0]
        AlignementToFather = [0.0, 0.0]
    )
)

SpotLight_GameSpeedBtn is BUCKSpotlightDescriptor
(
    UniqueName = "SpotLight_GameSpeedBtn"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToAnchor = [0.0, 0.0]
        AlignementToFather = [0.0, 0.0]
    )
)

//------------------------------------------------------------------------------

BUCKSpecificShortcutsForSelectionMainComponentDescriptor is PanelRoundedCorner
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 40.0]
        MagnifiableOffset = [10.0, 5.0]
        AlignementToFather = [1.0, 0.0]
        AlignementToAnchor = [1.0, 0.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContent
    HasBackground = true
    BackgroundBlockColorToken = 'TypeA'
    HasBorder = false
    Components =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [0.0, 0.0]
            )

            Axis = ~/ListAxis/Horizontal
            BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

            FirstMargin = TRTTILength(Magnifiable = 19.0)
            InterItemMargin = TRTTILength(Magnifiable = 13.0)
            LastMargin = TRTTILength(Magnifiable = 19.0)

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/DisplayStartingInformationButton
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/OrderPanelPanelButton
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/LoSPanelButton
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/ChatShortcutButton
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/EngagementRulesButton
                ),
            ]
        )
    ]
)

//------------------------------------------------------------------------------

UISpecificShortcutsForSelectionViewDescriptor is TUISpecificShortcutsForSelectionViewDescriptor
(
    MainComponentDescriptor = ~/BUCKSpecificShortcutsForSelectionMainComponentDescriptor
    MainComponentContainerUniqueName = "UISpecificShortcutsForSelectionViewContainer" // Permet d'indiquer l'endroit ou le composant doit s'insérer
)

//------------------------------------------------------------------------------

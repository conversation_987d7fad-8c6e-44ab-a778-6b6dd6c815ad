
TutorielMissionListBackground is "mission_dossierVide"
ChallengeMissionListBackground is "mission_dossierVide"
StrategicMissionListBackground is "mission_dossierVide"

//-------------------------------------------------------------------------------------
private ScenarioBriefButtonRadioManager is TBUCKRadioButtonManager()

private DlcLockWidth is 20.0
private FirstMarginWidth is 10.0

private template ScenarioBriefDescriptor
[
    GameType : int,
] is BUCKButtonDescriptor
(
    ElementName = "ScenarioBriefButton"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 25.0]
    )

    IsTogglable = true
    CannotDeselect = true
    LeftClickSound = SoundEvent_SelectTutorialOrChallenge

    RadioButtonManager = ~/ScenarioBriefButtonRadioManager

    Components =
    [
        Texte_Mission(GameType = <GameType>),
        BUCKButtonDescriptor
        (
            ElementName = "ButtonDLC"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            HasBackground = true
            BackgroundBlockColorToken = "DivisionBrief/ButtonOverlay"

            Components =
            [
                BUCKTextureDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        // RelativeWidthHeight = [0.0, 1.0]
                        MagnifiableWidthHeight = [DlcLockWidth, DlcLockWidth]
                        AlignementToFather = [0.0, 0.5]
                        AlignementToAnchor = [0.0, 0.5]
                        MagnifiableOffset = [ChallengeDoneColumnWidth * 0.5 - DlcLockWidth * 0.5 + FirstMarginWidth, 0.0] // on centre sur la colonne (le 2.5 étant la largeur de l'icone moins la firstMargin)
                    )

                    TextureFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1.0, 1.0] )
                    TextureToken = "UseOutGame_Cadenas"
                )
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------
private template Texte_Mission
[
    GameType : int,
] is BUCKListDescriptor
(
    ElementName = "ScenarioBriefInfos"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
    )
    Axis = ~/ListAxis/Horizontal

    FirstMargin = TRTTILength (Magnifiable = FirstMarginWidth)
    InterItemMargin = TRTTILength (Magnifiable = 5.0)
    LastMargin = TRTTILength (Magnifiable = 10.0)

    Elements =
    [
        // status
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "IsCompletedMultilist"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [ChallengeDoneColumnWidth, 0.0]
                    RelativeWidthHeight = [0.0, 1.0]
                )

                Components =
                [
                    BUCKTextureDescriptor
                    (
                        ElementName = "IsCompletedTexture"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            //RelativeWidthHeight = [0.8, 1.0]
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )
                        TextureColorToken = 'Noir'
                        ResizeMode = ~/TextureResizeMode/FitToContent

                        TextureFrame = TUIFramePropertyRTTI
                        (
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )
                    ),
                ]
            )
        ),

        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "OperationNameMultilist"
                ComponentFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1.0, 1.0] )

                Components =
                [
                    BUCKContainerDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        Components =
                        [
                            BUCKListDescriptor
                            (
                                ElementName = "MissionNameList"

                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [0.0, 1.0]
                                )
                                Axis = ~/ListAxis/Horizontal

                                Elements =
                                [
                                    BUCKListElementDescriptor
                                    (
                                        ExtendWeight = 1
                                        ComponentDescriptor = BUCKTextDescriptor
                                        (
                                            ElementName = "MissionName"
                                            ComponentFrame = TUIFramePropertyRTTI
                                            (
                                                RelativeWidthHeight = [1.0, 1.0]
                                            )

                                            ParagraphStyle = TParagraphStyle
                                            (
                                                Alignment = ~/UIText_Left
                                                VerticalAlignment = ~/UIText_VerticalCenter
                                            )

                                            TextStyle = 'Default'
                                            TypefaceToken = "Eurostyle"
                                            TextSize = '16'
                                            TextDico = ~/LocalisationConstantes/dico_interface_ingame
                                            TextColor = "noir_listeMission"

                                            // BigLineAction = ~/BigLineAction/ResizeFont

                                            Components =
                                            [
                                                BUCKTextureDescriptor
                                                (
                                                    ComponentFrame = TUIFramePropertyRTTI
                                                    (
                                                        RelativeWidthHeight = [1.1, 1.1]
                                                        AlignementToAnchor = [0.5, 0.5]
                                                        AlignementToFather = [0.5, 0.5]
                                                    )
                                                    TextureToken = 'EffetSurvol_listeMission'
                                                )
                                            ]
                                        )
                                    ),
                                    BUCKListElementDescriptor
                                    (
                                        // ExtendWeight = 0.20
                                        ComponentDescriptor = BUCKTextDescriptor
                                        (
                                            ElementName = "DlcName"
                                            ComponentFrame = TUIFramePropertyRTTI
                                            (
                                                RelativeWidthHeight = [0.0, 1.0]
                                            )

                                            ParagraphStyle = TParagraphStyle
                                            (
                                                Alignment = ~/UIText_Right
                                                VerticalAlignment = ~/UIText_VerticalCenter
                                            )
                                            HorizontalFitStyle = ~/FitStyle/FitToContent

                                            TextStyle = 'Default'
                                            TypefaceToken = "Eurostyle"
                                            TextSize = '8'
                                            TextDico = ~/LocalisationConstantes/dico_interface_outgame
                                            TextColor = "noir_listeMission"
                                            TextToken = "DIVSEL_DLC"

                                        )
                                    )
                                ]
                            )
                        ]
                    )
                ]
            )
        ) ] + ((<GameType> == ~/GameChallenge) ? [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "OperationCountryMultilist"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [ChallengeCountryColumnWidth, 0.0]
                )

                Components =
                [
                    BUCKContainerDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [30.0, 25.0]
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )

                        Components =
                        [
                            BUCKTextureDescriptor
                            (
                                ElementName = "OperationCountryTexture"
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 1.0]
                                )

                                TextureFrame   = TUIFramePropertyRTTI
                                (
                                    AlignementToAnchor = [0.5, 0.5]
                                    AlignementToFather = [0.5, 0.5]
                                )

                                ClipTextureToComponent = true
                                ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                            )
                        ]
                    )
                ]
            )
        ) ] : []) + [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "OperationDurationMultilist"
                ComponentFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [0.0, 1.0] )
                FitStyle = ~/ContainerFitStyle/FitToContentHorizontally

                Components =
                [
                    BUCKTextDescriptor
                    (
                        ElementName = "MissionDuration"

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [ChallengeDurationColumnWidth, 0.0]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = ~/UIText_Center
                            VerticalAlignment = ~/UIText_VerticalCenter
                        )

                        TextStyle = 'Default'
                        TypefaceToken = "Eurostyle"
                        TextSize = '12'
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextColor = "Noir"
                        TextToken = "CL_DIFF"
                    )
                ]
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------
private template ScenarioBriefsRack
[
    GameType : int,
] is BUCKRackDescriptor
(
    ElementName = "ScenarioBriefsRack"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical
    FirstMargin = TRTTILength(Magnifiable = FirstMarginWidth)
    InterItemMargin = TRTTILength(Magnifiable = 0.0)
    BladeDescriptor = ScenarioBriefDescriptor(GameType = <GameType>)
)

//-------------------------------------------------------------------------------------

private ScenarioDifficultyChoiceRadioManager is TBUCKRadioButtonManager()

private ScenarioDifficultyComponent is BUCKButtonDescriptor
(
    ElementName = "DifficultyButton"
    FitStyle = ~/ContainerFitStyle/FitToContent
    RadioButtonManager = ~/ScenarioDifficultyChoiceRadioManager
    IsTogglable = true
    CannotDeselect = true

    Components =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [150.0, 0.0]
            )

            Axis = ~/ListAxis/Vertical
            InterItemMargin = TRTTILength(Magnifiable = 5.0)
            ChildFitToContent = true

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "DifficultyName"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [0.0, 20.0]
                            RelativeWidthHeight = [1.0, 0.0]
                        )

                        ParagraphStyle = ~/paragraphStyleTextCenter
                        BigLineAction = ~/BigLineAction/ResizeFont

                        TextStyle = 'Default'
                        TypefaceToken = "Eurostyle"
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextColor = "Noir"
                        TextSize = "20"
                        TextPadding = TRTTILength4( Magnifiable = [20.0, 0.0, 20.0, 0.0] )

                        Components =
                        [
                            BUCKTextureDescriptor
                            (
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.1, 1.1]
                                    AlignementToAnchor = [0.5, 0.5]
                                    AlignementToFather = [0.5, 0.5]
                                )
                                TextureToken = 'EffetSurvol_listeMission'
                            )
                        ]
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextureDescriptor
                    (
                        ElementName = "DifficultyTexture"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            AlignementToFather = [0.5, 0.0]
                            AlignementToAnchor = [0.5, 0.0]
                        )
                        TextureFrame = TUIFramePropertyRTTI()
                        ResizeMode = ~/TextureResizeMode/FitToContent
                        TextureColorToken = 'Noir'
                    )
                ),
            ]
        ),
        BUCKSpecificHintableArea
        (
            ElementName = "DifficultyHint"
        )
    ]
)


//-------------------------------------------------------------------------------------

private SoloLaunchButtonHeight is 35.0

private template SoloQuitButton
[
    TextToken : string = "BTN_CLOSE",
    ElementName : string = "BackButton",

] is ConfirmButton
(
    ElementName = <ElementName>
    TextToken = <TextToken>
    Mapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_ESCAPE ) )
    ButtonMagnifiableWidthHeight = [175.0, 35.0]
    ButtonAlignementToAnchor = [0.5, 0.0]
    ButtonAlignementToFather = [0.5, 0.0]
    ButtonMagnifiableOffset = [0.0, 0.0]
    TextTypefaceToken = "Liberator"
    TextColorToken = "tampon_gris"
    BorderLineColorToken = "tampon_gris"
    HasBackground = false
    BackgroundBlockColorToken = "loginBoutonBackground_cyan"
    TextSizeToken = "32"
    BorderThicknessToken = "3"
    LeftClickSound = ~/SoundEvent_SoloNavigationButton
)

//-------------------------------------------------------------------------------------

private template ScenarioDescriptionContainer
[
    ElementName : string,
    ComponentFrame : TUIFramePropertyRTTI,
    GameType : int,
] is BUCKContainerDescriptor
(
    ElementName = <ElementName>
    ComponentFrame = <ComponentFrame>

    Components =
    [
        BUCKPerspectiveWarpOffscreenContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            DistortTextureDrawer = $/UserInterface/UIDistortTextureDrawer
            PointerEventsToAllow = ~/EAllowablePointerEventType/Move

            MagnifiableTopLeftOffset = [0.0, 0.0]
            MagnifiableTopRightOffset = [5.0, 5.0]
            MagnifiableBottomLeftOffset = [-9.0, 1.0]
            MagnifiableBottomRightOffset = [-4.0, 10.0]

            Components =
            [
                BUCKListDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 0.0]
                    )

                    Axis = ~/ListAxis/Vertical
                    FirstMargin = TRTTILength( Magnifiable = 40.0 )
                    LastMargin = TRTTILength( Magnifiable = 10.0 )

                    Elements =
                    [
                        // Description
                        BUCKListElementDescriptor
                        (
                            ExtendWeight = 1.0
                            ComponentDescriptor = BUCKContainerDescriptor
                            (
                                ElementName = "ScenarioDescriptionContainer"
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 1.0]
                                )

                                // envoie ResumeCampagneTypeHistorique ou ChallengeMoreDescriptionType pour les challenge
                                // envoie ResumeCampagneType3 pour campagne et tuto
                            )
                        ),
                        BUCKListElementSpacer( Magnifiable = 20.0),
                        // Difficulty choice
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKRackDescriptor
                            (
                                ElementName = "ScenarioDifficultyChoiceRack"

                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    AlignementToAnchor = [0.5, 0.0]
                                    AlignementToFather = [0.5, 0.0]
                                )

                                Axis = ~/ListAxis/Horizontal
                                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
                                InterItemMargin = TRTTILength( Magnifiable = 10.0 )
                                BladeDescriptor = ~/ScenarioDifficultyComponent
                            )
                        )
                    ] + (<GameType> == ~/GameChallenge ?
                    [
                        BUCKListElementSpacer( Magnifiable = 60.0)
                    ] :
                    [
                        BUCKListElementSpacer( Magnifiable = 10.0)
                    ]) +
                    [
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKContainerDescriptor
                            (
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 0.0]
                                    MagnifiableWidthHeight = [0.0, (<GameType> == ~/GameChallenge ? 26 : ~/SoloLaunchButtonHeight)]
                                    AlignementToAnchor = [0.5, 0.0]
                                    AlignementToFather = [0.5, 0.0]
                                )

                                Components = (<GameType> == ~/GameChallenge ?
                                [
                                    SoloLaunchButton
                                    (
                                        ElementName = "ShowMissionDetailsButton"
                                    ),
                                    SoloLaunchButton
                                    (
                                        TextToken = "cha_titre"
                                        TextDico = ~/LocalisationConstantes/dico_maps
                                    )
                                ] : (<GameType> == ~/GameStrategic ?
                                [
                                    ~/StrategicLaunchButtons
                                ] :
                                [
                                    SoloLaunchButton(),
                                ]))
                            )
                        ),
                    ]
                )
            ]
        ),
    ]
)

//-------------------------------------------------------------------------------------

StrategicLaunchButtons is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        AlignementToAnchor = [0.5, 1.0]
        AlignementToFather = [0.5, 1.0]
    )
    Axis = ~/ListAxis/Vertical
    InterItemMargin = TRTTILength(Magnifiable = 30.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SoloLaunchButton()
        ),
    ]
)

//-------------------------------------------------------------------------------------

template SoloNavigationButton
[
    ElementName : string = "",
    TextToken : string = "",
    ButtonMagnifiableWidthHeight : float2 = [175.0, 35.0],
    TextSizeToken : string = "32",
    HintTitleToken : string = "",
    HintBodyToken : string = "",
    HintExtendedToken : string = "",
    IsEmphasized : bool = false,
    Mapping : TEugBMutablePBaseClass = nil,
    HintableAreaElementName : string = "",
    TextDico : string = ~/LocalisationConstantes/dico_interface_outgame,
    ButtonAlignementToAnchor : float2 = [0.0,0.0],
    ButtonAlignementToFather : float2 = [0.0,0.0],
] is ConfirmButton
(
    ElementName = <ElementName>
    ButtonMagnifiableWidthHeight = <ButtonMagnifiableWidthHeight>
    ButtonAlignementToAnchor = <ButtonAlignementToAnchor>
    ButtonAlignementToFather = <ButtonAlignementToFather>

    BorderLineColorToken = <IsEmphasized> ? "tampon_vert" : "tampon_gris"
    BorderThicknessToken = "3"

    TextToken = <TextToken>
    HintableAreaComponent = BUCKSpecificHintableArea
    (
        ElementName = <HintableAreaElementName>
        HintTitleToken = <HintTitleToken>
        HintBodyToken = <HintBodyToken>
        HintExtendedToken = <HintExtendedToken>
        DicoToken = ~/LocalisationConstantes/dico_interface_outgame
    )
    TextTypefaceToken = "Liberator"
    TextColorToken = <IsEmphasized> ? "tampon_vert" : "tampon_gris"
    TextSizeToken = <TextSizeToken>
    TextPadding = TRTTILength4(Magnifiable = [4.0, 0.0, 4.0, 0.0])
    TextDico = <TextDico>

    BigLineAction = ~/BigLineAction/ResizeFont

    LeftClickSound = ~/SoundEvent_SoloNavigationButton

    Mapping = <Mapping>
)

//-------------------------------------------------------------------------------------

private template SoloLaunchButton
[
    ElementName : string = "SelectButton",
    TextToken : string = "DS_SELECT",
    TextDico : string = ~/LocalisationConstantes/dico_interface_outgame,
] is SoloNavigationButton
(
    ElementName = <ElementName>
    TextToken = <TextToken>
    TextDico = <TextDico>
    Mapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_ENTER ) )
    ButtonAlignementToAnchor = [0.5, 0.0]
    ButtonAlignementToFather = [0.5, 0.0]
    IsEmphasized = true
)

//-------------------------------------------------------------------------------------

private template ScenarioListContainer
[
    ListTitle : string,
    GameType : int,
    MagnifiableWidthHeight : float2 = [526.0, 715.0]

] is BUCKContainerDescriptor
(
    ElementName = "ScenarioListContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
       MagnifiableWidthHeight = <MagnifiableWidthHeight>
       MagnifiableOffset = [71.0, 31.0]
    )

    Components =
    [
        BUCKPerspectiveWarpOffscreenContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            DistortTextureDrawer = $/UserInterface/UIDistortTextureDrawer
            PointerEventsToAllow = ~/EAllowablePointerEventType/Move

            MagnifiableTopLeftOffset = [0.0,  0.0]
            MagnifiableTopRightOffset = [0.0, -5.0]
            MagnifiableBottomLeftOffset = [6.0, 0.0]
            MagnifiableBottomRightOffset = [7.0, -5.0]

            Components =
            [
                BUCKListDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 0.0]
                    )

                    Axis = ~/ListAxis/Vertical
                    LastMargin = TRTTILength( Magnifiable = 10.0 )

                    Elements =
                    [
                        // Description
                        BUCKListElementDescriptor
                        (
                            ExtendWeight = 1.0
                            ComponentDescriptor = BUCKContainerDescriptor
                            (
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 1.0]
                                )

                                Components = ((<GameType> == ~/GameChallenge) ?
                                [
                                    BUCKContainerDescriptor
                                    (
                                        ElementName = "ScenarioDetailsContainer"
                                        ComponentFrame = TUIFramePropertyRTTI
                                        (
                                            RelativeWidthHeight = [1.0, 1.0]
                                        )

                                        // Envoie ChallengeDescriptionType pour les challenge
                                    ),
                                ] : []) +
                                [
                                    ScenarioMultiList
                                    (
                                        ListTitle = <ListTitle>
                                        GameType = <GameType>
                                    ),
                                ]
                            )
                        ),
                        //Bouton back
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = SoloQuitButton()
                        ),
                    ]
                ),
            ]
        ),
    ]
)

//-------------------------------------------------------------------------------------

private template ScenarioMultiList
[
    ListTitle : string,
    GameType : int,
] is BUCKMultiListDescriptor
(
    ElementName = "ScenarioMultiList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    ColumnNames =
    [
        "IsCompletedMultilist",
        "OperationNameMultilist",
        "OperationDurationMultilist"
    ] + ((<GameType> == ~/GameChallenge) ? ["OperationCountryMultilist"] : [])

    SortableColumnNames = ((<GameType> == ~/GameTutorial) ? [] :
    [
        "IsCompletedMultilist",
        "OperationNameMultilist",
        "OperationDurationMultilist"
    ]) + ((<GameType> == ~/GameChallenge) ? ["OperationCountryMultilist"] : [])

    RackName = "ScenarioBriefsRack"

    TitleDescriptor = ScenarioTitleContainer(ListTitle = <ListTitle> GameType = <GameType>)
    ContentDescriptor = Scenario_List_ScrollingContainer(GameType = <GameType>)
)

//-------------------------------------------------------------------------------------

private template Scenario_List_ScrollingContainer
[
    GameType : int,
] is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    Components =
    [
        BUCKSpecificScrollingContainerDescriptor
        (
            ElementName = "ScenarioBriefsScrollingContainer"

            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            VerticalScrollbarComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [7.0, 0.0]
                MagnifiableOffset = [-6.0, 0.0]
                AlignementToFather = [1.0, 0.0]
                AlignementToAnchor = [1.0, 0.0]
            )

            ExternalScrollbar = true
            HasVerticalScrollbar = true

            Components =
            [
                ScenarioBriefsRack(GameType = <GameType>)
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------

template ChallengeTitleTextComponent
[
    TextToken : string,
    HorizontalAlignment : int = UIText_Center,
    HintBodyToken: string = ''
]
is BUCKTextDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    ParagraphStyle = TParagraphStyle
    (
        Alignment = <HorizontalAlignment>
        VerticalAlignment = UIText_VerticalCenter
    )

    BigLineAction = ~/BigLineAction/ResizeFont

    TextStyle = 'Default'
    TypefaceToken = "Eurostyle"
    TextToken = <TextToken>
    TextDico = ~/LocalisationConstantes/dico_interface_outgame
    TextColor = "Noir"
    TextSize = "14"
    TextPadding = TRTTILength4( Magnifiable = [2.0, 0.0, 2.0, 0.0] )

    Hint = BUCKSpecificHintableArea
    (
        DicoToken = ~/LocalisationConstantes/dico_interface_outgame
        HintTitleToken = <TextToken>
        HintBodyToken = <HintBodyToken>
    )
)

//-------------------------------------------------------------------------------------

template ScenarioTitleContainer
[
    ListTitle : string,
    GameType : int,
] is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    InterItemMargin = TRTTILength( Magnifiable = 6.0 )
    LastMargin = TRTTILength( Magnifiable = 0.0 )
    Axis = ~/ListAxis/Vertical

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight = [0.0, 70.0]
                )
                ParagraphStyle = paragraphStyleTextCenter
                TextStyle = "Default"
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/UserDefined
                TypefaceToken = "Eurostyle"
                BigLineAction = ~/BigLineAction/MultiLine
                TextToken = <ListTitle>
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TextColor = "Noir"
                TextSize = "20"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = titleColumnMissionList(GameType = <GameType>)
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.95, 0.0]
                    MagnifiableWidthHeight = [0.0, 2.0]
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                HasBorder = true
                BorderThicknessToken = '1'
                BorderLineColorToken = 'Noir'
                BordersToDraw = ~/TBorderSide/Bottom
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

ChallengeDoneColumnWidth is 60
ChallengeCountryColumnWidth is 70
ChallengeDurationColumnWidth is 70

template titleColumnMissionList
[
    GameType : int,
] is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 25.0]
    )

    Axis = ~/ListAxis/Horizontal

    FirstMargin = TRTTILength (Magnifiable = FirstMarginWidth)
    InterItemMargin = TRTTILength (Magnifiable = 5.0)
    LastMargin = TRTTILength (Magnifiable = 10.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = "IsCompletedMultilist"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [ChallengeDoneColumnWidth, 0.0]
                )

                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false
                HidePointerEvents = true
                Components =
                [
                    ChallengeTitleTextComponent
                    (
                        TextToken = 'CL_GAMSTS'
                        HintBodyToken = 'CL_statb'
                    ),
                ]
            )
        ),
        BUCKListElementDescriptor
        (
            ExtendWeight = 1
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = "OperationNameMultilist"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )

                SortingType = ~/MultiListSorting/String
                ShowCheckbox = false
                HidePointerEvents = true
                Components =
                [
                    ChallengeTitleTextComponent
                    (
                        TextToken = 'OP_TITLE'
                        HintBodyToken = 'CL_nameb'
                    )
                ]
            )
        ) ] + ((<GameType> == ~/GameChallenge) ? [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = "OperationCountryMultilist"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [ChallengeCountryColumnWidth, 0.0]
                )

                SortingType = ~/MultiListSorting/String
                ShowCheckbox = false
                HidePointerEvents = true
                Components =
                [
                    ChallengeTitleTextComponent
                    (
                        TextToken = "OLB_CNTRY"
                        HintBodyToken = 'CL_counb'
                    )
                ]
            )
        ) ] : []) + [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = "OperationDurationMultilist"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [ChallengeDurationColumnWidth, 0.0]
                )

                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false
                HidePointerEvents = true
                Components =
                [
                    ChallengeTitleTextComponent
                    (
                        TextToken = 'LS_Dur'
                        HintBodyToken = 'CL_durab'
                    )
                ]
            )
        )
    ]
)

//-------------------------------------------------------------------------------------

private template BUCKSpecificScenarioMainComponentDescriptor
[
    GameType : int,
    IsSubView : bool
] is WindowFrame
(
    HasBackground = false
    TitleToken = (<GameType> == ~/GameStrategic) ? "new_AG" : "new_HB"
    HasTitle =  (<IsSubView> ? false : true)
    ContentExtendWeight = (<IsSubView> ? 0.2 : 1.0)
    ContentRelativeWidthHeight = [1.0, 1.0]
    MargeHorizontale = 0
    ButtonList = []

    ContentComponents =
    [
        BUCKTranslationAnimatedContainerDescriptor
        (
            FramePropertyBeforeAnimation = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                AlignementToAnchor = [0.5, 0.0]
                AlignementToFather = [0.5, 0.8]
            )

            TriggerWhenSetVisible = true
            AnimationTotalDuration = 0.3
            AnimationModificator = ~/AnimModificator/SquareDecelerate

            FramePropertyAfterAnimation = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                AlignementToAnchor = [0.5, 1.0]
                AlignementToFather = [0.5, 1.0]
            )

            // Note : Game type is never tutorial, which uses UISpecificTutorialViewDescriptor
            Components = ((<GameType> == ~/GameChallenge) ?
            [
                ~/FullPanelPreviewMission,
            ] :
            [
                ~/StrategicFullPanel
            ])
        )
    ]
)
//-------------------------------------------------------------------------------------
StrategicFullPanel is BUCKTextureDescriptor
(
    ElementName = "StrategicFullPanel"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [1214.0, 806.0]
        AlignementToFather = [0.5, 1.0]
        AlignementToAnchor = [0.5, 1.0]
        MagnifiableOffset = [0,25]
    )

    TextureToken = ~/StrategicMissionListBackground
    HidePointerEvents = true

    Components =
    [
        // page de gauche
        ScenarioListContainer
        (
            MagnifiableWidthHeight = [526.0, 715.0+15]

            ListTitle = "str_list"
            GameType = ~/GameStrategic
        ),
        // page de droite
        ScenarioDescriptionContainer
        (
            ElementName = "ScenarioDescription"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [520.0, 715.0+40]
                MagnifiableOffset = [285.0, 0.0]
                AlignementToFather = [0.5, 0.0]
                AlignementToAnchor = [0.5, 0.0]
            )
            GameType = ~/GameStrategic
        ),
    ]
)

//-------------------------------------------------------------------------------------

FullPanelPreviewMission is BUCKTextureDescriptor
(
    ElementName = "FullPanelPreviewMission"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [1214.0, 806.0]
        AlignementToFather = [0.5, 1.0]
        AlignementToAnchor = [0.5, 1.0]
        MagnifiableOffset = [0,25]

    )

    TextureToken = ~/ChallengeMissionListBackground
    HidePointerEvents = true

    Components =
    [
        // page de gauche
        ScenarioListContainer
        (
            ListTitle = "op_list"
            GameType = ~/GameChallenge
        ),
        // page de droite
        ScenarioDescriptionContainer
        (
            ElementName = "ScenarioMap"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [520.0, 715.0]
                MagnifiableOffset = [285.0, 15.0]
                AlignementToFather = [0.5, 0.0]
                AlignementToAnchor = [0.5, 0.0]
            )
            GameType = ~/GameChallenge
        ),
    ]
)

//-------------------------------------------------------------------------------------

template UISpecificScenarioViewDescriptor
[
    GameType : int,
    ComponentContainerUniqueName : string = "",
    IsSubView : bool = false,
] is TUISpecificScenarioSelectionViewDescriptor
(
    MainComponentDescriptor = BUCKSpecificScenarioMainComponentDescriptor(GameType = <GameType> IsSubView = <IsSubView>)
    MainComponentContainerUniqueName = <ComponentContainerUniqueName> // Permet d'indiquer l'endroit ou le composant doit s'insérer

    ChallengeDescriptionType = "SummaryCampaignShowroom" // Left page
    ChallengeMoreDescriptionType = "ScenarioMapDescription" // Right page

    CloseButtonText = "BTN_CLOSE"
    BackButtonText = "BTN_BACK"
)

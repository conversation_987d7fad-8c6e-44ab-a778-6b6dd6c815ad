template CreateAccountLine
[
    ElementName : string = "",
    TitleToken : string,

    ComponentDescriptor = TBUCKContainerDescriptor,
]
is BUCKListDescriptor
(
    ElementName = <ElementName>
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 0.0]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Horizontal
    InterItemMargin = TRTTILength(Magnifiable = 6.0)
    LastMargin = TRTTILength(Magnifiable = 20.0)

    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [100.0, 0.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor = [0.0, 0.5]
                )

                ParagraphStyle = TParagraphStyle
                (
                    VerticalAlignment = ~/UIText_VerticalCenter
                    Alignment = UIText_Right
                )
                BigLineAction = ~/BigLineAction/BalancedMultiline
                HorizontalFitStyle = ~/FitStyle/MinBetweenUserDefinedAndContent
                VerticalFitStyle = ~/FitStyle/FitToContent

                TextStyle = "TextStyleEcranMoniteur"
                TypefaceToken = "UISecondFont"

                TextColor       = "Blanc"
                TextSize        = "14"

                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TextToken = <TitleToken>
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [230.0, 0.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor = [0.0, 0.5]
                )

                FitStyle = ~/ContainerFitStyle/FitToContentVertically

                Components =
                [
                    <ComponentDescriptor>
                ]
            )
        )
    ]
)

//-------------------------------------------------------------------------------------

template BUCKSpecificOutGameCreateAccountMainComponentDescriptor[
    IsAutologinAccount : bool
] is LoginTvContainer
(
    ElementName = "CreateAccountComponent"

    Components =
    [
        BUCKListDescriptor
        (
            ElementName = "CreateAccountContentList"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [450.0, 0.0]
            )

            Axis = ~/ListAxis/Vertical

            FirstMargin = TRTTILength( Magnifiable = 10.0)
            LastMargin = TRTTILength( Magnifiable = 10.0)

            BackgroundComponents =
            [
                ~/LoginPanelMonitorBackground
            ]

            ForegroundComponents =
            [
                ~/LoginPanelMonitorForeground
            ]

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTypingTextDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 0.0]
                            MagnifiableWidthHeight = [0.0, 15.0]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            VerticalAlignment = ~/UIText_VerticalCenter
                            Alignment = UIText_Center
                        )

                        HorizontalFitStyle = ~/FitStyle/UserDefined
                        VerticalFitStyle = ~/FitStyle/UserDefined

                        TextStyle = "TextStyleEcranMoniteur"
                        TypefaceToken = "UISecondFont"

                        TextColor       = "Blanc"
                        TextSize        = "16"

                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextToken = "T_CrAcc"

                        AnimDuration = 10000000
                        AnimTextInSec = 0.1
                    )
                ),
                BUCKListElementSpacer( Magnifiable = 20.0),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKListDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 0.0]
                        )

                        Axis = ~/ListAxis/Vertical

                        InterItemMargin = TRTTILength(Magnifiable = 5.0)

                        Elements =
                        [
                        ] + (<IsAutologinAccount> ? [ ] :
                        [
                            BUCKListElementDescriptor
                            (
                                ComponentDescriptor = EcranGaucheTextEditable
                                (
                                    ElementName = "Login"
                                    TokenTitle = "login"
                                )
                            ),
                        ]) +
                        [
                            BUCKListElementDescriptor
                            (
                                ComponentDescriptor = EcranGaucheTextEditable
                                (
                                    ElementName = "Email"
                                    TokenTitle = "email"
                                )
                            ),
                            BUCKListElementDescriptor
                            (
                                ComponentDescriptor = EcranGaucheTextEditable
                                (
                                    ElementName = "ConfirmationEmail"
                                    TokenTitle = "confemail"
                                )
                            ),
                            //-------------------------------------------------------------------------------------
                            ligneDateOfBirth,
                            //-------------------------------------------------------------------------------------
                        ] + (<IsAutologinAccount> ? [ ] :
                        [
                            BUCKListElementDescriptor
                            (
                                ComponentDescriptor = EcranGaucheTextEditable
                                (
                                    ElementName = "Password"
                                    TokenTitle = "password"
                                    IsPassword = true
                                )
                            ),
                            BUCKListElementDescriptor
                            (
                                ComponentDescriptor = EcranGaucheTextEditable
                                (
                                    ElementName = "ConfirmationPassword"
                                    TokenTitle = "confpass"
                                    IsPassword = true
                                )
                            ),
                            BUCKListElementDescriptor
                            (
                                ComponentDescriptor = EcranGaucheTextEditable
                                (
                                    ElementName = "Pseudo"
                                    TokenTitle = "pseudo"
                                )
                            ),
                            BUCKListElementDescriptor
                            (
                                ComponentDescriptor = EcranGaucheTextEditable
                                (
                                    ElementName = "CDKey"
                                    TokenTitle = "cdkey"
                                )
                            ),
                        ])
                    )
                ),
                BUCKListElementSpacer( Magnifiable = 10.0),

                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKContainerDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.8, 0.0]
                            AlignementToFather = [0.5, 0.0]
                            AlignementToAnchor = [0.5, 0.0]
                        )

                        FitStyle = ~/ContainerFitStyle/FitToContentVertically

                        Components =
                        [
                            BUCKListDescriptor
                            (
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [0.0, 0.0]
                                )

                                Axis = ~/ListAxis/Horizontal
                                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                                InterItemMargin = TRTTILength(Magnifiable = 10.0)

                                Elements =
                                [
                                    BUCKListElementDescriptor
                                    (
                                        ComponentDescriptor = BUCKSpecificCheckBoxDescriptor
                                        (
                                            ElementName = "ToSCheckbox"
                                            ComponentFrame = TUIFramePropertyRTTI
                                            (
                                                MagnifiableWidthHeight = [15.0, 15.0]
                                                AlignementToFather = [0.0, 0.5]
                                                AlignementToAnchor = [0.0, 0.5]
                                            )
                                        )
                                    ),
                                    BUCKListElementDescriptor
                                    (
                                        ExtendWeight = 1.0
                                        ComponentDescriptor = BUCKTextDescriptor
                                        (
                                            ComponentFrame = TUIFramePropertyRTTI
                                            (
                                                RelativeWidthHeight = [1.0, 0.0]
                                                MagnifiableWidthHeight = [0.0, 35.0]
                                                AlignementToFather = [0.0, 0.5]
                                                AlignementToAnchor = [0.0, 0.5]
                                            )

                                            ParagraphStyle = TParagraphStyle
                                            (
                                                VerticalAlignment = ~/UIText_VerticalCenter
                                                Alignment = UIText_Left
                                            )

                                            BigLineAction = ~/BigLineAction/MultiLine

                                            TextStyle = "TextStyleEcranMoniteur"
                                            TypefaceToken = "UISecondFont"

                                            TextColor       = "Blanc"
                                            TextSize        = "10"

                                            TextDico = ~/LocalisationConstantes/dico_interface_outgame
                                            TextToken = "TosApprov"
                                        )
                                    )
                                ]
                            )
                        ]
                    )
                ),
                BUCKListElementSpacer( Magnifiable = 10.0),

                //-------------------------------------------------------------------------------------

                BUCKListElementDescriptor
                (
                    ComponentDescriptor = affichageTermofUseEtPrivacy
                ),
                BUCKListElementSpacer( Magnifiable = 10.0),

                //-------------------------------------------------------------------------------------

                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKContainerDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.8, 0.0]
                            AlignementToFather = [0.5, 0.0]
                            AlignementToAnchor = [0.5, 0.0]
                        )

                        FitStyle = ~/ContainerFitStyle/FitToContentVertically

                        Components =
                        [
                            BUCKListDescriptor
                            (
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [0.0, 0.0]
                                )

                                Axis = ~/ListAxis/Horizontal
                                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                                InterItemMargin = TRTTILength(Magnifiable = 10.0)

                                Elements =
                                [
                                    BUCKListElementDescriptor
                                    (
                                        ComponentDescriptor = BUCKSpecificCheckBoxDescriptor
                                        (
                                            ElementName = "NewsletterCheckbox"
                                            ComponentFrame = TUIFramePropertyRTTI
                                            (
                                                MagnifiableWidthHeight = [15.0, 15.0]
                                                AlignementToFather = [0.0, 0.5]
                                                AlignementToAnchor = [0.0, 0.5]
                                            )
                                        )
                                    ),
                                    BUCKListElementDescriptor
                                    (
                                        ExtendWeight = 1.0
                                        ComponentDescriptor = BUCKTextDescriptor
                                        (
                                            ComponentFrame = TUIFramePropertyRTTI
                                            (
                                                RelativeWidthHeight = [1.0, 0.0]
                                                AlignementToFather = [0.0, 0.5]
                                                AlignementToAnchor = [0.0, 0.5]
                                            )

                                            ParagraphStyle = TParagraphStyle
                                            (
                                                VerticalAlignment = ~/UIText_VerticalCenter
                                                Alignment = UIText_Left
                                            )

                                            VerticalFitStyle = ~/FitStyle/FitToContent

                                            BigLineAction = ~/BigLineAction/MultiLine

                                            TextStyle = "TextStyleEcranMoniteur"
                                            TypefaceToken = "UISecondFont"

                                            TextColor       = "Blanc"
                                            TextSize        = "10"

                                            TextDico = ~/LocalisationConstantes/dico_interface_outgame
                                            TextToken = "CreAcc_Nw"
                                        )
                                    )
                                ]
                            )
                        ]
                    )
                ),
                BUCKListElementSpacer( Magnifiable = 10.0),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "ErrorText"

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 0.0]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            VerticalAlignment = ~/UIText_VerticalCenter
                            Alignment = UIText_Center
                        )

                        VerticalFitStyle = ~/FitStyle/FitToContent
                        BigLineAction = ~/BigLineAction/MultiLine

                        TextStyle       = "Default"
                        TypefaceToken   = "UISecondFont"

                        TextColor       = "Rouge"
                        TextSize        = "14"
                    )
                ),
            ]
        ),
        BUCKListDescriptor
        (
            ElementName = "CreateAccountButtonList"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [0.0, 25.0]
                MagnifiableOffset = [0.0, 0.0]
                AlignementToFather  = [0.5, 1.0]
                AlignementToAnchor  = [0.5, 1.0]
            )

            Axis = ~/ListAxis/Horizontal
            InterItemMargin = TRTTILength(Magnifiable = 5.0)

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = LoginScreenMainButton
                    (
                        ElementName = "CreateButton"
                        TextToken = "BTN_CREATE"

                        Mapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_ENTER ) )
                        BackgroundBlockColorToken = "loginBoutonBackground_cyan"
                    )
                ),
            ] + (<IsAutologinAccount> ? [] :
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = LoginScreenMainButton
                    (
                        ElementName = "CancelButton"
                        TextToken = "BTN_CANCEL"

                        Mapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_ESCAPE ) )
                    )
                ),
            ])
        ),
    ]
)

//-------------------------------------------------------------------------------------

private ligneDateOfBirth is BUCKListElementDescriptor
(
    ComponentDescriptor = CreateAccountLine
    (
        TitleToken = "Birthdate"
        ComponentDescriptor = BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [0.0, 18.0]
            )

            Axis = ~/ListAxis/Horizontal
            InterItemMargin = TRTTILength(Magnifiable = 5.0)

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ExtendWeight = 1.0
                    ComponentDescriptor = BUCKSpecificDropdownDescriptor
                    (
                        ElementName = "Day"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                            AlignementToFather = [0.0, 0.5]
                            AlignementToAnchor = [0.0, 0.5]
                        )
                        ShowArrow = false
                        IsForModale = true
                        FloatingListMagnifiableWidth = 100.0

                        HasBackground = true
                        BackgroundBlockColorToken = "VertLogin"
                        MainTextColorToken = "Noir"
                        MainTextSizeToken = "14"
                        ItemsTextSizeToken = "14"
                        MainTextTypefaceToken = "UISecondFont"
                        ItemsTextColorToken = "ButtonHUD/Dropdown"

                        HasBorder = false
                    )
                ),

                BUCKListElementDescriptor
                (
                    ExtendWeight = 2.0
                    ComponentDescriptor = BUCKSpecificDropdownDescriptor
                    (
                        ElementName = "Month"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                            AlignementToFather = [0.0, 0.5]
                            AlignementToAnchor = [0.0, 0.5]
                        )
                        ShowArrow = false
                        IsForModale = true
                        FloatingListMagnifiableWidth = 180.0

                        HasBackground = true
                        BackgroundBlockColorToken = "VertLogin"
                        MainTextColorToken = "Noir"
                        MainTextSizeToken = "14"
                        ItemsTextSizeToken = "14"
                        MainTextTypefaceToken = "UISecondFont"
                        ItemsTextColorToken = "ButtonHUD/Dropdown"

                        HasBorder = false
                    )
                ),

                BUCKListElementDescriptor
                (
                    ExtendWeight = 1.0
                    ComponentDescriptor = BUCKSpecificDropdownDescriptor
                    (
                        ElementName = "Year"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                            AlignementToFather = [0.0, 0.5]
                            AlignementToAnchor = [0.0, 0.5]
                        )
                        ShowArrow = false
                        IsForModale = true
                        FloatingListMagnifiableWidth = 110.0

                        HasBackground = true
                        BackgroundBlockColorToken = "VertLogin"
                        MainTextColorToken = "Noir"
                        MainTextSizeToken = "14"
                        ItemsTextSizeToken = "14"
                        MainTextTypefaceToken = "UISecondFont"
                        ItemsTextColorToken = "ButtonHUD/Dropdown"

                        HasBorder = false
                    )
                ),
            ]
        )
    )
)

//-------------------------------------------------------------------------------------

private affichageTermofUseEtPrivacy is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 10.0]
    )

    Axis = ~/ListAxis/Horizontal
    FirstMargin = TRTTILength (Magnifiable = 134.0)
    InterItemMargin = TRTTILength (Magnifiable = 34.0)

    Elements =
    [
       BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKButtonDescriptor
            (
                ElementName = "TosUseLink"
                ComponentFrame = TUIFramePropertyRTTI()

                FitStyle = ~/ContainerFitStyle/FitToContent

                Components =
                [
                    BUCKTextDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            AlignementToFather = [0.0, 0.5]
                            AlignementToAnchor = [0.0, 0.5]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            VerticalAlignment = ~/UIText_VerticalCenter
                            Alignment = UIText_Left
                        )

                        HasUnderline = true
                        UnderlineColor = "ConfirmButton/Border"
                        UnderlineThickness = "1"

                        VerticalFitStyle = ~/FitStyle/FitToContent
                        HorizontalFitStyle = ~/FitStyle/FitToContent

                        TextStyle = "TextStyleEcranMoniteur"
                        TypefaceToken = "UISecondFont"

                        TextColor       = "Blanc"
                        TextSize        = "10"

                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextToken = "TosUse"
                    )
                ]
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKButtonDescriptor
            (
                ElementName = "PrivacyPolicyLink"
                ComponentFrame = TUIFramePropertyRTTI()

                FitStyle = ~/ContainerFitStyle/FitToContent

                Components =
                [
                    BUCKTextDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            AlignementToFather = [0.0, 0.5]
                            AlignementToAnchor = [0.0, 0.5]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            VerticalAlignment = ~/UIText_VerticalCenter
                            Alignment = UIText_Left
                        )

                        HasUnderline = true
                        UnderlineColor = "ConfirmButton/Border"
                        UnderlineThickness = "1"

                        VerticalFitStyle = ~/FitStyle/FitToContent
                        HorizontalFitStyle = ~/FitStyle/FitToContent

                        TextStyle = "TextStyleEcranMoniteur"
                        TypefaceToken = "UISecondFont"

                        TextColor       = "Blanc"
                        TextSize        = "10"

                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextToken = "TosPrivPol"
                    )
                ]
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

template UISpecificOutGameCreateAccountViewDescriptor[
    IsAutologinAccount : bool
] is TUISpecificOutGameCreateAccountViewDescriptor
(
    MainComponentDescriptor = BUCKSpecificOutGameCreateAccountMainComponentDescriptor(IsAutologinAccount = <IsAutologinAccount>)
    MainComponentContainerUniqueName = "LoginPanel" // Permet d'indiquer l'endroit ou le composant doit s'insérer
)

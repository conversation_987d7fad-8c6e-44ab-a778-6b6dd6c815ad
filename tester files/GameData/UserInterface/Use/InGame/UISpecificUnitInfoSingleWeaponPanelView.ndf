template DamageComponentConfiguration
[
    Name : string,
    AmmoText : string,
    CalibreText : string,
] is TDamageComponentConfiguration
(
    MainComponentName = <Name>
    AmmoText = <AmmoText>
    CalibreText = <CalibreText>
)

// -------------------------------------------------------------------------------------------------

DamageTypeComponentConfigurations is MAP
[
    (-1,
        DamageComponentConfiguration
        (
            Name = "StandardAmmo"
            AmmoText = "WeaponStandardAmmoText"
            CalibreText = "WeaponStandardAmmoCalibreText"
        )
    ),
    (DamageFamily_flamme,
        DamageComponentConfiguration
        (
            Name = "Flame"
            AmmoText = "WeaponFlameAmmoText"
            CalibreText = "WeaponFlameCalibreText"
        )
    ),
    (DamageFamily_smoke,
        DamageComponentConfiguration
        (
            Name = "Smoke"
            AmmoText = "WeaponSmokeAmmoText"
            CalibreText = "WeaponSmokeCalibreText"
        )
    ),
]

// -------------------------------------------------------------------------------------------------

template WeaponDamageTypeTitle
[
    ElementName : string,
    Token : string,
    Scale : float,
] is BUCKListDescriptor
(
    ElementName = <ElementName>
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )
    Axis = ~/ListAxis/Vertical

    MagnifierMultiplication = <Scale>

    HasBorder = true
    BorderThicknessToken = "2"
    BorderLineColorToken = "MarronPanel_noir"
    BordersToDraw = ~/TBorderSide/Top | ~/TBorderSide/Left | ~/TBorderSide/Right

    FirstMargin = TRTTILength(Magnifiable = 2.0)
    LastMargin = TRTTILength(Magnifiable = 2.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = <ElementName> + 'Text'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight = [0.0, 15.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Left
                    VerticalAlignment = UIText_VerticalCenter
                    InterLine = 0
                )

                TextStyle = "Default"

                VerticalFitStyle = ~/FitStyle/UserDefined
                HorizontalFitStyle = ~/FitStyle/MinBetweenUserDefinedAndContent

                TypefaceToken = "Liberator"
                BigLineAction = ~/BigLineAction/ResizeFont

                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                TextToken = <Token>

                TextColor = "MarronPanel_noir"
                TextSize = "12"

                TextPadding = TRTTILength4( Magnifiable = [4.0, 0.0, 0.0, 0.0] )
            )
        )
    ]
)

// -------------------------------------------------------------------------------------------------

template WeaponDamageTypeAttribute
[
    ElementName : string,
    Token : string,
    ValueToken : string,
    HintToken : string = "",
    MagnifiableWidth : float = SingleWeaponColumnWidth,
    MagnifiableOffset : float2 =  [0.0, -1.0],
    HasBackground : bool = false,
    BackgroundBlockColorToken : string = 'MarronPanel_fonce',
    TextSize : string = '14',
    HasBorder : bool = false,
    BordersToDraw : int = ~/TBorderSide/Right,
    FirstMargin : float = 8.0,
    BackgroundComponents : bool = false,
    LastMargin : float = 3.0,
    Padding : float = 0.0,
    Magnifiable_barre : float = 91,
    FullGaugeIfNoMax : bool = false,
    BackgroundBlockColorTokenFondJauge : string ='MarronPanel_fonce',
    Scale : float = 1.0,
    IsSousGroupe : bool = false,
    HasSousGroupeText : boolean = false,
    TokenSousGroupe : string = 'PALYIYEIFQ',
]
is BUCKListDescriptor
(
    ElementName = <ElementName>
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [ <MagnifiableWidth>, 0.0]
    )
    HasBorder = <HasBorder>
    BorderThicknessToken = "2"
    BorderLineColorToken = "MarronPanel_noir"
    BordersToDraw = <BordersToDraw>
    Axis = ~/ListAxis/Vertical
    FirstMargin = TRTTILength (Magnifiable = <FirstMargin>)
    InterItemMargin = TRTTILength (Magnifiable = 0.0)
    LastMargin = TRTTILength (Magnifiable = <LastMargin>)
    HasBackground = <HasBackground>
    BackgroundBlockColorToken = <BackgroundBlockColorToken>
    MagnifierMultiplication = <Scale>

    Elements = (<IsSousGroupe> ? [] :
    [// nom de l'attribut
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [0.0, 15.0]
                    MagnifiableOffset = [5.0, 0.0]
                )

                Axis = ~/ListAxis/Horizontal
                InterItemMargin = TRTTILength(Magnifiable = 4.0)

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ElementName = <ElementName> + 'Title'

                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                MagnifiableWidthHeight = [100.0, 15.0]
                                AlignementToAnchor = [0.0, 0.5]
                                AlignementToFather = [0.0, 0.5]
                            )

                            ParagraphStyle = TParagraphStyle
                            (
                                Alignment = UIText_Left
                                VerticalAlignment = UIText_VerticalCenter
                                InterLine = 0
                            )

                            TextStyle = "Default"

                            VerticalFitStyle = ~/FitStyle/UserDefined
                            HorizontalFitStyle = ~/FitStyle/MinBetweenUserDefinedAndContent

                            TypefaceToken = "Liberator"
                            BigLineAction = ~/BigLineAction/ResizeFont

                            TextDico = ~/LocalisationConstantes/dico_interface_ingame
                            TextToken = <Token>

                            TextColor = "MarronPanel_noir"
                            TextSize = "12"
                        )
                    ),
                ]
            )
        ),
    ])
    + [
        // barre et valeur
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight = [0.0, 12.0]
                )
                Components =
                [
                    BUCKListDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                        )

                        Axis = ~/ListAxis/Horizontal

                        FirstMargin = TRTTILength( Magnifiable = 5.0 )
                        InterItemMargin = TRTTILength( Magnifiable = 1.0 )
                        LastMargin = TRTTILength( Magnifiable = 5.0 )

                        Elements =
                        [
                            // barre
                            BUCKListElementDescriptor
                            (
                                ComponentDescriptor =  BUCKGaugeDescriptor
                                (
                                    ElementName = <ElementName> + "Gauge"

                                    ComponentFrame = TUIFramePropertyRTTI
                                    (
                                        MagnifiableWidthHeight = [<Magnifiable_barre>, 12.0]
                                        AlignementToFather     = [0.0, 0.5]
                                        AlignementToAnchor     = [0.0, 0.5]
                                    )

                                    GridAlign = true
                                    HasBackground = true
                                    BackgroundBlockColorToken = <BackgroundBlockColorTokenFondJauge>
                                    HasBorder = false
                                    BorderThicknessToken = "1"
                                    BorderLineColorToken = "CyanChrono"

                                    DurationForGaugeFullChange = 1
                                    FullGaugeIfNoMax = <FullGaugeIfNoMax>

                                    GaugeComponentNames =
                                    [
                                        <ElementName> + 'Value',
                                        <ElementName> + 'VeteranceValue',
                                        <ElementName> + 'UntrainedValue',
                                    ]

                                    Components =
                                    [
                                        BUCKGaugeValueDescriptor
                                        (
                                            ElementName = <ElementName> + 'VeteranceValue'
                                            ComponentFrame = TUIFramePropertyRTTI
                                            (
                                                RelativeWidthHeight    = [0.0, 1.0]
                                                AlignementToFather     = [0.0, 0.5]
                                                AlignementToAnchor     = [0.0, 0.5]
                                            )

                                            HasBackground = true
                                            BackgroundBlockColorToken = "veteranceValueBonus"
                                        ),

                                        BUCKGaugeValueDescriptor
                                        (
                                            ElementName = <ElementName> + 'UntrainedValue'
                                            ComponentFrame = TUIFramePropertyRTTI
                                            (
                                                RelativeWidthHeight    = [0.0, 1.0]
                                                AlignementToFather     = [0.0, 0.5]
                                                AlignementToAnchor     = [0.0, 0.5]
                                            )

                                            HasBackground = true
                                            BackgroundBlockColorToken = "VividRed"
                                        ),

                                        BUCKGaugeValueDescriptor
                                        (
                                            ElementName = <ElementName> + 'Value'
                                            ComponentFrame = TUIFramePropertyRTTI
                                            (
                                                RelativeWidthHeight    = [0.0, 1.0]
                                                AlignementToFather     = [0.0, 0.5]
                                                AlignementToAnchor     = [0.0, 0.5]
                                            )

                                            HasBackground = true
                                            BackgroundBlockColorToken = "MarronPanel_BarreTresFonce"
                                            HasBorder = true
                                            BorderLineColorToken = "MarronPanel_noir"
                                            BorderThicknessToken = "3"
                                            BordersToDraw = ~/TBorderSide/Right
                                        ),
                                    ]
                                +
                                (<HasSousGroupeText> ?
                                    [
                                        BUCKTextDescriptor
                                        (
                                            ComponentFrame = TUIFramePropertyRTTI
                                            (
                                                RelativeWidthHeight = [1.0, 1.0]
                                            )

                                            ParagraphStyle = paragraphStyleTextLeftAlign
                                            TextStyle = "Default"
                                            HorizontalFitStyle = ~/FitStyle/UserDefined
                                            VerticalFitStyle = ~/FitStyle/UserDefined
                                            TypefaceToken = "Eurostyle"
                                            BigLineAction = ~/BigLineAction/CutByDots
                                            TextPadding = TRTTILength4 ( Magnifiable = [3,0,0,0])
                                            TextToken = <TokenSousGroupe>
                                            TextDico = ~/LocalisationConstantes/dico_interface_ingame
                                            TextColor = "MarronPanel_base"
                                            TextSize = "8"
                                        )
                                    ] : [])
                                )
                            ),
                            BUCKListElementDescriptor
                            (
                                ExtendWeight = 1.0
                                ComponentDescriptor = BUCKContainerDescriptor(ComponentFrame = TUIFramePropertyRTTI())
                            ),
                            // valeur
                            BUCKListElementDescriptor
                            (
                                ComponentDescriptor = BUCKTextDescriptor
                                (
                                    ElementName = <ElementName> + 'Text'
                                    ComponentFrame = TUIFramePropertyRTTI
                                    (
                                        MagnifiableOffset = <MagnifiableOffset>
                                    )

                                    ParagraphStyle = TParagraphStyle
                                    (
                                        Alignment = UIText_Left
                                        VerticalAlignment = UIText_Bottom
                                        InterLine = 0
                                    )

                                    TextStyle = "Default"
                                    TextPadding = TRTTILength4( Magnifiable = [0.0, 0.0, <Padding>, 0.0] )
                                    VerticalFitStyle = ~/FitStyle/FitToContent
                                    HorizontalFitStyle = ~/FitStyle/FitToContent

                                    TypefaceToken = "Eurostyle"
                                    BigLineAction = ~/BigLineAction/CutByDots

                                    TextDico = ~/LocalisationConstantes/dico_interface_ingame
                                    TextToken = <ValueToken>
                                    TextColor = "BlancTexte"
                                    TextSize = <TextSize>
                                )
                            ),
                        ]
                    )
                ]
            )
        ),
    ]

    BackgroundComponents =
    ( <BackgroundComponents> == true ?
       [
           PanelRoundedCorner
           (
               BorderLineColorToken = "MarronPanel_noir"
               BackgroundBlockColorToken = "Transparent"
               Radius = 1
           ),
       ] : []
    )

    ForegroundComponents =
    (   <HintToken> != "" ?
        [
            BUCKSpecificHintableArea
            (
                ElementName = <ElementName> + 'Hint'
                DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                HintBodyToken = <HintToken> + "B"
                HintExtendedToken = <HintToken> + "E"
            )
        ] : []
    )
)

// -------------------------------------------------------------------------------------------------
template BUCKSpecificUnitInfoSingleWeaponPanelMainComponentDescriptor
[
    Scale : float
]
is BUCKListDescriptor
(
    ElementName = 'PanelListeArmeAttribut'

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [SingleWeaponColumnWidth, 0.0]
    )

    Axis = ~/ListAxis/Vertical
    MagnifierMultiplication = <Scale>

    Elements =
    [
        // nom de la catégorie de l'arme
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = 'WeaponTitle'
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight = [0.0, 28.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                    InterLine = 0
                )

                Hint = BUCKSpecificHintableArea
                (
                    ElementName = 'WeaponTitleHint'
                    DicoToken = ~/LocalisationConstantes/dico_units
                )

                TextStyle = "Default"

                VerticalFitStyle = ~/FitStyle/UserDefined
                HorizontalFitStyle = ~/FitStyle/UserDefined

                TextDico = ~/LocalisationConstantes/dico_units
                HasBackground = true
                BackgroundBlockColorToken = "MarronPanel_fonce"
                TypefaceToken = "Liberator"
                BigLineAction = ~/BigLineAction/MultiLine
                TextColor = "MarronPanel_noir"
                TextSize = "12"

                HasBorder = true
                BorderLineColorToken = 'MarronPanel_noir'
                BorderThicknessToken = '2'
            )
        ),
        //  affiche les traits de l'arme
        BUCKListElementDescriptor
        (
            ComponentDescriptor = WeaponTraitList
        ),
        // affichage icone armes, nom armes et munitions
        BUCKListElementDescriptor
        (
            ComponentDescriptor = WeaponInformation
            (
                Scale = <Scale>
                IsSelection = false
                BordersToDraw = ~/TBorderSide/Bottom | ~/TBorderSide/Left | ~/TBorderSide/Right
            )
        ),

        //-------------------------------------------------------------------------------------:

        // liste des caracts de l'arme
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ElementName = "WeaponAttributes"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                )

                Axis = ~/ListAxis/Vertical
                InterItemMargin = TRTTILength (Magnifiable = 0)
            )
            // => envoie les template WeaponDamageTypeAttribute
        )
    ]
)

//-------------------------------------------------------------------------------------

WeaponTraitList is BUCKRackDescriptor
(
    ElementName = "WeaponTraitsList"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 24.0]
    )

    Axis = ~/ListAxis/Horizontal

    HasBorder = true
    BorderLineColorToken = 'MarronPanel_noir'
    BorderThicknessToken = '2'
    BordersToDraw = ~/TBorderSide/Left

    BladeDescriptor = BUCKContainerDescriptor
    (
        ElementName = "WeaponTrait"

        ComponentFrame = TUIFramePropertyRTTI
        (
            RelativeWidthHeight = [0.0, 1.0]
            MagnifiableWidthHeight = [SingleWeaponColumnWidth div 3.0, 0.0]
        )

        Components =
        [
            BUCKTextureDescriptor
            (
                ElementName = "WeaponTraitIconTexture"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )

                TextureFrame   = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )
            ),
            BUCKSpecificHintableArea
            (
                ElementName = "WeaponTraitHint"

                DicoToken = ~/LocalisationConstantes/dico_interface_ingame
            ),
        ]
    )

    // Sepearation des elements, attention a la taille
    ForegroundComponents =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
            )

            Axis = ~/ListAxis/Horizontal

            HasBorder = true
            BorderLineColorToken = 'MarronPanel_noir'
            BorderThicknessToken = '1'
            BordersToDraw = ~/TBorderSide/Bottom

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKContainerDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [SingleWeaponColumnWidth div 3.0, 0.0]
                        )

                        HasBorder = true
                        BorderLineColorToken = 'MarronPanel_noir'
                        BorderThicknessToken = '1'
                        BordersToDraw = ~/TBorderSide/Right
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKContainerDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [SingleWeaponColumnWidth div 3.0, 0.0]
                        )

                        HasBorder = true
                        BorderLineColorToken = 'MarronPanel_noir'
                        BorderThicknessToken = '1'
                        BordersToDraw = ~/TBorderSide/Right
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKContainerDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [SingleWeaponColumnWidth div 3.0, 0.0]
                        )
                        HasBorder = true
                        BorderLineColorToken = 'MarronPanel_noir'
                        BorderThicknessToken = '2'
                        BordersToDraw = ~/TBorderSide/Right
                    )
                )
            ]
        )
    ]
)

// -------------------------------------------------------------------------------------------------

WeaponDamageTypeAttributeDummy is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 2.0]
    )

    HasBackground = true
    BackgroundBlockColorToken = "MarronPanel_fonce"
)

// -------------------------------------------------------------------------------------------------

template UISpecificUnitInfoSingleWeaponPanelViewDescriptor
[
    Scale : float = 1.0
]
is TUISpecificUnitInfoSingleWeaponPanelViewDescriptor
(
    MainComponentDescriptor = BUCKSpecificUnitInfoSingleWeaponPanelMainComponentDescriptor( Scale = <Scale>)
    AttributeDescriptorsPool = MAP
    [
        // ------------------------------------------------
        // Weapon Damage
        // ------------------------------------------------
        ("WeaponDamageTitle",
            WeaponDamageTypeTitle
            (
                ElementName = "WeaponDmg"
                Token = "pi_des"
                Scale = <Scale>
            )
        ),
        // Weapon Penetration
        ("WeaponAP",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponAP" Token = "pi_des" ValueToken = "UIPW_MILLI" HintToken = "HIP_WAPV"
                TextSize = '30'
                MagnifiableOffset = [0.0, -15.0]
                HasBorder = true
                Padding = 0
                BordersToDraw = ~/TBorderSide/Left | ~/TBorderSide/Right
                Scale = <Scale>
                FirstMargin = 18
                LastMargin = 5
                HasSousGroupeText = true
                IsSousGroupe = true
                TokenSousGroupe = 'UIPT_AP'
            )
        ),
        // Weapon Damage
        ("WeaponDamage",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponDamage" Token = "TC_HE" ValueToken = "UIP_VAL" HintToken = "HIP_WDMG"
                HasBorder = true
                BordersToDraw = ~/TBorderSide/Left | ~/TBorderSide/Right
                TextSize = '16'
                MagnifiableOffset = [0.0, -4.0]
                Scale = <Scale>
                FirstMargin = 0
                LastMargin = 5
                HasSousGroupeText = true
                IsSousGroupe = true
                TokenSousGroupe = 'TC_HE'
            )
        ),

        // Weapon Suppression
        ("WeaponSuppress",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponSuppress" Token = "UIPT_SUPPR" ValueToken = "UIP_VAL" HintToken = "HIP_WSUPP"
                HasBorder = true
                BordersToDraw = ~/TBorderSide/Bottom | ~/TBorderSide/Left | ~/TBorderSide/Right
                TextSize = '16'
                MagnifiableOffset = [0.0, -4.0]
                Scale = <Scale>
                FirstMargin = 0
                LastMargin = 5
                HasSousGroupeText = true
                IsSousGroupe = true
                TokenSousGroupe = 'UIPT_SUPPR'
            )
        ),
        // ------------------------------------------------
        // Weapon Range
        // ------------------------------------------------
        ("WeaponRangeTitle",
            WeaponDamageTypeTitle
            (
                ElementName = "WeaponRng"
                Token = "pi_rng"
                Scale = <Scale>
            )
        ),
        // Weapon ground target Range
        ("WeaponRange",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponRange" Token = "pi_rng" ValueToken = "UIPW_METER" HintToken = "HIP_WRANG"
                Magnifiable_barre = 70
                HasBorder = true
                BordersToDraw =  ~/TBorderSide/Left | ~/TBorderSide/Right
                TextSize = '16'
                MagnifiableOffset = [0.0, -3.0]
                Scale = <Scale>
                FirstMargin = 4
                LastMargin = 5
                HasSousGroupeText = true
                IsSousGroupe = true
                TokenSousGroupe = 'UIPT_RANGE'
            )
        ),
        // Weapon heli target Range
        ("WeaponMaxRangeTBA",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponMaxRangeTBA" Token = "UIPT_RANGT" ValueToken = "UIPW_METER" HintToken = "HIP_WRANT"
                Magnifiable_barre = 70
                HasBorder = true
                BordersToDraw = ~/TBorderSide/Left | ~/TBorderSide/Right
                TextSize = '16'
                MagnifiableOffset = [0.0, -3.0]
                Scale = <Scale>
                FirstMargin = 0
                LastMargin = 5
                HasSousGroupeText = true
                IsSousGroupe = true
                TokenSousGroupe = 'UIPT_RANGT'
            )
        ),
        // Weapon aircraft target Range
        ("WeaponMaxRangeHA",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponMaxRangeHA" Token = "UIPT_RANGH" ValueToken = "UIPW_METER" HintToken = "HIP_WRANH"
                Magnifiable_barre = 70
                HasBorder = true
                BordersToDraw = ~/TBorderSide/Bottom | ~/TBorderSide/Left | ~/TBorderSide/Right
                TextSize = '16'
                MagnifiableOffset = [0.0, -3.0]
                Scale = <Scale>
                FirstMargin = 0
                LastMargin = 5
                HasSousGroupeText = true
                IsSousGroupe = true
                TokenSousGroupe = 'UIPT_RANGH'
            )
        ),
        // ------------------------------------------------
        // Weapon Accuracy
        // ------------------------------------------------
        ("WeaponPrecisionTitle",
            WeaponDamageTypeTitle
            (
                ElementName = "WeaponPrec"
                Token = "UIPT_PREC"
                Scale = <Scale>
            )
        ),
        // Weapon Static Accuracy
        ("WeaponAccuracy",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponAccuracy" Token = "UIPT_PREC" ValueToken = "UIPW_PCT" HintToken = "HIP_WPREC"
                FullGaugeIfNoMax = true
                HasBorder = true
                BordersToDraw = ~/TBorderSide/Left | ~/TBorderSide/Right
                TextSize = '16'
                MagnifiableOffset = [0.0, -4.0]
                Scale = <Scale>
                FirstMargin = 4
                LastMargin = 5
                HasSousGroupeText = true
                IsSousGroupe = true
                TokenSousGroupe = 'FBXRVZBVFG'
            )
        ),
        // Weapon Movement Accuracy
        ("WeaponAccuracyMovement",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponAccuracyMovement" Token = "UIPT_ACCMV" ValueToken = "UIPW_PCT" HintToken = "HIP_ACCMV" FullGaugeIfNoMax = true
                Scale = <Scale>
                HasBorder = true
                BordersToDraw = ~/TBorderSide/Bottom | ~/TBorderSide/Left | ~/TBorderSide/Right
                FirstMargin = 0
                LastMargin = 5
                HasSousGroupeText = true
                IsSousGroupe = true
            )
        ),
        // ------------------------------------------------
        // Weapon Shooting attributes
        // ------------------------------------------------
        ("WeaponAttributesTitle",
            WeaponDamageTypeTitle
            (
                ElementName = "WeaponAttr"
                Token = "pi_vi"
                Scale = <Scale>
            )
        ),
        // Weapon Rate of fire
        ("WeaponRateOfFire",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponRateOfFire" Token = "pi_vi" ValueToken = "UIPW_RPM" HintToken = "HIP_WROF"
                HasBorder = true
                BordersToDraw = ~/TBorderSide/Left | ~/TBorderSide/Right
                TextSize = '16'
                MagnifiableOffset = [0.0, -4.0]
                Scale = <Scale>
                FirstMargin = 4
                LastMargin = 5
                TokenSousGroupe = 'UIPT_ROF'
                HasSousGroupeText = true
                IsSousGroupe = true
            )
        ),
        // Weapon Aiming time
        ("WeaponAimingTime",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponAimingTime" Token = "UIPT_AT" ValueToken = "UIP_S" HintToken = "HIP_AT"
                HasBorder = true
                BordersToDraw = ~/TBorderSide/Left | ~/TBorderSide/Right
                TextSize = '16'
                MagnifiableOffset = [0.0, -4.0]
                Scale = <Scale>
                FirstMargin = 0
                LastMargin = 5
                HasSousGroupeText = true
                TokenSousGroupe = 'UIPT_AT'
                IsSousGroupe = true
            )
        ),
        // Weapon Reload time
        ("WeaponReloadTime",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponReloadTime" Token = "UIPT_RLDT" ValueToken = "UIP_S" HintToken = "HIP_WRLDT"
                HasBorder = true
                BordersToDraw = ~/TBorderSide/Left | ~/TBorderSide/Right
                TextSize = '16'
                MagnifiableOffset = [0.0, -4.0]
                Scale = <Scale>
                FirstMargin = 0
                LastMargin = 5
                HasSousGroupeText = true
                TokenSousGroupe = 'UIPT_RLDT'
                IsSousGroupe = true
            )
        ),
        // Weapon Nb of shots by salvo
        ("WeaponNbShotsBySalvo",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponNbShotsBySalvo" Token = "UIPT_NSBS" ValueToken = "UIP_VAL" HintToken = "HIP_NSBS"
                HasBorder = true
                BordersToDraw = ~/TBorderSide/Bottom | ~/TBorderSide/Left | ~/TBorderSide/Right
                BackgroundBlockColorToken = 'Transparent'
                TextSize = '16'
                MagnifiableOffset = [0.0, -4.0]
                Scale = <Scale>
                FirstMargin = 0
                LastMargin = 5
                HasSousGroupeText = true
                TokenSousGroupe = 'UIPT_NSBS'
                IsSousGroupe = true
            )
        ),
        // ------------------------------------------------
        // Weapon Supply cost
        // ------------------------------------------------
        ("WeaponSupplyCostTitle",
            WeaponDamageTypeTitle
            (
                ElementName = "WeaponSuppCost"
                Token = "UIPT_SC"
                Scale = <Scale>
            )
        ),
        // Weapon Supply Cost
        ("WeaponSupplyCost",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponSupplyCost" Token = "UIPT_SC" ValueToken = "UIP_VAL" HintToken = "HIP_SC"
                HasBorder = true
                BordersToDraw = ~/TBorderSide/Left | ~/TBorderSide/Right |~/TBorderSide/Bottom
                TextSize = '16'
                MagnifiableOffset = [0.0, -4.0]
                Scale = <Scale>
                FirstMargin = 2
                LastMargin = 5
                IsSousGroupe = true
            )
        ),

        // ------------------------------------------------
        // UNUSED but who knows
        // Weapon Area Of Effect
        ("WeaponAreaOfEffect",     WeaponDamageTypeAttribute(ElementName = "WeaponAreaOfEffect" Token = "UIPT_AOE" ValueToken = "UIP_VAL" HintToken = "HIP_WAOE" Scale = <Scale>)),
        // Weapon Reload Time

        // Weapon Traverse Speed
        ("WeaponTraverseSpeed",    WeaponDamageTypeAttribute(ElementName = "WeaponTraverseSpeed" Token = "UIPA_TRSPD" ValueToken = "UIP_DPS" HintToken = "HIP_WTRSP" Scale = <Scale>)),

        // Weapon Time between Two Shots
        ("WeaponTimeBetweenTwoShots",
            WeaponDamageTypeAttribute
            (
                ElementName = "WeaponTimeBetweenTwoShots" Token = "UIPT_TBTS" ValueToken = "UIP_S" HintToken = "HIP_TBTS"
                HasBorder = true
                BordersToDraw = ~/TBorderSide/Left | ~/TBorderSide/Right
                BackgroundBlockColorToken = 'Transparent'
                TextSize = '16'
                MagnifiableOffset = [0.0, -4.0]
                Scale = <Scale>
                FirstMargin = 0
                LastMargin = 5
                HasSousGroupeText = true
                TokenSousGroupe = 'UIPT_TBTS'
                IsSousGroupe = true
            )
        ),
        // ------------------------------------------------

    ]

    WeaponTraitsContainer = ~/WeaponTraits

    WeaponDamageTypeAttributeDummy = ~/WeaponDamageTypeAttributeDummy // permet de configurer les "" dans le tableau du DamageTypeConfiguration
    NotApplicableToken = "UIP_NA"
    NoValueToken = "UIP_NOVAL"
    InfiniteToken = "UIP_INF"
    WeaponNameAndAmountToken = "UIPW_NUM"
    WeaponBigStatTextToken = "UIPW_THSD"

    // Attributes whose value is prefered when lower
    SmallerIsBetterAttributes = ["WeaponAimingTime", "WeaponReloadTime", "WeaponSupplyCost"]

    // Configuration of the panel works like this:
    // The attributes will pile up horizontally first in the grid
    DamageTypeConfiguration = listeAffichage

    ValueTextColors = ~/InfoPanelValuesColors
    DamageTypeComponents = ~/DamageTypeComponentConfigurations

    MergeDamageTypes = true
)

// -------------------------------------------------------------------------------------------------

listeAffichage is
[
    "",
    "WeaponDamageTitle",
    "WeaponAP",
    "WeaponDamage",
    "WeaponSuppress",
    "",
    "WeaponRangeTitle",
    "WeaponRange",
    "WeaponMaxRangeTBA",
    "WeaponMaxRangeHA",
    "",
    "WeaponPrecisionTitle",
    "WeaponAccuracy",
    "WeaponAccuracyMovement",
    "",
    "WeaponAttributesTitle",
    "WeaponRateOfFire",
    "WeaponAimingTime",
    "WeaponReloadTime",
    "WeaponNbShotsBySalvo",
    "",
    "WeaponSupplyCostTitle",
    "WeaponSupplyCost",
]

export Modele_DCA_KS30_130mm_SOV is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/URSS/DCA/DCA_KS30_130mm/DCA_KS30_130mm.fbx"
)

export Modele_DCA_KS30_130mm_SOV_MID is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/URSS/DCA/DCA_KS30_130mm_MID/DCA_KS30_130mm_MID.fbx"
)

export Modele_DCA_KS30_130mm_SOV_LOW is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/URSS/DCA/DCA_KS30_130mm_LOW/DCA_KS30_130mm_LOW.fbx"
)

export DeployAnimation_DCA_KS30_130mm_SOV is TResourceMatrixArrayAnimation
(
    FileName   = 'GameData:/Assets/3D/Units/URSS/DCA/DCA_KS30_130mm/DCA_KS30_130mm_DEPLOY.fbx'
    PlayInLoop     = false
)

export DepictionOperator_Deployable_DCA_KS30_130mm_SOV is DepictionOperator_AnimationDeployable
(
    DeployAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/URSS/DCA/DCA_KS30_130mm/DCA_KS30_130mm_DEPLOY.fbx'
        PlayInLoop     = false
    )

    FoldAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/URSS/DCA/DCA_KS30_130mm/DCA_KS30_130mm_FOLD.fbx'
        PlayInLoop     = false
    )

    IdleAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/URSS/DCA/DCA_KS30_130mm/DCA_KS30_130mm_IDLE.fbx'
        PlayInLoop     = true
    )

    MoveFrontAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/URSS/DCA/DCA_KS30_130mm/DCA_KS30_130mm_MOVE.fbx'
        PlayInLoop     = true
    )

    MoveBackAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/URSS/DCA/DCA_KS30_130mm/DCA_KS30_130mm_MOVE_BACK.fbx'
        PlayInLoop     = true
    )

    DeployFromMoveTime  = 1.5
    DeployFromTurnTime  = 1.5
    FoldForMoveTime  = 1.5
    FoldForTurnTime  = 1.5
)
export Modele_DCA_ZPU4_POL is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/DDR/DCA/DCA_ZPU4/DCA_ZPU4.fbx"
    Textures="GameData:/Assets/3D/Units/POL/DCA/DCA_ZPU4"
)

export Modele_DCA_ZPU4_POL_MID is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/DDR/DCA/DCA_ZPU4/DCA_ZPU4.fbx"
    Textures="GameData:/Assets/3D/Units/POL/DCA/DCA_ZPU4"
)

export Modele_DCA_ZPU4_POL_LOW is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/DDR/DCA/DCA_ZPU4/DCA_ZPU4.fbx"
    Textures="GameData:/Assets/3D/Units/POL/DCA/DCA_ZPU4"
)

// export DeployAnimation_DCA_ZPU4_POL is TResourceMatrixArrayAnimation
// (
//     Mesh="GameData:/Assets/3D/Units/RFA/Canon/DCA_FK20_2_20mm_Zwillinge/DCA_FK20_2_20mm_Zwillinge_Deploy.fbx"
//     PlayInLoop     = false
// )

// export DepictionOperator_Deployable_DCA_ZPU4_POL is DepictionOperator_AnimationDeployable
// (
//     DeployAnimation = TResourceMatrixArrayAnimation
//     (
//         Mesh="GameData:/Assets/3D/Units/RFA/Canon/DCA_FK20_2_20mm_Zwillinge/DCA_FK20_2_20mm_Zwillinge_Deploy.fbx"
//         PlayInLoop     = false
//     )

//     FoldAnimation = TResourceMatrixArrayAnimation
//     (
//         Mesh="GameData:/Assets/3D/Units/RFA/Canon/DCA_FK20_2_20mm_Zwillinge/DCA_FK20_2_20mm_Zwillinge_Fold.fbx"
//         PlayInLoop     = false
//     )

//     IdleAnimation = TResourceMatrixArrayAnimation
//     (
//         Mesh="GameData:/Assets/3D/Units/RFA/Canon/DCA_FK20_2_20mm_Zwillinge/DCA_FK20_2_20mm_Zwillinge_Idle.fbx"
//         PlayInLoop     = true
//     )

//     MoveFrontAnimation = TResourceMatrixArrayAnimation
//     (
//         Mesh="GameData:/Assets/3D/Units/RFA/Canon/DCA_FK20_2_20mm_Zwillinge/DCA_FK20_2_20mm_Zwillinge_Move.fbx"
//         PlayInLoop     = true
//     )

//     MoveBackAnimation = TResourceMatrixArrayAnimation
//     (
//         Mesh="GameData:/Assets/3D/Units/RFA/Canon/DCA_FK20_2_20mm_Zwillinge/DCA_FK20_2_20mm_Zwillinge_Move_Back.fbx"
//         PlayInLoop     = true
//     )

//     DeployFromMoveTime  = 1.5
//     DeployFromTurnTime  = 1.5
//     FoldForMoveTime  = 1.5
//     FoldForTurnTime  = 1.5
// )

export Modele_Howz_B4M_203mm_SOV is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/URSS/Canon/Howz_Br2M_152mm/Howz_B4M_203mm.fbx"
)

export Modele_Howz_B4M_203mm_SOV_MID is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/URSS/Canon/Howz_Br2M_152mm/Howz_B4M_203mm.fbx"
)

export Modele_Howz_B4M_203mm_SOV_LOW is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/URSS/Canon/Howz_Br2M_152mm/Howz_B4M_203mm.fbx"
)

export DeployAnimation_Howz_B4M_203mm_SOV is TResourceMatrixArrayAnimation
(
    FileName   = 'GameData:/Assets/3D/Units/URSS/Canon/Howz_Br2M_152mm/Howz_Br2M_152mm_Deploy.fbx'
    PlayInLoop     = false
)

export DepictionOperator_Deployable_Howz_B4M_203mm_SOV is DepictionOperator_AnimationDeployable
(
    DeployAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/URSS/Canon/Howz_Br2M_152mm/Howz_Br2M_152mm_Deploy.fbx'
        PlayInLoop     = false
    )

    FoldAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/URSS/Canon/Howz_Br2M_152mm/Howz_Br2M_152mm_Fold.fbx'
        PlayInLoop     = false
    )

    IdleAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/URSS/Canon/Howz_Br2M_152mm/Howz_B4M_203mm.fbx'
        PlayInLoop     = true
    )

    MoveFrontAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/URSS/Canon/Howz_Br2M_152mm/Howz_Br2M_152mm_Move.fbx'
        PlayInLoop     = true
    )

    MoveBackAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/URSS/Canon/Howz_Br2M_152mm/Howz_Br2M_152mm_Move_Back.fbx'
        PlayInLoop     = true
    )

    DeployFromMoveTime  = 1.5
    DeployFromTurnTime  = 1.5
    FoldForMoveTime  = 1.5
    FoldForTurnTime  = 1.5
)
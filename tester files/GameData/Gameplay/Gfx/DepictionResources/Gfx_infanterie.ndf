//------------------------------------------------------------------------------
// Armes
//------------------------------------------------------------------------------

//------------------------------------------------------------------------------
//----------------------------------ARMES USA-----------------------------------
//------------------------------------------------------------------------------

export Modele_M72_LAW   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M72A4.fbx" )
export Modele_M47_DRAGON   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M47_Dragon_II.fbx" )
export Modele_Test_missile_AA_inf   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M72A4.fbx" )
export Modele_MANPAD_FIM92   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/FIM-92_Stinger.fbx" )
export Modele_MANPAD_FIM92_A   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/FIM-92_Stinger.fbx" )

export Modele_GrenUS is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Grenades/Pineapple_grenade.fbx" )

export Modele_M14_Sniper is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M14_Sniper_Rifle/M14_Sniper_Rifle.fbx" )
export Modele_M14 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M14_Sniper_Rifle/M14_Rifle.fbx" )
export Modele_M16A1_Carbine is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M16A1_Carbine.fbx" )
export Modele_M16A2   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M16A2.fbx" )
export Modele_M16A1 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M16A1.fbx" )
export Modele_M24_Sniper is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M24_Sniper_Rifle/M24_Sniper_Rifle.fbx" )

export Modele_MainNue is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/main_nue.fbx" )
export Modele_M60   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M60.fbx" )
export Modele_M72A4 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M72A4.fbx" )
export Modele_M202_Flash is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M202_Flash.fbx" )
export Modele_M249 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M249.fbx" )
export Modele_M240B              is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/M240B.fbx" )
export Modele_M47_DRAGON_II   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M47_Dragon_II.fbx" )
export Modele_M67_RCL   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M67_RCL.fbx" )
export Modele_AT_4   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/AT_4.fbx" )
export Modele_M82_Barrett   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M82_Barrett/M82_Barrett.fbx" )
export Modele_M1_Garand   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M1_Garand/M1_Garand.fbx" )
export Modele_M2_Flamethrower   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M2_Flamethrower/M2_Flamethrower.fbx" )
export Modele_M2_Flamethrower_fuel   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M2_Flamethrower/M2_Flamethrower_bidon.fbx" )
export Modele_M20_SuperBaz   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M20_Super_Bazooka/M20_Super_Bazooka.fbx" )
export Modele_M60E3   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/M60E3/M60E3.fbx" )
export Modele_Remington_Model_700   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/Remington_Model_700/Remington_Model_700.fbx" )
export Modele_Viper   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/Viper/Viper.fbx" )
//------------------------------------------------------------------------------
//------------------------------------ARMES CAN---------------------------------
//------------------------------------------------------------------------------
export Modele_C3A1_Sniper   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/CAN/C3A1_SNIPER_RIFLE/C3A1_SNIPER_RIFLE.fbx" )
//------------------------------------------------------------------------------
//------------------------------------ARMES SOV---------------------------------
//------------------------------------------------------------------------------
export Modele_AK74   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/AK_74.fbx" )
export Modele_AKs74   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/AKs_74.fbx" )
export Modele_AKs74U   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/AKs_74U.fbx" )
export Modele_AKM   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/AKM.fbx" )
export Modele_AKMS   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/AKMS.fbx" )
export Modele_Wz88_Tantal   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/Wz88_Tantal.fbx" )
export Modele_SA7_Grail   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/SA7_Grail.fbx" )
export Modele_SAM_igla   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/SA7_Grail.fbx" )
export Modele_MANPAD_igla   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/SA7_Grail.fbx" )
export Modele_MANPAD_Strela3   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/SA7_Grail.fbx" )
export Modele_ATGM_9K115_Metis_M   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/9K115_Metis.fbx" )
export Modele_AS_Val   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/AS_Val.fbx" )
export Modele_PKM   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/PKM.fbx" )
export Modele_RPK   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/RPK.fbx" )
export Modele_RPK74   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/RPK74/RPK74.fbx" )
export Modele_VSS_Vintorez   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/VSS_Vintorez.fbx" )
export Modele_RPG7VR   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/RPG_7VR.fbx" )
export Modele_RPG76Komar   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/RPG_76_Komar.fbx" )
export Modele_RPG16   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/RPG_16.fbx" )
export Modele_RPG18   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/RPG_18.fbx" )
export Modele_RPG22   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/RPG_22.fbx" )
export Modele_RPG26   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/RPG_26.fbx" )
export Modele_RPG27   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/RPG_27.fbx" )
export Modele_RPG2   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/RPG_2.fbx" )
export Modele_RPO_A_Shmel   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/RPO_A_Shmel.fbx" )
export Modele_AGI_3x40   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/AGI_3x40.fbx" )
export Modele_Dragunov   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/SVD_Dragunov/SVD_Dragunov.fbx" )
export Modele_Mosin_Nagant_Scope   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/Mosin_Nagant_Scope/Mosin_Nagant_Scope.fbx" )
export Modele_DP28   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/DP28/DP28.fbx" )
export Modele_PPS43   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/PPS43/PPS43.fbx" )

//------------------------------------------------------------------------------
//------------------------------------ARMES DDR---------------------------------
//------------------------------------------------------------------------------
export Modele_STG941   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/DDR/Wieger_StG_941.fbx" )
export Modele_STG942   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/DDR/Wieger_StG_942.fbx" )
export Modele_RPG7V   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/DDR/RPG_7V.fbx" )
export Modele_RPG29   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/DDR/RPG_29.fbx" )
export Modele_RPG29_RCL   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/DDR/RPG_29_RCL.fbx" )
export Modele_LPO50   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/DDR/LPO_50.fbx" )
export Modele_LPO50_fuel   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/DDR/LPO_50_Bidon.fbx" )
export Modele_MANPAD_Strela2M is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/SOV/SA7_Grail.fbx" )
export Modele_Skorpion is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/DDR/Skorpion_vz61.fbx" )
export Modele_PM63_RAK is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/DDR/PM63_RAK.fbx" )
//------------------------------------------------------------------------------
//------------------------------------ARMES RFA---------------------------------
//------------------------------------------------------------------------------
export Modele_Armbrust           is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/RFA/Armbrust_300.fbx" )
export Modele_Carl_Gustav_M2     is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/RFA/Carl_Gustav_M2.fbx" )
export Modele_G3A4               is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/RFA/G3A4.fbx" )
export Modele_Handflammpatrone   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/RFA/Handflammpatrone.fbx" )
export Modele_HK21               is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/RFA/HK21.fbx" )
export Modele_MG3                is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/RFA/MG3.fbx" )
export Modele_MP5A3              is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/RFA/MP5A3.fbx" )
export Modele_MP5SD3              is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/RFA/MP5SD3.fbx" )
export Modele_Panzerfaust_3      is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/RFA/Panzerfaust_3.fbx" )
export Modele_Panzerfaust_44     is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/RFA/Panzerfaust_44.fbx" )
export Modele_Uzi                is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/RFA/Uzi.fbx" )
export Modele_MANPAD_FIM43   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/USA/FIM_43_Redeye.fbx" )
export Modele_G3A3ZF               is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/RFA/G3A3ZF/G3A3ZF.fbx" )
export Modele_PSG1               is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/RFA/PSG1/PSG1.fbx" )
//------------------------------------------------------------------------------
//------------------------------------ARMES UK----------------------------------
//------------------------------------------------------------------------------
export Modele_Javelin            is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/Javelin.fbx" )
export Modele_MANPAD_Blowpipe    is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/Javelin.fbx" )
export Modele_Javelin_LML        is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/Javelin.fbx" )
export Modele_L1A1_SLR           is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/L1A1_SLR.fbx" )
export Modele_L1A1_SLR_scope     is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/L1A1_SLR_scope/L1A1_SLR_scope.fbx" )
export Modele_L2A3               is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/L2A3.fbx" )
export Modele_L7A2               is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/MAG_L7A2.fbx" )
export Modele_L85A1              is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/L85A1.fbx" )
export Modele_L86A1_LSW          is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/L86A1_LSW.fbx" )
export Modele_LAW_80             is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/LAW_80.fbx" )
export Modele_L4A4_Bren          is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/Bren.fbx" )
export Modele_L96A1              is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/L96A1/L96A1.fbx" )
export Modele_M3_Grease_Gun      is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/UK/M3_Grease_Gun/M3_Grease_Gun.fbx" )

//------------------------------------------------------------------------------
//------------------------------------ARMES FR----------------------------------
//------------------------------------------------------------------------------
export Modele_AANF1   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/AANF1.fbx" )
export Modele_APILAS   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/APILAS.fbx" )
export Modele_Famas_F1   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/Famas_F1.fbx" )
export Modele_FR_F2   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/FR_F2.fbx" )
export Modele_LRAC_F1   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/LRAC_F1.fbx" )
export Modele_MAS_49_56   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/MAS_49_56.fbx" )
export Modele_MAT_49   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/MAT_49.fbx" )
export Modele_Mistral   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/Mistral.fbx" )
export Modele_SIG_540   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/SIG_540.fbx" )
export Modele_SIG_543   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/SIG_543.fbx" )
export Modele_LRAC_73   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/LRAC_73mm.fbx" )
export Modele_LFP54   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/LFP54.fbx" )
export Modele_LFP54_fuel   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/LFP54_bidon.fbx" )
export Modele_FR_F1   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/FR_F1/FR_F1.fbx" )
export Modele_ATGM_Eryx   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/Eryx/Eryx.fbx" )
export Modele_Wasp_58   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/FR/Wasp_58/Wasp_58.fbx" )

//------------------------------------------------------------------------------
//------------------------------------ARMES BEL----------------------------------
//------------------------------------------------------------------------------
export Modele_FALO   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/BEL/FALO/FALO.fbx" )
export Modele_FNC   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/BEL/FNC/FNC.fbx" )
export Modele_FNC_Para   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/BEL/FNC_Para/FNC_Para.fbx" )
export Modele_Vigneron_M2   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/BEL/Vigneron_M2/Vigneron_M2.fbx" )
export Modele_FN_Model_30_11   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/BEL/FN_Model_30_11/FN_Model_30_11.fbx" )
export Modele_RL_83   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/BEL/RL_83/RL_83.fbx" )
export Modele_RL_100   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/BEL/RL_100/RL_100.fbx" )
export Modele_FAL_sniper   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/BEL/FAL_sniper/FAL_sniper.fbx" )

//------------------------------------------------------------------------------
//------------------------------------ARMES TCH----------------------------------
//------------------------------------------------------------------------------
export Modele_RPG_75   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/TCH/RPG_75/RPG_75.fbx" )
export Modele_T21_Tarasnice   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/TCH/T21_Tarasnice/T21_Tarasnice.fbx" )
export Modele_UK_vz_59   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/TCH/UK_vz_59/UK_vz_59.fbx" )
export Modele_UK_vz_59L   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/TCH/UK_vz_59L/UK_vz_59L.fbx" )
export Modele_Vz_58_rifle   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/TCH/Vz_58_rifle/Vz_58_rifle.fbx" )
export Modele_Vz_58V_rifle   is TResourceMesh( Mesh="GameData:/Assets/3D/Units/Ammo/Armes/TCH/Vz_58V_rifle/Vz_58V_rifle.fbx" )

//------------------------------------------------------------------------------
//-----------------------------------SOLDATS SOV--------------------------------
//------------------------------------------------------------------------------
SOV_MotoStrelki_1 is 'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki/MotoStrelki_1.fbx'
SOV_MotoStrelki_2 is 'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki/MotoStrelki_2.fbx'
SOV_MotoStrelki_NCO is 'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki/MotoStrelki_NCO.fbx'
SOV_MotoStrelki_Officer is 'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki/MotoStrelki_Officer.fbx'
SOV_MotoStrelki_Sniper is 'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki/MotoStrelki_Sniper.fbx'
SOV_MotoStrelki_spe is 'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki/MotoStrelki_spe.fbx'
SOV_MotoStrelki_1_LOW is 'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki/MotoStrelki_1_LOW.fbx'

SOV_VDV_1 is 'GameData:/Assets/3D/Units/URSS/Infanterie/VDV/VDV_1.fbx'
SOV_VDV_2 is 'GameData:/Assets/3D/Units/URSS/Infanterie/VDV/VDV_2.fbx'
SOV_VDV_NCO is 'GameData:/Assets/3D/Units/URSS/Infanterie/VDV/VDV_NCO.fbx'
SOV_VDV_Officer is 'GameData:/Assets/3D/Units/URSS/Infanterie/VDV/VDV_Officer.fbx'
SOV_VDV_Sniper is 'GameData:/Assets/3D/Units/URSS/Infanterie/VDV/VDV_Sniper.fbx'
SOV_VDV_spe is 'GameData:/Assets/3D/Units/URSS/Infanterie/VDV/VDV_spe.fbx'
SOV_VDV_1_LOW is 'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki/MotoStrelki_1_LOW.fbx'

Komendatura is 'GameData:/Assets/3D/Units/URSS/Infanterie/Komendatura/Komendatura.fbx'

SOV_MotoStrelki_1_TTsko is       'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki_TTsko/MotoStrelki_1_TTsko.fbx'
SOV_MotoStrelki_2_TTsko is       'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki_TTsko/MotoStrelki_2_TTsko.fbx'
SOV_MotoStrelki_TTsko_NCO is     'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki_TTsko/MotoStrelki_TTsko_NCO.fbx'
SOV_MotoStrelki_TTsko_Officer is 'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki_TTsko/MotoStrelki_TTsko_Officer.fbx'
SOV_MotoStrelki_TTsko_spe is     'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki_TTsko/MotoStrelki_TTsko_spe.fbx'
SOV_MotoStrelki_1_TTsko_LOW is       'GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki_TTsko/MotoStrelki_1_TTsko_LOW.fbx'

SOV_Rezervisti_1 is 'GameData:/Assets/3D/Units/URSS/Infanterie/Rezervisti/Rezervisti_1.fbx'
SOV_Rezervisti_2 is 'GameData:/Assets/3D/Units/URSS/Infanterie/Rezervisti/Rezervisti_2.fbx'
SOV_Rezervisti_3 is 'GameData:/Assets/3D/Units/URSS/Infanterie/Rezervisti/Rezervisti_3.fbx'
SOV_Rezervisti_NCO is 'GameData:/Assets/3D/Units/URSS/Infanterie/Rezervisti/Rezervisti_NCO.fbx'
SOV_Rezervisti_LOW is 'GameData:/Assets/3D/Units/URSS/Infanterie/Rezervisti/Rezervisti_1.fbx'

SOV_Morskaya_1 is 'GameData:/Assets/3D/Units/URSS/Infanterie/Morskaya/Morskaya_1.fbx'
SOV_Morskaya_2 is 'GameData:/Assets/3D/Units/URSS/Infanterie/Morskaya/Morskaya_2.fbx'
SOV_Morskaya_NCO is 'GameData:/Assets/3D/Units/URSS/Infanterie/Morskaya/Morskaya_NCO.fbx'
SOV_Morskaya_LOW is 'GameData:/Assets/3D/Units/URSS/Infanterie/Morskaya/Morskaya_1.fbx'

//------------------------------------------------------------------------------
//Motostrelki_sov
// export Modele_Motostrelki_sov is TResourceMesh( Mesh="GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki/MotoStrelki_1.fbx" )
// export Modele_Motostrelki_sov_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/URSS/Infanterie/MotoStrelki/MotoStrelki_1.fbx" )
//------------------------------------------------------------------------------

//Partisan_SOV
export Modele_Partisan_SOV is TResourceMesh( Mesh=SOV_Rezervisti_NCO )
export Modele_Partisan_SOV_02 is TResourceMesh( Mesh=SOV_Rezervisti_1 )
export Modele_Partisan_SOV_03 is TResourceMesh( Mesh=SOV_Rezervisti_2 )
export Modele_Partisan_SOV_04 is TResourceMesh( Mesh=SOV_Rezervisti_3 )
export Modele_Partisan_SOV_LOW is TResourceMesh( Mesh=SOV_Rezervisti_LOW )

//Security_Naval_SOV
export Modele_Security_Naval_SOV is TResourceMesh( Mesh=SOV_Morskaya_NCO )
export Modele_Security_Naval_SOV_02 is TResourceMesh( Mesh=SOV_Morskaya_NCO )
export Modele_Security_Naval_SOV_03 is TResourceMesh( Mesh=SOV_Morskaya_1 )
export Modele_Security_Naval_SOV_04 is TResourceMesh( Mesh=SOV_Morskaya_2 )
export Modele_Security_Naval_SOV_LOW is TResourceMesh( Mesh=SOV_Morskaya_LOW )

//MP_Combat_SOV
export Modele_MP_Combat_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_Officer )
export Modele_MP_Combat_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_MP_Combat_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_MP_Combat_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_MP_Combat_SOV_05 is TResourceMesh( Mesh=Komendatura )
export Modele_MP_Combat_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//MP_SOV
export Modele_MP_SOV is TResourceMesh( Mesh=Komendatura )
export Modele_MP_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//ATGM_Fagot_SOV
export Modele_ATGM_Fagot_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_ATGM_Fagot_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_ATGM_Fagot_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_ATGM_Fagot_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_Officer )
export Modele_ATGM_Fagot_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_ATGM_Fagot_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_ATGM_Fagot_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//ATGM_Konkurs_SOV
export Modele_ATGM_Konkurs_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_ATGM_Konkurs_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_ATGM_Konkurs_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_ATGM_Konkurs_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_Officer )
export Modele_ATGM_Konkurs_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_ATGM_Konkurs_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_ATGM_Konkurs_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//Engineers_Flam_TTsko_SOV
export Modele_Engineers_Flam_TTsko_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_NCO )
export Modele_Engineers_Flam_TTsko_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_Engineers_Flam_TTsko_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_Engineers_Flam_TTsko_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_spe )
export Modele_Engineers_Flam_TTsko_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//Alfa_Group_SOV
export Modele_Alfa_Group_SOV is TResourceMesh( Mesh=Civil1_Mullet )
export Modele_Alfa_Group_SOV_02 is TResourceMesh( Mesh=Civil2_Trucker )
export Modele_Alfa_Group_SOV_03 is TResourceMesh( Mesh=Civil3_Mannschaft )
export Modele_Alfa_Group_SOV_04 is TResourceMesh( Mesh=Civil4_Suit )
export Modele_Alfa_Group_SOV_05 is TResourceMesh( Mesh=Civil5_DoubleDenim )
export Modele_Alfa_Group_SOV_06 is TResourceMesh( Mesh=Civil6_Miami )
export Modele_Alfa_Group_SOV_07 is TResourceMesh( Mesh=Civil7_Pastel_Male )
export Modele_Alfa_Group_SOV_08 is TResourceMesh( Mesh=Civil8_Princess )
export Modele_Alfa_Group_SOV_09 is TResourceMesh( Mesh=Civil9_Punk_Female )
export Modele_Alfa_Group_SOV_10 is TResourceMesh( Mesh=Civil10_Punk_Male )
export Modele_Alfa_Group_SOV_LOW is TResourceMesh( Mesh=Civil1_Mullet_LOW )

//Engineers_Flam_SOV
export Modele_Engineers_Flam_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_Engineers_Flam_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_Engineers_Flam_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_Engineers_Flam_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_Engineers_Flam_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//HMG_NSV_SOV
export Modele_HMG_NSV_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_HMG_NSV_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_HMG_NSV_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_HMG_NSV_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_Officer )
export Modele_HMG_NSV_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_HMG_NSV_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_HMG_NSV_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//Engineers_TTsko_SOV
export Modele_Engineers_TTsko_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_NCO )
export Modele_Engineers_TTsko_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_Engineers_TTsko_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_Engineers_TTsko_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_spe )
export Modele_Engineers_TTsko_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//Engineers_Flam_Reserve_SOV
export Modele_Engineers_Flam_Reserve_SOV is TResourceMesh( Mesh=SOV_Rezervisti_1 )
export Modele_Engineers_Flam_Reserve_SOV_02 is TResourceMesh( Mesh=SOV_Rezervisti_2 )
export Modele_Engineers_Flam_Reserve_SOV_03 is TResourceMesh( Mesh=SOV_Rezervisti_3 )
export Modele_Engineers_Flam_Reserve_SOV_LOW is TResourceMesh( Mesh=SOV_Rezervisti_LOW )

//Engineers_Reserve_SOV
export Modele_Engineers_Reserve_SOV is TResourceMesh( Mesh=SOV_Rezervisti_1 )
export Modele_Engineers_Reserve_SOV_02 is TResourceMesh( Mesh=SOV_Rezervisti_2 )
export Modele_Engineers_Reserve_SOV_03 is TResourceMesh( Mesh=SOV_Rezervisti_3 )
export Modele_Engineers_Reserve_SOV_LOW is TResourceMesh( Mesh=SOV_Rezervisti_LOW )

//Engineers_SOV
export Modele_Engineers_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_Engineers_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_Engineers_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_Engineers_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_Engineers_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//Engineers_Scout_TTsko_SOV
export Modele_Engineers_Scout_TTsko_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_NCO )
export Modele_Engineers_Scout_TTsko_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_Engineers_Scout_TTsko_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_Engineers_Scout_TTsko_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_spe )
export Modele_Engineers_Scout_TTsko_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//Engineers_Scout_SOV
export Modele_Engineers_Scout_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_Engineers_Scout_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_Engineers_Scout_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_Engineers_Scout_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_Engineers_Scout_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//MANPAD_Igla_TTsko_SOV
export Modele_MANPAD_Igla_TTsko_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_NCO )
export Modele_MANPAD_Igla_TTsko_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_MANPAD_Igla_TTsko_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_MANPAD_Igla_TTsko_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//MANPAD_Strela_2M_SOV
export Modele_MANPAD_Strela_2M_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_MANPAD_Strela_2M_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_MANPAD_Strela_2M_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_MANPAD_Strela_2M_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_MANPAD_Strela_2M_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//MANPAD_Strela_3_SOV
export Modele_MANPAD_Strela_3_SOV is TResourceMesh( Mesh=SOV_Rezervisti_1 )
export Modele_MANPAD_Strela_3_SOV_02 is TResourceMesh( Mesh=SOV_Rezervisti_2 )
export Modele_MANPAD_Strela_3_SOV_03 is TResourceMesh( Mesh=SOV_Rezervisti_3 )
export Modele_MANPAD_Strela_3_SOV_LOW is TResourceMesh( Mesh=SOV_Rezervisti_LOW )

//MANPAD_Igla_SOV
export Modele_MANPAD_Igla_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_MANPAD_Igla_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_MANPAD_Igla_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_MANPAD_Igla_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_MANPAD_Igla_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//FireSupport_TTsko_SOV
export Modele_FireSupport_TTsko_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_FireSupport_TTsko_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_FireSupport_TTsko_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_FireSupport_TTsko_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_NCO )
export Modele_FireSupport_TTsko_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_spe )
export Modele_FireSupport_TTsko_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_FireSupport_TTsko_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//FireSupport_SOV
export Modele_FireSupport_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_FireSupport_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//Reserve_SOV
export Modele_Reserve_SOV is TResourceMesh( Mesh=SOV_Rezervisti_1 )
export Modele_Reserve_SOV_02 is TResourceMesh( Mesh=SOV_Rezervisti_2 )
export Modele_Reserve_SOV_03 is TResourceMesh( Mesh=SOV_Rezervisti_3 )
export Modele_Reserve_SOV_LOW is TResourceMesh( Mesh=SOV_Rezervisti_LOW )

//MotRifles_BTR_TTsko_SOV
export Modele_MotRifles_BTR_TTsko_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_MotRifles_BTR_TTsko_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_MotRifles_BTR_TTsko_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_MotRifles_BTR_TTsko_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_NCO )
export Modele_MotRifles_BTR_TTsko_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_spe )
export Modele_MotRifles_BTR_TTsko_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_MotRifles_BTR_TTsko_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//MotRifles_BTR_SOV
export Modele_MotRifles_BTR_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_MotRifles_BTR_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_MotRifles_BTR_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_MotRifles_BTR_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_MotRifles_BTR_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_MotRifles_BTR_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//MotRifles_Metis_TTsko_SOV
export Modele_MotRifles_Metis_TTsko_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_MotRifles_Metis_TTsko_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_MotRifles_Metis_TTsko_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_MotRifles_Metis_TTsko_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_NCO )
export Modele_MotRifles_Metis_TTsko_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_spe )
export Modele_MotRifles_Metis_TTsko_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_MotRifles_Metis_TTsko_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//MotRifles_Metis_SOV
export Modele_MotRifles_Metis_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_MotRifles_Metis_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_MotRifles_Metis_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_MotRifles_Metis_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_MotRifles_Metis_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_MotRifles_Metis_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//MotRifles_HMG_TTsko_SOV
export Modele_MotRifles_HMG_TTsko_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_MotRifles_HMG_TTsko_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_MotRifles_HMG_TTsko_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_MotRifles_HMG_TTsko_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_NCO )
export Modele_MotRifles_HMG_TTsko_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_spe )
export Modele_MotRifles_HMG_TTsko_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_MotRifles_HMG_TTsko_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//MotRifles_HMG_SOV
export Modele_MotRifles_HMG_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_MotRifles_HMG_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_MotRifles_HMG_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_MotRifles_HMG_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_MotRifles_HMG_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_MotRifles_HMG_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//Tuto_MotRifles_SOV
export Modele_Tuto_MotRifles_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_Tuto_MotRifles_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_Tuto_MotRifles_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_Tuto_MotRifles_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_Officer )
export Modele_Tuto_MotRifles_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_Tuto_MotRifles_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_Tuto_MotRifles_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//MotRifles_TTsko_SOV
export Modele_MotRifles_TTsko_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_MotRifles_TTsko_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_MotRifles_TTsko_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_MotRifles_TTsko_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_NCO )
export Modele_MotRifles_TTsko_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_spe )
export Modele_MotRifles_TTsko_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_MotRifles_TTsko_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//MotRifles_RPG22_SOV
export Modele_MotRifles_RPG22_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_MotRifles_RPG22_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_MotRifles_RPG22_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_MotRifles_RPG22_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_MotRifles_RPG22_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_MotRifles_RPG22_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//Security_Reserve_SOV
export Modele_Security_Reserve_SOV is TResourceMesh( Mesh=SOV_Rezervisti_1 )
export Modele_Security_Reserve_SOV_02 is TResourceMesh( Mesh=SOV_Rezervisti_3 )
export Modele_Security_Reserve_SOV_03 is TResourceMesh( Mesh=SOV_Rezervisti_2 )
export Modele_Security_Reserve_SOV_LOW is TResourceMesh( Mesh=SOV_Rezervisti_LOW )

//Security_SOV
export Modele_Security_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_Security_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_Security_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_Security_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_Security_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//MotRifles_SOV
export Modele_MotRifles_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_MotRifles_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_MotRifles_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_MotRifles_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//Scout_Spetsnaz_SOV
export Modele_Scout_Spetsnaz_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_Officer )
export Modele_Scout_Spetsnaz_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Scout_Spetsnaz_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_Scout_Spetsnaz_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_Scout_Spetsnaz_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_Scout_Spetsnaz_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_Scout_Spetsnaz_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//Scout_SpetsnazGRU_Stinger_SOV
export Modele_Scout_SpetsnazGRU_Stinger_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Scout_SpetsnazGRU_Stinger_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Scout_SpetsnazGRU_Stinger_SOV_03 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Scout_SpetsnazGRU_Stinger_SOV_04 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Scout_SpetsnazGRU_Stinger_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Scout_Spetsnaz_VDV_SOV
export Modele_Scout_Spetsnaz_VDV_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Scout_Spetsnaz_VDV_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Scout_Spetsnaz_VDV_SOV_03 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Scout_Spetsnaz_VDV_SOV_04 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Scout_Spetsnaz_VDV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Sniper_Spetsnaz_SOV
export Modele_Sniper_Spetsnaz_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Sniper_Spetsnaz_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Sniper_Spetsnaz_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Spetsnaz_CMD_SOV
export Modele_Spetsnaz_CMD_SOV is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_Spetsnaz_CMD_SOV_02 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Spetsnaz_CMD_SOV_03 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Spetsnaz_CMD_SOV_04 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Spetsnaz_CMD_SOV_05 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_Spetsnaz_CMD_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Spetsnaz_FireSupport_SOV
export Modele_Spetsnaz_FireSupport_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Spetsnaz_FireSupport_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Spetsnaz_FireSupport_SOV_03 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Spetsnaz_FireSupport_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Spetsnaz_FireSupport_SOV_05 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_Spetsnaz_FireSupport_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Spetsnaz_Vympel_SOV
export Modele_Spetsnaz_Vympel_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Spetsnaz_Vympel_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Spetsnaz_Vympel_SOV_03 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Spetsnaz_Vympel_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Spetsnaz_Vympel_SOV_05 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_Spetsnaz_Vympel_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Spetsnaz_SOV
export Modele_Spetsnaz_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Spetsnaz_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Spetsnaz_SOV_03 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Spetsnaz_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Spetsnaz_SOV_05 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_Spetsnaz_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Scout_LRRP_SOV
export Modele_Scout_LRRP_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_Scout_LRRP_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_Scout_LRRP_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_Scout_LRRP_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_Scout_LRRP_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//Scout_TTsko_SOV
export Modele_Scout_TTsko_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_Scout_TTsko_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_Scout_TTsko_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_Scout_TTsko_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_NCO )
export Modele_Scout_TTsko_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_spe )
export Modele_Scout_TTsko_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_Scout_TTsko_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//Scout_SIGINT_SOV
export Modele_Scout_SIGINT_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_Scout_SIGINT_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_Scout_SIGINT_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_Scout_SIGINT_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_Scout_SIGINT_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//Scout_SOV
export Modele_Scout_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_Scout_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_Scout_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_Scout_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_Scout_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//MotoStrelki_SOV
export Modele_MotoStrelki_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_MotoStrelki_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_MotoStrelki_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_MotoStrelki_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_Officer )
export Modele_MotoStrelki_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_MotoStrelki_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_MotoStrelki_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//Engineers_CMD_TTsko_SOV
export Modele_Engineers_CMD_TTsko_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_Officer )
export Modele_Engineers_CMD_TTsko_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_NCO )
export Modele_Engineers_CMD_TTsko_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_Engineers_CMD_TTsko_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_Engineers_CMD_TTsko_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//Engineers_CMD_SOV
export Modele_Engineers_CMD_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_Officer )
export Modele_Engineers_CMD_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_Engineers_CMD_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_Engineers_CMD_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_Engineers_CMD_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//MotRifles_CMD_TTsko_SOV
export Modele_MotRifles_CMD_TTsko_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_Officer )
export Modele_MotRifles_CMD_TTsko_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_NCO )
export Modele_MotRifles_CMD_TTsko_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_MotRifles_CMD_TTsko_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_MotRifles_CMD_TTsko_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//Reserve_CMD_SOV
export Modele_Reserve_CMD_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_Officer )
export Modele_Reserve_CMD_SOV_02 is TResourceMesh( Mesh=SOV_Rezervisti_1 )
export Modele_Reserve_CMD_SOV_03 is TResourceMesh( Mesh=SOV_Rezervisti_2 )
export Modele_Reserve_CMD_SOV_04 is TResourceMesh( Mesh=SOV_Rezervisti_3 )
export Modele_Reserve_CMD_SOV_LOW is TResourceMesh( Mesh=SOV_Rezervisti_LOW )

//MotRifles_CMD_SOV
export Modele_MotRifles_CMD_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_Officer )
export Modele_MotRifles_CMD_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_MotRifles_CMD_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_MotRifles_CMD_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_MotRifles_CMD_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//DShV_CMD_SOV
export Modele_DShV_CMD_SOV is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_DShV_CMD_SOV_02 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_DShV_CMD_SOV_03 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_DShV_CMD_SOV_04 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_DShV_CMD_SOV_05 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_DShV_CMD_SOV_06 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_DShV_CMD_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//DShV_RPG16_SOV
export Modele_DShV_RPG16_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_DShV_RPG16_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_DShV_RPG16_SOV_03 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_DShV_RPG16_SOV_04 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_DShV_RPG16_SOV_05 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_DShV_RPG16_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//DShV_Hvy_SOV
export Modele_DShV_Hvy_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_DShV_Hvy_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_DShV_Hvy_SOV_03 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_DShV_Hvy_SOV_04 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_DShV_Hvy_SOV_05 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_DShV_Hvy_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//DShV_SOV
export Modele_DShV_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_DShV_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_DShV_SOV_03 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_DShV_SOV_04 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_DShV_SOV_05 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_DShV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//HvyScout_DShV_SOV
export Modele_HvyScout_DShV_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_HvyScout_DShV_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_HvyScout_DShV_SOV_03 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_HvyScout_DShV_SOV_04 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_HvyScout_DShV_SOV_05 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_HvyScout_DShV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//VDV_CMD_SOV
export Modele_VDV_CMD_SOV is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_VDV_CMD_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_VDV_CMD_SOV_03 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_VDV_CMD_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_VDV_CMD_SOV_05 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_VDV_CMD_SOV_06 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_VDV_CMD_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Engineers_CMD_DShV_SOV
export Modele_Engineers_CMD_DShV_SOV is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_Engineers_CMD_DShV_SOV_02 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Engineers_CMD_DShV_SOV_03 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Engineers_CMD_DShV_SOV_04 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Engineers_CMD_DShV_SOV_05 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Engineers_CMD_DShV_SOV_06 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_Engineers_CMD_DShV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Engineers_CMD_VDV_SOV
export Modele_Engineers_CMD_VDV_SOV is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Engineers_CMD_VDV_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Engineers_CMD_VDV_SOV_03 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Engineers_CMD_VDV_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_Engineers_CMD_VDV_SOV_05 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Engineers_CMD_VDV_SOV_06 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_Engineers_CMD_VDV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//DShV_HMG_SOV
export Modele_DShV_HMG_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_DShV_HMG_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_DShV_HMG_SOV_03 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_DShV_HMG_SOV_04 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_DShV_HMG_SOV_05 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_DShV_HMG_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//VDV_HMG_SOV
export Modele_VDV_HMG_SOV is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_VDV_HMG_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_VDV_HMG_SOV_03 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_VDV_HMG_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_VDV_HMG_SOV_05 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_VDV_HMG_SOV_06 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_VDV_HMG_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//DShV_Afgantsy_SOV
export Modele_DShV_Afgantsy_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_DShV_Afgantsy_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_DShV_Afgantsy_SOV_03 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_DShV_Afgantsy_SOV_04 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_DShV_Afgantsy_SOV_05 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_DShV_Afgantsy_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//VDV_Afgantsy_SOV
export Modele_VDV_Afgantsy_SOV is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_VDV_Afgantsy_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_VDV_Afgantsy_SOV_03 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_VDV_Afgantsy_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_VDV_Afgantsy_SOV_05 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_VDV_Afgantsy_SOV_06 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_VDV_Afgantsy_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//VDV_SOV
export Modele_VDV_SOV is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_VDV_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_VDV_SOV_03 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_VDV_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_VDV_SOV_05 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_VDV_SOV_06 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_VDV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Engineers_Flam_DShV_SOV
export Modele_Engineers_Flam_DShV_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Engineers_Flam_DShV_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Engineers_Flam_DShV_SOV_03 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_Engineers_Flam_DShV_SOV_04 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Engineers_Flam_DShV_SOV_05 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Engineers_Flam_DShV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Engineers_Flam_VDV_SOV
export Modele_Engineers_Flam_VDV_SOV is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Engineers_Flam_VDV_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Engineers_Flam_VDV_SOV_03 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Engineers_Flam_VDV_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_Engineers_Flam_VDV_SOV_05 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Engineers_Flam_VDV_SOV_06 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_Engineers_Flam_VDV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//DShV_Metis_SOV
export Modele_DShV_Metis_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_DShV_Metis_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_DShV_Metis_SOV_03 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_DShV_Metis_SOV_04 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_DShV_Metis_SOV_05 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_DShV_Metis_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//VDV_Metis_SOV
export Modele_VDV_Metis_SOV is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_VDV_Metis_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_VDV_Metis_SOV_03 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_VDV_Metis_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_VDV_Metis_SOV_05 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_VDV_Metis_SOV_06 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_VDV_Metis_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Engineers_DShV_SOV
export Modele_Engineers_DShV_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Engineers_DShV_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Engineers_DShV_SOV_03 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_Engineers_DShV_SOV_04 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Engineers_DShV_SOV_05 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Engineers_DShV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Engineers_VDV_SOV
export Modele_Engineers_VDV_SOV is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Engineers_VDV_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Engineers_VDV_SOV_03 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Engineers_VDV_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_Engineers_VDV_SOV_05 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Engineers_VDV_SOV_06 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_Engineers_VDV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//MANPAD_Igla_DShV_SOV
export Modele_MANPAD_Igla_DShV_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_MANPAD_Igla_DShV_SOV_01 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_MANPAD_Igla_DShV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//MANPAD_Igla_VDV_SOV
export Modele_MANPAD_Igla_VDV_SOV is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_MANPAD_Igla_VDV_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_MANPAD_Igla_VDV_SOV_03 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_MANPAD_Igla_VDV_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_MANPAD_Igla_VDV_SOV_05 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_MANPAD_Igla_VDV_SOV_06 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_MANPAD_Igla_VDV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//VDV_Konkurs_SOV
export Modele_VDV_Konkurs_SOV is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_VDV_Konkurs_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_VDV_Konkurs_SOV_03 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_VDV_Konkurs_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_VDV_Konkurs_SOV_05 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_VDV_Konkurs_SOV_06 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_VDV_Konkurs_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Moto_Scout_SOV
export Modele_Moto_Scout_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_Moto_Scout_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_Moto_Scout_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_Moto_Scout_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_Officer )
export Modele_Moto_Scout_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_Sniper )
export Modele_Moto_Scout_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_Moto_Scout_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//VDV_Combine_SOV
export Modele_VDV_Combine_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_VDV_Combine_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_VDV_Combine_SOV_03 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_VDV_Combine_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_VDV_Combine_SOV_05 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_VDV_Combine_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//VDV_Mech_SOV
export Modele_VDV_Mech_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_VDV_Mech_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_VDV_Mech_SOV_03 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_VDV_Mech_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_VDV_Mech_SOV_05 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_VDV_Mech_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//HvyScout_TTsko_SOV
export Modele_HvyScout_TTsko_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_spe )
export Modele_HvyScout_TTsko_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_HvyScout_TTsko_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_2_TTsko )
export Modele_HvyScout_TTsko_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_NCO )
export Modele_HvyScout_TTsko_SOV_05 is TResourceMesh( Mesh=SOV_MotoStrelki_TTsko_spe )
export Modele_HvyScout_TTsko_SOV_06 is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko )
export Modele_HvyScout_TTsko_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_TTsko_LOW )

//HvyScout_Reserve_SOV
export Modele_HvyScout_Reserve_SOV is TResourceMesh( Mesh=SOV_Rezervisti_1 )
export Modele_HvyScout_Reserve_SOV_02 is TResourceMesh( Mesh=SOV_Rezervisti_2 )
export Modele_HvyScout_Reserve_SOV_03 is TResourceMesh( Mesh=SOV_Rezervisti_3 )
export Modele_HvyScout_Reserve_SOV_LOW is TResourceMesh( Mesh=SOV_Rezervisti_LOW )

//HvyScout_SOV
export Modele_HvyScout_SOV is TResourceMesh( Mesh=SOV_MotoStrelki_NCO )
export Modele_HvyScout_SOV_02 is TResourceMesh( Mesh=SOV_MotoStrelki_2 )
export Modele_HvyScout_SOV_03 is TResourceMesh( Mesh=SOV_MotoStrelki_1 )
export Modele_HvyScout_SOV_04 is TResourceMesh( Mesh=SOV_MotoStrelki_spe )
export Modele_HvyScout_SOV_LOW is TResourceMesh( Mesh=SOV_MotoStrelki_1_LOW )

//Scout_DShV_SOV
export Modele_Scout_DShV_SOV is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Scout_DShV_SOV_02 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Scout_DShV_SOV_03 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_Scout_DShV_SOV_04 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Scout_DShV_SOV_05 is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Scout_DShV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )

//Scout_VDV_SOV
export Modele_Scout_VDV_SOV is TResourceMesh( Mesh=SOV_VDV_1 )
export Modele_Scout_VDV_SOV_02 is TResourceMesh( Mesh=SOV_VDV_2 )
export Modele_Scout_VDV_SOV_03 is TResourceMesh( Mesh=SOV_VDV_NCO )
export Modele_Scout_VDV_SOV_04 is TResourceMesh( Mesh=SOV_VDV_Officer )
export Modele_Scout_VDV_SOV_05 is TResourceMesh( Mesh=SOV_VDV_Sniper )
export Modele_Scout_VDV_SOV_06 is TResourceMesh( Mesh=SOV_VDV_spe )
export Modele_Scout_VDV_SOV_LOW is TResourceMesh( Mesh=SOV_VDV_1_LOW )


//------------------------------------------------------------------------------
//-----------------------------------SOLDATS USA--------------------------------
//------------------------------------------------------------------------------
US_GI_1 is 'GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx'
US_GI_2 is 'GameData:/Assets/3D/Units/US/Infanterie/GI/GI_2.fbx'
US_GI_NCO is 'GameData:/Assets/3D/Units/US/Infanterie/GI/GI_NCO.fbx'
US_GI_Officer is 'GameData:/Assets/3D/Units/US/Infanterie/GI/GI_Officer.fbx'
US_GI_Sniper is 'GameData:/Assets/3D/Units/US/Infanterie/GI/GI_Sniper.fbx'
US_GI_spe is 'GameData:/Assets/3D/Units/US/Infanterie/GI/GI_spe.fbx'
US_GI_1_LOW is 'GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1_LOW.fbx'

US_AB_1 is 'GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_1.fbx'
US_AB_2 is 'GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_2.fbx'
US_AB_NCO is 'GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_NCO.fbx'
US_AB_Officer is 'GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_Officer.fbx'
US_AB_Sniper is 'GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_Sniper.fbx'
US_AB_spe is 'GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_spe.fbx'

US_Rangers_1 is 'GameData:/Assets/3D/Units/US/Infanterie/Rangers/Ranger_1.fbx'
US_Rangers_NCO is  'GameData:/Assets/3D/Units/US/Infanterie/Rangers/Ranger_NCO.fbx'

US_GB_1 is 'GameData:/Assets/3D/Units/US/Infanterie/GreenBerets/GreenBeret_1.fbx'
US_GB_NCO is 'GameData:/Assets/3D/Units/US/Infanterie/GreenBerets/GreenBeret_NCO.fbx'
US_GB_1_LOW is 'GameData:/Assets/3D/Units/US/Infanterie/GreenBerets/GreenBeret_1_LOW.fbx'

NatGuard_1 is       'GameData:/Assets/3D/Units/US/Infanterie/NationalGuard/NatGuard_1.fbx'
NatGuard_2 is       'GameData:/Assets/3D/Units/US/Infanterie/NationalGuard/NatGuard_2.fbx'
NatGuard_3 is       'GameData:/Assets/3D/Units/US/Infanterie/NationalGuard/NatGuard_3.fbx'
NatGuard_NCO is       'GameData:/Assets/3D/Units/US/Infanterie/NationalGuard/NatGuard_NCO.fbx'

US_MP_1 is 'GameData:/Assets/3D/Units/US/Infanterie/MP/MP_1.fbx'
US_MP_2 is 'GameData:/Assets/3D/Units/US/Infanterie/MP/MP_2.fbx'

US_Delta_1 is 'GameData:/Assets/3D/Units/US/Infanterie/Airborne/Delta_1.fbx'
US_Delta_2 is 'GameData:/Assets/3D/Units/US/Infanterie/Airborne/Delta_2.fbx'
US_Delta_NCO is 'GameData:/Assets/3D/Units/US/Infanterie/GreenBerets/Delta_NCO.fbx'
US_Delta_1_LOW is 'GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1_LOW.fbx'

US_USMC_NCO_1 is 'GameData:/Assets/3D/Units/US/Infanterie/USMC/USMC_NCO_1.fbx'

US_AlaskanScout_1 is 'GameData:/Assets/3D/Units/US/Infanterie/AlaskanScout/AlaskanScout_1.fbx'
US_AlaskanScout_2 is 'GameData:/Assets/3D/Units/US/Infanterie/AlaskanScout/AlaskanScout_2.fbx'
US_AlaskanScout_NCO is 'GameData:/Assets/3D/Units/US/Infanterie/AlaskanScout/AlaskanScout_NCO.fbx'

US_SEAL_1 is 'GameData:/Assets/3D/Units/US/Infanterie/NAVY_SEAL/SEAL_1.fbx'
US_SEAL_2 is 'GameData:/Assets/3D/Units/US/Infanterie/NAVY_SEAL/SEAL_2.fbx'

US_Pathfinder_1 is 'GameData:/Assets/3D/Units/US/Infanterie/Pathfinder/PathfinderUS_1.fbx'

US_USAF_Sec_1 is 'GameData:/Assets/3D/Units/US/Infanterie/USAF_SECURITY/USAF_Sec_1.fbx'
US_USAF_Sec_NCO is 'GameData:/Assets/3D/Units/US/Infanterie/USAF_SECURITY/USAF_Sec_NCO.fbx'

//------------------------------------------------------------------------------
//Scout_NG_Alaska_US
export Modele_Scout_NG_Alaska_US is TResourceMesh( Mesh=US_AlaskanScout_1 )
export Modele_Scout_NG_Alaska_US_02 is TResourceMesh( Mesh=US_AlaskanScout_2 )
export Modele_Scout_NG_Alaska_US_03 is TResourceMesh( Mesh=US_AlaskanScout_NCO )
export Modele_Scout_NG_Alaska_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Sniper_NG_Alaska_US
export Modele_Sniper_NG_Alaska_US is TResourceMesh( Mesh=US_AlaskanScout_2 )
export Modele_Sniper_NG_Alaska_US_02 is TResourceMesh( Mesh=US_AlaskanScout_NCO )
export Modele_Sniper_NG_Alaska_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//HvyScout_NG_Alaska_US
export Modele_HvyScout_NG_Alaska_US is TResourceMesh( Mesh=US_AlaskanScout_1 )
export Modele_HvyScout_NG_Alaska_US_02 is TResourceMesh( Mesh=US_AlaskanScout_2 )
export Modele_HvyScout_NG_Alaska_US_03 is TResourceMesh( Mesh=US_AlaskanScout_NCO )
export Modele_HvyScout_NG_Alaska_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Navy_SEAL_US
export Modele_Navy_SEAL_US is TResourceMesh( Mesh=US_Rangers_1 )
export Modele_Navy_SEAL_US_02 is TResourceMesh( Mesh=US_SEAL_2 )
export Modele_Navy_SEAL_US_03 is TResourceMesh( Mesh=US_SEAL_1 )
export Modele_Navy_SEAL_US_04 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Navy_SEAL_US_LOW is TResourceMesh( Mesh=US_Delta_1_LOW )

//Pathfinder_NG_US
export Modele_Pathfinder_NG_US is TResourceMesh( Mesh=US_Pathfinder_1 )
export Modele_Pathfinder_NG_US_02 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Pathfinder_NG_US_03 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Pathfinder_NG_US_04 is TResourceMesh( Mesh=US_Pathfinder_1 )
export Modele_Pathfinder_NG_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MP_Combat_USAF_US
export Modele_MP_Combat_USAF_US is TResourceMesh( Mesh=US_USAF_Sec_NCO )
export Modele_MP_Combat_USAF_US_02 is TResourceMesh( Mesh=US_USAF_Sec_1 )
export Modele_MP_Combat_USAF_US_03 is TResourceMesh( Mesh=US_GI_1 )
export Modele_MP_Combat_USAF_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_MP_Combat_USAF_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MP_Patrol_USAF_US
export Modele_MP_Patrol_USAF_US is TResourceMesh( Mesh=US_USAF_Sec_NCO )
export Modele_MP_Patrol_USAF_US_02 is TResourceMesh( Mesh=US_USAF_Sec_1 )
export Modele_MP_Patrol_USAF_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Security_USMC_US
export Modele_Security_USMC_US is TResourceMesh( Mesh=US_USMC_NCO_1 )
export Modele_Security_USMC_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Security_USMC_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Security_USMC_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Airborne_half_Dragon_US
export Modele_Airborne_half_Dragon_US is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_half_Dragon_US_02 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_half_Dragon_US_03 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Airborne_half_Dragon_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_half_Dragon_US_05 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_half_Dragon_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Airborne_half_M60_US
export Modele_Airborne_half_M60_US is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_half_M60_US_02 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_half_M60_US_03 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Airborne_half_M60_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_half_M60_US_05 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_half_M60_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Aero_half_CMD_US
export Modele_Aero_half_CMD_US is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Aero_half_CMD_US_02 is TResourceMesh( Mesh=US_AB_Officer )
export Modele_Aero_half_CMD_US_03 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Aero_half_CMD_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Aero_half_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Airborne_CMD_US
export Modele_Airborne_CMD_US is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_CMD_US_02 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_CMD_US_03 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_CMD_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_CMD_US_05 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_CMD_US_06 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_CMD_US_07 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_CMD_US_08 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_CMD_US_09 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Airborne_CMD_US_10 is TResourceMesh( Mesh=US_AB_Officer )
export Modele_Airborne_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Airborne_Dragon_US
export Modele_Airborne_Dragon_US is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_Dragon_US_02 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_Dragon_US_03 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_Dragon_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_Dragon_US_05 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_Dragon_US_06 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_Dragon_US_07 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_Dragon_US_08 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_Dragon_US_09 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Airborne_Dragon_US_10 is TResourceMesh( Mesh=US_AB_Officer )
export Modele_Airborne_Dragon_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//LRRP_Aero_US
export Modele_LRRP_Aero_US is TResourceMesh( Mesh=US_AB_NCO )
export Modele_LRRP_Aero_US_02 is TResourceMesh( Mesh=US_AB_spe )
export Modele_LRRP_Aero_US_03 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_LRRP_Aero_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_LRRP_Aero_US_05 is TResourceMesh( Mesh=US_AB_1 )
export Modele_LRRP_Aero_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Scout_Aero_US
export Modele_Scout_Aero_US is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Scout_Aero_US_02 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Scout_Aero_US_03 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Scout_Aero_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Scout_Aero_US_05 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Scout_Aero_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Airborne_Scout_US
export Modele_Airborne_Scout_US is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_Scout_US_02 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_Scout_US_03 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_Scout_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_Scout_US_05 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_Scout_US_06 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_Scout_US_07 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_Scout_US_08 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_Scout_US_09 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Airborne_Scout_US_10 is TResourceMesh( Mesh=US_AB_Officer )
export Modele_Airborne_Scout_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Airborne_HMG_US
export Modele_Airborne_HMG_US is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_HMG_US_02 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_HMG_US_03 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_HMG_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_HMG_US_05 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_HMG_US_06 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_HMG_US_07 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_HMG_US_08 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_HMG_US_09 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Airborne_HMG_US_10 is TResourceMesh( Mesh=US_AB_Officer )
export Modele_Airborne_HMG_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Airborne_MP_RCL_US
export Modele_Airborne_MP_RCL_US is TResourceMesh( Mesh=US_MP_1 )
export Modele_Airborne_MP_RCL_US_02 is TResourceMesh( Mesh=US_MP_2 )
export Modele_Airborne_MP_RCL_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Airborne_MP_US
export Modele_Airborne_MP_US is TResourceMesh( Mesh=US_MP_1 )
export Modele_Airborne_MP_US_02 is TResourceMesh( Mesh=US_MP_2 )
export Modele_Airborne_MP_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//AeroEngineer_CMD_US
export Modele_AeroEngineer_CMD_US is TResourceMesh( Mesh=US_AB_NCO )
export Modele_AeroEngineer_CMD_US_02 is TResourceMesh( Mesh=US_AB_Officer )
export Modele_AeroEngineer_CMD_US_03 is TResourceMesh( Mesh=US_AB_1 )
export Modele_AeroEngineer_CMD_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_AeroEngineer_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Airborne_Engineer_CMD_US
export Modele_Airborne_Engineer_CMD_US is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_Engineer_CMD_US_02 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_Engineer_CMD_US_03 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_Engineer_CMD_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_Engineer_CMD_US_05 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_Engineer_CMD_US_06 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_Engineer_CMD_US_07 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_Engineer_CMD_US_08 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_Engineer_CMD_US_09 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Airborne_Engineer_CMD_US_10 is TResourceMesh( Mesh=US_AB_Officer )
export Modele_Airborne_Engineer_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Airborne_Engineers_Flash_US
export Modele_Airborne_Engineers_Flash_US is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_Engineers_Flash_US_02 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_Engineers_Flash_US_03 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_Engineers_Flash_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_Engineers_Flash_US_05 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_Engineers_Flash_US_06 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_Engineers_Flash_US_07 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_Engineers_Flash_US_08 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_Engineers_Flash_US_09 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Airborne_Engineers_Flash_US_10 is TResourceMesh( Mesh=US_AB_Officer )
export Modele_Airborne_Engineers_Flash_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//AeroEngineers_US
export Modele_AeroEngineers_US is TResourceMesh( Mesh=US_AB_NCO )
export Modele_AeroEngineers_US_02 is TResourceMesh( Mesh=US_AB_spe )
export Modele_AeroEngineers_US_03 is TResourceMesh( Mesh=US_AB_1 )
export Modele_AeroEngineers_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_AeroEngineers_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Airborne_Engineers_US
export Modele_Airborne_Engineers_US is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_Engineers_US_02 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_Engineers_US_03 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_Engineers_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_Engineers_US_05 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_Engineers_US_06 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_Engineers_US_07 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_Engineers_US_08 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_Engineers_US_09 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Airborne_Engineers_US_10 is TResourceMesh( Mesh=US_AB_Officer )
export Modele_Airborne_Engineers_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//AeroRifles_AB_US
export Modele_AeroRifles_AB_US is TResourceMesh( Mesh=US_AB_NCO )
export Modele_AeroRifles_AB_US_02 is TResourceMesh( Mesh=US_AB_spe )
export Modele_AeroRifles_AB_US_03 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_AeroRifles_AB_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_AeroRifles_AB_US_05 is TResourceMesh( Mesh=US_AB_1 )
export Modele_AeroRifles_AB_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//AeroRifles_AT4_US
export Modele_AeroRifles_AT4_US is TResourceMesh( Mesh=US_AB_NCO )
export Modele_AeroRifles_AT4_US_02 is TResourceMesh( Mesh=US_AB_spe )
export Modele_AeroRifles_AT4_US_03 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_AeroRifles_AT4_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_AeroRifles_AT4_US_05 is TResourceMesh( Mesh=US_AB_1 )
export Modele_AeroRifles_AT4_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//AeroRifles_Dragon_US
export Modele_AeroRifles_Dragon_US is TResourceMesh( Mesh=US_AB_NCO )
export Modele_AeroRifles_Dragon_US_02 is TResourceMesh( Mesh=US_AB_spe )
export Modele_AeroRifles_Dragon_US_03 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_AeroRifles_Dragon_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_AeroRifles_Dragon_US_05 is TResourceMesh( Mesh=US_AB_1 )
export Modele_AeroRifles_Dragon_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Aero_half_AT4_US
export Modele_Aero_half_AT4_US is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Aero_half_AT4_US_02 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Aero_half_AT4_US_03 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Aero_half_AT4_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Aero_half_AT4_US_05 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Aero_half_AT4_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Aero_half_Dragon_US
export Modele_Aero_half_Dragon_US is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Aero_half_Dragon_US_02 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Aero_half_Dragon_US_03 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Aero_half_Dragon_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Aero_half_Dragon_US_05 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Aero_half_Dragon_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Airborne_US
export Modele_Airborne_US is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_US_02 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_US_03 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_US_05 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_US_06 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_US_07 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_US_08 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_US_09 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Airborne_US_10 is TResourceMesh( Mesh=US_AB_Officer )
export Modele_Airborne_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Airborne_half_LAW_US
export Modele_Airborne_half_LAW_US is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_half_LAW_US_02 is TResourceMesh( Mesh=US_AB_1 )
export Modele_Airborne_half_LAW_US_03 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_half_LAW_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_Airborne_half_LAW_US_05 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_half_LAW_US_06 is TResourceMesh( Mesh=US_AB_NCO )
export Modele_Airborne_half_LAW_US_07 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_half_LAW_US_08 is TResourceMesh( Mesh=US_AB_spe )
export Modele_Airborne_half_LAW_US_09 is TResourceMesh( Mesh=US_AB_Sniper )
export Modele_Airborne_half_LAW_US_10 is TResourceMesh( Mesh=US_AB_Officer )
export Modele_Airborne_half_LAW_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//AeroRifles_US
export Modele_AeroRifles_US is TResourceMesh( Mesh=US_GI_NCO )
export Modele_AeroRifles_US_02 is TResourceMesh( Mesh=US_GI_spe )
export Modele_AeroRifles_US_03 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_AeroRifles_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_AeroRifles_US_05 is TResourceMesh( Mesh=US_GI_1 )
export Modele_AeroRifles_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//LightRifles_CMD_US
export Modele_LightRifles_CMD_US is TResourceMesh( Mesh=US_GI_Officer )
export Modele_LightRifles_CMD_US_02 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_LightRifles_CMD_US_03 is TResourceMesh( Mesh=US_GI_1 )
export Modele_LightRifles_CMD_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_LightRifles_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//LightRifles_Dragon_US
export Modele_LightRifles_Dragon_US is TResourceMesh( Mesh=US_GI_NCO )
export Modele_LightRifles_Dragon_US_02 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_LightRifles_Dragon_US_03 is TResourceMesh( Mesh=US_GI_spe )
export Modele_LightRifles_Dragon_US_04 is TResourceMesh( Mesh=US_GI_1 )
export Modele_LightRifles_Dragon_US_05 is TResourceMesh( Mesh=US_GI_2 )
export Modele_LightRifles_Dragon_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//LightRifles_AT4_US
export Modele_LightRifles_AT4_US is TResourceMesh( Mesh=US_GI_NCO )
export Modele_LightRifles_AT4_US_02 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_LightRifles_AT4_US_03 is TResourceMesh( Mesh=US_GI_spe )
export Modele_LightRifles_AT4_US_04 is TResourceMesh( Mesh=US_GI_1 )
export Modele_LightRifles_AT4_US_05 is TResourceMesh( Mesh=US_GI_2 )
export Modele_LightRifles_AT4_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//LightRifles_Viper_US
export Modele_LightRifles_Viper_US is TResourceMesh( Mesh=US_GI_NCO )
export Modele_LightRifles_Viper_US_02 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_LightRifles_Viper_US_03 is TResourceMesh( Mesh=US_GI_spe )
export Modele_LightRifles_Viper_US_04 is TResourceMesh( Mesh=US_GI_1 )
export Modele_LightRifles_Viper_US_05 is TResourceMesh( Mesh=US_GI_2 )
export Modele_LightRifles_Viper_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//LightRifles_LAW_US
export Modele_LightRifles_LAW_US is TResourceMesh( Mesh=US_GI_NCO )
export Modele_LightRifles_LAW_US_02 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_LightRifles_LAW_US_03 is TResourceMesh( Mesh=US_GI_spe )
export Modele_LightRifles_LAW_US_04 is TResourceMesh( Mesh=US_GI_1 )
export Modele_LightRifles_LAW_US_05 is TResourceMesh( Mesh=US_GI_2 )
export Modele_LightRifles_LAW_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//LightRifles_RCL_US
export Modele_LightRifles_RCL_US is TResourceMesh( Mesh=US_GI_NCO )
export Modele_LightRifles_RCL_US_02 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_LightRifles_RCL_US_03 is TResourceMesh( Mesh=US_GI_spe )
export Modele_LightRifles_RCL_US_04 is TResourceMesh( Mesh=US_GI_1 )
export Modele_LightRifles_RCL_US_05 is TResourceMesh( Mesh=US_GI_2 )
export Modele_LightRifles_RCL_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rangers_CMD_US
export Modele_Rangers_CMD_US is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Rangers_CMD_US_02 is TResourceMesh( Mesh=US_Rangers_NCO )
export Modele_Rangers_CMD_US_03 is TResourceMesh( Mesh=US_Rangers_1 )
export Modele_Rangers_CMD_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rangers_CMD_US_05 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rangers_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Ranger_tuto_US
export Modele_Ranger_tuto_US is TResourceMesh( Mesh=US_Rangers_NCO )
export Modele_Ranger_tuto_US_02 is TResourceMesh( Mesh=US_Rangers_1 )
export Modele_Ranger_tuto_US_03 is TResourceMesh( Mesh=US_Rangers_1 )
export Modele_Ranger_tuto_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Ranger_tuto_US_05 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Ranger_tuto_US_06 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Ranger_tuto_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Ranger_Dragon_US
export Modele_Ranger_Dragon_US is TResourceMesh( Mesh=US_Rangers_NCO )
export Modele_Ranger_Dragon_US_02 is TResourceMesh( Mesh=US_Rangers_1 )
export Modele_Ranger_Dragon_US_03 is TResourceMesh( Mesh=US_Rangers_1 )
export Modele_Ranger_Dragon_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Ranger_Dragon_US_05 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Ranger_Dragon_US_06 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Ranger_Dragon_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Ranger_US
export Modele_Ranger_US is TResourceMesh( Mesh=US_Rangers_NCO )
export Modele_Ranger_US_02 is TResourceMesh( Mesh=US_Rangers_1 )
export Modele_Ranger_US_03 is TResourceMesh( Mesh=US_Rangers_1 )
export Modele_Ranger_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Ranger_US_05 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Ranger_US_06 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Ranger_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//HMG_M2_US
export Modele_HMG_M2_US is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx" )
export Modele_HMG_M2_US_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_spe.fbx" )

//Mortier_81mm_US
export Modele_Mortier_81mm_US is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx" )
export Modele_Mortier_81mm_US_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_spe.fbx" )

//Mortier_107mm_US
//export Modele_Mortier_107mm_US is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx" )
//export Modele_Mortier_107mm_US_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_spe.fbx" )

//HMG_M60_US
export Modele_HMG_M60_US is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx" )
export Modele_HMG_M60_US_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_spe.fbx" )

//MP_RCL_US
export Modele_MP_RCL_US is TResourceMesh( Mesh=US_MP_1 )
export Modele_MP_RCL_US_02 is TResourceMesh( Mesh=US_MP_2 )
export Modele_MP_RCL_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MP_CMD_US
export Modele_MP_CMD_US is TResourceMesh( Mesh=US_MP_1 )
export Modele_MP_CMD_US_02 is TResourceMesh( Mesh=US_MP_2 )
export Modele_MP_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MP_Combat_US
export Modele_MP_Combat_US is TResourceMesh( Mesh=US_MP_1 )
export Modele_MP_Combat_US_02 is TResourceMesh( Mesh=US_MP_2 )
export Modele_MP_Combat_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MP_US
export Modele_MP_US is TResourceMesh( Mesh=US_MP_1 )
export Modele_MP_US_02 is TResourceMesh( Mesh=US_MP_2 )
export Modele_MP_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//NatGuard_CMD_US
export Modele_NatGuard_CMD_US is TResourceMesh( Mesh=NatGuard_1 )
export Modele_NatGuard_CMD_US_02 is TResourceMesh( Mesh=NatGuard_2 )
export Modele_NatGuard_CMD_US_03 is TResourceMesh( Mesh=NatGuard_3 )
export Modele_NatGuard_CMD_US_04 is TResourceMesh( Mesh=NatGuard_NCO )
export Modele_NatGuard_CMD_US_05 is TResourceMesh( Mesh=NatGuard_1 )
export Modele_NatGuard_CMD_US_06 is TResourceMesh( Mesh=NatGuard_2 )
export Modele_NatGuard_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//NatGuard_M67_US
export Modele_NatGuard_M67_US is TResourceMesh( Mesh=NatGuard_1 )
export Modele_NatGuard_M67_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//NatGuard_Engineers_CMD_US
export Modele_NatGuard_Engineers_CMD_US is TResourceMesh( Mesh=NatGuard_1 )
export Modele_NatGuard_Engineers_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//NatGuard_Engineers_Flam_US
export Modele_NatGuard_Engineers_Flam_US is TResourceMesh( Mesh=NatGuard_1 )
export Modele_NatGuard_Engineers_Flam_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//NatGuard_Engineers_M67_US
export Modele_NatGuard_Engineers_M67_US is TResourceMesh( Mesh=NatGuard_1 )
export Modele_NatGuard_Engineers_M67_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//NatGuard_Engineers_US
export Modele_NatGuard_Engineers_US is TResourceMesh( Mesh=NatGuard_1 )
export Modele_NatGuard_Engineers_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//NatGuard_Dragon_US
export Modele_NatGuard_Dragon_US is TResourceMesh( Mesh=NatGuard_1 )
export Modele_NatGuard_Dragon_US_02 is TResourceMesh( Mesh=NatGuard_2 )
export Modele_NatGuard_Dragon_US_03 is TResourceMesh( Mesh=NatGuard_3 )
export Modele_NatGuard_Dragon_US_04 is TResourceMesh( Mesh=NatGuard_NCO )
export Modele_NatGuard_Dragon_US_05 is TResourceMesh( Mesh=NatGuard_2 )
export Modele_NatGuard_Dragon_US_06 is TResourceMesh( Mesh=NatGuard_3 )
export Modele_NatGuard_Dragon_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//NatGuard_LAW_US
export Modele_NatGuard_LAW_US is TResourceMesh( Mesh=NatGuard_1 )
export Modele_NatGuard_LAW_US_02 is TResourceMesh( Mesh=NatGuard_2 )
export Modele_NatGuard_LAW_US_03 is TResourceMesh( Mesh=NatGuard_3 )
export Modele_NatGuard_LAW_US_04 is TResourceMesh( Mesh=NatGuard_NCO )
export Modele_NatGuard_LAW_US_05 is TResourceMesh( Mesh=NatGuard_1 )
export Modele_NatGuard_LAW_US_06 is TResourceMesh( Mesh=NatGuard_3 )
export Modele_NatGuard_LAW_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_CMD_US
export Modele_Rifles_CMD_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_CMD_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_CMD_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_CMD_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_CMD_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_CMD_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_CMD_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_CMD_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_CMD_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Rifles_CMD_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Rifles_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//GSR_US
export Modele_GSR_US is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx" )
export Modele_GSR_US_02 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx" )
export Modele_GSR_US_03 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_2.fbx" )
export Modele_GSR_US_04 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_NCO.fbx" )
export Modele_GSR_US_05 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_Officer.fbx" )
export Modele_GSR_US_06 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_Sniper.fbx" )
export Modele_GSR_US_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_spe.fbx" )

//GreenBerets_MP5_US
export Modele_GreenBerets_MP5_US is TResourceMesh( Mesh=Civil1_Mullet )
export Modele_GreenBerets_MP5_US_02 is TResourceMesh( Mesh=Civil3_Mannschaft )
export Modele_GreenBerets_MP5_US_03 is TResourceMesh( Mesh=Civil5_DoubleDenim )
export Modele_GreenBerets_MP5_US_04 is TResourceMesh( Mesh=Civil2_Trucker )
export Modele_GreenBerets_MP5_US_05 is TResourceMesh( Mesh=Civil7_Pastel_Male )
export Modele_GreenBerets_MP5_US_06 is TResourceMesh( Mesh=Civil6_Miami )
export Modele_GreenBerets_MP5_US_LOW is TResourceMesh( Mesh=Civil1_Mullet_LOW )

//StayBehind_BEL
export Modele_StayBehind_BEL is TResourceMesh( Mesh=Civil1_Mullet )
export Modele_StayBehind_BEL_LOW is TResourceMesh( Mesh=Civil1_Mullet_LOW )

//DeltaForce_US
export Modele_DeltaForce_US is TResourceMesh( Mesh=US_Delta_NCO )
export Modele_DeltaForce_US_02 is TResourceMesh( Mesh=US_Delta_2 )
export Modele_DeltaForce_US_03 is TResourceMesh( Mesh=US_Delta_1 )
export Modele_DeltaForce_US_LOW is TResourceMesh( Mesh=US_Delta_1_LOW )

//GreenBerets_ODA_US
export Modele_GreenBerets_ODA_US is TResourceMesh( Mesh=US_GB_NCO )
export Modele_GreenBerets_ODA_US_02 is TResourceMesh( Mesh=US_GB_NCO )
export Modele_GreenBerets_ODA_US_03 is TResourceMesh( Mesh=US_GB_1 )
export Modele_GreenBerets_ODA_US_04 is TResourceMesh( Mesh=US_Rangers_1 )
export Modele_GreenBerets_ODA_US_05 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_GreenBerets_ODA_US_LOW is TResourceMesh( Mesh=US_GB_1_LOW )

//GreenBerets_Demo_US
export Modele_GreenBerets_Demo_US is TResourceMesh( Mesh=US_GB_NCO )
export Modele_GreenBerets_Demo_US_02 is TResourceMesh( Mesh=US_GB_NCO )
export Modele_GreenBerets_Demo_US_03 is TResourceMesh( Mesh=US_GB_1 )
export Modele_GreenBerets_Demo_US_04 is TResourceMesh( Mesh=US_Rangers_1 )
export Modele_GreenBerets_Demo_US_05 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_GreenBerets_Demo_US_LOW is TResourceMesh( Mesh=US_GB_1_LOW )

//GreenBerets_US
export Modele_GreenBerets_US is TResourceMesh( Mesh=US_GB_NCO )
export Modele_GreenBerets_US_02 is TResourceMesh( Mesh=US_GB_NCO )
export Modele_GreenBerets_US_03 is TResourceMesh( Mesh=US_GB_1 )
export Modele_GreenBerets_US_04 is TResourceMesh( Mesh=US_Rangers_1 )
export Modele_GreenBerets_US_05 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_GreenBerets_US_LOW is TResourceMesh( Mesh=US_GB_1_LOW )

//LRRP_FOLT_US
export Modele_LRRP_FOLT_US is TResourceMesh( Mesh=US_Rangers_1 )
export Modele_LRRP_FOLT_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//LRRP_US
export Modele_LRRP_US is TResourceMesh( Mesh=US_Rangers_1 )
export Modele_LRRP_US_02 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_LRRP_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_LRRP_US_04 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_LRRP_US_05 is TResourceMesh( Mesh=US_Rangers_NCO )
export Modele_LRRP_US_06 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_LRRP_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_HMG_US
export Modele_Rifles_HMG_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_HMG_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_HMG_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_HMG_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_HMG_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_HMG_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_HMG_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_HMG_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_HMG_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Rifles_HMG_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Rifles_HMG_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Tuto_Rifles_US
export Modele_Tuto_Rifles_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Tuto_Rifles_US_02 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Tuto_Rifles_US_03 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Tuto_Rifles_US_04 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Tuto_Rifles_US_05 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Tuto_Rifles_US_06 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Tuto_Rifles_US_07 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Tuto_Rifles_US_08 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Tuto_Rifles_US_09 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Tuto_Rifles_US_10 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Tuto_Rifles_US_11 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Tuto_Rifles_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mot_CMD_US
export Modele_Rifles_Mot_CMD_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_Mot_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mot_Dragon_US
export Modele_Rifles_Mot_Dragon_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_Mot_Dragon_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mot_HMG_US
export Modele_Rifles_Mot_HMG_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_Mot_HMG_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mot_US
export Modele_Rifles_Mot_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_Mot_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Cavalry_US
export Modele_Rifles_Cavalry_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_Cavalry_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_Cavalry_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_Cavalry_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_Cavalry_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_Cavalry_US_06 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_Cavalry_US_07 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Rifles_Cavalry_US_08 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Rifles_Cavalry_US_09 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Rifles_Cavalry_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_US
export Modele_Rifles_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Rifles_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Rifles_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Dragon_US
export Modele_Rifles_Dragon_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_Dragon_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_Dragon_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_Dragon_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_Dragon_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_Dragon_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_Dragon_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_Dragon_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_Dragon_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Rifles_Dragon_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Rifles_Dragon_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_LAW_US
export Modele_Rifles_LAW_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_LAW_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_LAW_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_LAW_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_LAW_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_LAW_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_LAW_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_LAW_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_LAW_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Rifles_LAW_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Rifles_LAW_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_half_AT4_US
export Modele_Rifles_half_AT4_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_half_AT4_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_half_AT4_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_half_AT4_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_half_AT4_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_half_AT4_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_half_AT4_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_half_AT4_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_half_AT4_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Rifles_half_AT4_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Rifles_half_AT4_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_half_Dragon_NG_US
export Modele_Rifles_half_Dragon_NG_US is TResourceMesh( Mesh=NatGuard_1 )
export Modele_Rifles_half_Dragon_NG_US_02 is TResourceMesh( Mesh=NatGuard_2 )
export Modele_Rifles_half_Dragon_NG_US_03 is TResourceMesh( Mesh=NatGuard_3 )
export Modele_Rifles_half_Dragon_NG_US_04 is TResourceMesh( Mesh=NatGuard_NCO )
export Modele_Rifles_half_Dragon_NG_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_half_Dragon_US
export Modele_Rifles_half_Dragon_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_half_Dragon_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_half_Dragon_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_half_Dragon_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_half_Dragon_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_half_Dragon_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_half_Dragon_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_half_Dragon_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_half_Dragon_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Rifles_half_Dragon_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Rifles_half_Dragon_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_half_M60_US
export Modele_Rifles_half_M60_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_half_M60_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_half_M60_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_half_M60_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_half_M60_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_half_M60_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_half_M60_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_half_M60_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_half_M60_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Rifles_half_M60_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Rifles_half_M60_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_half_CMD_NG_US
export Modele_Rifles_half_CMD_NG_US is TResourceMesh( Mesh=NatGuard_1 )
export Modele_Rifles_half_CMD_NG_US_02 is TResourceMesh( Mesh=NatGuard_2 )
export Modele_Rifles_half_CMD_NG_US_03 is TResourceMesh( Mesh=NatGuard_3 )
export Modele_Rifles_half_CMD_NG_US_04 is TResourceMesh( Mesh=NatGuard_NCO )
export Modele_Rifles_half_CMD_NG_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_half_CMD_US
export Modele_Rifles_half_CMD_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_half_CMD_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_half_CMD_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_half_CMD_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_half_CMD_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_half_CMD_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_half_CMD_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_half_CMD_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_half_CMD_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Rifles_half_CMD_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Rifles_half_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//HvyScout_NG_Dragon_US
export Modele_HvyScout_NG_Dragon_US is TResourceMesh( Mesh=NatGuard_NCO )
export Modele_HvyScout_NG_Dragon_US_02 is TResourceMesh( Mesh=NatGuard_2 )
export Modele_HvyScout_NG_Dragon_US_03 is TResourceMesh( Mesh=NatGuard_3 )
export Modele_HvyScout_NG_Dragon_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//HvyScout_NG_US
export Modele_HvyScout_NG_US is TResourceMesh( Mesh=NatGuard_NCO )
export Modele_HvyScout_NG_US_02 is TResourceMesh( Mesh=NatGuard_2 )
export Modele_HvyScout_NG_US_03 is TResourceMesh( Mesh=NatGuard_3 )
export Modele_HvyScout_NG_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Scout_NG_US
export Modele_Scout_NG_US is TResourceMesh( Mesh=NatGuard_NCO )
export Modele_Scout_NG_US_02 is TResourceMesh( Mesh=NatGuard_2 )
export Modele_Scout_NG_US_03 is TResourceMesh( Mesh=NatGuard_3 )
export Modele_Scout_NG_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MANPAD_Redeye_US
export Modele_MANPAD_Redeye_US is TResourceMesh( Mesh=NatGuard_1 )
export Modele_MANPAD_Redeye_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MANPAD_Stinger_NG_US
export Modele_MANPAD_Stinger_NG_US is TResourceMesh( Mesh=NatGuard_1 )
export Modele_MANPAD_Stinger_NG_US_02 is TResourceMesh( Mesh=NatGuard_2 )
export Modele_MANPAD_Stinger_NG_US_03 is TResourceMesh( Mesh=NatGuard_3 )
export Modele_MANPAD_Stinger_NG_US_04 is TResourceMesh( Mesh=NatGuard_NCO )
export Modele_MANPAD_Stinger_NG_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_half_LAW_NG_US
export Modele_Rifles_half_LAW_NG_US is TResourceMesh( Mesh=NatGuard_1 )
export Modele_Rifles_half_LAW_NG_US_02 is TResourceMesh( Mesh=NatGuard_2 )
export Modele_Rifles_half_LAW_NG_US_03 is TResourceMesh( Mesh=NatGuard_3 )
export Modele_Rifles_half_LAW_NG_US_04 is TResourceMesh( Mesh=NatGuard_NCO )
export Modele_Rifles_half_LAW_NG_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_half_LAW_US
export Modele_Rifles_half_LAW_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_half_LAW_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_half_LAW_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_half_LAW_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Rifles_half_LAW_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_half_LAW_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Rifles_half_LAW_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_half_LAW_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Rifles_half_LAW_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Rifles_half_LAW_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Rifles_half_LAW_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//AeroRifles_CMD_US
export Modele_AeroRifles_CMD_US is TResourceMesh( Mesh=US_AB_NCO )
export Modele_AeroRifles_CMD_US_02 is TResourceMesh( Mesh=US_AB_Officer )
export Modele_AeroRifles_CMD_US_03 is TResourceMesh( Mesh=US_AB_1 )
export Modele_AeroRifles_CMD_US_04 is TResourceMesh( Mesh=US_AB_2 )
export Modele_AeroRifles_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//GreenBerets_CMD_US
export Modele_GreenBerets_CMD_US is TResourceMesh( Mesh=US_GB_NCO )
export Modele_GreenBerets_CMD_US_02 is TResourceMesh( Mesh=US_AB_Officer )
export Modele_GreenBerets_CMD_US_03 is TResourceMesh( Mesh=US_GB_1 )
export Modele_GreenBerets_CMD_US_04 is TResourceMesh( Mesh=US_GB_NCO )
export Modele_GreenBerets_CMD_US_05 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_GreenBerets_CMD_US_LOW is TResourceMesh( Mesh=US_GB_1_LOW )

//Commander_US
export Modele_Commander_US is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_Officer.fbx" )
export Modele_Commander_US_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_Officer.fbx" )

//CavalryScout_US
export Modele_CavalryScout_US is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_Sniper.fbx" )
export Modele_CavalryScout_US_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_Sniper.fbx" )

//CavalryScout_CMD_US
export Modele_CavalryScout_CMD_US is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_Sniper.fbx" )
export Modele_CavalryScout_CMD_US_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_Sniper.fbx" )

//LRRP_CEWI_US
export Modele_LRRP_CEWI_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_LRRP_CEWI_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Scout_US
export Modele_Scout_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Scout_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Scout_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Scout_US_04 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Scout_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Scout_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Scout_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Scout_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Scout_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Scout_US_10 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Scout_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Engineer_CMD_US
export Modele_Engineer_CMD_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Engineer_CMD_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Engineer_CMD_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Engineer_CMD_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Engineer_CMD_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Engineer_CMD_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Engineer_CMD_US_07 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Engineer_CMD_US_08 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Engineer_CMD_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Engineer_CMD_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Engineer_CMD_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Engineers_Dragon_US
export Modele_Engineers_Dragon_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Engineers_Dragon_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Engineers_Dragon_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Engineers_Dragon_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Engineers_Dragon_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Engineers_Dragon_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Engineers_Dragon_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Engineers_Dragon_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Engineers_Dragon_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Engineers_Dragon_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Engineers_Dragon_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//AirCav_Scout_US
export Modele_AirCav_Scout_US is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_1.fbx" )
export Modele_AirCav_Scout_US_02 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_2.fbx" )
export Modele_AirCav_Scout_US_03 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_NCO.fbx" )
export Modele_AirCav_Scout_US_04 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_Officer.fbx" )
export Modele_AirCav_Scout_US_05 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_Sniper.fbx" )
export Modele_AirCav_Scout_US_06 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_spe.fbx" )
export Modele_AirCav_Scout_US_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_1.fbx" )

//ATGM_Dragon_US
export Modele_ATGM_Dragon_US is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_2.fbx" )
export Modele_ATGM_Dragon_US_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_2.fbx" )

//ATGM_ITOW_US
export Modele_ATGM_ITOW_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_ATGM_ITOW_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_ATGM_ITOW_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_ATGM_ITOW_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_ATGM_ITOW_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_ATGM_ITOW_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_ATGM_ITOW_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_ATGM_ITOW_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_ATGM_ITOW_US_09 is TResourceMesh( Mesh=US_GI_2 )
export Modele_ATGM_ITOW_US_10 is TResourceMesh( Mesh=US_GI_2 )
export Modele_ATGM_ITOW_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//ATGM_TOW2_US
export Modele_ATGM_TOW2_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_ATGM_TOW2_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_ATGM_TOW2_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_ATGM_TOW2_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_ATGM_TOW2_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_ATGM_TOW2_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_ATGM_TOW2_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_ATGM_TOW2_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_ATGM_TOW2_US_09 is TResourceMesh( Mesh=US_GI_2 )
export Modele_ATGM_TOW2_US_10 is TResourceMesh( Mesh=US_GI_2 )
export Modele_ATGM_TOW2_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//ATGM_TOW_US
export Modele_ATGM_TOW_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_ATGM_TOW_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_ATGM_TOW_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_ATGM_TOW_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_ATGM_TOW_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_ATGM_TOW_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_ATGM_TOW_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_ATGM_TOW_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_ATGM_TOW_US_09 is TResourceMesh( Mesh=US_GI_2 )
export Modele_ATGM_TOW_US_10 is TResourceMesh( Mesh=US_GI_2 )
export Modele_ATGM_TOW_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Engineers_US
export Modele_Engineers_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Engineers_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Engineers_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Engineers_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Engineers_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Engineers_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Engineers_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Engineers_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Engineers_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Engineers_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Engineers_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Engineers_Flash_US
export Modele_Engineers_Flash_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Engineers_Flash_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Engineers_Flash_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Engineers_Flash_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Engineers_Flash_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Engineers_Flash_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Engineers_Flash_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Engineers_Flash_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Engineers_Flash_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Engineers_Flash_US_10 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Engineers_Flash_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MANPAD_Stinger_C_Aero_US
export Modele_MANPAD_Stinger_C_Aero_US is TResourceMesh( Mesh=US_AB_1 )
export Modele_MANPAD_Stinger_C_Aero_US_02 is TResourceMesh( Mesh=US_AB_2 )
export Modele_MANPAD_Stinger_C_Aero_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MANPAD_Stinger_C_para_US
export Modele_MANPAD_Stinger_C_para_US is TResourceMesh( Mesh=US_AB_1 )
export Modele_MANPAD_Stinger_C_para_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MANPAD_Stinger_C_US
export Modele_MANPAD_Stinger_C_US is TResourceMesh( Mesh=US_GI_1 )  //'GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_spe.fbx'
export Modele_MANPAD_Stinger_C_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW ) //'GameData:/Assets/3D/Units/US/Infanterie/Airborne/Airborne_spe.fbx'

//MANPAD_Stinger_US
export Modele_MANPAD_Stinger_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_MANPAD_Stinger_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_MANPAD_Stinger_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_MANPAD_Stinger_US_04 is TResourceMesh( Mesh=US_GI_2 )
export Modele_MANPAD_Stinger_US_05 is TResourceMesh( Mesh=US_GI_2 )
export Modele_MANPAD_Stinger_US_06 is TResourceMesh( Mesh=US_GI_1 )
export Modele_MANPAD_Stinger_US_07 is TResourceMesh( Mesh=US_GI_2 )
export Modele_MANPAD_Stinger_US_08 is TResourceMesh( Mesh=US_GI_2 )
export Modele_MANPAD_Stinger_US_09 is TResourceMesh( Mesh=US_GI_1 )
export Modele_MANPAD_Stinger_US_10 is TResourceMesh( Mesh=US_GI_1 )
export Modele_MANPAD_Stinger_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )


//Scout_Mech_US
export Modele_Scout_Mech_US is TResourceMesh( Mesh=US_GI_1 )
export Modele_Scout_Mech_US_02 is TResourceMesh( Mesh=US_GI_1 )
export Modele_Scout_Mech_US_03 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Scout_Mech_US_04 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Scout_Mech_US_05 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Scout_Mech_US_06 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Scout_Mech_US_07 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Scout_Mech_US_08 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Scout_Mech_US_09 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Scout_Mech_US_10 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Scout_Mech_US_LOW is TResourceMesh( Mesh=US_GI_1_LOW )


//------------------------------------------------------------------------------
//
//
//                                   SOLDATS NL
//
//
//------------------------------------------------------------------------------

NL_Commando_1 is 'GameData:/Assets/3D/Units/NL/Infanterie/Commando/Commando_1.fbx'
NL_Commando_2 is 'GameData:/Assets/3D/Units/NL/Infanterie/Commando/Commando_2.fbx'
NL_Commando_NCO is 'GameData:/Assets/3D/Units/NL/Infanterie/Commando/Commando_NCO.fbx'
NL_Commando_1_LOW is 'GameData:/Assets/3D/Units/NL/Infanterie/Commando/Commando_1_LOW.fbx'
NL_Infanterie_1 is 'GameData:/Assets/3D/Units/NL/Infanterie/Infanterie/Infanterie_1.fbx'
NL_Infanterie_2 is 'GameData:/Assets/3D/Units/NL/Infanterie/Infanterie/Infanterie_2.fbx'
NL_Infanterie_NCO is 'GameData:/Assets/3D/Units/NL/Infanterie/Infanterie/Infanterie_NCO.fbx'
NL_Infanterie_Officer is 'GameData:/Assets/3D/Units/NL/Infanterie/Infanterie/Infanterie_Officer.fbx'
NL_Infanterie_1_LOW is 'GameData:/Assets/3D/Units/NL/Infanterie/Infanterie/Infanterie_1_LOW.fbx'
NL_KMAR_1 is 'GameData:/Assets/3D/Units/NL/Infanterie/KMAR/KMAR_1.fbx'
NL_KMAR_2 is 'GameData:/Assets/3D/Units/NL/Infanterie/KMAR/KMAR_2.fbx'
NL_KMAR_1_LOW is 'GameData:/Assets/3D/Units/NL/Infanterie/KMAR/KMAR_1_LOW.fbx'
NL_Marinier_1 is 'GameData:/Assets/3D/Units/NL/Infanterie/Marinier/Marinier_1.fbx'
NL_Marinier_2 is 'GameData:/Assets/3D/Units/NL/Infanterie/Marinier/Marinier_2.fbx'
NL_Marinier_NCO is 'GameData:/Assets/3D/Units/NL/Infanterie/Marinier/Marinier_NCO.fbx'
NL_Marinier_Officer is 'GameData:/Assets/3D/Units/NL/Infanterie/Marinier/Marinier_Officer.fbx'
NL_Marinier_spe is 'GameData:/Assets/3D/Units/NL/Infanterie/Marinier/Marinier_spe.fbx'
NL_Marinier_1_LOW is 'GameData:/Assets/3D/Units/NL/Infanterie/Marinier/Marinier_1_LOW.fbx'
NL_MarinierCmdo_1 is 'GameData:/Assets/3D/Units/NL/Infanterie/MarinierCmdo/MarinierCmdo_1.fbx'
NL_MarinierCmdo_NCO is 'GameData:/Assets/3D/Units/NL/Infanterie/MarinierCmdo/MarinierCmdo_NCO.fbx'
NL_MarinierCmdo_1_LOW is 'GameData:/Assets/3D/Units/NL/Infanterie/MarinierCmdo/MarinierCmdo_1_LOW.fbx'
NL_Natres_1 is 'GameData:/Assets/3D/Units/NL/Infanterie/Natres/Natres_1.fbx'
NL_Natres_2 is 'GameData:/Assets/3D/Units/NL/Infanterie/Natres/Natres_2.fbx'
NL_Natres_1_LOW is 'GameData:/Assets/3D/Units/NL/Infanterie/Natres/Natres_1_LOW.fbx'


//MANPAD_Stinger_C_Marine_NL
export Modele_MANPAD_Stinger_C_Marine_NL is TResourceMesh( Mesh=NL_Marinier_1 )
export Modele_MANPAD_Stinger_C_Marine_NL_02 is TResourceMesh( Mesh=NL_Marinier_2 )
export Modele_MANPAD_Stinger_C_Marine_NL_LOW is TResourceMesh( Mesh=NL_MarinierCmdo_1_LOW )

//MANPAD_Stinger_C_NL
export Modele_MANPAD_Stinger_C_NL is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_MANPAD_Stinger_C_NL_02 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_MANPAD_Stinger_C_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Reserve_NL
export Modele_Reserve_NL is TResourceMesh( Mesh=NL_Natres_1 )
export Modele_Reserve_NL_02 is TResourceMesh( Mesh=NL_Natres_2 )
export Modele_Reserve_NL_LOW is TResourceMesh( Mesh=NL_Natres_1_LOW )

//Engineers_Scout_NL
export Modele_Engineers_Scout_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Engineers_Scout_NL_02 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Engineers_Scout_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Engineers_Scout_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Mech_Rifles_M72_LAW_NL
export Modele_Mech_Rifles_M72_LAW_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Mech_Rifles_M72_LAW_NL_02 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Mech_Rifles_M72_LAW_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Mech_Rifles_M72_LAW_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Rifles_M72_LAW_NL
export Modele_Rifles_M72_LAW_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Rifles_M72_LAW_NL_02 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Rifles_M72_LAW_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Rifles_M72_LAW_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Security_CMD_NL
export Modele_Security_CMD_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Security_CMD_NL_02 is TResourceMesh( Mesh=NL_Infanterie_Officer )
export Modele_Security_CMD_NL_03 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Security_CMD_NL_04 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Security_CMD_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Security_Mobile_NL
export Modele_Security_Mobile_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Security_Mobile_NL_02 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Security_Mobile_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Security_Mobile_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Security_NL
export Modele_Security_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Security_NL_02 is TResourceMesh( Mesh=NL_Natres_1 )
export Modele_Security_NL_03 is TResourceMesh( Mesh=NL_Natres_2 )
export Modele_Security_NL_04 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Security_NL_05 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Security_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Mech_Rifles_Carl_NL
export Modele_Mech_Rifles_Carl_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Mech_Rifles_Carl_NL_02 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Mech_Rifles_Carl_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Mech_Rifles_Carl_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Rifles_Carl_NL
export Modele_Rifles_Carl_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Rifles_Carl_NL_02 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Rifles_Carl_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Rifles_Carl_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Mech_Rifles_Dragon_NL
export Modele_Mech_Rifles_Dragon_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Mech_Rifles_Dragon_NL_02 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Mech_Rifles_Dragon_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Mech_Rifles_Dragon_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Rifles_Dragon_NL
export Modele_Rifles_Dragon_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Rifles_Dragon_NL_02 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Rifles_Dragon_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Rifles_Dragon_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Mech_Rifles_CMD_NL
export Modele_Mech_Rifles_CMD_NL is TResourceMesh( Mesh=NL_Infanterie_Officer )
export Modele_Mech_Rifles_CMD_NL_02 is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Mech_Rifles_CMD_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Mech_Rifles_CMD_NL_04 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Mech_Rifles_CMD_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Rifles_CMD_NL
export Modele_Rifles_CMD_NL is TResourceMesh( Mesh=NL_Infanterie_Officer )
export Modele_Rifles_CMD_NL_02 is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Rifles_CMD_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Rifles_CMD_NL_04 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Rifles_CMD_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//MP_AT_NL
export Modele_MP_AT_NL is TResourceMesh( Mesh=NL_KMAR_1 )
export Modele_MP_AT_NL_02 is TResourceMesh( Mesh=NL_KMAR_2 )
export Modele_MP_AT_NL_LOW is TResourceMesh( Mesh=NL_KMAR_1_LOW )

//MP_CMD_NL
export Modele_MP_CMD_NL is TResourceMesh( Mesh=NL_KMAR_1 )
export Modele_MP_CMD_NL_02 is TResourceMesh( Mesh=NL_KMAR_2 )
export Modele_MP_CMD_NL_LOW is TResourceMesh( Mesh=NL_KMAR_1_LOW )

//MP_NL
export Modele_MP_NL is TResourceMesh( Mesh=NL_KMAR_1 )
export Modele_MP_NL_02 is TResourceMesh( Mesh=NL_KMAR_2 )
export Modele_MP_NL_LOW is TResourceMesh( Mesh=NL_KMAR_1_LOW )

//Groupe_AT_Marines_NL
export Modele_Groupe_AT_Marines_NL is TResourceMesh( Mesh=NL_Marinier_NCO )
export Modele_Groupe_AT_Marines_NL_02 is TResourceMesh( Mesh=NL_Marinier_spe )
export Modele_Groupe_AT_Marines_NL_03 is TResourceMesh( Mesh=NL_Marinier_2 )
export Modele_Groupe_AT_Marines_NL_04 is TResourceMesh( Mesh=NL_Marinier_1 )
export Modele_Groupe_AT_Marines_NL_LOW is TResourceMesh( Mesh=NL_Marinier_1_LOW )

//Marines_Dragon_NL
export Modele_Marines_Dragon_NL is TResourceMesh( Mesh=NL_Marinier_NCO )
export Modele_Marines_Dragon_NL_LOW is TResourceMesh( Mesh=NL_Marinier_1_LOW )

//Marines_NL
export Modele_Marines_NL is TResourceMesh( Mesh=NL_Marinier_NCO )
export Modele_Marines_NL_02 is TResourceMesh( Mesh=NL_Marinier_spe )
export Modele_Marines_NL_03 is TResourceMesh( Mesh=NL_Marinier_2 )
export Modele_Marines_NL_04 is TResourceMesh( Mesh=NL_Marinier_1 )
export Modele_Marines_NL_LOW is TResourceMesh( Mesh=NL_Marinier_1_LOW )

//Marine_Scout_NL
export Modele_Marine_Scout_NL is TResourceMesh( Mesh=NL_MarinierCmdo_NCO )
export Modele_Marine_Scout_NL_02 is TResourceMesh( Mesh=NL_Marinier_spe )
export Modele_Marine_Scout_NL_03 is TResourceMesh( Mesh=NL_Marinier_2 )
export Modele_Marine_Scout_NL_04 is TResourceMesh( Mesh=NL_Marinier_1 )
export Modele_Marine_Scout_NL_05 is TResourceMesh( Mesh=NL_MarinierCmdo_1 )
export Modele_Marine_Scout_NL_LOW is TResourceMesh( Mesh=NL_MarinierCmdo_1_LOW )

//Marines_CMD_NL
export Modele_Marines_CMD_NL is TResourceMesh( Mesh=NL_Marinier_Officer )
export Modele_Marines_CMD_NL_02 is TResourceMesh( Mesh=NL_Marinier_NCO )
export Modele_Marines_CMD_NL_03 is TResourceMesh( Mesh=NL_Marinier_spe )
export Modele_Marines_CMD_NL_04 is TResourceMesh( Mesh=NL_Marinier_2 )
export Modele_Marines_CMD_NL_05 is TResourceMesh( Mesh=NL_Marinier_1 )
export Modele_Marines_CMD_NL_LOW is TResourceMesh( Mesh=NL_Marinier_1_LOW )

//Engineers_CMD_NL
export Modele_Engineers_CMD_NL is TResourceMesh( Mesh=NL_Infanterie_Officer )
export Modele_Engineers_CMD_NL_02 is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Engineers_CMD_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Engineers_CMD_NL_04 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Engineers_CMD_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Commando_CMD_NL
export Modele_Commando_CMD_NL is TResourceMesh( Mesh=NL_Commando_NCO )
export Modele_Commando_CMD_NL_02 is TResourceMesh( Mesh=NL_Commando_1 )
export Modele_Commando_CMD_NL_03 is TResourceMesh( Mesh=NL_Commando_2 )
export Modele_Commando_CMD_NL_LOW is TResourceMesh( Mesh=NL_Commando_1_LOW )

//Commando_NL
export Modele_Commando_NL is TResourceMesh( Mesh=NL_Commando_NCO )
export Modele_Commando_NL_02 is TResourceMesh( Mesh=NL_Commando_1 )
export Modele_Commando_NL_03 is TResourceMesh( Mesh=NL_Commando_2 )
export Modele_Commando_NL_LOW is TResourceMesh( Mesh=NL_Commando_1_LOW )

//Marine_SF_NL
export Modele_Marine_SF_NL is TResourceMesh( Mesh=NL_Commando_NCO )
export Modele_Marine_SF_NL_LOW is TResourceMesh( Mesh=NL_Commando_1_LOW )

//LRRP_NL
export Modele_LRRP_NL is TResourceMesh( Mesh=NL_Commando_NCO )
export Modele_LRRP_NL_02 is TResourceMesh( Mesh=NL_Commando_1 )
export Modele_LRRP_NL_03 is TResourceMesh( Mesh=NL_Commando_2 )
export Modele_LRRP_NL_LOW is TResourceMesh( Mesh=NL_Commando_1_LOW )

//Scout_NL
export Modele_Scout_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Scout_NL_02 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Scout_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Scout_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Scout_AT_NL
export Modele_Scout_AT_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Scout_AT_NL_02 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Scout_AT_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Scout_AT_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Engineers_Flam_NL
export Modele_Engineers_Flam_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Engineers_Flam_NL_02 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Engineers_Flam_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Engineers_Flam_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//Engineers_NL
export Modele_Engineers_NL is TResourceMesh( Mesh=NL_Infanterie_NCO )
export Modele_Engineers_NL_02 is TResourceMesh( Mesh=NL_Infanterie_2 )
export Modele_Engineers_NL_03 is TResourceMesh( Mesh=NL_Infanterie_1 )
export Modele_Engineers_NL_LOW is TResourceMesh( Mesh=NL_Infanterie_1_LOW )

//------------------------------------------------------------------------------
//
//
//                                   SOLDATS CAN
//
//
//------------------------------------------------------------------------------
CAN_NCO_1 is 'GameData:/Assets/3D/Units/CAN/Infanterie/Soldier/NCO_1.fbx'
CAN_Officer_1 is 'GameData:/Assets/3D/Units/CAN/Infanterie/Soldier/Officer_1.fbx'
CAN_Soldier_1 is 'GameData:/Assets/3D/Units/CAN/Infanterie/Soldier/Soldier_1.fbx'
CAN_Soldier_2 is 'GameData:/Assets/3D/Units/CAN/Infanterie/Soldier/Soldier_2.fbx'
CAN_Soldier_3 is 'GameData:/Assets/3D/Units/CAN/Infanterie/Soldier/Soldier_3.fbx'
CAN_Soldier_AA is 'GameData:/Assets/3D/Units/CAN/Infanterie/Soldier/Soldier_AA.fbx'
CAN_MP_1 is 'GameData:/Assets/3D/Units/CAN/Infanterie/MP/MP_1.fbx'
CAN_MP_2 is 'GameData:/Assets/3D/Units/CAN/Infanterie/MP/MP_2.fbx'

//Commando_CAN
export Modele_Commando_CAN is TResourceMesh( Mesh=US_GI_1 )
export Modele_Commando_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Commando_CMD_CAN
export Modele_Commando_CMD_CAN is TResourceMesh( Mesh=US_GI_1 )
export Modele_Commando_CMD_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Commando_Scout_CAN
export Modele_Commando_Scout_CAN is TResourceMesh( Mesh=US_GI_1 )
export Modele_Commando_Scout_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Engineers_CAN
export Modele_Engineers_CAN is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Engineers_CAN_02 is TResourceMesh( Mesh=CAN_Soldier_1 )
export Modele_Engineers_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Engineers_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Engineers_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Engineers_CMD_CAN
export Modele_Engineers_CMD_CAN is TResourceMesh( Mesh=CAN_Officer_1 )
export Modele_Engineers_CMD_CAN_02 is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Engineers_CMD_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_AA )
export Modele_Engineers_CMD_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Engineers_CMD_CAN_05 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Engineers_CMD_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Engineers_Flam_CAN
export Modele_Engineers_Flam_CAN is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Engineers_Flam_CAN_02 is TResourceMesh( Mesh=CAN_Soldier_1 )
export Modele_Engineers_Flam_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Engineers_Flam_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Engineers_Flam_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MANPAD_Blowpipe_CAN
export Modele_MANPAD_Blowpipe_CAN is TResourceMesh( Mesh=CAN_Soldier_AA )
export Modele_MANPAD_Blowpipe_CAN_02 is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_MANPAD_Blowpipe_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MANPAD_Javelin_CAN
export Modele_MANPAD_Javelin_CAN is TResourceMesh( Mesh=CAN_Soldier_AA )
export Modele_MANPAD_Javelin_CAN_02 is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_MANPAD_Javelin_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Sniper_CAN
export Modele_Sniper_CAN is TResourceMesh( Mesh=UK_Ghillie )
export Modele_Sniper_CAN_LOW is TResourceMesh( Mesh=UK_Ghillie_LOW )

//Pathfinders_CAN
export Modele_Pathfinders_CAN is TResourceMesh( Mesh=US_GI_1 )
export Modele_Pathfinders_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Reserve_CAN
export Modele_Reserve_CAN is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Reserve_CAN_02 is TResourceMesh( Mesh=CAN_Soldier_1 )
export Modele_Reserve_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Reserve_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Reserve_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Reserve_CMD_CAN
export Modele_Reserve_CMD_CAN is TResourceMesh( Mesh=CAN_Officer_1 )
export Modele_Reserve_CMD_CAN_02 is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Reserve_CMD_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_AA )
export Modele_Reserve_CMD_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Reserve_CMD_CAN_05 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Reserve_CMD_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mech_CAN
export Modele_Rifles_Mech_CAN is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Rifles_Mech_CAN_02 is TResourceMesh( Mesh=CAN_Soldier_1 )
export Modele_Rifles_Mech_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Rifles_Mech_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Rifles_Mech_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mech_CMD_CAN
export Modele_Rifles_Mech_CMD_CAN is TResourceMesh( Mesh=CAN_Officer_1 )
export Modele_Rifles_Mech_CMD_CAN_02 is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Rifles_Mech_CMD_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_AA )
export Modele_Rifles_Mech_CMD_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Rifles_Mech_CMD_CAN_05 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Rifles_Mech_CMD_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mech_Escorte_CAN
export Modele_Rifles_Mech_Escorte_CAN is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Rifles_Mech_Escorte_CAN_02 is TResourceMesh( Mesh=CAN_Soldier_1 )
export Modele_Rifles_Mech_Escorte_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Rifles_Mech_Escorte_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Rifles_Mech_Escorte_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mech_MAG_CAN
export Modele_Rifles_Mech_MAG_CAN is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Rifles_Mech_MAG_CAN_02 is TResourceMesh( Mesh=CAN_Soldier_1 )
export Modele_Rifles_Mech_MAG_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Rifles_Mech_MAG_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Rifles_Mech_MAG_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mot_CAN
export Modele_Rifles_Mot_CAN is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Rifles_Mot_CAN_02 is TResourceMesh( Mesh=CAN_Soldier_1 )
export Modele_Rifles_Mot_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Rifles_Mot_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Rifles_Mot_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mot_CMD_CAN
export Modele_Rifles_Mot_CMD_CAN is TResourceMesh( Mesh=CAN_Officer_1 )
export Modele_Rifles_Mot_CMD_CAN_02 is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Rifles_Mot_CMD_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_AA )
export Modele_Rifles_Mot_CMD_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Rifles_Mot_CMD_CAN_05 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Rifles_Mot_CMD_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mot_CarlG_CAN
export Modele_Rifles_Mot_CarlG_CAN is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Rifles_Mot_CarlG_CAN_02 is TResourceMesh( Mesh=CAN_Soldier_1 )
export Modele_Rifles_Mot_CarlG_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Rifles_Mot_CarlG_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Rifles_Mot_CarlG_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mot_Eryx_CAN
export Modele_Rifles_Mot_Eryx_CAN is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Rifles_Mot_Eryx_CAN_02 is TResourceMesh( Mesh=CAN_Soldier_1 )
export Modele_Rifles_Mot_Eryx_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Rifles_Mot_Eryx_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Rifles_Mot_Eryx_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Scout_CAN
export Modele_Scout_CAN is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Scout_CAN_02 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Scout_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Scout_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Scout_GSR_CAN
export Modele_Scout_GSR_CAN is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Scout_GSR_CAN_02 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Scout_GSR_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Scout_GSR_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Groupe_AT_CAN
export Modele_Groupe_AT_CAN is TResourceMesh( Mesh=CAN_NCO_1 )
export Modele_Groupe_AT_CAN_02 is TResourceMesh( Mesh=CAN_Soldier_1 )
export Modele_Groupe_AT_CAN_03 is TResourceMesh( Mesh=CAN_Soldier_2 )
export Modele_Groupe_AT_CAN_04 is TResourceMesh( Mesh=CAN_Soldier_3 )
export Modele_Groupe_AT_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MP_CAN
export Modele_MP_CAN is TResourceMesh( Mesh=CAN_MP_2 )
export Modele_MP_CAN_02 is TResourceMesh( Mesh=CAN_MP_1 )
export Modele_MP_CAN_LOW is TResourceMesh( Mesh=US_GI_1_LOW )


//------------------------------------------------------------------------------
//
//
//                                   SOLDATS ESP
//
//
//------------------------------------------------------------------------------

//Rifles_Mech_C90_ESP
export Modele_Rifles_Mech_C90_ESP is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_Mech_C90_ESP_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mech_CMD_ESP
export Modele_Rifles_Mech_CMD_ESP is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_Mech_CMD_ESP_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mech_LMG_ESP
export Modele_Rifles_Mech_LMG_ESP is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_Mech_LMG_ESP_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//Rifles_Mech_M65_ESP
export Modele_Rifles_Mech_M65_ESP is TResourceMesh( Mesh=US_GI_1 )
export Modele_Rifles_Mech_M65_ESP_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//MP_ESP
export Modele_MP_ESP is TResourceMesh( Mesh=US_GI_1 )
export Modele_MP_ESP_LOW is TResourceMesh( Mesh=US_GI_1_LOW )


//------------------------------------------------------------------------------
//                                   SOLDATS DDR
//------------------------------------------------------------------------------


DDR_MotSchutzen_1 is 'GameData:/Assets/3D/Units/DDR/Infanterie/MotSchutzen/MotSchutzen_1.fbx'
DDR_MotSchutzen_2 is 'GameData:/Assets/3D/Units/DDR/Infanterie/MotSchutzen/MotSchutzen_2.fbx'
DDR_MotSchutzen_NCO is 'GameData:/Assets/3D/Units/DDR/Infanterie/MotSchutzen/MotSchutzen_NCO.fbx'
DDR_MotSchutzen_Officer is 'GameData:/Assets/3D/Units/DDR/Infanterie/MotSchutzen/MotSchutzen_Officer.fbx'
DDR_MotSchutzen_Sniper is 'GameData:/Assets/3D/Units/DDR/Infanterie/MotSchutzen/MotSchutzen_Sniper.fbx'
DDR_MotSchutzen_spe is 'GameData:/Assets/3D/Units/DDR/Infanterie/MotSchutzen/MotSchutzen_spe.fbx'
DDR_MotSchutzen_LOW is 'GameData:/Assets/3D/Units/DDR/Infanterie/MotSchutzen/MotSchutzen_1_LOW.fbx'

DDR_Grenzer_1 is 'GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_1.fbx'
DDR_Grenzer_2 is 'GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_2.fbx'
DDR_Grenzer_NCO is 'GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_NCO.fbx'
DDR_Grenzer_LOW is 'GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_1_LOW.fbx'

DDR_FJB_1 is 'GameData:/Assets/3D/Units/DDR/Infanterie/FJB/FJB_1.fbx'
DDR_FJB_2 is 'GameData:/Assets/3D/Units/DDR/Infanterie/FJB/FJB_2.fbx'
DDR_FJB_NCO is 'GameData:/Assets/3D/Units/DDR/Infanterie/FJB/FJB_NCO.fbx'
DDR_FJB_Officer is 'GameData:/Assets/3D/Units/DDR/Infanterie/FJB/FJB_Officer.fbx'
DDR_FJB_LOW is 'GameData:/Assets/3D/Units/DDR/Infanterie/FJB/FJB_1_LOW.fbx'

DDR_KdA_1 is 'GameData:/Assets/3D/Units/DDR/Infanterie/KdA/KdA_1.fbx'
DDR_KdA_2 is 'GameData:/Assets/3D/Units/DDR/Infanterie/KdA/KdA_2.fbx'
DDR_KdA_NCO is 'GameData:/Assets/3D/Units/DDR/Infanterie/KdA/KdA_NCO.fbx'
DDR_KdA_LOW is 'GameData:/Assets/3D/Units/DDR/Infanterie/KdA/KdA_1_LOW.fbx'

Wach_1 is 'GameData:/Assets/3D/Units/DDR/Infanterie/Wach/Wach_1.fbx'
Wach_2 is 'GameData:/Assets/3D/Units/DDR/Infanterie/Wach/Wach_2.fbx'
Wach_NCO is 'GameData:/Assets/3D/Units/DDR/Infanterie/Wach/Wach_NCO.fbx'
Wach_1_LOW is 'GameData:/Assets/3D/Units/DDR/Infanterie/Wach/Wach_1_LOW.fbx'

Militarstreifen_1 is 'GameData:/Assets/3D/Units/DDR/Infanterie/Militarstreifen/Militarstreifen_1.fbx'
Militarstreifen_2 is 'GameData:/Assets/3D/Units/DDR/Infanterie/Militarstreifen/Militarstreifen_2.fbx'
Militarstreifen_1_LOW is 'GameData:/Assets/3D/Units/DDR/Infanterie/Militarstreifen/Militarstreifen_1_LOW.fbx'

DDR_KSK_1 is 'GameData:/Assets/3D/Units/DDR/Infanterie/FJB/KSK_1.fbx'
DDR_KSK_2 is 'GameData:/Assets/3D/Units/DDR/Infanterie/FJB/KSK_2.fbx'
DDR_KSK_NCO is 'GameData:/Assets/3D/Units/DDR/Infanterie/FJB/KSK_NCO.fbx'

//------------------------------------------------------------------------------

//Reserve_HMG_DDR
export Modele_Reserve_HMG_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Reserve_HMG_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_Sniper )
export Modele_Reserve_HMG_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_Reserve_HMG_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_Reserve_HMG_DDR_05 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_Reserve_HMG_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Reserve_DDR
export Modele_Reserve_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Reserve_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_Sniper )
export Modele_Reserve_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_Reserve_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_Reserve_DDR_05 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_Reserve_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Reserve_CMD_DDR
export Modele_Reserve_CMD_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_Officer )
export Modele_Reserve_CMD_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Reserve_CMD_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_Sniper )
export Modele_Reserve_CMD_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_Reserve_CMD_DDR_05 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_Reserve_CMD_DDR_06 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_Reserve_CMD_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//MotSchutzen_DDR
export Modele_MotSchutzen_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_MotSchutzen_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_MotSchutzen_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_MotSchutzen_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_Officer )
export Modele_MotSchutzen_DDR_05 is TResourceMesh( Mesh=DDR_MotSchutzen_Sniper )
export Modele_MotSchutzen_DDR_06 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_MotSchutzen_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )
//------------------------------------------------------------------------------

//MP_DDR
export Modele_MP_DDR is TResourceMesh( Mesh=Militarstreifen_1 )
export Modele_MP_DDR_02 is TResourceMesh( Mesh=Militarstreifen_2 )
export Modele_MP_DDR_LOW is TResourceMesh( Mesh=Militarstreifen_1_LOW )

//Grenzer_CMD_DDR
export Modele_Grenzer_CMD_DDR is TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_1.fbx" )
export Modele_Grenzer_CMD_DDR_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_1.fbx" )

//Grenzer_Flam_DDR
export Modele_Grenzer_Flam_DDR is TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_1.fbx" )
export Modele_Grenzer_Flam_DDR_02 is  TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_2.fbx" )
export Modele_Grenzer_Flam_DDR_03 is  TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_NCO.fbx" )
export Modele_Grenzer_Flam_DDR_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_1.fbx" )

//Grenzer_DDR
export Modele_Grenzer_DDR is     TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_1.fbx" )
export Modele_Grenzer_DDR_02 is  TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_2.fbx" )
export Modele_Grenzer_DDR_03 is  TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_NCO.fbx" )
export Modele_Grenzer_DDR_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_1.fbx" )

//Grenzer_Mot_DDR
export Modele_Grenzer_Mot_DDR is     TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_1.fbx" )
export Modele_Grenzer_Mot_DDR_02 is  TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_2.fbx" )
export Modele_Grenzer_Mot_DDR_03 is  TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_NCO.fbx" )
export Modele_Grenzer_Mot_DDR_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/Grenzer/Grenzer_1.fbx" )

//------------------------------------------------------------------------------
//FJB_DDR
export Modele_FJB_DDR is     TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/FJB/FJB_1.fbx" )
export Modele_FJB_DDR_02 is  TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/FJB/FJB_2.fbx" )
export Modele_FJB_DDR_03 is  TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/FJB/FJB_NCO.fbx" )
export Modele_FJB_DDR_04 is  TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/FJB/FJB_Officer.fbx" )
export Modele_FJB_DDR_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/FJB/FJB_1.fbx" )

//KSK18_DDR
export Modele_KSK18_DDR is TResourceMesh( Mesh=DDR_KSK_NCO )
export Modele_KSK18_DDR_02 is TResourceMesh( Mesh=DDR_KSK_2 )
export Modele_KSK18_DDR_03 is TResourceMesh( Mesh=DDR_KSK_1 )
export Modele_KSK18_DDR_04 is     TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/FJB/FJB_1.fbx" )
export Modele_KSK18_DDR_05 is  TResourceMesh( Mesh="GameData:/Assets/3D/Units/DDR/Infanterie/FJB/FJB_2.fbx" )
export Modele_KSK18_DDR_LOW is TResourceMesh( Mesh=DDR_FJB_LOW )

//ATGM_Fagot_DDR
export Modele_ATGM_Fagot_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_ATGM_Fagot_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_ATGM_Fagot_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//    Wachregiment_SMG_DDR
export Modele_Wachregiment_SMG_DDR is TResourceMesh( Mesh=Wach_NCO )
export Modele_Wachregiment_SMG_DDR_02 is TResourceMesh( Mesh=Wach_1 )
export Modele_Wachregiment_SMG_DDR_03 is TResourceMesh( Mesh=Wach_2 )
export Modele_Wachregiment_SMG_DDR_LOW is TResourceMesh( Mesh=Wach_1_LOW )

//  Wachregiment_DDR
export Modele_Wachregiment_DDR is TResourceMesh( Mesh=Wach_NCO )
export Modele_Wachregiment_DDR_02 is TResourceMesh( Mesh=Wach_2 )
export Modele_Wachregiment_DDR_03 is TResourceMesh( Mesh=Wach_1 )
export Modele_Wachregiment_DDR_LOW is TResourceMesh( Mesh=Wach_1_LOW )

//  Wachregiment_CMD_DDR
export Modele_Wachregiment_CMD_DDR is TResourceMesh( Mesh=Wach_NCO )
export Modele_Wachregiment_CMD_DDR_02 is TResourceMesh( Mesh=Wach_2 )
export Modele_Wachregiment_CMD_DDR_03 is TResourceMesh( Mesh=Wach_1 )
export Modele_Wachregiment_CMD_DDR_LOW is TResourceMesh( Mesh=Wach_1_LOW )

//Groupe_AT_Wach_DDR
export Modele_Groupe_AT_Wach_DDR is TResourceMesh( Mesh=Wach_NCO )
export Modele_Groupe_AT_Wach_DDR_02 is TResourceMesh( Mesh=Wach_2 )
export Modele_Groupe_AT_Wach_DDR_03 is TResourceMesh( Mesh=Wach_1 )
export Modele_Groupe_AT_Wach_DDR_LOW is TResourceMesh( Mesh=Wach_1_LOW )

//Scout_Wach_DDR
export Modele_Scout_Wach_DDR is TResourceMesh( Mesh=Wach_NCO )
export Modele_Scout_Wach_DDR_02 is TResourceMesh( Mesh=Wach_2 )
export Modele_Scout_Wach_DDR_03 is TResourceMesh( Mesh=Wach_1 )
export Modele_Scout_Wach_DDR_LOW is TResourceMesh( Mesh=Wach_1_LOW )

//Wachregiment_RPG_DDR
export Modele_Wachregiment_RPG_DDR is TResourceMesh( Mesh=Wach_NCO )
export Modele_Wachregiment_RPG_DDR_02 is TResourceMesh( Mesh=Wach_2 )
export Modele_Wachregiment_RPG_DDR_03 is TResourceMesh( Mesh=Wach_1 )
export Modele_Wachregiment_RPG_DDR_LOW is TResourceMesh( Mesh=Wach_1_LOW )

//  Scout_LRRP_DDR
export Modele_Scout_LRRP_DDR is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_Scout_LRRP_DDR_02 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_Scout_LRRP_DDR_03 is TResourceMesh( Mesh=DDR_FJB_NCO )
export Modele_Scout_LRRP_DDR_04 is TResourceMesh( Mesh=DDR_FJB_Officer )
export Modele_Scout_LRRP_DDR_05 is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_Scout_LRRP_DDR_06 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_Scout_LRRP_DDR_LOW is TResourceMesh( Mesh=DDR_FJB_LOW )

//  Scout_CMD_DDR

export Modele_Scout_CMD_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_Officer )
export Modele_Scout_CMD_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Scout_CMD_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_Scout_CMD_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_Scout_CMD_DDR_05 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_Scout_CMD_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//  MotRifles_CMD_DDR

export Modele_MotRifles_CMD_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_Officer )
export Modele_MotRifles_CMD_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_MotRifles_CMD_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_MotRifles_CMD_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_MotRifles_CMD_DDR_05 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_MotRifles_CMD_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//  KdA_DDR

export Modele_KdA_DDR is TResourceMesh( Mesh=DDR_KdA_NCO )
export Modele_KdA_DDR_02 is TResourceMesh( Mesh=DDR_KdA_1 )
export Modele_KdA_DDR_03 is TResourceMesh( Mesh=DDR_KdA_2 )
export Modele_KdA_DDR_LOW is TResourceMesh( Mesh=DDR_KdA_LOW )

//  KdA_CMD_DDR

export Modele_KdA_CMD_DDR is TResourceMesh( Mesh=DDR_KdA_NCO )
export Modele_KdA_CMD_DDR_02 is TResourceMesh( Mesh=DDR_KdA_NCO )
export Modele_KdA_CMD_DDR_03 is TResourceMesh( Mesh=DDR_KdA_1 )
export Modele_KdA_CMD_DDR_04 is TResourceMesh( Mesh=DDR_KdA_2 )
export Modele_KdA_CMD_DDR_LOW is TResourceMesh( Mesh=DDR_KdA_LOW )

//Scout_KdA_DDR
export Modele_Scout_KdA_DDR is TResourceMesh( Mesh=DDR_KdA_NCO )
export Modele_Scout_KdA_DDR_02 is TResourceMesh( Mesh=DDR_KdA_2 )
export Modele_Scout_KdA_DDR_03 is TResourceMesh( Mesh=DDR_KdA_1 )
export Modele_Scout_KdA_DDR_LOW is TResourceMesh( Mesh=DDR_KdA_LOW )

//  Fallschirmjager_FlaseFlag_Demo_DDR

export Modele_Fallschirmjager_FlaseFlag_Demo_DDR is TResourceMesh( Mesh=US_GI_1 )
export Modele_Fallschirmjager_FlaseFlag_Demo_DDR_02 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Fallschirmjager_FlaseFlag_Demo_DDR_03 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Fallschirmjager_FlaseFlag_Demo_DDR_04 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Fallschirmjager_FlaseFlag_Demo_DDR_05 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Fallschirmjager_FlaseFlag_Demo_DDR_06 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Fallschirmjager_FlaseFlag_Demo_DDR_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//  Fallschirmjager_FalseFlag_CMD_DDR

export Modele_Fallschirmjager_FalseFlag_CMD_DDR is TResourceMesh( Mesh=US_GI_1 )
export Modele_Fallschirmjager_FalseFlag_CMD_DDR_02 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Fallschirmjager_FalseFlag_CMD_DDR_03 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Fallschirmjager_FalseFlag_CMD_DDR_04 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Fallschirmjager_FalseFlag_CMD_DDR_05 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Fallschirmjager_FalseFlag_CMD_DDR_06 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Fallschirmjager_FalseFlag_CMD_DDR_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//  Fallschirmjager_CMD_DDR

export Modele_Fallschirmjager_CMD_DDR is TResourceMesh( Mesh=DDR_FJB_Officer )
export Modele_Fallschirmjager_CMD_DDR_02 is TResourceMesh( Mesh=DDR_FJB_NCO )
export Modele_Fallschirmjager_CMD_DDR_03 is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_Fallschirmjager_CMD_DDR_04 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_Fallschirmjager_CMD_DDR_LOW is TResourceMesh( Mesh=DDR_FJB_LOW )

//ATGM_Fagot_FJ_DDR
export Modele_ATGM_Fagot_FJ_DDR is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_ATGM_Fagot_FJ_DDR_02 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_ATGM_Fagot_FJ_DDR_LOW is TResourceMesh( Mesh=DDR_FJB_LOW )

//ATGM_Konkurs_DDR
export Modele_ATGM_Konkurs_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_ATGM_Konkurs_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_ATGM_Konkurs_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Engineers_CMD_DDR
export Modele_Engineers_CMD_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_Officer )
export Modele_Engineers_CMD_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Engineers_CMD_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_Engineers_CMD_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_Engineers_CMD_DDR_05 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_Engineers_CMD_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Engineers_Naval_CMD_DDR
export Modele_Engineers_Naval_CMD_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Engineers_Naval_CMD_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Engineers_Naval_DDR
export Modele_Engineers_Naval_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Engineers_Naval_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Engineers_Naval_Flam_DDR
export Modele_Engineers_Naval_Flam_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Engineers_Naval_Flam_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Engineers_Naval_Scout_DDR
export Modele_Engineers_Naval_Scout_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Engineers_Naval_Scout_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Engineers_DDR
export Modele_Engineers_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Engineers_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_Engineers_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_Engineers_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_Engineers_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Engineers_AGI_DDR
export Modele_Engineers_AGI_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Engineers_AGI_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_Engineers_AGI_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_Engineers_AGI_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_Engineers_AGI_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Engineers_Flam_DDR
export Modele_Engineers_Flam_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Engineers_Flam_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_Engineers_Flam_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_Engineers_Flam_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_Engineers_Flam_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Luftsturmjager_HMG_DDR
export Modele_Luftsturmjager_HMG_DDR is TResourceMesh( Mesh=DDR_FJB_NCO )
export Modele_Luftsturmjager_HMG_DDR_02 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_Luftsturmjager_HMG_DDR_03 is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_Luftsturmjager_HMG_DDR_LOW is TResourceMesh( Mesh=DDR_FJB_LOW )

//Fallschirmjager_HMG_DDR
export Modele_Fallschirmjager_HMG_DDR is TResourceMesh( Mesh=DDR_FJB_NCO )
export Modele_Fallschirmjager_HMG_DDR_02 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_Fallschirmjager_HMG_DDR_03 is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_Fallschirmjager_HMG_DDR_LOW is TResourceMesh( Mesh=DDR_FJB_LOW )

//Luftsturmjager_Metis_DDR
export Modele_Luftsturmjager_Metis_DDR is TResourceMesh( Mesh=DDR_FJB_NCO )
export Modele_Luftsturmjager_Metis_DDR_02 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_Luftsturmjager_Metis_DDR_03 is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_Luftsturmjager_Metis_DDR_LOW is TResourceMesh( Mesh=DDR_FJB_LOW )

//Luftsturmjager_DDR
export Modele_Luftsturmjager_DDR is TResourceMesh( Mesh=DDR_FJB_NCO )
export Modele_Luftsturmjager_DDR_02 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_Luftsturmjager_DDR_03 is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_Luftsturmjager_DDR_LOW is TResourceMesh( Mesh=DDR_FJB_LOW )

//Luftsturmjager_CMD_DDR
export Modele_Luftsturmjager_CMD_DDR is TResourceMesh( Mesh=DDR_FJB_Officer )
export Modele_Luftsturmjager_CMD_DDR_02 is TResourceMesh( Mesh=DDR_FJB_NCO )
export Modele_Luftsturmjager_CMD_DDR_03 is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_Luftsturmjager_CMD_DDR_04 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_Luftsturmjager_CMD_DDR_LOW is TResourceMesh( Mesh=DDR_FJB_LOW )

//Fallschirmjager_DDR
export Modele_Fallschirmjager_DDR is TResourceMesh( Mesh=DDR_FJB_NCO )
export Modele_Fallschirmjager_DDR_02 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_Fallschirmjager_DDR_03 is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_Fallschirmjager_DDR_LOW is TResourceMesh( Mesh=DDR_FJB_LOW )

//Fallschirmjager_Metys_DDR
export Modele_Fallschirmjager_Metys_DDR is TResourceMesh( Mesh=DDR_FJB_NCO )
export Modele_Fallschirmjager_Metys_DDR_02 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_Fallschirmjager_Metys_DDR_03 is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_Fallschirmjager_Metys_DDR_LOW is TResourceMesh( Mesh=DDR_FJB_LOW )

//Fallschirmjager_FalseFlag_DDR
export Modele_Fallschirmjager_FalseFlag_DDR is TResourceMesh( Mesh=US_GI_1 )
export Modele_Fallschirmjager_FalseFlag_DDR_02 is TResourceMesh( Mesh=US_GI_2 )
export Modele_Fallschirmjager_FalseFlag_DDR_03 is TResourceMesh( Mesh=US_GI_NCO )
export Modele_Fallschirmjager_FalseFlag_DDR_04 is TResourceMesh( Mesh=US_GI_spe )
export Modele_Fallschirmjager_FalseFlag_DDR_05 is TResourceMesh( Mesh=US_GI_Sniper )
export Modele_Fallschirmjager_FalseFlag_DDR_06 is TResourceMesh( Mesh=US_GI_Officer )
export Modele_Fallschirmjager_FalseFlag_DDR_LOW is TResourceMesh( Mesh=US_GI_1_LOW )

//HvyScout_DDR
export Modele_HvyScout_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_HvyScout_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_HvyScout_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_HvyScout_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_Officer )
export Modele_HvyScout_DDR_05 is TResourceMesh( Mesh=DDR_MotSchutzen_Sniper )
export Modele_HvyScout_DDR_06 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_HvyScout_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//MANPAD_Igla_DDR
export Modele_MANPAD_Igla_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_MANPAD_Igla_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_MANPAD_Igla_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//MANPAD_Strela_2M_DDR
export Modele_MANPAD_Strela_2M_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_MANPAD_Strela_2M_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_MANPAD_Strela_2M_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//MANPAD_Strela_2M_FJ_DDR
export Modele_MANPAD_Strela_2M_FJ_DDR is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_MANPAD_Strela_2M_FJ_DDR_02 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_MANPAD_Strela_2M_FJ_DDR_LOW is TResourceMesh( Mesh=DDR_FJB_LOW )

//Moto_Scout_DDR
export Modele_Moto_Scout_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_Moto_Scout_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_Moto_Scout_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Moto_Scout_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_Officer )
export Modele_Moto_Scout_DDR_05 is TResourceMesh( Mesh=DDR_MotSchutzen_Sniper )
export Modele_Moto_Scout_DDR_06 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_Moto_Scout_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//MotRifles_Strela_DDR
export Modele_MotRifles_Strela_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_MotRifles_Strela_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_MotRifles_Strela_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_MotRifles_Strela_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_Officer )
export Modele_MotRifles_Strela_DDR_05 is TResourceMesh( Mesh=DDR_MotSchutzen_Sniper )
export Modele_MotRifles_Strela_DDR_06 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_MotRifles_Strela_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//MotRifles_BTR_DDR
export Modele_MotRifles_BTR_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_MotRifles_BTR_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_MotRifles_BTR_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_MotRifles_BTR_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_MotRifles_BTR_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//MotRifles_HMG_DDR
export Modele_MotRifles_HMG_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_MotRifles_HMG_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_MotRifles_HMG_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_MotRifles_HMG_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_MotRifles_HMG_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Security_DDR
export Modele_Security_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_Security_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_Security_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Security_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_Officer )
export Modele_Security_DDR_05 is TResourceMesh( Mesh=DDR_MotSchutzen_Sniper )
export Modele_Security_DDR_06 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_Security_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//MotRifles_SVD_DDR
export Modele_MotRifles_SVD_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_MotRifles_SVD_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_MotRifles_SVD_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_Sniper )
export Modele_MotRifles_SVD_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_MotRifles_SVD_DDR_05 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_MotRifles_SVD_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//MotRifles_RPG27_DDR
export Modele_MotRifles_RPG27_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_MotRifles_RPG27_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_MotRifles_RPG27_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_MotRifles_RPG27_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_MotRifles_RPG27_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//MotRifles_DDR
export Modele_MotRifles_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_MotRifles_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_MotRifles_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_MotRifles_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_MotRifles_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//MotRifles_Metis_DDR
export Modele_MotRifles_Metis_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_MotRifles_Metis_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_MotRifles_Metis_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_MotRifles_Metis_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_MotRifles_Metis_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Scout_Reserve_DDR
export Modele_Scout_Reserve_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_Scout_Reserve_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Scout_DDR
export Modele_Scout_DDR is TResourceMesh( Mesh=DDR_MotSchutzen_1 )
export Modele_Scout_DDR_02 is TResourceMesh( Mesh=DDR_MotSchutzen_2 )
export Modele_Scout_DDR_03 is TResourceMesh( Mesh=DDR_MotSchutzen_NCO )
export Modele_Scout_DDR_04 is TResourceMesh( Mesh=DDR_MotSchutzen_Officer )
export Modele_Scout_DDR_05 is TResourceMesh( Mesh=DDR_MotSchutzen_Sniper )
export Modele_Scout_DDR_06 is TResourceMesh( Mesh=DDR_MotSchutzen_spe )
export Modele_Scout_DDR_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//Scout_FJ_DDR
export Modele_Scout_FJ_DDR is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_Scout_FJ_DDR_02 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_Scout_FJ_DDR_03 is TResourceMesh( Mesh=DDR_FJB_NCO )
export Modele_Scout_FJ_DDR_04 is TResourceMesh( Mesh=DDR_FJB_Officer )
export Modele_Scout_FJ_DDR_05 is TResourceMesh( Mesh=DDR_FJB_1 )
export Modele_Scout_FJ_DDR_06 is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_Scout_FJ_DDR_LOW is TResourceMesh( Mesh=DDR_FJB_LOW )

//Volkspolizei_CMD_DDR

export Modele_Volkspolizei_CMD_DDR is TResourceMesh( Mesh=DDR_Grenzer_1 )
export Modele_Volkspolizei_CMD_DDR_02 is TResourceMesh( Mesh=DDR_Grenzer_2 )
export Modele_Volkspolizei_CMD_DDR_03 is TResourceMesh( Mesh=DDR_Grenzer_NCO )
export Modele_Volkspolizei_CMD_DDR_04 is TResourceMesh( Mesh=DDR_Grenzer_1 )
export Modele_Volkspolizei_CMD_DDR_05 is TResourceMesh( Mesh=DDR_Grenzer_2 )
export Modele_Volkspolizei_CMD_DDR_06 is TResourceMesh( Mesh=DDR_Grenzer_NCO )
export Modele_Volkspolizei_CMD_DDR_LOW is TResourceMesh( Mesh=DDR_Grenzer_LOW )

//Volkspolizei_DDR

export Modele_Volkspolizei_DDR is TResourceMesh( Mesh=DDR_Grenzer_1 )
export Modele_Volkspolizei_DDR_02 is TResourceMesh( Mesh=DDR_Grenzer_2 )
export Modele_Volkspolizei_DDR_03 is TResourceMesh( Mesh=DDR_Grenzer_NCO )
export Modele_Volkspolizei_DDR_04 is TResourceMesh( Mesh=DDR_Grenzer_1 )
export Modele_Volkspolizei_DDR_05 is TResourceMesh( Mesh=DDR_Grenzer_2 )
export Modele_Volkspolizei_DDR_06 is TResourceMesh( Mesh=DDR_Grenzer_NCO )
export Modele_Volkspolizei_DDR_LOW is TResourceMesh( Mesh=DDR_Grenzer_LOW )


//------------------------------------------------------------------------------
//
//
//                                   SOLDATS TCH
//
//
//------------------------------------------------------------------------------

TCH_MP is 'GameData:/Assets/3D/Units/TCH/Infanterie/MP/MP.fbx'
TCH_MP_LOW is 'GameData:/Assets/3D/Units/TCH/Infanterie/MP/MP.fbx'

TCH_AA_vz60 is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz60/AA_vz60.fbx'
TCH_Gunner_vz60 is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz60/Gunner_vz60.fbx'
TCH_NCO_vz60 is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz60/NCO_vz60.fbx'
TCH_Officer_vz60 is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz60/Officer_vz60.fbx'
TCH_Soldier_vz60_1 is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz60/Soldier_vz60_1.fbx'
TCH_Soldier_vz60_2 is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz60/Soldier_vz60_2.fbx'
TCH_Soldier_vz60_1_LM is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz60/Soldier_vz60_1_LM.fbx'
TCH_Soldier_vz60_2_LM is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz60/Soldier_vz60_2_LM.fbx'
TCH_NCO_vz60_LM is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz60/NCO_vz60_LM.fbx'
TCH_Soldier_vz60_LOW is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz60/Soldier_vz60_1.fbx'

TCH_AA_vz85 is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz85/AA_vz85.fbx'
TCH_NCO_vz85 is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz85/NCO_vz85.fbx'
TCH_Officer_vz85 is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz85/Officer_vz85.fbx'
TCH_Soldier_vz85_1 is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz85/Soldier_vz85_1.fbx'
TCH_Soldier_vz85_2 is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz85/Soldier_vz85_2.fbx'
TCH_Soldier_vz85_LOW is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz85/Soldier_vz85_1.fbx'

TCH_AA_Vysadkar is 'GameData:/Assets/3D/Units/TCH/Infanterie/Vysadkar/AA_Vysadkar.fbx'
TCH_NCO_Vysadkar is 'GameData:/Assets/3D/Units/TCH/Infanterie/Vysadkar/NCO_Vysadkar.fbx'
TCH_Officer_Vysadkar is 'GameData:/Assets/3D/Units/TCH/Infanterie/Vysadkar/Officer_Vysadkar.fbx'
TCH_Vysadkar_1 is 'GameData:/Assets/3D/Units/TCH/Infanterie/Vysadkar/Vysadkar_1.fbx'
TCH_Vysadkar_2 is 'GameData:/Assets/3D/Units/TCH/Infanterie/Vysadkar/Vysadkar_2.fbx'
TCH_Vysadkar_LOW is 'GameData:/Assets/3D/Units/TCH/Infanterie/Soldier_vz85/Soldier_vz85_1.fbx'

//------------------------------------------------------------------------------

//MP_TCH
export Modele_MP_TCH is TResourceMesh( Mesh=TCH_MP )
export Modele_MP_TCH_LOW is TResourceMesh( Mesh=TCH_MP_LOW )

//MANPAD_Strela_2M_TCH
export Modele_MANPAD_Strela_2M_TCH is TResourceMesh( Mesh=TCH_AA_vz60 )
export Modele_MANPAD_Strela_2M_TCH_02 is TResourceMesh( Mesh=TCH_NCO_vz60 )
export Modele_MANPAD_Strela_2M_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz60_LOW )

//MANPAD_Igla_Vysadkari_TCH
export Modele_MANPAD_Igla_Vysadkari_TCH is TResourceMesh( Mesh=TCH_AA_Vysadkar )
export Modele_MANPAD_Igla_Vysadkari_TCH_02 is TResourceMesh( Mesh=TCH_NCO_Vysadkar )
export Modele_MANPAD_Igla_Vysadkari_TCH_LOW is TResourceMesh( Mesh=TCH_Vysadkar_LOW )

//MANPAD_Igla_TCH
export Modele_MANPAD_Igla_TCH is TResourceMesh( Mesh=TCH_AA_vz85 )
export Modele_MANPAD_Igla_TCH_02 is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_MANPAD_Igla_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//Militia_CMD_TCH
export Modele_Militia_CMD_TCH is TResourceMesh( Mesh=TCH_Officer_vz60 )
export Modele_Militia_CMD_TCH_02 is TResourceMesh( Mesh=TCH_NCO_vz60_LM )
export Modele_Militia_CMD_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz60_1_LM )
export Modele_Militia_CMD_TCH_04 is TResourceMesh( Mesh=TCH_Soldier_vz60_2_LM )
export Modele_Militia_CMD_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz60_LOW )

//Militia_TCH
export Modele_Militia_TCH is TResourceMesh( Mesh=TCH_NCO_vz60_LM )
export Modele_Militia_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz60_1_LM )
export Modele_Militia_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz60_2_LM )
export Modele_Militia_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz60_LOW )

//Scout_Militia_TCH
export Modele_Scout_Militia_TCH is TResourceMesh( Mesh=TCH_NCO_vz60_LM )
export Modele_Scout_Militia_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz60_1_LM )
export Modele_Scout_Militia_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz60_2_LM )
export Modele_Scout_Militia_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz60_LOW )

//Reserve_TCH
export Modele_Reserve_TCH is TResourceMesh( Mesh=TCH_NCO_vz60 )
export Modele_Reserve_TCH_02 is TResourceMesh( Mesh=TCH_Gunner_vz60 )
export Modele_Reserve_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz60_2 )
export Modele_Reserve_TCH_04 is TResourceMesh( Mesh=TCH_Soldier_vz60_1 )
export Modele_Reserve_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz60_LOW )

//Vysadkari_CMD_TCH
export Modele_Vysadkari_CMD_TCH is TResourceMesh( Mesh=TCH_Officer_Vysadkar )
export Modele_Vysadkari_CMD_TCH_02 is TResourceMesh( Mesh=TCH_NCO_Vysadkar )
export Modele_Vysadkari_CMD_TCH_03 is TResourceMesh( Mesh=TCH_Vysadkar_1 )
export Modele_Vysadkari_CMD_TCH_04 is TResourceMesh( Mesh=TCH_Vysadkar_2 )
export Modele_Vysadkari_CMD_TCH_LOW is TResourceMesh( Mesh=TCH_Vysadkar_LOW )

//Scout_Vysadkari_TCH
export Modele_Scout_Vysadkari_TCH is TResourceMesh( Mesh=TCH_NCO_Vysadkar )
export Modele_Scout_Vysadkari_TCH_02 is TResourceMesh( Mesh=TCH_Vysadkar_1 )
export Modele_Scout_Vysadkari_TCH_03 is TResourceMesh( Mesh=TCH_Vysadkar_2 )
export Modele_Scout_Vysadkari_TCH_LOW is TResourceMesh( Mesh=TCH_Vysadkar_LOW )

//Vysadkari_TCH
export Modele_Vysadkari_TCH is TResourceMesh( Mesh=TCH_NCO_Vysadkar )
export Modele_Vysadkari_TCH_02 is TResourceMesh( Mesh=TCH_Vysadkar_1 )
export Modele_Vysadkari_TCH_03 is TResourceMesh( Mesh=TCH_Vysadkar_2 )
export Modele_Vysadkari_TCH_LOW is TResourceMesh( Mesh=TCH_Vysadkar_LOW )

//Engineers_Vysadkari_TCH
export Modele_Engineers_Vysadkari_TCH is TResourceMesh( Mesh=TCH_NCO_Vysadkar )
export Modele_Engineers_Vysadkari_TCH_02 is TResourceMesh( Mesh=TCH_Vysadkar_1 )
export Modele_Engineers_Vysadkari_TCH_03 is TResourceMesh( Mesh=TCH_Vysadkar_2 )
export Modele_Engineers_Vysadkari_TCH_LOW is TResourceMesh( Mesh=TCH_Vysadkar_LOW )

//Engineers_CMD_TCH
export Modele_Engineers_CMD_TCH is TResourceMesh( Mesh=TCH_Officer_vz85 )
export Modele_Engineers_CMD_TCH_02 is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_Engineers_CMD_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_Engineers_CMD_TCH_04 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_Engineers_CMD_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//Engineers_Flam_TCH
export Modele_Engineers_Flam_TCH is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_Engineers_Flam_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_Engineers_Flam_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_Engineers_Flam_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//Engineers_Scout_TCH
export Modele_Engineers_Scout_TCH is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_Engineers_Scout_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_Engineers_Scout_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_Engineers_Scout_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//Engineers_TCH
export Modele_Engineers_TCH is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_Engineers_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_Engineers_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_Engineers_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//Groupe_AT_TCH
export Modele_Groupe_AT_TCH is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_Groupe_AT_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_Groupe_AT_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_Groupe_AT_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//HvyScout_TCH
export Modele_HvyScout_TCH is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_HvyScout_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_HvyScout_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_HvyScout_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//MotRifles_CMD_TCH
export Modele_MotRifles_CMD_TCH is TResourceMesh( Mesh=TCH_Officer_vz85 )
export Modele_MotRifles_CMD_TCH_02 is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_MotRifles_CMD_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_MotRifles_CMD_TCH_04 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_MotRifles_CMD_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//MotRifles_TCH
export Modele_MotRifles_TCH is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_MotRifles_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_MotRifles_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_MotRifles_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//MotRifles_RPG7_TCH
export Modele_MotRifles_RPG7_TCH is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_MotRifles_RPG7_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_MotRifles_RPG7_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_MotRifles_RPG7_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//Rifles_CMD_TCH
export Modele_Rifles_CMD_TCH is TResourceMesh( Mesh=TCH_Officer_vz85 )
export Modele_Rifles_CMD_TCH_02 is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_Rifles_CMD_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_Rifles_CMD_TCH_04 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_Rifles_CMD_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//Rifles_HMG_TCH
export Modele_Rifles_HMG_TCH is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_Rifles_HMG_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_Rifles_HMG_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_Rifles_HMG_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//Rifles_TCH
export Modele_Rifles_TCH is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_Rifles_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_Rifles_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_Rifles_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//Scout_LRRP_TCH
export Modele_Scout_LRRP_TCH is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_Scout_LRRP_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_Scout_LRRP_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_Scout_LRRP_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//Scout_SF_TCH
export Modele_Scout_SF_TCH is TResourceMesh( Mesh=TCH_NCO_Vysadkar )
export Modele_Scout_SF_TCH_02 is TResourceMesh( Mesh=TCH_NCO_Vysadkar )
export Modele_Scout_SF_TCH_03 is TResourceMesh( Mesh=TCH_NCO_Vysadkar )
export Modele_Scout_SF_TCH_LOW is TResourceMesh( Mesh=TCH_Vysadkar_LOW )

//Scout_SIGINT_TCH
export Modele_Scout_SIGINT_TCH is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_Scout_SIGINT_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz85_1 )
export Modele_Scout_SIGINT_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_Scout_SIGINT_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//Scout_TCH
export Modele_Scout_TCH is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_Scout_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_Scout_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//Sniper_TCH
export Modele_Sniper_TCH is TResourceMesh( Mesh=TCH_NCO_vz85 )
export Modele_Sniper_TCH_02 is TResourceMesh( Mesh=TCH_Soldier_vz85_2 )
export Modele_Sniper_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz85_LOW )

//Security_TCH
export Modele_Security_TCH is TResourceMesh( Mesh=TCH_NCO_vz60 )
export Modele_Security_TCH_02 is TResourceMesh( Mesh=TCH_Gunner_vz60 )
export Modele_Security_TCH_03 is TResourceMesh( Mesh=TCH_Soldier_vz60_2 )
export Modele_Security_TCH_04 is TResourceMesh( Mesh=TCH_Soldier_vz60_1 )
export Modele_Security_TCH_LOW is TResourceMesh( Mesh=TCH_Soldier_vz60_LOW )


//------------------------------------------------------------------------------
//
//
//                                   SOLDATS RFA
//
//
//------------------------------------------------------------------------------
RFA_BGS_1 is 'GameData:/Assets/3D/Units/RFA/Infanterie/BGS/BGS_1.fbx'
RFA_BGS_2 is 'GameData:/Assets/3D/Units/RFA/Infanterie/BGS/BGS_2.fbx'
RFA_BGS_3 is 'GameData:/Assets/3D/Units/RFA/Infanterie/BGS/BGS_3.fbx'
RFA_BGS_1_LOW is 'GameData:/Assets/3D/Units/RFA/Infanterie/BGS/BGS_1_LOW.fbx'

RFA_Fallschirm_1 is 'GameData:/Assets/3D/Units/RFA/Infanterie/Fallschirm/Fallschirm_1.fbx'
RFA_Fallschirm_2 is  'GameData:/Assets/3D/Units/RFA/Infanterie/Fallschirm/Fallschirm_2.fbx'
RFA_Fallschirm_NCO is  'GameData:/Assets/3D/Units/RFA/Infanterie/Fallschirm/Fallschirm_NCO.fbx'
RFA_Fallschirm_Officer is  'GameData:/Assets/3D/Units/RFA/Infanterie/Fallschirm/Fallschirm_Officer.fbx'
RFA_Fallschirm_spe is  'GameData:/Assets/3D/Units/RFA/Infanterie/Fallschirm/Fallschirm_spe.fbx'
RFA_Fallschirm_LOW is  'GameData:/Assets/3D/Units/RFA/Infanterie/Fallschirm/Fallschirm_1_LOW.fbx'

RFA_Fernspaher_1 is 'GameData:/Assets/3D/Units/RFA/Infanterie/Fernspaher/Fernspaher_1.fbx'
RFA_Fernspaher_2 is 'GameData:/Assets/3D/Units/RFA/Infanterie/Fernspaher/Fernspaher_2.fbx'
RFA_Fernspaher_LOW is 'GameData:/Assets/3D/Units/RFA/Infanterie/Fernspaher/Fernspaher_1_LOW.fbx'

RFA_Jager_1 is 'GameData:/Assets/3D/Units/RFA/Infanterie/Jager/Jager_1.fbx'
RFA_Jager_NCO is  'GameData:/Assets/3D/Units/RFA/Infanterie/Jager/Jager_NCO.fbx'
RFA_Jager_spe is  'GameData:/Assets/3D/Units/RFA/Infanterie/Jager/Jager_spe.fbx'
RFA_Jager_LOW is  'GameData:/Assets/3D/Units/RFA/Infanterie/Jager/Jager_1_LOW.fbx'

RFA_PzGrenadier_1 is 'GameData:/Assets/3D/Units/RFA/Infanterie/PzGrenadier/PzGrenadier_1.fbx'
RFA_PzGrenadier_2 is 'GameData:/Assets/3D/Units/RFA/Infanterie/PzGrenadier/PzGrenadier_2.fbx'
RFA_PzGrenadier_NCO is 'GameData:/Assets/3D/Units/RFA/Infanterie/PzGrenadier/PzGrenadier_NCO.fbx'
RFA_PzGrenadier_Officer is 'GameData:/Assets/3D/Units/RFA/Infanterie/PzGrenadier/PzGrenadier_Officer.fbx'
RFA_PzGrenadier_sniper is 'GameData:/Assets/3D/Units/RFA/Infanterie/PzGrenadier/PzGrenadier_Sniper.fbx'
RFA_PzGrenadier_spe is 'GameData:/Assets/3D/Units/RFA/Infanterie/PzGrenadier/PzGrenadier_spe.fbx'
RFA_PzGrenadier_LOW is 'GameData:/Assets/3D/Units/RFA/Infanterie/PzGrenadier/PzGrenadier_1_LOW.fbx'

Feldjager_2 is 'GameData:/Assets/3D/Units/RFA/Infanterie/PzGrenadier/Feldjager_2.fbx'
Feldjager_NCO is 'GameData:/Assets/3D/Units/RFA/Infanterie/PzGrenadier/Feldjager_NCO.fbx'

FRP_1 is 'GameData:/Assets/3D/Units/RFA/Infanterie/FRP/FRP_1.fbx'
FRP_2 is 'GameData:/Assets/3D/Units/RFA/Infanterie/FRP/FRP_2.fbx'

SEK_1 is 'GameData:/Assets/3D/Units/RFA/Infanterie/BGS/SEK_1.fbx'
SEK_2 is 'GameData:/Assets/3D/Units/RFA/Infanterie/BGS/SEK_2.fbx'
SEK_3 is 'GameData:/Assets/3D/Units/RFA/Infanterie/BGS/SEK_3.fbx'

//'GameData:/Assets/3D/Units/RFA/Infanterie/PzGrenadier/Feldjager_2.fbx'
//'GameData:/Assets/3D/Units/RFA/Infanterie/PzGrenadier/Feldjager_NCO.fbx'

//------------------------------------------------------------------------------
//SEK_RFA
export Modele_SEK_RFA is TResourceMesh( Mesh=SEK_1 )
export Modele_SEK_RFA_02 is TResourceMesh( Mesh=SEK_1 )
export Modele_SEK_RFA_03 is TResourceMesh( Mesh=SEK_2 )
export Modele_SEK_RFA_04 is TResourceMesh( Mesh=SEK_2 )
export Modele_SEK_RFA_05 is TResourceMesh( Mesh=SEK_3 )
export Modele_SEK_RFA_06 is TResourceMesh( Mesh=SEK_3 )
export Modele_SEK_RFA_LOW is TResourceMesh( Mesh=SEK_1 )

//BGS_hvy_RFA
export Modele_BGS_hvy_RFA is TResourceMesh( Mesh=SEK_3 )
export Modele_BGS_hvy_RFA_02 is TResourceMesh( Mesh=RFA_BGS_1 )
export Modele_BGS_hvy_RFA_03 is TResourceMesh( Mesh=RFA_BGS_3 )
export Modele_BGS_hvy_RFA_LOW is TResourceMesh( Mesh=RFA_BGS_1_LOW )

//BGS_RFA
export Modele_BGS_RFA is TResourceMesh( Mesh=RFA_BGS_3 )
export Modele_BGS_RFA_02 is TResourceMesh( Mesh=RFA_BGS_2 )
export Modele_BGS_RFA_03 is TResourceMesh( Mesh=RFA_BGS_1 )
export Modele_BGS_RFA_LOW is TResourceMesh( Mesh=RFA_BGS_1_LOW )

//------------------------------------------------------------------------------
//Fallschirm_B1_RFA
export Modele_Fallschirm_B1_RFA is TResourceMesh( Mesh=RFA_Fallschirm_NCO )
export Modele_Fallschirm_B1_RFA_02 is TResourceMesh( Mesh=RFA_Fallschirm_spe )
export Modele_Fallschirm_B1_RFA_03 is TResourceMesh( Mesh=RFA_Fallschirm_1 )
export Modele_Fallschirm_B1_RFA_04 is TResourceMesh( Mesh=RFA_Fallschirm_2 )
export Modele_Fallschirm_B1_RFA_LOW is TResourceMesh( Mesh=RFA_Fallschirm_LOW )

//Fallschirm_Engineers_RFA
export Modele_Fallschirm_Engineers_RFA is TResourceMesh( Mesh=RFA_Fallschirm_NCO )
export Modele_Fallschirm_Engineers_RFA_02 is TResourceMesh( Mesh=RFA_Fallschirm_spe )
export Modele_Fallschirm_Engineers_RFA_03 is TResourceMesh( Mesh=RFA_Fallschirm_1 )
export Modele_Fallschirm_Engineers_RFA_04 is TResourceMesh( Mesh=RFA_Fallschirm_2 )
export Modele_Fallschirm_Engineers_RFA_LOW is TResourceMesh( Mesh=RFA_Fallschirm_LOW )

//Fallschirm_Reserve_RFA
export Modele_Fallschirm_Reserve_RFA is TResourceMesh( Mesh=RFA_Fallschirm_NCO )
export Modele_Fallschirm_Reserve_RFA_LOW is TResourceMesh( Mesh=RFA_Fallschirm_LOW )

//Fallschirm_RFA
export Modele_Fallschirm_RFA is TResourceMesh( Mesh=RFA_Fallschirm_NCO )
export Modele_Fallschirm_RFA_02 is TResourceMesh( Mesh=RFA_Fallschirm_spe )
export Modele_Fallschirm_RFA_03 is TResourceMesh( Mesh=RFA_Fallschirm_1 )
export Modele_Fallschirm_RFA_04 is TResourceMesh( Mesh=RFA_Fallschirm_2 )
export Modele_Fallschirm_RFA_LOW is TResourceMesh( Mesh=RFA_Fallschirm_LOW )

//------------------------------------------------------------------------------


//Fernspaher_RFA
export Modele_Fernspaher_RFA is TResourceMesh( Mesh=RFA_Fernspaher_1 )
export Modele_Fernspaher_RFA_02 is TResourceMesh( Mesh=RFA_Fernspaher_1 )
export Modele_Fernspaher_RFA_03 is TResourceMesh( Mesh=RFA_Fernspaher_1 )
export Modele_Fernspaher_RFA_04 is TResourceMesh( Mesh=RFA_Fernspaher_2 )
export Modele_Fernspaher_RFA_05 is TResourceMesh( Mesh=RFA_Fernspaher_2 )
export Modele_Fernspaher_RFA_06 is TResourceMesh( Mesh=RFA_Fernspaher_2 )
export Modele_Fernspaher_RFA_LOW is TResourceMesh( Mesh=RFA_Fernspaher_LOW )

//------------------------------------------------------------------------------

//Jager_CMD_RFA
export Modele_Jager_CMD_RFA is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Jager_CMD_RFA_02 is TResourceMesh( Mesh=RFA_Jager_NCO )
export Modele_Jager_CMD_RFA_03 is TResourceMesh( Mesh=RFA_Jager_spe )
export Modele_Jager_CMD_RFA_04 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Jager_CMD_RFA_05 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Jager_CMD_RFA_06 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Jager_CMD_RFA_LOW is TResourceMesh( Mesh=RFA_Jager_LOW )

//Jager_Carl_RFA
export Modele_Jager_Carl_RFA is TResourceMesh( Mesh=RFA_Jager_NCO )
export Modele_Jager_Carl_RFA_02 is TResourceMesh( Mesh=RFA_Jager_spe )
export Modele_Jager_Carl_RFA_03 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Jager_Carl_RFA_LOW is TResourceMesh( Mesh=RFA_Jager_LOW )

//Jager_noAT_RFA
export Modele_Jager_noAT_RFA is TResourceMesh( Mesh=RFA_Jager_NCO )
export Modele_Jager_noAT_RFA_02 is TResourceMesh( Mesh=RFA_Jager_spe )
export Modele_Jager_noAT_RFA_03 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Jager_noAT_RFA_LOW is TResourceMesh( Mesh=RFA_Jager_LOW )

//Jager_RFA
export Modele_Jager_RFA is TResourceMesh( Mesh=RFA_Jager_NCO )
export Modele_Jager_RFA_02 is TResourceMesh( Mesh=RFA_Jager_spe )
export Modele_Jager_RFA_03 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Jager_RFA_LOW is TResourceMesh( Mesh=RFA_Jager_LOW )

//------------------------------------------------------------------------------
//Engineers_Geb_RFA_RFA
export Modele_Engineers_Geb_RFA_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Engineers_Geb_RFA_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Gebirgsjager_CMD_RFA
export Modele_Gebirgsjager_CMD_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Gebirgsjager_CMD_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Gebirgsjager_Hvy_RFA
export Modele_Gebirgsjager_Hvy_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Gebirgsjager_Hvy_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Gebirgsjager_JagdKdo_RFA
export Modele_Gebirgsjager_JagdKdo_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Gebirgsjager_JagdKdo_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Gebirgsjager_RFA
export Modele_Gebirgsjager_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Gebirgsjager_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Gebirgsjager_Scout_RFA
export Modele_Gebirgsjager_Scout_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Gebirgsjager_Scout_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Hochgebirgjager_RFA
export Modele_Hochgebirgjager_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Hochgebirgjager_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Sniper_Geb_RFA
export Modele_Sniper_Geb_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Sniper_Geb_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//PzGrenadier_RFA
export Modele_PzGrenadier_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_PzGrenadier_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_PzGrenadier_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_spe )
export Modele_PzGrenadier_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_PzGrenadier_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_PzGrenadier_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Panzergrenadier_PzF3_RFA
export Modele_Panzergrenadier_PzF3_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Panzergrenadier_PzF3_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_Panzergrenadier_PzF3_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_spe )
export Modele_Panzergrenadier_PzF3_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_Panzergrenadier_PzF3_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_Panzergrenadier_PzF3_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Panzergrenadier_IFV_RFA
export Modele_Panzergrenadier_IFV_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Panzergrenadier_IFV_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_Panzergrenadier_IFV_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_spe )
export Modele_Panzergrenadier_IFV_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_Panzergrenadier_IFV_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_Panzergrenadier_IFV_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Panzergrenadier_APC_RFA
export Modele_Panzergrenadier_APC_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Panzergrenadier_APC_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_Panzergrenadier_APC_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_spe )
export Modele_Panzergrenadier_APC_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_Panzergrenadier_APC_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_Panzergrenadier_APC_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//-------------------------------------------------------------------------------------

//-------------------------------------------------------------------------------------
//Security_RFA
export Modele_Security_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_Security_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_Security_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Security_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_Security_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_Security_RFA_06 is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Security_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Scout_RFA
export Modele_Scout_RFA is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Scout_RFA_02 is TResourceMesh( Mesh=RFA_Jager_NCO )
export Modele_Scout_RFA_03 is TResourceMesh( Mesh=RFA_Jager_spe )
export Modele_Scout_RFA_04 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Scout_RFA_05 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Scout_RFA_06 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Scout_RFA_LOW is TResourceMesh( Mesh=RFA_Jager_LOW )

//MANPAD_Redeye_RFA
export Modele_MANPAD_Redeye_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_MANPAD_Redeye_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_MANPAD_Redeye_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_MANPAD_Redeye_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_Officer )
export Modele_MANPAD_Redeye_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_MANPAD_Redeye_RFA_06 is TResourceMesh( Mesh=RFA_PzGrenadier_spe )
export Modele_MANPAD_Redeye_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Engineers_AT_RFA
export Modele_Engineers_AT_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_Engineers_AT_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_Engineers_AT_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Engineers_AT_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_Officer )
export Modele_Engineers_AT_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_Engineers_AT_RFA_06 is TResourceMesh( Mesh=RFA_PzGrenadier_spe )
export Modele_Engineers_AT_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Engineers_CMD_RFA
export Modele_Engineers_CMD_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_Engineers_CMD_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_Engineers_CMD_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Engineers_CMD_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_Officer )
export Modele_Engineers_CMD_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_Engineers_CMD_RFA_06 is TResourceMesh( Mesh=RFA_PzGrenadier_spe )
export Modele_Engineers_CMD_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//LRRP_RFA
export Modele_LRRP_RFA is TResourceMesh( Mesh=RFA_Fernspaher_1 )
export Modele_LRRP_RFA_02 is TResourceMesh( Mesh=RFA_Fernspaher_1 )
export Modele_LRRP_RFA_03 is TResourceMesh( Mesh=RFA_Fernspaher_1 )
export Modele_LRRP_RFA_04 is TResourceMesh( Mesh=RFA_Fernspaher_2 )
export Modele_LRRP_RFA_05 is TResourceMesh( Mesh=RFA_Fernspaher_2 )
export Modele_LRRP_RFA_06 is TResourceMesh( Mesh=RFA_Fernspaher_2 )
export Modele_LRRP_RFA_LOW is TResourceMesh( Mesh=RFA_Fernspaher_LOW )


//KSM_RFA
export Modele_KSM_RFA is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx" )
export Modele_KSM_RFA_02 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx" )
export Modele_KSM_RFA_03 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx" )
export Modele_KSM_RFA_04 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx" )
export Modele_KSM_RFA_05 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx" )
export Modele_KSM_RFA_06 is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx" )
export Modele_KSM_RFA_LOW is TResourceMesh( Mesh="GameData:/Assets/3D/Units/US/Infanterie/GI/GI_1.fbx" )

//Reserve_Polizei_RFA
export Modele_Reserve_Polizei_RFA is TResourceMesh( Mesh=FRP_1 )
export Modele_Reserve_Polizei_RFA_02 is TResourceMesh( Mesh=FRP_2 )
export Modele_Reserve_Polizei_RFA_LOW is TResourceMesh( Mesh=RFA_BGS_1_LOW )

//HeimatschutzJager_RFA
export Modele_HeimatschutzJager_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_HeimatschutzJager_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_HeimatschutzJager_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_HeimatschutzJager_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_Officer )
export Modele_HeimatschutzJager_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_HeimatschutzJager_RFA_06 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_HeimatschutzJager_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Feldgendarmerie_RFA
export Modele_Feldgendarmerie_RFA is TResourceMesh( Mesh=Feldjager_2 )
export Modele_Feldgendarmerie_RFA_02 is TResourceMesh( Mesh=Feldjager_2 )
export Modele_Feldgendarmerie_RFA_03 is TResourceMesh( Mesh=Feldjager_2 )
export Modele_Feldgendarmerie_RFA_04 is TResourceMesh( Mesh=Feldjager_NCO )
export Modele_Feldgendarmerie_RFA_05 is TResourceMesh( Mesh=Feldjager_2 )
export Modele_Feldgendarmerie_RFA_06 is TResourceMesh( Mesh=Feldjager_NCO )
export Modele_Feldgendarmerie_RFA_LOW is TResourceMesh( Mesh=RFA_Jager_LOW )

//Engineers_RFA
export Modele_Engineers_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_Engineers_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_Engineers_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Engineers_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_Officer )
export Modele_Engineers_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_Engineers_RFA_06 is TResourceMesh( Mesh=RFA_PzGrenadier_spe )
export Modele_Engineers_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Engineers_Flam_RFA
export Modele_Engineers_Flam_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_Engineers_Flam_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_Engineers_Flam_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Engineers_Flam_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_Officer )
export Modele_Engineers_Flam_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_Engineers_Flam_RFA_06 is TResourceMesh( Mesh=RFA_PzGrenadier_spe )
export Modele_Engineers_Flam_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Engineers_Reserve_RFA
export Modele_Engineers_Reserve_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_Engineers_Reserve_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_Engineers_Reserve_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Engineers_Reserve_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_Engineers_Reserve_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Jager_Aufk_RFA
export Modele_Jager_Aufk_RFA is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Jager_Aufk_RFA_02 is TResourceMesh( Mesh=RFA_Jager_NCO )
export Modele_Jager_Aufk_RFA_03 is TResourceMesh( Mesh=RFA_Jager_spe )
export Modele_Jager_Aufk_RFA_04 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Jager_Aufk_RFA_05 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Jager_Aufk_RFA_06 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_Jager_Aufk_RFA_LOW is TResourceMesh( Mesh=RFA_Jager_LOW )

//ATGM_Milan_2_RFA
export Modele_ATGM_Milan_2_RFA is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_ATGM_Milan_2_RFA_02 is TResourceMesh( Mesh=RFA_Jager_NCO )
export Modele_ATGM_Milan_2_RFA_03 is TResourceMesh( Mesh=RFA_Jager_spe )
export Modele_ATGM_Milan_2_RFA_04 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_ATGM_Milan_2_RFA_05 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_ATGM_Milan_2_RFA_06 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_ATGM_Milan_2_RFA_LOW is TResourceMesh( Mesh=RFA_Jager_LOW )

//ATGM_Milan_1_RFA
export Modele_ATGM_Milan_1_RFA is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_ATGM_Milan_1_RFA_02 is TResourceMesh( Mesh=RFA_Jager_NCO )
export Modele_ATGM_Milan_1_RFA_03 is TResourceMesh( Mesh=RFA_Jager_spe )
export Modele_ATGM_Milan_1_RFA_04 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_ATGM_Milan_1_RFA_05 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_ATGM_Milan_1_RFA_06 is TResourceMesh( Mesh=RFA_Jager_1 )
export Modele_ATGM_Milan_1_RFA_LOW is TResourceMesh( Mesh=RFA_Jager_LOW )

//Panzergrenadier_CMD_RFA
export Modele_Panzergrenadier_CMD_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_Officer )
export Modele_Panzergrenadier_CMD_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Panzergrenadier_CMD_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_spe )
export Modele_Panzergrenadier_CMD_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_Panzergrenadier_CMD_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_Panzergrenadier_CMD_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Scout_CMD_RFA
export Modele_Scout_CMD_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_Scout_CMD_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_Scout_CMD_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_Scout_CMD_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_Officer )
export Modele_Scout_CMD_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_Scout_CMD_RFA_06 is TResourceMesh( Mesh=RFA_PzGrenadier_spe )
export Modele_Scout_CMD_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//KSM_CMD_RFA
export Modele_KSM_CMD_RFA is TResourceMesh( Mesh=RFA_PzGrenadier_1 )
export Modele_KSM_CMD_RFA_02 is TResourceMesh( Mesh=RFA_PzGrenadier_2 )
export Modele_KSM_CMD_RFA_03 is TResourceMesh( Mesh=RFA_PzGrenadier_NCO )
export Modele_KSM_CMD_RFA_04 is TResourceMesh( Mesh=RFA_PzGrenadier_Officer )
export Modele_KSM_CMD_RFA_05 is TResourceMesh( Mesh=RFA_PzGrenadier_sniper )
export Modele_KSM_CMD_RFA_06 is TResourceMesh( Mesh=RFA_PzGrenadier_spe )
export Modele_KSM_CMD_RFA_LOW is TResourceMesh( Mesh=RFA_PzGrenadier_LOW )

//Fallschirmjager_CMD_RFA
export Modele_Fallschirmjager_CMD_RFA is TResourceMesh( Mesh=RFA_Fallschirm_1 )
export Modele_Fallschirmjager_CMD_RFA_02 is TResourceMesh( Mesh=RFA_Fallschirm_2 )
export Modele_Fallschirmjager_CMD_RFA_03 is TResourceMesh( Mesh=RFA_Fallschirm_NCO )
export Modele_Fallschirmjager_CMD_RFA_04 is TResourceMesh( Mesh=RFA_Fallschirm_Officer )
export Modele_Fallschirmjager_CMD_RFA_05 is TResourceMesh( Mesh=RFA_Fallschirm_spe )
export Modele_Fallschirmjager_CMD_RFA_06 is TResourceMesh( Mesh=RFA_Fallschirm_1 )
export Modele_Fallschirmjager_CMD_RFA_LOW is TResourceMesh( Mesh=RFA_Fallschirm_LOW )

//Fallschirmjager_Scout_RFA
export Modele_Fallschirmjager_Scout_RFA is TResourceMesh( Mesh=RFA_Fallschirm_1 )
export Modele_Fallschirmjager_Scout_RFA_02 is TResourceMesh( Mesh=RFA_Fallschirm_2 )
export Modele_Fallschirmjager_Scout_RFA_03 is TResourceMesh( Mesh=RFA_Fallschirm_spe )
export Modele_Fallschirmjager_Scout_RFA_04 is TResourceMesh( Mesh=RFA_Fallschirm_NCO )
export Modele_Fallschirmjager_Scout_RFA_05 is TResourceMesh( Mesh=RFA_Fallschirm_spe )
export Modele_Fallschirmjager_Scout_RFA_06 is TResourceMesh( Mesh=RFA_Fallschirm_1 )
export Modele_Fallschirmjager_Scout_RFA_LOW is TResourceMesh( Mesh=RFA_Fallschirm_LOW )


//------------------------------------------------------------------------------
//-----------------------------------SOLDATS FR---------------------------------
//------------------------------------------------------------------------------

FR_Chasseur_1 is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_1.fbx'
FR_Chasseur_1_flamer is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_1_flamer.fbx'
FR_Chasseur_2 is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_2.fbx'
FR_Chasseur_NCO is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_NCO.fbx'
FR_Chasseur_Officer is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_Officer.fbx'
FR_Chasseur_Sniper is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_Sniper.fbx'
FR_Chasseur_spe is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_spe.fbx'
FR_Chasseur_spe_flamer is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_spe_flamer.fbx'
FR_Chasseur_LOW is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_1_LOW.fbx'

FR_Gendarme_2 is 'GameData:/Assets/3D/Units/FR/Infanterie/Gendarmes/Gendarme_2.fbx'
FR_Gendarme_NCO is 'GameData:/Assets/3D/Units/FR/Infanterie/Gendarmes/Gendarme_NCO.fbx'
FR_Gendarme_LOW is 'GameData:/Assets/3D/Units/FR/Infanterie/Gendarmes/Gendarme_2_LOW.fbx'

FR_Alpin_NCO is 'GameData:/Assets/3D/Units/FR/Infanterie/Alpins/Alpin_NCO.fbx'
FR_Alpin_NCO_LOW is 'GameData:/Assets/3D/Units/FR/Infanterie/Alpins/Alpin_NCO_LOW.fbx'
FR_RCAM is 'GameData:/Assets/3D/Units/FR/Infanterie/RCAM/RCAM.fbx'

FR_Legion_1 is 'GameData:/Assets/3D/Units/FR/Infanterie/Legion/Legion_1.fbx'
FR_Legion_2 is 'GameData:/Assets/3D/Units/FR/Infanterie/Legion/Legion_2.fbx'
FR_Legion_NCO is 'GameData:/Assets/3D/Units/FR/Infanterie/Legion/Legion_NCO.fbx'
FR_Legion_spe is 'GameData:/Assets/3D/Units/FR/Infanterie/Legion/Legion_spe.fbx'
FR_SapeurLegion_NCO is 'GameData:/Assets/3D/Units/FR/Infanterie/Legion/SapeurLegion_NCO.fbx'
FR_Legion_Officer is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Legion_Officer.fbx'
FR_SapeurLegion_Officer is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/SapeurLegion_Officer.fbx'
FR_Legion_1_LOW is 'GameData:/Assets/3D/Units/FR/Infanterie/Legion/Legion_1_LOW.fbx'

FR_Hussard_NCO is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Hussard_NCO.fbx'

FR_Para_NCO is 'GameData:/Assets/3D/Units/FR/Infanterie/Paras/Para_NCO.fbx'
FR_Para_spe is 'GameData:/Assets/3D/Units/FR/Infanterie/Paras/Para_spe.fbx'
FR_Para_spe_flamer is 'GameData:/Assets/3D/Units/FR/Infanterie/Paras/Para_spe_flamer.fbx'
FR_Para_LOW is 'GameData:/Assets/3D/Units/FR/Infanterie/Paras/Para_NCO_LOW.fbx'

CRAP_1 is 'GameData:/Assets/3D/Units/FR/Infanterie/CRAP/CRAP_1.fbx'
CRAP_2 is 'GameData:/Assets/3D/Units/FR/Infanterie/CRAP/CRAP_2.fbx'
CRAP_3 is 'GameData:/Assets/3D/Units/FR/Infanterie/CRAP/CRAP_3.fbx'
CRAP_NCO is 'GameData:/Assets/3D/Units/FR/Infanterie/CRAP/CRAP_NCO.fbx'
CRAP_1_LOW is 'GameData:/Assets/3D/Units/FR/Infanterie/CRAP/CRAP_1_LOW.fbx'

FR_RIMa_1 is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/RIMa_1.fbx'
FR_RIMa_NCO is 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/RIMa_NCO.fbx'

FR_Reserviste_1 is 'GameData:/Assets/3D/Units/FR/Infanterie/Reserviste/Reserviste_1.fbx'
FR_Reserviste_2 is 'GameData:/Assets/3D/Units/FR/Infanterie/Reserviste/Reserviste_2.fbx'

FR_Commando_Air_1 is 'GameData:/Assets/3D/Units/FR/Infanterie/Commando_Air/Commando_Air_1.fbx'
FR_Commando_Air_2 is 'GameData:/Assets/3D/Units/FR/Infanterie/Commando_Air/Commando_Air_2.fbx'
FR_Commando_Air_NCO is 'GameData:/Assets/3D/Units/FR/Infanterie/Commando_Air/Commando_Air_NCO.fbx'

FR_Gendarme_Air_1 is 'GameData:/Assets/3D/Units/FR/Infanterie/Gendarmerie_Air/Gendarme_Air_1.fbx'
FR_Gendarme_Air_Officier is 'GameData:/Assets/3D/Units/FR/Infanterie/Gendarmerie_Air/Gendarme_Air_Officier.fbx'

// 'GameData:/Assets/3D/Units/FR/Infanterie/Alpins/Alpin_NCO.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_1.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_2.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_NCO.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_Officer.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_Sniper.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/Chasseur_spe.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Gendarmes/Gendarme_2.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Gendarmes/Gendarme_NCO.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Legion/Legion_1.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Legion/Legion_2.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Legion/Legion_NCO.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Legion/Legion_spe.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Paras/Para_NCO.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Paras/Para_spe.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Alpins/Alpin_NCO.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/RCAM/RCAM.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/RIMa_1.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Chasseurs/RIMa_NCO.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Reserviste/Reserviste_1.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Reserviste/Reserviste_2.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Commando_Air/Commando_Air_1.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Commando_Air/Commando_Air_2.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Commando_Air/Commando_Air_NCO.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Gendarmerie_Air/Gendarme_Air_1.fbx'
// 'GameData:/Assets/3D/Units/FR/Infanterie/Gendarmerie_Air/Gendarme_Air_Officier.fbx'

//------------------------------------------------------------------------------
//Scout_RIMa_FR
export Modele_Scout_RIMa_FR is TResourceMesh( Mesh=FR_RIMa_NCO )
export Modele_Scout_RIMa_FR_02 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Scout_RIMa_FR_03 is TResourceMesh( Mesh=FR_RIMa_1 )
export Modele_Scout_RIMa_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Scout_RIMa_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Sniper_RIMa_FR
export Modele_Sniper_RIMa_FR is TResourceMesh( Mesh=FR_RIMa_NCO )
export Modele_Sniper_RIMa_FR_02 is TResourceMesh( Mesh=FR_RIMa_1 )
export Modele_Sniper_RIMa_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Rifles_RIMa_APILAS_FR
export Modele_Rifles_RIMa_APILAS_FR is TResourceMesh( Mesh=FR_RIMa_NCO )
export Modele_Rifles_RIMa_APILAS_FR_02 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Rifles_RIMa_APILAS_FR_03 is TResourceMesh( Mesh=FR_RIMa_1 )
export Modele_Rifles_RIMa_APILAS_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Rifles_RIMa_APILAS_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Rifles_RIMa_CMD_FR
export Modele_Rifles_RIMa_CMD_FR is TResourceMesh( Mesh=FR_Chasseur_Officer )
export Modele_Rifles_RIMa_CMD_FR_02 is TResourceMesh( Mesh=FR_RIMa_NCO )
export Modele_Rifles_RIMa_CMD_FR_03 is TResourceMesh( Mesh=FR_RIMa_1 )
export Modele_Rifles_RIMa_CMD_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Rifles_RIMa_CMD_FR_05 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Rifles_RIMa_CMD_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Rifles_RIMa_FR
export Modele_Rifles_RIMa_FR is TResourceMesh( Mesh=FR_RIMa_NCO )
export Modele_Rifles_RIMa_FR_02 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Rifles_RIMa_FR_03 is TResourceMesh( Mesh=FR_RIMa_1 )
export Modele_Rifles_RIMa_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Rifles_RIMa_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Sniper_Hubert_FR
export Modele_Sniper_Hubert_FR is TResourceMesh( Mesh=CRAP_2 )
export Modele_Sniper_Hubert_FR_LOW is TResourceMesh( Mesh=CRAP_1_LOW )

//SAS_Sniper_FR
export Modele_SAS_Sniper_FR is TResourceMesh( Mesh=CRAP_2 )
export Modele_SAS_Sniper_FR_02 is TResourceMesh( Mesh=CRAP_3 )
export Modele_SAS_Sniper_FR_LOW is TResourceMesh( Mesh=CRAP_1_LOW )

//Commandos_CRAP_Para_FR
export Modele_Commandos_CRAP_Para_FR is TResourceMesh( Mesh=CRAP_NCO )
export Modele_Commandos_CRAP_Para_FR_02 is TResourceMesh( Mesh=CRAP_1 )
export Modele_Commandos_CRAP_Para_FR_03 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Commandos_CRAP_Para_FR_04 is TResourceMesh( Mesh=CRAP_3 )
export Modele_Commandos_CRAP_Para_FR_LOW is TResourceMesh( Mesh=CRAP_1_LOW )

//LRRP_FR
export Modele_LRRP_FR is TResourceMesh( Mesh=CRAP_1 )
export Modele_LRRP_FR_02 is TResourceMesh( Mesh=CRAP_2 )
export Modele_LRRP_FR_03 is TResourceMesh( Mesh=CRAP_3 )
export Modele_LRRP_FR_04 is TResourceMesh( Mesh=CRAP_NCO )
export Modele_LRRP_FR_LOW is TResourceMesh( Mesh=CRAP_1_LOW )

//Chasseurs_CMD_FR
export Modele_Chasseurs_CMD_FR is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Chasseurs_CMD_FR_02 is TResourceMesh( Mesh=FR_Chasseur_Officer )
export Modele_Chasseurs_CMD_FR_03 is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Chasseurs_CMD_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Chasseurs_CMD_FR_05 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Chasseurs_CMD_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Groupe_AT_FR
export Modele_Groupe_AT_FR is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Groupe_AT_FR_02 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Groupe_AT_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Groupe_AT_FR_04 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Groupe_AT_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Reserviste_CMD_FR
export Modele_Reserviste_CMD_FR is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Reserviste_CMD_FR_02 is TResourceMesh( Mesh=FR_Chasseur_Officer )
export Modele_Reserviste_CMD_FR_03 is TResourceMesh( Mesh=FR_Reserviste_1 )
export Modele_Reserviste_CMD_FR_04 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Reserviste_CMD_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Scout_Reserviste_FR
export Modele_Scout_Reserviste_FR is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Scout_Reserviste_FR_02 is TResourceMesh( Mesh=FR_Reserviste_1 )
export Modele_Scout_Reserviste_FR_03 is TResourceMesh( Mesh=FR_Reserviste_2 )
export Modele_Scout_Reserviste_FR_04 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Scout_Reserviste_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Reserviste_FAMAS_FR
export Modele_Reserviste_FAMAS_FR is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Reserviste_FAMAS_FR_02 is TResourceMesh( Mesh=FR_Reserviste_2 )
export Modele_Reserviste_FAMAS_FR_03 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Reserviste_FAMAS_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Reserviste_FAMAS_FR_05 is TResourceMesh( Mesh=FR_Reserviste_1 )
export Modele_Reserviste_FAMAS_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Reserviste_DMR_FR
export Modele_Reserviste_DMR_FR is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Reserviste_DMR_FR_02 is TResourceMesh( Mesh=FR_Reserviste_2 )
export Modele_Reserviste_DMR_FR_03 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Reserviste_DMR_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Reserviste_DMR_FR_05 is TResourceMesh( Mesh=FR_Reserviste_1 )
export Modele_Reserviste_DMR_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Reserviste_FR
export Modele_Reserviste_FR is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Reserviste_FR_02 is TResourceMesh( Mesh=FR_Reserviste_2 )
export Modele_Reserviste_FR_03 is TResourceMesh( Mesh=FR_Reserviste_1 )
export Modele_Reserviste_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Reserviste_FR_05 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Reserviste_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Sapeurs_CMD_FR
export Modele_Sapeurs_CMD_FR is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Sapeurs_CMD_FR_02 is TResourceMesh( Mesh=FR_Chasseur_Officer )
export Modele_Sapeurs_CMD_FR_03 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Sapeurs_CMD_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Sapeurs_CMD_FR_05 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Sapeurs_CMD_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Sapeurs_Rhin_FR
export Modele_Sapeurs_Rhin_FR is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Sapeurs_Rhin_FR_02 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Sapeurs_Rhin_FR_03 is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Sapeurs_Rhin_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Officer )
export Modele_Sapeurs_Rhin_FR_05 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Sapeurs_Rhin_FR_06 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Sapeurs_Rhin_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Sapeurs_FR
export Modele_Sapeurs_FR is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Sapeurs_FR_02 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Sapeurs_FR_03 is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Sapeurs_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Officer )
export Modele_Sapeurs_FR_05 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Sapeurs_FR_06 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Sapeurs_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Sapeurs_Flam_FR
export Modele_Sapeurs_Flam_FR is TResourceMesh( Mesh=FR_Chasseur_1_flamer )
export Modele_Sapeurs_Flam_FR_02 is TResourceMesh( Mesh=FR_Chasseur_spe_flamer )
export Modele_Sapeurs_Flam_FR_03 is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Sapeurs_Flam_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Sapeurs_Flam_FR_05 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Sapeurs_Flam_FR_06 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Sapeurs_Flam_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Para_Sapeurs_Flam_FR
export Modele_Para_Sapeurs_Flam_FR is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_Para_Sapeurs_Flam_FR_02 is TResourceMesh( Mesh=FR_Para_spe_flamer )
export Modele_Para_Sapeurs_Flam_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1_flamer )
export Modele_Para_Sapeurs_Flam_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Para_Sapeurs_Flam_FR_05 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Para_Sapeurs_Flam_FR_06 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Para_Sapeurs_Flam_FR_LOW is TResourceMesh( Mesh=FR_Para_LOW )

//Sapeurs_Legion_Flam_FR
export Modele_Sapeurs_Legion_Flam_FR is TResourceMesh( Mesh=FR_SapeurLegion_NCO )
export Modele_Sapeurs_Legion_Flam_FR_02 is TResourceMesh( Mesh=FR_Legion_spe )
export Modele_Sapeurs_Legion_Flam_FR_03 is TResourceMesh( Mesh=FR_Legion_1 )
export Modele_Sapeurs_Legion_Flam_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1_flamer )
export Modele_Sapeurs_Legion_Flam_FR_05 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Sapeurs_Legion_Flam_FR_LOW is TResourceMesh( Mesh=FR_Legion_1_LOW )

//Scout_FR
export Modele_Scout_FR is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Scout_FR_02 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Scout_FR_03 is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Scout_FR_04 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Scout_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Hussards_FR
export Modele_Hussards_FR is TResourceMesh( Mesh=FR_Hussard_NCO )
export Modele_Hussards_FR_02 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Hussards_FR_03 is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Hussards_FR_04 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Hussards_FR_05 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Hussards_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Commandos_Legion_FR
export Modele_Commandos_Legion_FR is TResourceMesh( Mesh=CRAP_NCO )
export Modele_Commandos_Legion_FR_02 is TResourceMesh( Mesh=CRAP_2 )
export Modele_Commandos_Legion_FR_03 is TResourceMesh( Mesh=CRAP_3 )
export Modele_Commandos_Legion_FR_LOW is TResourceMesh( Mesh=CRAP_1_LOW )

//Commandos_FR
export Modele_Commandos_FR is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Commandos_FR_02 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Commandos_FR_03 is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_Commandos_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Commandos_FR_05 is TResourceMesh( Mesh=FR_Para_spe )
export Modele_Commandos_FR_06 is TResourceMesh( Mesh=FR_Para_spe )
export Modele_Commandos_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Commandos_Air_FR
export Modele_Commandos_Air_FR is TResourceMesh( Mesh=FR_Commando_Air_NCO )
export Modele_Commandos_Air_FR_02 is TResourceMesh( Mesh=FR_Commando_Air_1 )
export Modele_Commandos_Air_FR_03 is TResourceMesh( Mesh=FR_Commando_Air_2 )
export Modele_Commandos_Air_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Escorte_FR
export Modele_Escorte_FR is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Escorte_FR_02 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Escorte_FR_03 is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Escorte_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Escorte_FR_05 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Escorte_FR_06 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Escorte_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Para_CMD_FR
export Modele_Para_CMD_FR is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_Para_CMD_FR_02 is TResourceMesh( Mesh=FR_Para_spe )
export Modele_Para_CMD_FR_03 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Para_CMD_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Para_CMD_FR_05 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Para_CMD_FR_LOW is TResourceMesh( Mesh=FR_Para_LOW )

//Groupe_AT_para_FR
export Modele_Groupe_AT_para_FR is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_Groupe_AT_para_FR_02 is TResourceMesh( Mesh=FR_Para_spe )
export Modele_Groupe_AT_para_FR_03 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Groupe_AT_para_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Groupe_AT_para_FR_05 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Groupe_AT_para_FR_LOW is TResourceMesh( Mesh=FR_Para_LOW )

//Para_Marine_Eryx_FR
export Modele_Para_Marine_Eryx_FR is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_Para_Marine_Eryx_FR_02 is TResourceMesh( Mesh=FR_Para_spe )
export Modele_Para_Marine_Eryx_FR_03 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Para_Marine_Eryx_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Para_Marine_Eryx_FR_05 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Para_Marine_Eryx_FR_LOW is TResourceMesh( Mesh=FR_Para_LOW )

//Para_Marine_FR
export Modele_Para_Marine_FR is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_Para_Marine_FR_02 is TResourceMesh( Mesh=FR_Para_spe )
export Modele_Para_Marine_FR_03 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Para_Marine_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Para_Marine_FR_05 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Para_Marine_FR_LOW is TResourceMesh( Mesh=FR_Para_LOW )

//Para_FR
export Modele_Para_FR is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_Para_FR_02 is TResourceMesh( Mesh=FR_Para_spe )
export Modele_Para_FR_03 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Para_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Para_FR_05 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Para_FR_LOW is TResourceMesh( Mesh=FR_Para_LOW )

//Para_Legion_FR
export Modele_Para_Legion_FR is TResourceMesh( Mesh=FR_Legion_NCO )
export Modele_Para_Legion_FR_02 is TResourceMesh( Mesh=FR_Legion_1 )
export Modele_Para_Legion_FR_03 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Para_Legion_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Para_Legion_FR_LOW is TResourceMesh( Mesh=FR_Legion_1_LOW )

//Para_Sapeurs_CMD_FR
export Modele_Para_Sapeurs_CMD_FR is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_Para_Sapeurs_CMD_FR_02 is TResourceMesh( Mesh=FR_Para_spe )
export Modele_Para_Sapeurs_CMD_FR_03 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Para_Sapeurs_CMD_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Para_Sapeurs_CMD_FR_05 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Para_Sapeurs_CMD_FR_LOW is TResourceMesh( Mesh=FR_Para_LOW )

//Para_Sapeurs_FR
export Modele_Para_Sapeurs_FR is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_Para_Sapeurs_FR_02 is TResourceMesh( Mesh=FR_Para_spe )
export Modele_Para_Sapeurs_FR_03 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Para_Sapeurs_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Para_Sapeurs_FR_05 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Para_Sapeurs_FR_LOW is TResourceMesh( Mesh=FR_Para_LOW )

//SAS_CMD_FR
export Modele_SAS_CMD_FR is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_SAS_CMD_FR_02 is TResourceMesh( Mesh=FR_Para_spe )
export Modele_SAS_CMD_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_SAS_CMD_FR_04 is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_SAS_CMD_FR_LOW is TResourceMesh( Mesh=FR_Para_LOW )

//SAS_FR
export Modele_SAS_FR is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_SAS_FR_02 is TResourceMesh( Mesh=FR_Para_spe )
export Modele_SAS_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_SAS_FR_04 is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_SAS_FR_05 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_SAS_FR_06 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_SAS_FR_LOW is TResourceMesh( Mesh=FR_Para_LOW )

//Sniper_FJ_DDR
export Modele_Sniper_FJ_DDR is TResourceMesh( Mesh=DDR_FJB_2 )
export Modele_Sniper_FJ_DDR_02 is TResourceMesh( Mesh=DDR_FJB_NCO )
export Modele_Sniper_FJ_DDR_LOW is TResourceMesh( Mesh=FR_Para_LOW )

//Sniper_Fern_RFA
export Modele_Sniper_Fern_RFA is TResourceMesh( Mesh=RFA_Fernspaher_1 )
export Modele_Sniper_Fern_RFA_02 is TResourceMesh( Mesh=RFA_Fernspaher_2 )
export Modele_Sniper_Fern_RFA_LOW is TResourceMesh( Mesh=FR_Para_LOW )

//Sniper_M82_US
export Modele_Sniper_M82_US is TResourceMesh( Mesh=UK_Ghillie )
export Modele_Sniper_M82_US_LOW is TResourceMesh( Mesh=UK_Ghillie_LOW )

//Sniper_US
export Modele_Sniper_US is TResourceMesh( Mesh=UK_Ghillie )
export Modele_Sniper_US_LOW is TResourceMesh( Mesh=UK_Ghillie_LOW )

//Scout_para_FR
export Modele_Scout_para_FR is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_Scout_para_FR_02 is TResourceMesh( Mesh=FR_Para_spe )
export Modele_Scout_para_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Scout_para_FR_04 is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_Scout_para_FR_LOW is TResourceMesh( Mesh=FR_Para_LOW )

//Chasseurs_FR
export Modele_Chasseurs_FR is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Chasseurs_FR_02 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Chasseurs_FR_03 is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Chasseurs_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Chasseurs_FR_05 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Chasseurs_FR_06 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Chasseurs_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Rifles_Aero_CMD_FR
export Modele_Rifles_Aero_CMD_FR is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Rifles_Aero_CMD_FR_02 is TResourceMesh( Mesh=FR_Chasseur_Officer )
export Modele_Rifles_Aero_CMD_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Rifles_Aero_CMD_FR_04 is TResourceMesh( Mesh=FR_RCAM )
export Modele_Rifles_Aero_CMD_FR_05 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Rifles_Aero_CMD_FR_06 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Rifles_Aero_CMD_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Rifles_CMD_FR
export Modele_Rifles_CMD_FR is TResourceMesh( Mesh=FR_Chasseur_Officer )
export Modele_Rifles_CMD_FR_02 is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Rifles_CMD_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Rifles_CMD_FR_04 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Rifles_CMD_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Rifles_DMR_FR
export Modele_Rifles_DMR_FR is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Rifles_DMR_FR_02 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Rifles_DMR_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Rifles_DMR_FR_04 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Rifles_DMR_FR_05 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Rifles_DMR_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Rifles_APILAS_FR
export Modele_Rifles_APILAS_FR is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Rifles_APILAS_FR_02 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Rifles_APILAS_FR_03 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Rifles_APILAS_FR_04 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Rifles_APILAS_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Gendarmerie_Mobile_CMD_FR
export Modele_Gendarmerie_Mobile_CMD_FR is TResourceMesh( Mesh=FR_Gendarme_NCO )
export Modele_Gendarmerie_Mobile_CMD_FR_02 is TResourceMesh( Mesh=FR_Gendarme_NCO )
export Modele_Gendarmerie_Mobile_CMD_FR_03 is TResourceMesh( Mesh=FR_Gendarme_2 )
export Modele_Gendarmerie_Mobile_CMD_FR_04 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Gendarmerie_Mobile_CMD_FR_LOW is TResourceMesh( Mesh=FR_Gendarme_LOW )

//Gendarmerie_Air_FR
export Modele_Gendarmerie_Air_FR is TResourceMesh( Mesh=FR_Gendarme_Air_Officier )
export Modele_Gendarmerie_Air_FR_02 is TResourceMesh( Mesh=FR_Gendarme_Air_1 )
export Modele_Gendarmerie_Air_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Gendarmerie_Air_FR_LOW is TResourceMesh( Mesh=FR_Gendarme_LOW )

//Gendarmerie_Mobile_FR
export Modele_Gendarmerie_Mobile_FR is TResourceMesh( Mesh=FR_Gendarme_NCO )
export Modele_Gendarmerie_Mobile_FR_02 is TResourceMesh( Mesh=FR_Gendarme_2 )
export Modele_Gendarmerie_Mobile_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Gendarmerie_Mobile_FR_LOW is TResourceMesh( Mesh=FR_Gendarme_LOW )

//Gendarmerie_FR
export Modele_Gendarmerie_FR is TResourceMesh( Mesh=FR_Gendarme_NCO )
export Modele_Gendarmerie_FR_02 is TResourceMesh( Mesh=FR_Gendarme_2 )
export Modele_Gendarmerie_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Gendarmerie_FR_LOW is TResourceMesh( Mesh=FR_Gendarme_LOW )

//Rifles_Aero_FR
export Modele_Rifles_Aero_FR is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Rifles_Aero_FR_02 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Rifles_Aero_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Rifles_Aero_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Rifles_Aero_FR_05 is TResourceMesh( Mesh=FR_RCAM )
export Modele_Rifles_Aero_FR_06 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Rifles_Aero_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//Sapeurs_Legion_CMD_FR
export Modele_Sapeurs_Legion_CMD_FR is TResourceMesh( Mesh=FR_SapeurLegion_Officer )
export Modele_Sapeurs_Legion_CMD_FR_02 is TResourceMesh( Mesh=FR_SapeurLegion_NCO )
export Modele_Sapeurs_Legion_CMD_FR_03 is TResourceMesh( Mesh=FR_Legion_spe )
export Modele_Sapeurs_Legion_CMD_FR_04 is TResourceMesh( Mesh=FR_Legion_2 )
export Modele_Sapeurs_Legion_CMD_FR_05 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Sapeurs_Legion_CMD_FR_06 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Sapeurs_Legion_CMD_FR_LOW is TResourceMesh( Mesh=FR_Legion_1_LOW )

//Sapeurs_Legion_FR
export Modele_Sapeurs_Legion_FR is TResourceMesh( Mesh=FR_SapeurLegion_NCO )
export Modele_Sapeurs_Legion_FR_02 is TResourceMesh( Mesh=FR_Legion_spe )
export Modele_Sapeurs_Legion_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Sapeurs_Legion_FR_04 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Sapeurs_Legion_FR_05 is TResourceMesh( Mesh=FR_Legion_2 )
export Modele_Sapeurs_Legion_FR_LOW is TResourceMesh( Mesh=FR_Legion_1_LOW )

//Rifles_Legion_APILAS_FR
export Modele_Rifles_Legion_APILAS_FR is TResourceMesh( Mesh=FR_Legion_NCO )
export Modele_Rifles_Legion_APILAS_FR_02 is TResourceMesh( Mesh=FR_Legion_spe )
export Modele_Rifles_Legion_APILAS_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Rifles_Legion_APILAS_FR_04 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Rifles_Legion_APILAS_FR_05 is TResourceMesh( Mesh=FR_Legion_2 )
export Modele_Rifles_Legion_APILAS_FR_LOW is TResourceMesh( Mesh=FR_Legion_1_LOW )

//Rifles_Legion_CMD_FR
export Modele_Rifles_Legion_CMD_FR is TResourceMesh( Mesh=FR_Legion_Officer )
export Modele_Rifles_Legion_CMD_FR_02 is TResourceMesh( Mesh=FR_Legion_NCO )
export Modele_Rifles_Legion_CMD_FR_03 is TResourceMesh( Mesh=FR_Legion_spe )
export Modele_Rifles_Legion_CMD_FR_04 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Rifles_Legion_CMD_FR_05 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Rifles_Legion_CMD_FR_06 is TResourceMesh( Mesh=FR_Legion_2 )
export Modele_Rifles_Legion_CMD_FR_LOW is TResourceMesh( Mesh=FR_Legion_1_LOW )

//Rifles_Legion_FR
export Modele_Rifles_Legion_FR is TResourceMesh( Mesh=FR_Legion_NCO )
export Modele_Rifles_Legion_FR_02 is TResourceMesh( Mesh=FR_Legion_spe )
export Modele_Rifles_Legion_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Rifles_Legion_FR_04 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Rifles_Legion_FR_05 is TResourceMesh( Mesh=FR_Legion_2 )
export Modele_Rifles_Legion_FR_LOW is TResourceMesh( Mesh=FR_Legion_1_LOW )

//Groupe_AT_Legion_FR
export Modele_Groupe_AT_Legion_FR is TResourceMesh( Mesh=FR_Legion_NCO )
export Modele_Groupe_AT_Legion_FR_02 is TResourceMesh( Mesh=FR_Legion_spe )
export Modele_Groupe_AT_Legion_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Groupe_AT_Legion_FR_04 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Groupe_AT_Legion_FR_05 is TResourceMesh( Mesh=FR_Legion_2 )
export Modele_Groupe_AT_Legion_FR_LOW is TResourceMesh( Mesh=FR_Legion_1_LOW )

//Scout_Legion_FR
export Modele_Scout_Legion_FR is TResourceMesh( Mesh=FR_Legion_NCO )
export Modele_Scout_Legion_FR_02 is TResourceMesh( Mesh=FR_Legion_spe )
export Modele_Scout_Legion_FR_03 is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Scout_Legion_FR_04 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Scout_Legion_FR_05 is TResourceMesh( Mesh=FR_Legion_2 )
export Modele_Scout_Legion_FR_LOW is TResourceMesh( Mesh=FR_Legion_1_LOW )

//Sniper_Legion_FR
export Modele_Sniper_Legion_FR is TResourceMesh( Mesh=FR_Legion_NCO )
export Modele_Sniper_Legion_FR_02 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Sniper_Legion_FR_LOW is TResourceMesh( Mesh=FR_Legion_1_LOW )

//Rifles_Alpin_FR
export Modele_Rifles_Alpin_FR is TResourceMesh( Mesh=FR_Alpin_NCO )
export Modele_Rifles_Alpin_FR_LOW is TResourceMesh( Mesh=FR_Alpin_NCO_LOW )

//Rifles_FR
export Modele_Rifles_FR is  TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_Rifles_FR_02 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Rifles_FR_03 is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_Rifles_FR_04 is TResourceMesh( Mesh=FR_Chasseur_Sniper )
export Modele_Rifles_FR_05 is TResourceMesh( Mesh=FR_Chasseur_spe )
export Modele_Rifles_FR_06 is TResourceMesh( Mesh=FR_Chasseur_2 )
export Modele_Rifles_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//MANPAD_Mistral_para_FR
export Modele_MANPAD_Mistral_para_FR is TResourceMesh( Mesh=FR_Para_NCO )
export Modele_MANPAD_Mistral_para_FR_02 is TResourceMesh( Mesh=FR_Para_spe )
export Modele_MANPAD_Mistral_para_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//MANPAD_Mistral_FR
export Modele_MANPAD_Mistral_FR is TResourceMesh( Mesh=FR_Chasseur_1 )
export Modele_MANPAD_Mistral_FR_02 is TResourceMesh( Mesh=FR_Chasseur_NCO )
export Modele_MANPAD_Mistral_FR_LOW is TResourceMesh( Mesh=FR_Chasseur_LOW )

//MANPAD_Mistral_Legion_FR
export Modele_MANPAD_Mistral_Legion_FR is TResourceMesh( Mesh=FR_Legion_NCO )
export Modele_MANPAD_Mistral_Legion_FR_02 is TResourceMesh( Mesh=FR_Legion_spe )
export Modele_MANPAD_Mistral_Legion_FR_LOW is TResourceMesh( Mesh=FR_Legion_1_LOW )

//------------------------------------------------------------------------------
//-----------------------------------SOLDATS UK---------------------------------
//------------------------------------------------------------------------------
// 'GameData:/Assets/3D/Units/UK/Infanterie/Paratroopers/Para_1.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/Paratroopers/Para_2.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/Paratroopers/Para_NCO.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/Paratroopers/Para_Officer.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/Paratroopers/Para_spe.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Rifleman_1.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Rifleman_2.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Rifleman_NCO.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Rifleman_Officer.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Rifleman_Sniper.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Rifleman_spe.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/RMP/RMP_1.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/RMP/RMP_NCO.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/SAS/SAS_1.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/SAS/SAS_2.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/SAS/SAS_NCO.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/Territorial/Territorial_1.fbx'
// 'GameData:/Assets/3D/Units/UK/Infanterie/Territorial/Territorial_2.fbx'

UK_Para_1 is 'GameData:/Assets/3D/Units/UK/Infanterie/Paratroopers/Para_1.fbx'
UK_Para_2 is 'GameData:/Assets/3D/Units/UK/Infanterie/Paratroopers/Para_2.fbx'
UK_Para_NCO is 'GameData:/Assets/3D/Units/UK/Infanterie/Paratroopers/Para_NCO.fbx'
UK_Para_Officer is 'GameData:/Assets/3D/Units/UK/Infanterie/Paratroopers/Para_Officer.fbx'
UK_Para_spe is 'GameData:/Assets/3D/Units/UK/Infanterie/Paratroopers/Para_spe.fbx'
UK_Para_1_LOW is 'GameData:/Assets/3D/Units/UK/Infanterie/Paratroopers/Para_1_LOW.fbx'
UK_Para_Pathfinder is 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Para_Pathfinder.fbx'

UK_Rifleman_1 is 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Rifleman_1.fbx'
UK_Rifleman_2 is 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Rifleman_2.fbx'
UK_Rifleman_NCO is 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Rifleman_NCO.fbx'
UK_Rifleman_Officer is 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Rifleman_Officer.fbx'
UK_Rifleman_Sniper is 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Rifleman_Sniper.fbx'
UK_Rifleman_spe is 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Rifleman_spe.fbx'
UK_Rifleman_1_LOW is 'GameData:/Assets/3D/Units/UK/Infanterie/Rifleman/Rifleman_1_LOW.fbx'

UK_RMP_1 is 'GameData:/Assets/3D/Units/UK/Infanterie/RMP/RMP_1.fbx'
UK_RMP_NCO is 'GameData:/Assets/3D/Units/UK/Infanterie/RMP/RMP_NCO.fbx'
UK_RMP_1_LOW is 'GameData:/Assets/3D/Units/UK/Infanterie/RMP/RMP_1_LOW.fbx'

UK_SAS_1 is 'GameData:/Assets/3D/Units/UK/Infanterie/SAS/SAS_1.fbx'
UK_SAS_2 is 'GameData:/Assets/3D/Units/UK/Infanterie/SAS/SAS_2.fbx'
UK_SAS_NCO is 'GameData:/Assets/3D/Units/UK/Infanterie/SAS/SAS_NCO.fbx'
UK_SAS_1_LOW is 'GameData:/Assets/3D/Units/UK/Infanterie/SAS/SAS_1_LOW.fbx'

UK_Territorial_1 is 'GameData:/Assets/3D/Units/UK/Infanterie/Territorial/Territorial_1.fbx'
UK_Territorial_2 is 'GameData:/Assets/3D/Units/UK/Infanterie/Territorial/Territorial_2.fbx'
UK_Territorial_1_LOW is 'GameData:/Assets/3D/Units/UK/Infanterie/Territorial/Territorial_1_LOW.fbx'

UK_Ghillie is 'GameData:/Assets/3D/Units/UK/Infanterie/Ghillie/Ghillie.fbx'
UK_Ghillie_LOW is 'GameData:/Assets/3D/Units/UK/Infanterie/Ghillie/Ghillie_LOW.fbx'
//------------------------------------------------------------------------------
UK_Gurkhas_1 is 'GameData:/Assets/3D/Units/UK/Infanterie/Gurkhas/Gurkhas_1.fbx'
UK_Gurkhas_2 is 'GameData:/Assets/3D/Units/UK/Infanterie/Gurkhas/Gurkhas_2.fbx'
UK_Gurkhas_sniper is 'GameData:/Assets/3D/Units/UK/Infanterie/Gurkhas/Gurkhas_sniper.fbx'
UK_Gurkhas_spe is 'GameData:/Assets/3D/Units/UK/Infanterie/Gurkhas/Gurkhas_spe.fbx'
UK_Gurkhas_1_LOW is 'GameData:/Assets/3D/Units/UK/Infanterie/Gurkhas/Gurkhas_1_LOW.fbx'

//Pathfinders_UK
export Modele_Pathfinders_UK is TResourceMesh( Mesh=UK_Para_2 )
export Modele_Pathfinders_UK_02 is TResourceMesh( Mesh=UK_Para_spe )
export Modele_Pathfinders_UK_03 is TResourceMesh( Mesh=UK_Para_Pathfinder )
export Modele_Pathfinders_UK_04 is TResourceMesh( Mesh=UK_Para_1 )
export Modele_Pathfinders_UK_05 is TResourceMesh( Mesh=UK_Para_2 )
export Modele_Pathfinders_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//AT_Group_Gurkhas_UK
export Modele_AT_Group_Gurkhas_UK is TResourceMesh( Mesh=UK_Gurkhas_spe )
export Modele_AT_Group_Gurkhas_UK_02 is TResourceMesh( Mesh=UK_Gurkhas_2 )
export Modele_AT_Group_Gurkhas_UK_03 is TResourceMesh( Mesh=UK_Gurkhas_1 )
export Modele_AT_Group_Gurkhas_UK_LOW is TResourceMesh( Mesh=UK_Gurkhas_1_LOW )

//Rifles_Gurkhas_UK
export Modele_Rifles_Gurkhas_UK is TResourceMesh( Mesh=UK_Gurkhas_sniper )
export Modele_Rifles_Gurkhas_UK_02 is TResourceMesh( Mesh=UK_Gurkhas_spe )
export Modele_Rifles_Gurkhas_UK_03 is TResourceMesh( Mesh=UK_Gurkhas_1 )
export Modele_Rifles_Gurkhas_UK_04 is TResourceMesh( Mesh=UK_Gurkhas_2 )
export Modele_Rifles_Gurkhas_UK_LOW is TResourceMesh( Mesh=UK_Gurkhas_1_LOW )

//Rifles_Gurkhas_CMD_UK
export Modele_Rifles_Gurkhas_CMD_UK is TResourceMesh( Mesh=UK_Rifleman_Officer )
export Modele_Rifles_Gurkhas_CMD_UK_02 is TResourceMesh( Mesh=UK_Gurkhas_spe )
export Modele_Rifles_Gurkhas_CMD_UK_03 is TResourceMesh( Mesh=UK_Gurkhas_1 )
export Modele_Rifles_Gurkhas_CMD_UK_04 is TResourceMesh( Mesh=UK_Gurkhas_2 )
export Modele_Rifles_Gurkhas_CMD_UK_LOW is TResourceMesh( Mesh=UK_Gurkhas_1_LOW )

//Sniper_UK
export Modele_Sniper_UK is TResourceMesh( Mesh=UK_Ghillie )
export Modele_Sniper_UK_LOW is TResourceMesh( Mesh=UK_Ghillie_LOW )

//Engineers_AT_UK
export Modele_Engineers_AT_UK is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Engineers_AT_UK_02 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Engineers_AT_UK_03 is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Engineers_AT_UK_04 is TResourceMesh( Mesh=UK_Rifleman_Officer )
export Modele_Engineers_AT_UK_05 is TResourceMesh( Mesh=UK_Rifleman_Sniper )
export Modele_Engineers_AT_UK_06 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Engineers_AT_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//Engineers_Airmobile_UK
export Modele_Engineers_Airmobile_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Engineers_Airmobile_UK_02 is TResourceMesh( Mesh=UK_Rifleman_Sniper )
export Modele_Engineers_Airmobile_UK_03 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Engineers_Airmobile_UK_04 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Engineers_Airmobile_UK_05 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Engineers_Airmobile_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//Airmobile_Mot_UK
export Modele_Airmobile_Mot_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Airmobile_Mot_UK_02 is TResourceMesh( Mesh=UK_Rifleman_Sniper )
export Modele_Airmobile_Mot_UK_03 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Airmobile_Mot_UK_04 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Airmobile_Mot_UK_05 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Airmobile_Mot_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//Airmobile_UK
export Modele_Airmobile_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Airmobile_UK_02 is TResourceMesh( Mesh=UK_Rifleman_Sniper )
export Modele_Airmobile_UK_03 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Airmobile_UK_04 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Airmobile_UK_05 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Airmobile_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//Airmobile_MILAN__UK
export Modele_Airmobile_MILAN_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Airmobile_MILAN_UK_02 is TResourceMesh( Mesh=UK_Rifleman_Sniper )
export Modele_Airmobile_MILAN_UK_03 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Airmobile_MILAN_UK_04 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Airmobile_MILAN_UK_05 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Airmobile_MILAN_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//Airmobile_Mot_CMD_UK
export Modele_Airmobile_Mot_CMD_UK is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Airmobile_Mot_CMD_UK_02 is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Airmobile_Mot_CMD_UK_03 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Airmobile_Mot_CMD_UK_04 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Airmobile_Mot_CMD_UK_05 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Airmobile_Mot_CMD_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//Airmobile_CMD_UK
export Modele_Airmobile_CMD_UK is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Airmobile_CMD_UK_02 is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Airmobile_CMD_UK_03 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Airmobile_CMD_UK_04 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Airmobile_CMD_UK_05 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Airmobile_CMD_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Scout_Airmobile_UK
export Modele_Scout_Airmobile_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Scout_Airmobile_UK_02 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Scout_Airmobile_UK_03 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Scout_Airmobile_UK_04 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Scout_Airmobile_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Pioneer_UK
export Modele_Pioneer_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Pioneer_UK_02 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Pioneer_UK_03 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Pioneer_UK_04 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Pioneer_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Engineers_UK
export Modele_Engineers_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Engineers_UK_02 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Engineers_UK_03 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Engineers_UK_04 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Engineers_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Rifles_Berlin_UK
export Modele_Rifles_Berlin_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Rifles_Berlin_UK_02 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Rifles_Berlin_UK_03 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Rifles_Berlin_UK_04 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Rifles_Berlin_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Rifles_AT_UK
export Modele_Rifles_AT_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Rifles_AT_UK_02 is TResourceMesh( Mesh=UK_Rifleman_Sniper )
export Modele_Rifles_AT_UK_03 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Rifles_AT_UK_04 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Rifles_AT_UK_05 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Rifles_AT_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Engineers_TA_UK
export Modele_Engineers_TA_UK is TResourceMesh( Mesh=UK_Territorial_1 )
export Modele_Engineers_TA_UK_02 is TResourceMesh( Mesh=UK_Territorial_2 )
export Modele_Engineers_TA_UK_LOW is TResourceMesh( Mesh=UK_Territorial_1_LOW )

//Paratroopers_CMD_UK
export Modele_Paratroopers_CMD_UK is TResourceMesh( Mesh=UK_Para_Officer )
export Modele_Paratroopers_CMD_UK_02 is TResourceMesh( Mesh=UK_Para_NCO )
export Modele_Paratroopers_CMD_UK_03 is TResourceMesh( Mesh=UK_Para_1 )
export Modele_Paratroopers_CMD_UK_04 is TResourceMesh( Mesh=UK_Para_2 )
export Modele_Paratroopers_CMD_UK_05 is TResourceMesh( Mesh=UK_Para_spe )
export Modele_Paratroopers_CMD_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//Paratroopers_UK
export Modele_Paratroopers_UK is TResourceMesh( Mesh=UK_Para_NCO )
export Modele_Paratroopers_UK_02 is TResourceMesh( Mesh=UK_Para_spe )
export Modele_Paratroopers_UK_03 is TResourceMesh( Mesh=UK_Para_1 )
export Modele_Paratroopers_UK_04 is TResourceMesh( Mesh=UK_Para_2 )
export Modele_Paratroopers_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//Paratroopers_TA_UK
export Modele_Paratroopers_TA_UK is TResourceMesh( Mesh=UK_Para_NCO )
export Modele_Paratroopers_TA_UK_02 is TResourceMesh( Mesh=UK_Para_spe )
export Modele_Paratroopers_TA_UK_03 is TResourceMesh( Mesh=UK_Territorial_1 )
export Modele_Paratroopers_TA_UK_04 is TResourceMesh( Mesh=UK_Territorial_2 )
export Modele_Paratroopers_TA_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//Paratroopers_MILAN_TA_UK
export Modele_Paratroopers_MILAN_TA_UK is TResourceMesh( Mesh=UK_Para_Officer )
export Modele_Paratroopers_MILAN_TA_UK_02 is TResourceMesh( Mesh=UK_Para_NCO )
export Modele_Paratroopers_MILAN_TA_UK_03 is TResourceMesh( Mesh=UK_Para_spe )
export Modele_Paratroopers_MILAN_TA_UK_04 is TResourceMesh( Mesh=UK_Territorial_1 )
export Modele_Paratroopers_MILAN_TA_UK_05 is TResourceMesh( Mesh=UK_Territorial_2 )
export Modele_Paratroopers_MILAN_TA_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//Paratroopers_Engineers_CMD_UK
export Modele_Paratroopers_Engineers_CMD_UK is TResourceMesh( Mesh=UK_Para_Officer )
export Modele_Paratroopers_Engineers_CMD_UK_02 is TResourceMesh( Mesh=UK_Para_NCO )
export Modele_Paratroopers_Engineers_CMD_UK_03 is TResourceMesh( Mesh=UK_Para_1 )
export Modele_Paratroopers_Engineers_CMD_UK_04 is TResourceMesh( Mesh=UK_Para_2 )
export Modele_Paratroopers_Engineers_CMD_UK_05 is TResourceMesh( Mesh=UK_Para_spe )
export Modele_Paratroopers_Engineers_CMD_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//Paratroopers_Engineers_CarlG_UK
export Modele_Paratroopers_Engineers_CarlG_UK is TResourceMesh( Mesh=UK_Para_NCO )
export Modele_Paratroopers_Engineers_CarlG_UK_02 is TResourceMesh( Mesh=UK_Para_spe )
export Modele_Paratroopers_Engineers_CarlG_UK_03 is TResourceMesh( Mesh=UK_Para_1 )
export Modele_Paratroopers_Engineers_CarlG_UK_04 is TResourceMesh( Mesh=UK_Para_2 )
export Modele_Paratroopers_Engineers_CarlG_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//Paratroopers_Engineers_UK
export Modele_Paratroopers_Engineers_UK is TResourceMesh( Mesh=UK_Para_NCO )
export Modele_Paratroopers_Engineers_UK_02 is TResourceMesh( Mesh=UK_Para_spe )
export Modele_Paratroopers_Engineers_UK_03 is TResourceMesh( Mesh=UK_Para_1 )
export Modele_Paratroopers_Engineers_UK_04 is TResourceMesh( Mesh=UK_Para_2 )
export Modele_Paratroopers_Engineers_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//Engineers_CMD_UK
export Modele_Engineers_CMD_UK is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Engineers_CMD_UK_02 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Engineers_CMD_UK_03 is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Engineers_CMD_UK_04 is TResourceMesh( Mesh=UK_Rifleman_Officer )
export Modele_Engineers_CMD_UK_05 is TResourceMesh( Mesh=UK_Rifleman_Sniper )
export Modele_Engineers_CMD_UK_06 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Engineers_CMD_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Rifles_CMD_UK
export Modele_Rifles_CMD_UK is TResourceMesh( Mesh=UK_Rifleman_Officer )
export Modele_Rifles_CMD_UK_02 is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Rifles_CMD_UK_03 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Rifles_CMD_UK_04 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Rifles_CMD_UK_05 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Rifles_CMD_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Territorial_CMD_UK
export Modele_Territorial_CMD_UK is TResourceMesh( Mesh=UK_Territorial_1 )
export Modele_Territorial_CMD_UK_02 is TResourceMesh( Mesh=UK_Territorial_2 )
export Modele_Territorial_CMD_UK_LOW is TResourceMesh( Mesh=UK_Territorial_1_LOW )

//Scout_Para_UK
export Modele_Scout_Para_UK is TResourceMesh( Mesh=UK_Para_1 )
export Modele_Scout_Para_UK_02 is TResourceMesh( Mesh=UK_Para_NCO )
export Modele_Scout_Para_UK_03 is TResourceMesh( Mesh=UK_Para_2 )
export Modele_Scout_Para_UK_04 is TResourceMesh( Mesh=UK_Para_2 )
export Modele_Scout_Para_UK_05 is TResourceMesh( Mesh=UK_Para_Officer )
export Modele_Scout_Para_UK_06 is TResourceMesh( Mesh=UK_Para_spe )
export Modele_Scout_Para_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//LRRP_UK
export Modele_LRRP_UK is TResourceMesh( Mesh=UK_SAS_1 )
export Modele_LRRP_UK_02 is TResourceMesh( Mesh=UK_SAS_2 )
export Modele_LRRP_UK_03 is  TResourceMesh( Mesh=UK_SAS_NCO )
export Modele_LRRP_UK_LOW is TResourceMesh( Mesh=UK_SAS_1_LOW )

//Scout_Motorized_UK
export Modele_Scout_Motorized_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Scout_Motorized_UK_02 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Scout_Motorized_UK_03 is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Scout_Motorized_UK_04 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Scout_Motorized_UK_05 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Scout_Motorized_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Scout_UK
export Modele_Scout_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Scout_UK_02 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Scout_UK_03 is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Scout_UK_04 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Scout_UK_05 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Scout_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )


//Scout_AT_UK
export Modele_Scout_AT_UK is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Scout_AT_UK_02 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Scout_AT_UK_03 is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Scout_AT_UK_04 is TResourceMesh( Mesh=UK_Rifleman_Officer )
export Modele_Scout_AT_UK_05 is TResourceMesh( Mesh=UK_Rifleman_Sniper )
export Modele_Scout_AT_UK_06 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Scout_AT_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Scout_TA_UK
export Modele_Scout_TA_UK is     TResourceMesh( Mesh=UK_Territorial_1 )
export Modele_Scout_TA_UK_02 is  TResourceMesh( Mesh=UK_Territorial_2 )
export Modele_Scout_TA_UK_LOW is TResourceMesh( Mesh=UK_Territorial_1_LOW )

//MANPAD_Blowpipe_UK
export Modele_MANPAD_Blowpipe_UK is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_MANPAD_Blowpipe_UK_02 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_MANPAD_Blowpipe_UK_03 is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_MANPAD_Blowpipe_UK_04 is TResourceMesh( Mesh=UK_Rifleman_Officer )
export Modele_MANPAD_Blowpipe_UK_05 is TResourceMesh( Mesh=UK_Rifleman_Sniper )
export Modele_MANPAD_Blowpipe_UK_06 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_MANPAD_Blowpipe_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//MANPAD_Javelin_para_UK
export Modele_MANPAD_Javelin_para_UK is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_MANPAD_Javelin_para_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//MANPAD_Javelin_UK
export Modele_MANPAD_Javelin_UK is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_MANPAD_Javelin_UK_02 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_MANPAD_Javelin_UK_03 is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_MANPAD_Javelin_UK_04 is TResourceMesh( Mesh=UK_Rifleman_Officer )
export Modele_MANPAD_Javelin_UK_05 is TResourceMesh( Mesh=UK_Rifleman_Sniper )
export Modele_MANPAD_Javelin_UK_06 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_MANPAD_Javelin_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Security_UK
export Modele_Security_UK is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Security_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Rifles_RAF_UK
export Modele_Rifles_RAF_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Rifles_RAF_UK_02 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Rifles_RAF_UK_03 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Rifles_RAF_UK_04 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Rifles_RAF_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Gun_Group_UK
export Modele_Gun_Group_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Gun_Group_UK_02 is TResourceMesh( Mesh=UK_Rifleman_Sniper )
export Modele_Gun_Group_UK_03 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Gun_Group_UK_04 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Gun_Group_UK_05 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Gun_Group_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Rifles_Mot_UK
export Modele_Rifles_Mot_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Rifles_Mot_UK_02 is TResourceMesh( Mesh=UK_Rifleman_Sniper )
export Modele_Rifles_Mot_UK_03 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Rifles_Mot_UK_04 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Rifles_Mot_UK_05 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Rifles_Mot_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Rifles_UK
export Modele_Rifles_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Rifles_UK_02 is TResourceMesh( Mesh=UK_Rifleman_Sniper )
export Modele_Rifles_UK_03 is TResourceMesh( Mesh=UK_Rifleman_1 )
export Modele_Rifles_UK_04 is TResourceMesh( Mesh=UK_Rifleman_2 )
export Modele_Rifles_UK_05 is TResourceMesh( Mesh=UK_Rifleman_spe )
export Modele_Rifles_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Guards_UK
export Modele_Guards_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Guards_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Guards_CarlG_UK
export Modele_Guards_CarlG_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Guards_CarlG_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Guards_CMD_UK
export Modele_Guards_CMD_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Guards_CMD_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Rifles_Patrol_UK
export Modele_Rifles_Patrol_UK is TResourceMesh( Mesh=UK_Rifleman_NCO )
export Modele_Rifles_Patrol_UK_LOW is TResourceMesh( Mesh=UK_Rifleman_1_LOW )

//Gun_Group_Paras_UK
export Modele_Gun_Group_Paras_UK is TResourceMesh( Mesh=UK_Para_1 )
export Modele_Gun_Group_Paras_UK_02 is TResourceMesh( Mesh=UK_Para_NCO )
export Modele_Gun_Group_Paras_UK_03 is TResourceMesh( Mesh=UK_Para_2 )
export Modele_Gun_Group_Paras_UK_04 is TResourceMesh( Mesh=UK_Para_2 )
export Modele_Gun_Group_Paras_UK_05 is TResourceMesh( Mesh=UK_Para_Officer )
export Modele_Gun_Group_Paras_UK_06 is TResourceMesh( Mesh=UK_Para_spe )
export Modele_Gun_Group_Paras_UK_LOW is TResourceMesh( Mesh=UK_Para_1_LOW )

//SAS_UK
export Modele_SAS_UK is TResourceMesh( Mesh=UK_SAS_1 )
export Modele_SAS_UK_02 is TResourceMesh( Mesh=UK_SAS_2 )
export Modele_SAS_UK_03 is  TResourceMesh( Mesh=UK_SAS_NCO )
export Modele_SAS_UK_LOW is TResourceMesh( Mesh=UK_SAS_1_LOW )

//SAS_G_UK
export Modele_SAS_G_UK is TResourceMesh( Mesh=UK_SAS_1 )
export Modele_SAS_G_UK_LOW is TResourceMesh( Mesh=UK_SAS_1_LOW )

//RMP_UK
export Modele_RMP_UK is   TResourceMesh( Mesh=UK_RMP_1 )
export Modele_RMP_UK_02 is TResourceMesh( Mesh=UK_RMP_NCO )
export Modele_RMP_UK_LOW is TResourceMesh( Mesh=UK_RMP_1_LOW )

//Gun_Group_TA_UK
export Modele_Gun_Group_TA_UK is TResourceMesh( Mesh=UK_Territorial_1 )
export Modele_Gun_Group_TA_UK_02 is  TResourceMesh( Mesh=UK_Territorial_2 )
export Modele_Gun_Group_TA_UK_LOW is TResourceMesh( Mesh=UK_Territorial_1_LOW )

//AT_Group_TA_UK
export Modele_AT_Group_TA_UK is TResourceMesh( Mesh=UK_Territorial_1 )
export Modele_AT_Group_TA_UK_02 is  TResourceMesh( Mesh=UK_Territorial_2 )
export Modele_AT_Group_TA_UK_LOW is TResourceMesh( Mesh=UK_Territorial_1_LOW )

//Territorial_UK
export Modele_Territorial_UK is     TResourceMesh( Mesh=UK_Territorial_1 )
export Modele_Territorial_UK_02 is  TResourceMesh( Mesh=UK_Territorial_2 )
export Modele_Territorial_UK_LOW is TResourceMesh( Mesh=UK_Territorial_1_LOW )

//-------------------------------------------------------------------------------
//-----------------------------------SOLDATS BEL---------------------------------
//-------------------------------------------------------------------------------
BEL_Chasseur_1 is 'GameData:/Assets/3D/Units/BEL/Infanterie/Belgian_Chasseurs/Chasseurs_1.fbx'
BEL_Chasseur_2 is 'GameData:/Assets/3D/Units/BEL/Infanterie/Belgian_Chasseurs/Chasseurs_2.fbx'
BEL_Chasseur_NCO is 'GameData:/Assets/3D/Units/BEL/Infanterie/Belgian_Chasseurs/Chasseurs_NCO.fbx'
BEL_Chasseur_1_LOW is 'GameData:/Assets/3D/Units/BEL/Infanterie/Belgian_Chasseurs/Chasseurs_1_LOW.fbx'

BEL_ESR_1 is 'GameData:/Assets/3D/Units/BEL/Infanterie/ESR/ESR_1.fbx'
BEL_ESR_2 is 'GameData:/Assets/3D/Units/BEL/Infanterie/ESR/ESR_2.fbx'
BEL_ESR_1_LOW is 'GameData:/Assets/3D/Units/BEL/Infanterie/ESR/ESR_1_LOW.fbx'

BEL_Linie_1 is 'GameData:/Assets/3D/Units/BEL/Infanterie/Linie/Linie_1.fbx'
BEL_Linie_1_flamer is 'GameData:/Assets/3D/Units/BEL/Infanterie/Linie/Linie_1_flamer.fbx'
BEL_Linie_2 is 'GameData:/Assets/3D/Units/BEL/Infanterie/Linie/Linie_2.fbx'
BEL_Linie_NCO is 'GameData:/Assets/3D/Units/BEL/Infanterie/Linie/Linie_NCO.fbx'
BEL_Linie_Officer is 'GameData:/Assets/3D/Units/BEL/Infanterie/Linie/Linie_Officer.fbx'
BEL_Linie_spe is 'GameData:/Assets/3D/Units/BEL/Infanterie/Linie/Linie_spe.fbx'
BEL_Linie_1_LOW is 'GameData:/Assets/3D/Units/BEL/Infanterie/Linie/Linie_1_LOW.fbx'

BEL_Lux_1 is 'GameData:/Assets/3D/Units/BEL/Infanterie/Lux/Lux_1.fbx'
BEL_Lux_2 is 'GameData:/Assets/3D/Units/BEL/Infanterie/Lux/Lux_2.fbx'
BEL_Lux_NCO is 'GameData:/Assets/3D/Units/BEL/Infanterie/Lux/Lux_NCO.fbx'
BEL_Lux_Officer is 'GameData:/Assets/3D/Units/BEL/Infanterie/Lux/Lux_Officer.fbx'
BEL_Lux_1_LOW is 'GameData:/Assets/3D/Units/BEL/Infanterie/Lux/Lux_1_LOW.fbx'

BEL_MP_1 is 'GameData:/Assets/3D/Units/BEL/Infanterie/MP/MP.fbx'
BEL_MP_Para is 'GameData:/Assets/3D/Units/BEL/Infanterie/MP/ParaMP.fbx'
BEL_MP_1_LOW is 'GameData:/Assets/3D/Units/BEL/Infanterie/MP/MP_LOW.fbx'

BEL_Para_1 is 'GameData:/Assets/3D/Units/BEL/Infanterie/Para/Para_1.fbx'
BEL_Para_2 is 'GameData:/Assets/3D/Units/BEL/Infanterie/Para/Para_2.fbx'
BEL_Para_NCO is 'GameData:/Assets/3D/Units/BEL/Infanterie/Para/Para_NCO.fbx'
BEL_Para_Officer is 'GameData:/Assets/3D/Units/BEL/Infanterie/Para/Para_Officer.fbx'
BEL_Para_spe is 'GameData:/Assets/3D/Units/BEL/Infanterie/Para/Para_spe.fbx'
BEL_Para_1_LOW is 'GameData:/Assets/3D/Units/BEL/Infanterie/Para/Para_1_LOW.fbx'

BEL_ParaCmdo_1 is 'GameData:/Assets/3D/Units/BEL/Infanterie/ParaCmdo/ParaCmdo_1.fbx'
BEL_ParaCmdo_2 is 'GameData:/Assets/3D/Units/BEL/Infanterie/ParaCmdo/ParaCmdo_2.fbx'
BEL_ParaCmdo_NCO is 'GameData:/Assets/3D/Units/BEL/Infanterie/ParaCmdo/ParaCmdo_NCO.fbx'
BEL_ParaCmdo_Officer is 'GameData:/Assets/3D/Units/BEL/Infanterie/ParaCmdo/ParaCmdo_Officer.fbx'
BEL_ParaCmdo_spe is 'GameData:/Assets/3D/Units/BEL/Infanterie/ParaCmdo/ParaCmdo_spe.fbx'
BEL_ParaCmdo_1_LOW is 'GameData:/Assets/3D/Units/BEL/Infanterie/ParaCmdo/ParaCmdo_1_LOW.fbx'

//-------------------------------------------------------------------------------

//ParaCmdo_Pionier_BEL
export Modele_ParaCmdo_Pionier_BEL is TResourceMesh( Mesh=BEL_Para_Officer )
export Modele_ParaCmdo_Pionier_BEL_02 is TResourceMesh( Mesh=BEL_Para_NCO )
export Modele_ParaCmdo_Pionier_BEL_03 is TResourceMesh( Mesh=BEL_Para_spe )
export Modele_ParaCmdo_Pionier_BEL_04 is TResourceMesh( Mesh=BEL_Para_1 )
export Modele_ParaCmdo_Pionier_BEL_05 is TResourceMesh( Mesh=BEL_Para_2 )
export Modele_ParaCmdo_Pionier_BEL_LOW is TResourceMesh( Mesh=BEL_ParaCmdo_1_LOW )

//ParaCmdo_AT_BEL
export Modele_ParaCmdo_AT_BEL is TResourceMesh( Mesh=BEL_ParaCmdo_NCO )
export Modele_ParaCmdo_AT_BEL_02 is TResourceMesh( Mesh=BEL_ParaCmdo_spe )
export Modele_ParaCmdo_AT_BEL_03 is TResourceMesh( Mesh=BEL_ParaCmdo_1 )
export Modele_ParaCmdo_AT_BEL_04 is TResourceMesh( Mesh=BEL_ParaCmdo_2 )
export Modele_ParaCmdo_AT_BEL_LOW is TResourceMesh( Mesh=BEL_ParaCmdo_1_LOW )

//ParaCmdo_BEL
export Modele_ParaCmdo_BEL is TResourceMesh( Mesh=BEL_ParaCmdo_NCO )
export Modele_ParaCmdo_BEL_02 is TResourceMesh( Mesh=BEL_ParaCmdo_spe )
export Modele_ParaCmdo_BEL_03 is TResourceMesh( Mesh=BEL_ParaCmdo_1 )
export Modele_ParaCmdo_BEL_04 is TResourceMesh( Mesh=BEL_ParaCmdo_2 )
export Modele_ParaCmdo_BEL_LOW is TResourceMesh( Mesh=BEL_ParaCmdo_1_LOW )

//ParaCmdo_CMD_BEL
export Modele_ParaCmdo_CMD_BEL is TResourceMesh( Mesh=BEL_ParaCmdo_Officer )
export Modele_ParaCmdo_CMD_BEL_02 is TResourceMesh( Mesh=BEL_ParaCmdo_NCO )
export Modele_ParaCmdo_CMD_BEL_03 is TResourceMesh( Mesh=BEL_ParaCmdo_spe )
export Modele_ParaCmdo_CMD_BEL_04 is TResourceMesh( Mesh=BEL_ParaCmdo_1 )
export Modele_ParaCmdo_CMD_BEL_05 is TResourceMesh( Mesh=BEL_ParaCmdo_2 )
export Modele_ParaCmdo_CMD_BEL_LOW is TResourceMesh( Mesh=BEL_ParaCmdo_1_LOW )

//Scout_ParaCmdo_BEL
export Modele_Scout_ParaCmdo_BEL is TResourceMesh( Mesh=BEL_ParaCmdo_NCO )
export Modele_Scout_ParaCmdo_BEL_02 is TResourceMesh( Mesh=BEL_ParaCmdo_spe )
export Modele_Scout_ParaCmdo_BEL_03 is TResourceMesh( Mesh=BEL_ParaCmdo_1 )
export Modele_Scout_ParaCmdo_BEL_04 is TResourceMesh( Mesh=BEL_ParaCmdo_2 )
export Modele_Scout_ParaCmdo_BEL_LOW is TResourceMesh( Mesh=BEL_ParaCmdo_1_LOW )

//Scout_ParaCmdo_Mech_BEL
export Modele_Scout_ParaCmdo_Mech_BEL is TResourceMesh( Mesh=BEL_ParaCmdo_NCO )
export Modele_Scout_ParaCmdo_Mech_BEL_02 is TResourceMesh( Mesh=BEL_ParaCmdo_1 )
export Modele_Scout_ParaCmdo_Mech_BEL_03 is TResourceMesh( Mesh=BEL_ParaCmdo_spe )
export Modele_Scout_ParaCmdo_Mech_BEL_04 is TResourceMesh( Mesh=BEL_ParaCmdo_2 )
export Modele_Scout_ParaCmdo_Mech_BEL_LOW is TResourceMesh( Mesh=BEL_ParaCmdo_1_LOW )

//MANPAD_Mistral_para_BEL
export Modele_MANPAD_Mistral_para_BEL is TResourceMesh( Mesh=BEL_ParaCmdo_NCO )
export Modele_MANPAD_Mistral_para_BEL_02 is TResourceMesh( Mesh=BEL_ParaCmdo_1 )
export Modele_MANPAD_Mistral_para_BEL_LOW is TResourceMesh( Mesh=BEL_ParaCmdo_1_LOW )

//LRRP_BEL
export Modele_LRRP_BEL is TResourceMesh( Mesh=BEL_ESR_1 )
export Modele_LRRP_BEL_02 is TResourceMesh( Mesh=BEL_ESR_2 )
export Modele_LRRP_BEL_03 is TResourceMesh( Mesh=BEL_ParaCmdo_1 )
export Modele_LRRP_BEL_04 is TResourceMesh( Mesh=BEL_ParaCmdo_spe )
export Modele_LRRP_BEL_05 is TResourceMesh( Mesh=BEL_ESR_2 )
export Modele_LRRP_BEL_06 is TResourceMesh( Mesh=BEL_ESR_1 )
export Modele_LRRP_BEL_07 is TResourceMesh( Mesh=BEL_ESR_2 )
export Modele_LRRP_BEL_08 is TResourceMesh( Mesh=BEL_ParaCmdo_spe )
export Modele_LRRP_BEL_LOW is TResourceMesh( Mesh=BEL_ESR_1_LOW )

//Provincial_CMD_BEL
export Modele_Provincial_CMD_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Provincial_CMD_BEL_02 is TResourceMesh( Mesh=BEL_Linie_Officer )
export Modele_Provincial_CMD_BEL_03 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Provincial_CMD_BEL_04 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Provincial_CMD_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Provincial_CMD_BEL_06 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Provincial_CMD_BEL_07 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Provincial_CMD_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Provincial_BEL
export Modele_Provincial_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Provincial_BEL_02 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Provincial_BEL_03 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Provincial_BEL_04 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Provincial_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Provincial_BEL_06 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Provincial_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Rifles_CMD_BEL
export Modele_Rifles_CMD_BEL is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Rifles_CMD_BEL_02 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Rifles_CMD_BEL_03 is TResourceMesh( Mesh=BEL_Linie_Officer )
export Modele_Rifles_CMD_BEL_04 is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Rifles_CMD_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Rifles_CMD_BEL_06 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Rifles_CMD_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Reserve_BEL
export Modele_Reserve_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Reserve_BEL_02 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Reserve_BEL_03 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Reserve_BEL_04 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Reserve_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Reserve_BEL_06 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Reserve_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Rifles_AT_BEL
export Modele_Rifles_AT_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Rifles_AT_BEL_02 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Rifles_AT_BEL_03 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Rifles_AT_BEL_04 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Rifles_AT_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Rifles_AT_BEL_06 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Rifles_AT_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Mech_Rifles_AT_BEL
export Modele_Mech_Rifles_AT_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Mech_Rifles_AT_BEL_02 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Mech_Rifles_AT_BEL_03 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Mech_Rifles_AT_BEL_04 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Mech_Rifles_AT_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Mech_Rifles_AT_BEL_06 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Mech_Rifles_AT_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Mech_Rifles_CMD_BEL
export Modele_Mech_Rifles_CMD_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Mech_Rifles_CMD_BEL_02 is TResourceMesh( Mesh=BEL_Linie_Officer )
export Modele_Mech_Rifles_CMD_BEL_03 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Mech_Rifles_CMD_BEL_04 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Mech_Rifles_CMD_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Mech_Rifles_CMD_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Mech_Rifles_MG_BEL
export Modele_Mech_Rifles_MG_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Mech_Rifles_MG_BEL_02 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Mech_Rifles_MG_BEL_03 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Mech_Rifles_MG_BEL_04 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Mech_Rifles_MG_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Mech_Rifles_MG_BEL_06 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Mech_Rifles_MG_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Rifles_BEL
export Modele_Rifles_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Rifles_BEL_02 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Rifles_BEL_03 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Rifles_BEL_04 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Rifles_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Rifles_BEL_06 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Rifles_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//ChasseurArdennais_BEL
export Modele_ChasseurArdennais_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_ChasseurArdennais_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//ChasseurArdennais_CMD_BEL
export Modele_ChasseurArdennais_CMD_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_ChasseurArdennais_CMD_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//MANPAD_Mistral_BEL
export Modele_MANPAD_Mistral_BEL is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_MANPAD_Mistral_BEL_01 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_MANPAD_Mistral_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Engineers_CMD_BEL
export Modele_Engineers_CMD_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Engineers_CMD_BEL_02 is TResourceMesh( Mesh=BEL_Linie_Officer )
export Modele_Engineers_CMD_BEL_03 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Engineers_CMD_BEL_04 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Engineers_CMD_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Engineers_CMD_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Engineers_AT_BEL
export Modele_Engineers_AT_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Engineers_AT_BEL_02 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Engineers_AT_BEL_03 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Engineers_AT_BEL_04 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Engineers_AT_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Engineers_AT_BEL_06 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Engineers_AT_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Engineers_BEL
export Modele_Engineers_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Engineers_BEL_02 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Engineers_BEL_03 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Engineers_BEL_04 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Engineers_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Engineers_BEL_06 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Engineers_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Engineers_Flam_BEL
export Modele_Engineers_Flam_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Engineers_Flam_BEL_02 is TResourceMesh( Mesh=BEL_Linie_1_flamer )
export Modele_Engineers_Flam_BEL_03 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Engineers_Flam_BEL_04 is TResourceMesh( Mesh=BEL_Linie_1_flamer )
export Modele_Engineers_Flam_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Engineers_Flam_BEL_06 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Engineers_Flam_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Scout_Provincial_AT_BEL
export Modele_Scout_Provincial_AT_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Scout_Provincial_AT_BEL_02 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Scout_Provincial_AT_BEL_03 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Scout_Provincial_AT_BEL_04 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Scout_Provincial_AT_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Scout_Provincial_AT_BEL_06 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Scout_Provincial_AT_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Scout_Provincial_BEL
export Modele_Scout_Provincial_BEL is TResourceMesh( Mesh=BEL_Linie_NCO )
export Modele_Scout_Provincial_BEL_02 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Scout_Provincial_BEL_03 is TResourceMesh( Mesh=BEL_Linie_spe )
export Modele_Scout_Provincial_BEL_04 is TResourceMesh( Mesh=BEL_Linie_1 )
export Modele_Scout_Provincial_BEL_05 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Scout_Provincial_BEL_06 is TResourceMesh( Mesh=BEL_Linie_2 )
export Modele_Scout_Provincial_BEL_LOW is TResourceMesh( Mesh=BEL_Linie_1_LOW )

//Scout_BEL
export Modele_Scout_BEL is TResourceMesh( Mesh=BEL_Chasseur_NCO )
export Modele_Scout_BEL_02 is TResourceMesh( Mesh=BEL_Chasseur_2 )
export Modele_Scout_BEL_03 is TResourceMesh( Mesh=BEL_Chasseur_1 )
export Modele_Scout_BEL_LOW is TResourceMesh( Mesh=BEL_Chasseur_1_LOW )

//Scout_AT_BEL
export Modele_Scout_AT_BEL is TResourceMesh( Mesh=BEL_Chasseur_NCO )
export Modele_Scout_AT_BEL_02 is TResourceMesh( Mesh=BEL_Chasseur_2 )
export Modele_Scout_AT_BEL_03 is TResourceMesh( Mesh=BEL_Chasseur_1 )
export Modele_Scout_AT_BEL_LOW is TResourceMesh( Mesh=BEL_Chasseur_1_LOW )

//Sniper_ESR_BEL
export Modele_Sniper_ESR_BEL is TResourceMesh( Mesh=BEL_ESR_1 )
export Modele_Sniper_ESR_BEL_02 is TResourceMesh( Mesh=BEL_ESR_2 )
export Modele_Sniper_ESR_BEL_LOW is TResourceMesh( Mesh=BEL_ESR_1_LOW )

//MP_para_BEL
export Modele_MP_para_BEL is TResourceMesh( Mesh=BEL_MP_Para )
export Modele_MP_para_BEL_LOW is TResourceMesh( Mesh=BEL_MP_1_LOW )

//MP_BEL
export Modele_MP_BEL is TResourceMesh( Mesh=BEL_MP_1 )
export Modele_MP_BEL_LOW is TResourceMesh( Mesh=BEL_MP_1_LOW )

//MP_Combat_BEL
export Modele_MP_Combat_BEL is TResourceMesh( Mesh=BEL_MP_1 )
export Modele_MP_Combat_BEL_LOW is TResourceMesh( Mesh=BEL_MP_1_LOW )

//------------------------------------------------------------------------------
//
//
//                                   SOLDATS LUX
//
//
//------------------------------------------------------------------------------

//LightRifles_CMD_LUX
export Modele_LightRifles_CMD_LUX is TResourceMesh( Mesh=BEL_Lux_1 )
export Modele_LightRifles_CMD_LUX_02 is TResourceMesh( Mesh=BEL_Lux_2 )
export Modele_LightRifles_CMD_LUX_03 is TResourceMesh( Mesh=BEL_Lux_NCO )
export Modele_LightRifles_CMD_LUX_04 is TResourceMesh( Mesh=BEL_Lux_Officer )
export Modele_LightRifles_CMD_LUX_05 is TResourceMesh( Mesh=BEL_Lux_2 )
export Modele_LightRifles_CMD_LUX_LOW is TResourceMesh( Mesh=BEL_Lux_1_LOW )

//LightRifles_LUX
export Modele_LightRifles_LUX is TResourceMesh( Mesh=BEL_Lux_1 )
export Modele_LightRifles_LUX_02 is TResourceMesh( Mesh=BEL_Lux_2 )
export Modele_LightRifles_LUX_03 is TResourceMesh( Mesh=BEL_Lux_NCO )
export Modele_LightRifles_LUX_04 is TResourceMesh( Mesh=BEL_Lux_1 )
export Modele_LightRifles_LUX_05 is TResourceMesh( Mesh=BEL_Lux_2 )
export Modele_LightRifles_LUX_06 is TResourceMesh( Mesh=BEL_Lux_NCO )
export Modele_LightRifles_LUX_LOW is TResourceMesh( Mesh=BEL_Lux_1_LOW )

//Scout_LUX
export Modele_Scout_LUX is TResourceMesh( Mesh=BEL_Lux_1 )
export Modele_Scout_LUX_02 is TResourceMesh( Mesh=BEL_Lux_2 )
export Modele_Scout_LUX_03 is TResourceMesh( Mesh=BEL_Lux_NCO )
export Modele_Scout_LUX_LOW is TResourceMesh( Mesh=BEL_Lux_1_LOW )


//-------------------------------------------------------------------------------
//-----------------------------------SOLDATS POL---------------------------------
//-------------------------------------------------------------------------------
POL_Komandosi_1 is 'GameData:/Assets/3D/Units/POL/Infanterie/Komandosi/Komandosi_1.fbx'
POL_Komandosi_2 is 'GameData:/Assets/3D/Units/POL/Infanterie/Komandosi/Komandosi_2.fbx'
POL_Komandosi_NCO is 'GameData:/Assets/3D/Units/POL/Infanterie/Komandosi/Komandosi_NCO.fbx'
POL_Komandosi_Officer is 'GameData:/Assets/3D/Units/POL/Infanterie/Komandosi/Komandosi_Officer.fbx'
POL_Komandosi_spe is 'GameData:/Assets/3D/Units/POL/Infanterie/Komandosi/Komandosi_spe.fbx'
POL_Komandosi_1_LOW is 'GameData:/Assets/3D/Units/POL/Infanterie/Komandosi/Komandosi_1_LOW.fbx'

POL_Niebieskie_1 is 'GameData:/Assets/3D/Units/POL/Infanterie/Niebieskie/Niebieskie_1.fbx'
POL_Niebieskie_2 is 'GameData:/Assets/3D/Units/POL/Infanterie/Niebieskie/Niebieskie_2.fbx'
POL_Niebieskie_NCO is 'GameData:/Assets/3D/Units/POL/Infanterie/Niebieskie/Niebieskie_NCO.fbx'
POL_Niebieskie_Officer is 'GameData:/Assets/3D/Units/POL/Infanterie/Niebieskie/Niebieskie_Officer.fbx'
POL_Niebieskie_spe is 'GameData:/Assets/3D/Units/POL/Infanterie/Niebieskie/Niebieskie_spe.fbx'
POL_Niebieskie_1_LOW is 'GameData:/Assets/3D/Units/POL/Infanterie/Niebieskie/Niebieskie_1_LOW.fbx'

POL_Piechota_1 is 'GameData:/Assets/3D/Units/POL/Infanterie/Piechota/Piechota_1.fbx'
POL_Piechota_2 is 'GameData:/Assets/3D/Units/POL/Infanterie/Piechota/Piechota_2.fbx'
POL_Piechota_NCO is 'GameData:/Assets/3D/Units/POL/Infanterie/Piechota/Piechota_NCO.fbx'
POL_Piechota_Officer is 'GameData:/Assets/3D/Units/POL/Infanterie/Piechota/Piechota_Officer.fbx'

POL_Spado_1 is 'GameData:/Assets/3D/Units/POL/Infanterie/Spado/Spado_1.fbx'
POL_Spado_2 is 'GameData:/Assets/3D/Units/POL/Infanterie/Spado/Spado_2.fbx'
POL_Spado_NCO is 'GameData:/Assets/3D/Units/POL/Infanterie/Spado/Spado_NCO.fbx'
POL_Spado_Officer is 'GameData:/Assets/3D/Units/POL/Infanterie/Spado/Spado_Officer.fbx'
POL_Spado_spe is 'GameData:/Assets/3D/Units/POL/Infanterie/Spado/Spado_spe.fbx'

POL_WSW_1 is 'GameData:/Assets/3D/Units/POL/Infanterie/WSW/WSW_1.fbx'
POL_WSW_2 is 'GameData:/Assets/3D/Units/POL/Infanterie/WSW/WSW_2.fbx'

//WSW_POL
export Modele_WSW_POL is TResourceMesh( Mesh=POL_WSW_1 )
export Modele_WSW_POL_02 is TResourceMesh( Mesh=POL_WSW_2 )
export Modele_WSW_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//MotRifles_CMD_POL
export Modele_MotRifles_CMD_POL is TResourceMesh( Mesh=POL_Piechota_Officer )
export Modele_MotRifles_CMD_POL_02 is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_MotRifles_CMD_POL_03 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_MotRifles_CMD_POL_04 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_MotRifles_CMD_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//MotRifles_POL
export Modele_MotRifles_POL is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_MotRifles_POL_02 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_MotRifles_POL_03 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_MotRifles_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//MotRifles_SVD_POL
export Modele_MotRifles_SVD_POL is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_MotRifles_SVD_POL_02 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_MotRifles_SVD_POL_03 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_MotRifles_SVD_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Naval_Engineers_POL
export Modele_Naval_Engineers_POL is TResourceMesh( Mesh=POL_Niebieskie_NCO )
export Modele_Naval_Engineers_POL_02 is TResourceMesh( Mesh=POL_Niebieskie_spe )
export Modele_Naval_Engineers_POL_03 is TResourceMesh( Mesh=POL_Niebieskie_1 )
export Modele_Naval_Engineers_POL_04 is TResourceMesh( Mesh=POL_Niebieskie_2 )
export Modele_Naval_Engineers_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Naval_Engineers_Flam_POL
export Modele_Naval_Engineers_Flam_POL is TResourceMesh( Mesh=POL_Niebieskie_NCO )
export Modele_Naval_Engineers_Flam_POL_02 is TResourceMesh( Mesh=POL_Niebieskie_spe )
export Modele_Naval_Engineers_Flam_POL_03 is TResourceMesh( Mesh=POL_Niebieskie_1 )
export Modele_Naval_Engineers_Flam_POL_04 is TResourceMesh( Mesh=POL_Niebieskie_2 )
export Modele_Naval_Engineers_Flam_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Naval_Engineers_CMD_POL
export Modele_Naval_Engineers_CMD_POL is TResourceMesh( Mesh=POL_Niebieskie_NCO )
export Modele_Naval_Engineers_CMD_POL_02 is TResourceMesh( Mesh=POL_Niebieskie_spe )
export Modele_Naval_Engineers_CMD_POL_03 is TResourceMesh( Mesh=POL_Niebieskie_Officer )
export Modele_Naval_Engineers_CMD_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Naval_Rifle_CMD_POL
export Modele_Naval_Rifle_CMD_POL is TResourceMesh( Mesh=POL_Niebieskie_NCO )
export Modele_Naval_Rifle_CMD_POL_02 is TResourceMesh( Mesh=POL_Niebieskie_spe )
export Modele_Naval_Rifle_CMD_POL_03 is TResourceMesh( Mesh=POL_Niebieskie_Officer )
export Modele_Naval_Rifle_CMD_POL_04 is TResourceMesh( Mesh=POL_Niebieskie_1 )
export Modele_Naval_Rifle_CMD_POL_05 is TResourceMesh( Mesh=POL_Niebieskie_2 )
export Modele_Naval_Rifle_CMD_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Naval_Rifle_POL
export Modele_Naval_Rifle_POL is TResourceMesh( Mesh=POL_Niebieskie_NCO )
export Modele_Naval_Rifle_POL_02 is TResourceMesh( Mesh=POL_Niebieskie_spe )
export Modele_Naval_Rifle_POL_03 is TResourceMesh( Mesh=POL_Niebieskie_1 )
export Modele_Naval_Rifle_POL_04 is TResourceMesh( Mesh=POL_Niebieskie_2 )
export Modele_Naval_Rifle_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Commandos_Marine_POL
export Modele_Commandos_Marine_POL is TResourceMesh( Mesh=POL_Niebieskie_NCO )
export Modele_Commandos_Marine_POL_02 is TResourceMesh( Mesh=POL_Niebieskie_spe )
export Modele_Commandos_Marine_POL_03 is TResourceMesh( Mesh=POL_Niebieskie_1 )
export Modele_Commandos_Marine_POL_04 is TResourceMesh( Mesh=POL_Niebieskie_2 )
export Modele_Commandos_Marine_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Rifles_CMD_POL
export Modele_Rifles_CMD_POL is TResourceMesh( Mesh=POL_Piechota_Officer )
export Modele_Rifles_CMD_POL_02 is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_Rifles_CMD_POL_03 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_Rifles_CMD_POL_04 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_Rifles_CMD_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Rifles_HMG_POL
export Modele_Rifles_HMG_POL is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_Rifles_HMG_POL_02 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_Rifles_HMG_POL_03 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_Rifles_HMG_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Reserve_POL
export Modele_Reserve_POL is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_Reserve_POL_02 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_Reserve_POL_03 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_Reserve_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Reserve_CMD_POL
export Modele_Reserve_CMD_POL is TResourceMesh( Mesh=POL_Piechota_Officer )
export Modele_Reserve_CMD_POL_02 is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_Reserve_CMD_POL_03 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_Reserve_CMD_POL_04 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_Reserve_CMD_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Rifles_POL
export Modele_Rifles_POL is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_Rifles_POL_02 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_Rifles_POL_03 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_Rifles_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Scout_POL
export Modele_Scout_POL is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_Scout_POL_02 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_Scout_POL_03 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_Scout_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Para_CMD_POL
export Modele_Para_CMD_POL is TResourceMesh( Mesh=POL_Spado_Officer )
export Modele_Para_CMD_POL_02 is TResourceMesh( Mesh=POL_Spado_spe )
export Modele_Para_CMD_POL_03 is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_Para_CMD_POL_04 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_Para_CMD_POL_05 is TResourceMesh( Mesh=POL_Spado_2 )
export Modele_Para_CMD_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Scout_LRRP_Para_POL
export Modele_Scout_LRRP_Para_POL is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_Scout_LRRP_Para_POL_02 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_Scout_LRRP_Para_POL_03 is TResourceMesh( Mesh=POL_Spado_2 )
export Modele_Scout_LRRP_Para_POL_04 is TResourceMesh( Mesh=POL_Spado_spe )
export Modele_Scout_LRRP_Para_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Commandos_Para_CMD_POL
export Modele_Commandos_Para_CMD_POL is TResourceMesh( Mesh=POL_Komandosi_Officer )
export Modele_Commandos_Para_CMD_POL_02 is TResourceMesh( Mesh=POL_Komandosi_NCO )
export Modele_Commandos_Para_CMD_POL_03 is TResourceMesh( Mesh=POL_Komandosi_spe )
export Modele_Commandos_Para_CMD_POL_04 is TResourceMesh( Mesh=POL_Komandosi_1 )
export Modele_Commandos_Para_CMD_POL_05 is TResourceMesh( Mesh=POL_Komandosi_2 )
export Modele_Commandos_Para_CMD_POL_LOW is TResourceMesh( Mesh=POL_Komandosi_1_LOW )

//Commandos_Para_POL
export Modele_Commandos_Para_POL is TResourceMesh( Mesh=POL_Komandosi_NCO )
export Modele_Commandos_Para_POL_02 is TResourceMesh( Mesh=POL_Komandosi_spe )
export Modele_Commandos_Para_POL_03 is TResourceMesh( Mesh=POL_Komandosi_1 )
export Modele_Commandos_Para_POL_04 is TResourceMesh( Mesh=POL_Komandosi_2 )
export Modele_Commandos_Para_POL_LOW is TResourceMesh( Mesh=POL_Komandosi_1_LOW )

//Para_Security_POL
export Modele_Para_Security_POL is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_Para_Security_POL_02 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_Para_Security_POL_03 is TResourceMesh( Mesh=POL_Spado_2 )
export Modele_Para_Security_POL_04 is TResourceMesh( Mesh=POL_Spado_spe )
export Modele_Para_Security_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Groupe_AT_Para_POL
export Modele_Groupe_AT_Para_POL is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_Groupe_AT_Para_POL_02 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_Groupe_AT_Para_POL_03 is TResourceMesh( Mesh=POL_Spado_2 )
export Modele_Groupe_AT_Para_POL_04 is TResourceMesh( Mesh=POL_Spado_spe )
export Modele_Groupe_AT_Para_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Para_POL
export Modele_Para_POL is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_Para_POL_02 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_Para_POL_03 is TResourceMesh( Mesh=POL_Spado_2 )
export Modele_Para_POL_04 is TResourceMesh( Mesh=POL_Spado_spe )
export Modele_Para_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Para_HMG_POL
export Modele_Para_HMG_POL is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_Para_HMG_POL_02 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_Para_HMG_POL_03 is TResourceMesh( Mesh=POL_Spado_2 )
export Modele_Para_HMG_POL_04 is TResourceMesh( Mesh=POL_Spado_spe )
export Modele_Para_HMG_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Para_Metis_POL
export Modele_Para_Metis_POL is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_Para_Metis_POL_02 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_Para_Metis_POL_03 is TResourceMesh( Mesh=POL_Spado_2 )
export Modele_Para_Metis_POL_04 is TResourceMesh( Mesh=POL_Spado_spe )
export Modele_Para_Metis_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Engineers_paras_CMD_POL
export Modele_Engineers_paras_CMD_POL is TResourceMesh( Mesh=POL_Spado_Officer )
export Modele_Engineers_paras_CMD_POL_02 is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_Engineers_paras_CMD_POL_03 is TResourceMesh( Mesh=POL_Spado_spe )
export Modele_Engineers_paras_CMD_POL_04 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_Engineers_paras_CMD_POL_05 is TResourceMesh( Mesh=POL_Spado_2 )
export Modele_Engineers_paras_CMD_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Engineers_paras_POL
export Modele_Engineers_paras_POL is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_Engineers_paras_POL_02 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_Engineers_paras_POL_03 is TResourceMesh( Mesh=POL_Spado_2 )
export Modele_Engineers_paras_POL_04 is TResourceMesh( Mesh=POL_Spado_spe )
export Modele_Engineers_paras_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Engineers_paras_Flam_POL
export Modele_Engineers_paras_Flam_POL is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_Engineers_paras_Flam_POL_02 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_Engineers_paras_Flam_POL_03 is TResourceMesh( Mesh=POL_Spado_2 )
export Modele_Engineers_paras_Flam_POL_04 is TResourceMesh( Mesh=POL_Spado_spe )
export Modele_Engineers_paras_Flam_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//HvyScout_POL
export Modele_HvyScout_POL is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_HvyScout_POL_02 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_HvyScout_POL_03 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_HvyScout_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Scout_para_POL
export Modele_Scout_para_POL is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_Scout_para_POL_02 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_Scout_para_POL_03 is TResourceMesh( Mesh=POL_Spado_2 )
export Modele_Scout_para_POL_04 is TResourceMesh( Mesh=POL_Spado_spe )
export Modele_Scout_para_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Commandos_CMD_POL
export Modele_Commandos_CMD_POL is TResourceMesh( Mesh=POL_Spado_Officer )
export Modele_Commandos_CMD_POL_02 is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_Commandos_CMD_POL_03 is TResourceMesh( Mesh=POL_Spado_spe )
export Modele_Commandos_CMD_POL_04 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_Commandos_CMD_POL_05 is TResourceMesh( Mesh=POL_Spado_2 )
export Modele_Commandos_CMD_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Engineers_CMD_POL
export Modele_Engineers_CMD_POL is TResourceMesh( Mesh=POL_Piechota_Officer )
export Modele_Engineers_CMD_POL_02 is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_Engineers_CMD_POL_03 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_Engineers_CMD_POL_04 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_Engineers_CMD_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Engineers_POL
export Modele_Engineers_POL is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_Engineers_POL_02 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_Engineers_POL_03 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_Engineers_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Engineers_Scout_POL
export Modele_Engineers_Scout_POL is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_Engineers_Scout_POL_02 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_Engineers_Scout_POL_03 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_Engineers_Scout_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Engineers_Flam_POL
export Modele_Engineers_Flam_POL is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_Engineers_Flam_POL_02 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_Engineers_Flam_POL_03 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_Engineers_Flam_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//MANPAD_Strela_2M_POL
export Modele_MANPAD_Strela_2M_POL is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_MANPAD_Strela_2M_POL_02 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_MANPAD_Strela_2M_POL_LOW is TResourceMesh( Mesh=DDR_MotSchutzen_LOW )

//MANPAD_Strela_2M_Para_POL
export Modele_MANPAD_Strela_2M_Para_POL is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_MANPAD_Strela_2M_Para_POL_02 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_MANPAD_Strela_2M_Para_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//MANPAD_Strela_2M_Naval_POL
export Modele_MANPAD_Strela_2M_Naval_POL is TResourceMesh( Mesh=POL_Niebieskie_NCO )
export Modele_MANPAD_Strela_2M_Naval_POL_02 is TResourceMesh( Mesh=POL_Niebieskie_1 )
export Modele_MANPAD_Strela_2M_Naval_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Commandos_POL
export Modele_Commandos_POL is TResourceMesh( Mesh=POL_Spado_NCO )
export Modele_Commandos_POL_02 is TResourceMesh( Mesh=POL_Spado_1 )
export Modele_Commandos_POL_03 is TResourceMesh( Mesh=POL_Spado_2 )
export Modele_Commandos_POL_04 is TResourceMesh( Mesh=POL_Spado_spe )
export Modele_Commandos_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Groupe_AT_POL
export Modele_Groupe_AT_POL is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_Groupe_AT_POL_02 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_Groupe_AT_POL_03 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_Groupe_AT_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//Sniper_POL
export Modele_Sniper_POL is TResourceMesh( Mesh=POL_Komandosi_2 )
export Modele_Sniper_POL_02 is TResourceMesh( Mesh=POL_Komandosi_spe )
export Modele_Sniper_POL_LOW is TResourceMesh( Mesh=POL_Komandosi_1_LOW )

//Sniper_paras_POL
export Modele_Sniper_paras_POL is TResourceMesh( Mesh=POL_Komandosi_1 )
export Modele_Sniper_paras_POL_02 is TResourceMesh( Mesh=POL_Komandosi_spe )
export Modele_Sniper_paras_POL_LOW is TResourceMesh( Mesh=POL_Komandosi_1_LOW )

//Scout_SF_POL
export Modele_Scout_SF_POL is TResourceMesh( Mesh=POL_Komandosi_NCO )
export Modele_Scout_SF_POL_02 is TResourceMesh( Mesh=POL_Komandosi_spe )
export Modele_Scout_SF_POL_03 is TResourceMesh( Mesh=POL_Komandosi_2 )
export Modele_Scout_SF_POL_04 is TResourceMesh( Mesh=POL_Komandosi_1 )
export Modele_Scout_SF_POL_LOW is TResourceMesh( Mesh=POL_Komandosi_1_LOW )

//Scout_LRRP_POL
export Modele_Scout_LRRP_POL is TResourceMesh( Mesh=POL_Piechota_NCO )
export Modele_Scout_LRRP_POL_02 is TResourceMesh( Mesh=POL_Piechota_2 )
export Modele_Scout_LRRP_POL_03 is TResourceMesh( Mesh=POL_Piechota_1 )
export Modele_Scout_LRRP_POL_LOW is TResourceMesh( Mesh=POL_Niebieskie_1_LOW )

//-------------------------------------------------------------------------------
//-----------------------------------CIVILIANS-----------------------------------
//-------------------------------------------------------------------------------
Civil1_Mullet is      'GameData:/Assets/3D/Units/URSS/Infanterie/Civil1_Mullet/Civil1_Mullet.fbx'
Civil1_Mullet_LOW is      'GameData:/Assets/3D/Units/URSS/Infanterie/Civil1_Mullet/Civil1_Mullet_LOW.fbx'
Civil2_Trucker is     'GameData:/Assets/3D/Units/URSS/Infanterie/Civil2_Trucker/Civil2_Trucker.fbx'
Civil3_Mannschaft is  'GameData:/Assets/3D/Units/URSS/Infanterie/Civil3_Mannschaft/Civil3_Mannschaft.fbx'
Civil4_Suit is        'GameData:/Assets/3D/Units/URSS/Infanterie/Civil4_Suit/Civil4_Suit.fbx'
Civil5_DoubleDenim is 'GameData:/Assets/3D/Units/URSS/Infanterie/Civil5_DoubleDenim/Civil5_DoubleDenim.fbx'
Civil6_Miami is       'GameData:/Assets/3D/Units/URSS/Infanterie/Civil6_Miami/Civil6_Miami.fbx'
Civil7_Pastel_Male is 'GameData:/Assets/3D/Units/URSS/Infanterie/Civil7_Pastel_Male/Civil7_Pastel_Male.fbx'
Civil8_Princess is    'GameData:/Assets/3D/Units/URSS/Infanterie/Civil8_Princess/Civil8_Princess.fbx'
Civil9_Punk_Female is 'GameData:/Assets/3D/Units/URSS/Infanterie/Civil9_Punk_Female/Civil9_Punk_Female.fbx'
Civil10_Punk_Male is  'GameData:/Assets/3D/Units/URSS/Infanterie/Civil10_Punk_Male/Civil10_Punk_Male.fbx'

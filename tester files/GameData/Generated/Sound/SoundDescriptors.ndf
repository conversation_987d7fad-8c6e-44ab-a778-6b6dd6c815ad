// Ne pas éditer, ce fichier est généré par SoundDescriptorsFileWriter


export Sound_AmbianceArtilleryImpactsArmyGeneral is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Artillery_Impact_Army_General_OneShot_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Artillery_Impact_Army_General_OneShot_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Artillery_Impact_Army_General_OneShot_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Artillery_Impact_Army_General_OneShot_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Artillery_Impact_Army_General_OneShot_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Artillery_Impact_Army_General_OneShot_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Artillery_Impact_Army_General_OneShot_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Artillery_Impact_Army_General_OneShot_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Artillery_Impact_Army_General_OneShot_09.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_AmbianceArtilleryImpactsArmyGeneral is TSoundHappening
(
    SoundDescList = Sound_AmbianceArtilleryImpactsArmyGeneral
    Volume = 1
)


export Sound_AmbianceOfficeArmyGeneral is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_11.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_12.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_13.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_14.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_15.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_16.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_17.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_18.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_19.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_20.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_21.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_22.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_23.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_24.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_25.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_26.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_27.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_OneShot_28.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_AmbianceOfficeArmyGeneral is TSoundHappening
(
    SoundDescList = Sound_AmbianceOfficeArmyGeneral
    Volume = 1
)


export Sound_CityAmbiance is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_CityAmbiance is TSoundHappening
(
    SoundDescList = Sound_CityAmbiance
    Volume = 1
)


export Sound_CityAmbianceHLM is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_CityAmbianceHLM is TSoundHappening
(
    SoundDescList = Sound_CityAmbianceHLM
    Volume = 1
)


export Sound_CityAmbianceHLM_Evenements is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_11.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_12.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_13.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_14.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_15.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_16.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_17.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_18.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_20.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_21.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_HLM_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HLM_Evenement_22.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_CityAmbianceHLM_Evenements is TSoundHappening
(
    SoundDescList = Sound_CityAmbianceHLM_Evenements
    Volume = 1
)


export Sound_CityAmbiance_Evenements is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City_Evenement_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City_Evenement_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City_Evenement_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City_Evenement_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City_Evenement_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City_Evenement_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City_Evenement_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City_Evenement_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City_Evenement_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City_Evenement_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City_Evenement_11.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_City_Evenement_12.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_CityAmbiance_Evenements is TSoundHappening
(
    SoundDescList = Sound_CityAmbiance_Evenements
    Volume = 1
)


export Sound_CrashAvion is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur_Avion
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/mort/avionEcrasement.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_CrashAvion is TSoundHappening
(
    SoundDescList = Sound_CrashAvion
    Volume = 2
)


export Sound_DestructionBatiment is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Building_Collapse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Building_Collapse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Building_Collapse_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Building_Collapse_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Building_Collapse_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Building_Collapse_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Building_Collapse_7.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_DestructionBatiment is TSoundHappening
(
    SoundDescList = Sound_DestructionBatiment
    Volume = 1
)


export Sound_DestructionHelico is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Helico_Moyen.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_DestructionHelico is TSoundHappening
(
    SoundDescList = Sound_DestructionHelico
    Volume = 1
)


export Sound_DestructionHelicoPetit is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Petite
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Helico_Leger.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_DestructionHelicoPetit is TSoundHappening
(
    SoundDescList = Sound_DestructionHelicoPetit
    Volume = 1
)


export Sound_DestructionHelico_Eau is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Helico_Eau.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_DestructionHelico_Eau is TSoundHappening
(
    SoundDescList = Sound_DestructionHelico_Eau
    Volume = 1
)


export Sound_DestructionUnite is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_MBT_Internal_Explo_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_MBT_Internal_Explo_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_MBT_Internal_Explo_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_MBT_Internal_Explo_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_MBT_Internal_Explo_05.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_DestructionUnite is TSoundHappening
(
    SoundDescList = Sound_DestructionUnite
    Volume = 1
)


export Sound_DestructionUniteGros is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_MBT_Internal_Explo_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_MBT_Internal_Explo_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_MBT_Internal_Explo_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_MBT_Internal_Explo_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_MBT_Internal_Explo_05.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_DestructionUniteGros is TSoundHappening
(
    SoundDescList = Sound_DestructionUniteGros
    Volume = 1
)


export Sound_DestructionUniteGros_Avion is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_7.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_DestructionUniteGros_Avion is TSoundHappening
(
    SoundDescList = Sound_DestructionUniteGros_Avion
    Volume = 1
)


export Sound_DestructionUnitePetit is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Petite
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_Mort_Vehicule_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Petite
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_Mort_Vehicule_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Petite
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_Mort_Vehicule_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Petite
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_Mort_Vehicule_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Petite
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_Mort_Vehicule_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Petite
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_Mort_Vehicule_06.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_DestructionUnitePetit is TSoundHappening
(
    SoundDescList = Sound_DestructionUnitePetit
    Volume = 1
)


export Sound_DestructionUnite_Avion is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_7.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_DestructionUnite_Avion is TSoundHappening
(
    SoundDescList = Sound_DestructionUnite_Avion
    Volume = 1
)


export Sound_DestructionUnite_Avion_Eau is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/Crash_Avion_Eau_1.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_DestructionUnite_Avion_Eau is TSoundHappening
(
    SoundDescList = Sound_DestructionUnite_Avion_Eau
    Volume = 1
)


export Sound_DestructionUnite_Ravitaillement is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Mort_Unite_Grosse
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Morts/GE_Mort_Vehicule_Ravitaillement_01.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_DestructionUnite_Ravitaillement is TSoundHappening
(
    SoundDescList = Sound_DestructionUnite_Ravitaillement
    Volume = 1
)


export Sound_EauAmbiance is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Eau
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Eau.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_EauAmbiance is TSoundHappening
(
    SoundDescList = Sound_EauAmbiance
    Volume = 1
)


export Sound_EauAmbiance_Evenements is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Eau_Evenement_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Eau_Evenement_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Eau_Evenement_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Eau_Evenement_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_City_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Eau_Evenement_05.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_EauAmbiance_Evenements is TSoundHappening
(
    SoundDescList = Sound_EauAmbiance_Evenements
    Volume = 1
)


export Sound_FULDA_100mm_2A29_Rapira is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_2A29_Rapira_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_2A29_Rapira_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_2A29_Rapira_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_2A29_Rapira_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_100mm_2A29_Rapira is TSoundHappening
(
    SoundDescList = Sound_FULDA_100mm_2A29_Rapira
    Volume = 2
)


export Sound_FULDA_100mm_BMP3 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_BMP3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_100mm_BMP3 is TSoundHappening
(
    SoundDescList = Sound_FULDA_100mm_BMP3
    Volume = 2
)


export Sound_FULDA_100mm_D10T is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_T55_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_T55_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_T55_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_100mm_D10T is TSoundHappening
(
    SoundDescList = Sound_FULDA_100mm_D10T
    Volume = 2
)


export Sound_FULDA_105mm_Abbot is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abbot_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abbot_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abbot_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abbot_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abbot_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abbot_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_105mm_Abbot is TSoundHappening
(
    SoundDescList = Sound_FULDA_105mm_Abbot
    Volume = 2
)


export Sound_FULDA_105mm_CN_105_F1 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_CN_105_F1_AMX_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_CN_105_F1_AMX_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_CN_105_F1_AMX_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_CN_105_F1_AMX_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_CN_105_F1_AMX_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_CN_105_F1_AMX_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_105mm_CN_105_F1 is TSoundHappening
(
    SoundDescList = Sound_FULDA_105mm_CN_105_F1
    Volume = 2
)


export Sound_FULDA_105mm_Leo_A1 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_LeoA1_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_LeoA1_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_LeoA1_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_LeoA1_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_LeoA1_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_LeoA1_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_105mm_Leo_A1 is TSoundHappening
(
    SoundDescList = Sound_FULDA_105mm_Leo_A1
    Volume = 2
)


export Sound_FULDA_105mm_M1_Abrams is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abrams_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abrams_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abrams_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abrams_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abrams_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abrams_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abrams_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abrams_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abrams_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_Abrams_10.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_105mm_M1_Abrams is TSoundHappening
(
    SoundDescList = Sound_FULDA_105mm_M1_Abrams
    Volume = 2
)


export Sound_FULDA_105mm_M60_Patton is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_M60_Patton_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_M60_Patton_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_M60_Patton_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_M60_Patton_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_M60_Patton_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/105mm_canon_tir_M60_Patton_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_105mm_M60_Patton is TSoundHappening
(
    SoundDescList = Sound_FULDA_105mm_M60_Patton
    Volume = 2
)


export Sound_FULDA_107mm_Mortar is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M30_107mm_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M30_107mm_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M30_107mm_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M30_107mm_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M30_107mm_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M30_107mm_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M30_107mm_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M30_107mm_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M30_107mm_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M30_107mm_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M30_107mm_11.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_107mm_Mortar is TSoundHappening
(
    SoundDescList = Sound_FULDA_107mm_Mortar
    Volume = 2
)


export Sound_FULDA_115mm_U5TS_T62M is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_U5TS_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_U5TS_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_U5TS_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_U5TS_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_115mm_U5TS_T62M is TSoundHappening
(
    SoundDescList = Sound_FULDA_115mm_U5TS_T62M
    Volume = 2
)


export Sound_FULDA_120mm_Challenger is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Challenger_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Challenger_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Challenger_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Challenger_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Challenger_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Challenger_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Challenger_7.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Challenger_8.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_120mm_Challenger is TSoundHappening
(
    SoundDescList = Sound_FULDA_120mm_Challenger
    Volume = 2
)


export Sound_FULDA_120mm_Leo2 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Leo2_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Leo2_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Leo2_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Leo2_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Leo2_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Leo2_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Leo2_7.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Leo2_8.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_120mm_Leo2 is TSoundHappening
(
    SoundDescList = Sound_FULDA_120mm_Leo2
    Volume = 2
)


export Sound_FULDA_120mm_M1_Abrams is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Abrams_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Abrams_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Abrams_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Abrams_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Abrams_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Abrams_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Abrams_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Abrams_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Abrams_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Abrams_10.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_120mm_M1_Abrams is TSoundHappening
(
    SoundDescList = Sound_FULDA_120mm_M1_Abrams
    Volume = 2
)


export Sound_FULDA_122mm is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_Gvozdika_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_Gvozdika_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_Gvozdika_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_Gvozdika_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_Gvozdika_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_Gvozdika_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_122mm is TSoundHappening
(
    SoundDescList = Sound_FULDA_122mm
    Volume = 2
)


export Sound_FULDA_122mm_IS2 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_IS2_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_IS2_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_IS2_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_122mm_IS2 is TSoundHappening
(
    SoundDescList = Sound_FULDA_122mm_IS2
    Volume = 2
)


export Sound_FULDA_125mm_T64 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/125mm_canon_tir_T64_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/125mm_canon_tir_T64_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/125mm_canon_tir_T64_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/125mm_canon_tir_T64_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_125mm_T64 is TSoundHappening
(
    SoundDescList = Sound_FULDA_125mm_T64
    Volume = 2
)


export Sound_FULDA_125mm_T80 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/125mm_canon_tir_T80_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/125mm_canon_tir_T80_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/125mm_canon_tir_T80_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/125mm_canon_tir_T80_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/125mm_canon_tir_T80_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_125mm_T80 is TSoundHappening
(
    SoundDescList = Sound_FULDA_125mm_T80
    Volume = 2
)


export Sound_FULDA_152mm is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_Akatsiya_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_Akatsiya_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_Akatsiya_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_Akatsiya_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_152mm is TSoundHappening
(
    SoundDescList = Sound_FULDA_152mm
    Volume = 2
)


export Sound_FULDA_152mm_Sheridan is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_M81_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_M81_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_M81_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_152mm_Sheridan is TSoundHappening
(
    SoundDescList = Sound_FULDA_152mm_Sheridan
    Volume = 2
)


export Sound_FULDA_155mm is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_M109A2_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_M109A2_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_M109A2_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_M109A2_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_M109A2_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_M109A2_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_155mm is TSoundHappening
(
    SoundDescList = Sound_FULDA_155mm
    Volume = 2
)


export Sound_FULDA_155mm_GCT is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_GCT_AufF1_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_GCT_AufF1_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_GCT_AufF1_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_GCT_AufF1_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_GCT_AufF1_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_GCT_AufF1_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_GCT_AufF1_7.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_155mm_GCT is TSoundHappening
(
    SoundDescList = Sound_FULDA_155mm_GCT
    Volume = 2
)


export Sound_FULDA_203mm is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_M110_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_M110_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_M110_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_M110_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_M110_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_M110_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_M110_7.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_203mm is TSoundHappening
(
    SoundDescList = Sound_FULDA_203mm
    Volume = 2
)


export Sound_FULDA_203mm_Malka is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_Malka_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_Malka_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_Malka_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_Malka_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_Malka_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_Malka_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/203mm_canon_tir_Malka_7.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_203mm_Malka is TSoundHappening
(
    SoundDescList = Sound_FULDA_203mm_Malka
    Volume = 2
)


export Sound_FULDA_240mm_Mortar is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M240_240mm_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M240_240mm_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M240_240mm_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M240_240mm_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/M240_240mm_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_240mm_Mortar is TSoundHappening
(
    SoundDescList = Sound_FULDA_240mm_Mortar
    Volume = 2
)


export Sound_FULDA_2A38_chain_gun is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_30mm_autocanon_Tunguska_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_30mm_autocanon_Tunguska_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_30mm_autocanon_Tunguska_3.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_2A38_chain_gun is TSoundHappening
(
    SoundDescList = Sound_FULDA_2A38_chain_gun
    Volume = 2
)


export Sound_FULDA_2A42_chain_gun is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_BMP2_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_BMP2_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_BMP2_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_BMP2_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_2A42_chain_gun is TSoundHappening
(
    SoundDescList = Sound_FULDA_2A42_chain_gun
    Volume = 2
)


export Sound_FULDA_2A72_chain_gun is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_BMP3_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_BMP3_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_BMP3_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_BMP3_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_BMP3_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_BMP3_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_BMP3_7.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_2A72_chain_gun is TSoundHappening
(
    SoundDescList = Sound_FULDA_2A72_chain_gun
    Volume = 2
)


export Sound_FULDA_3M8M_KRUG_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_9M38M1_BUK_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_9M38M1_BUK_2.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_3M8M_KRUG_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_3M8M_KRUG_Firing
    Volume = 2
)


export Sound_FULDA_73mm_BMP1 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/73mm_canon_tir_BMP1_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/73mm_canon_tir_BMP1_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/73mm_canon_tir_BMP1_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_73mm_BMP1 is TSoundHappening
(
    SoundDescList = Sound_FULDA_73mm_BMP1
    Volume = 2
)


export Sound_FULDA_76mm_L23A1 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/76mm_canon_tir_L23A1_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/76mm_canon_tir_L23A1_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/76mm_canon_tir_L23A1_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/76mm_canon_tir_L23A1_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/76mm_canon_tir_L23A1_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/76mm_canon_tir_L23A1_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_76mm_L23A1 is TSoundHappening
(
    SoundDescList = Sound_FULDA_76mm_L23A1
    Volume = 2
)


export Sound_FULDA_76mm_M32A1 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/76mm_canon_tir_M32A1_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/76mm_canon_tir_M32A1_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/76mm_canon_tir_M32A1_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/76mm_canon_tir_M32A1_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_76mm_M32A1 is TSoundHappening
(
    SoundDescList = Sound_FULDA_76mm_M32A1
    Volume = 2
)


export Sound_FULDA_76mm_PT76 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/76mm_canon_tir_PT76_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/76mm_canon_tir_PT76_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/76mm_canon_tir_PT76_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_76mm_PT76 is TSoundHappening
(
    SoundDescList = Sound_FULDA_76mm_PT76
    Volume = 2
)


export Sound_FULDA_81mm_Mortar is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/L16_81mm_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/L16_81mm_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/L16_81mm_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/L16_81mm_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/L16_81mm_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/L16_81mm_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/L16_81mm_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/L16_81mm_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/L16_81mm_09.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_81mm_Mortar is TSoundHappening
(
    SoundDescList = Sound_FULDA_81mm_Mortar
    Volume = 2
)


export Sound_FULDA_82mm_Mortar_Vasilek is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/Vasilek_81mm_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/Vasilek_81mm_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/Vasilek_81mm_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Mortier/Vasilek_81mm_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_82mm_Mortar_Vasilek is TSoundHappening
(
    SoundDescList = Sound_FULDA_82mm_Mortar_Vasilek
    Volume = 2
)


export Sound_FULDA_90_GIAT_F1 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/90mm_canon_tir_GIATF1_1.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_90_GIAT_F1 is TSoundHappening
(
    SoundDescList = Sound_FULDA_90_GIAT_F1
    Volume = 2
)


export Sound_FULDA_90mm_M48_Patton is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/90mm_canon_tir_M48_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/90mm_canon_tir_M48_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/90mm_canon_tir_M48_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/90mm_canon_tir_M48_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/90mm_canon_tir_M48_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_90mm_M48_Patton is TSoundHappening
(
    SoundDescList = Sound_FULDA_90mm_M48_Patton
    Volume = 2
)


export Sound_FULDA_9M311_Tunguska_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_9M311_Tunguska.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_9M311_Tunguska_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_9M311_Tunguska_Firing
    Volume = 2
)


export Sound_FULDA_9M33_OSA_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_9M33_OSA_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_9M33_OSA_2.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_9M33_OSA_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_9M33_OSA_Firing
    Volume = 2
)


export Sound_FULDA_9M38M1_BUK_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_9M38M1_BUK_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_9M38M1_BUK_2.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_9M38M1_BUK_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_9M38M1_BUK_Firing
    Volume = 2
)


export Sound_FULDA_AA52_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/AA52_mitrailleuse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/AA52_mitrailleuse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/AA52_mitrailleuse_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/AA52_mitrailleuse_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/AA52_mitrailleuse_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/AA52_mitrailleuse_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_AA52_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_AA52_Firing
    Volume = 1
)


export Sound_FULDA_AANF1_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/AANF1_mitrailleuse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/AANF1_mitrailleuse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/AANF1_mitrailleuse_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/AANF1_mitrailleuse_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/AANF1_mitrailleuse_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/AANF1_mitrailleuse_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_AANF1_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_AANF1_Firing
    Volume = 1
)


export Sound_FULDA_AGM114_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Hellfire_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Hellfire_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Hellfire_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_AGM114_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_AGM114_Firing
    Volume = 2
)


export Sound_FULDA_AGM65_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_AGM65.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_AGM65_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_AGM65_Firing
    Volume = 2
)


export Sound_FULDA_AGS_17_Grenade_Launcher is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_LanceGrenade_tir_AGS_17_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_LanceGrenade_tir_AGS_17_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_LanceGrenade_tir_AGS_17_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_LanceGrenade_tir_AGS_17_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_AGS_17_Grenade_Launcher is TSoundHappening
(
    SoundDescList = Sound_FULDA_AGS_17_Grenade_Launcher
    Volume = 2
)


export Sound_FULDA_AK74_Burst_3_Rounds is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/AK74_firing_3_rounds_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/AK74_firing_3_rounds_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/AK74_firing_3_rounds_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/AK74_firing_3_rounds_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/AK74_firing_3_rounds_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/AK74_firing_3_rounds_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/AK74_firing_3_rounds_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/AK74_firing_3_rounds_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/AK74_firing_3_rounds_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/AK74_firing_3_rounds_10.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_AK74_Burst_3_Rounds is TSoundHappening
(
    SoundDescList = Sound_FULDA_AK74_Burst_3_Rounds
    Volume = 1
)


export Sound_FULDA_APILAS_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_APILAS_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_APILAS_2.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_APILAS_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_APILAS_Firing
    Volume = 2
)


export Sound_FULDA_APZ23_chain_gun is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_23mm_autocanon_Shilka_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_23mm_autocanon_Shilka_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_23mm_autocanon_Shilka_3.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_23mm_autocanon_Shilka_4.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_23mm_autocanon_Shilka_5.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_23mm_autocanon_Shilka_6.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_23mm_autocanon_Shilka_7.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_APZ23_chain_gun is TSoundHappening
(
    SoundDescList = Sound_FULDA_APZ23_chain_gun
    Volume = 2
)


export Sound_FULDA_ASPIDE_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_ASPIDE_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_ASPIDE_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_ASPIDE_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_ASPIDE_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_ASPIDE_Firing
    Volume = 2
)


export Sound_FULDA_AT4_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_AT4_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_AT4_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_AT4_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_AT4_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_AT4_Firing
    Volume = 2
)


export Sound_FULDA_Bereg_130mm is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/130mm_canon_tir_A222_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/130mm_canon_tir_A222_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/130mm_canon_tir_A222_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/130mm_canon_tir_A222_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Bereg_130mm is TSoundHappening
(
    SoundDescList = Sound_FULDA_Bereg_130mm
    Volume = 2
)


export Sound_FULDA_Bofor_autocanon is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_40mm_autocanon_Bofor_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_40mm_autocanon_Bofor_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_40mm_autocanon_Bofor_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_40mm_autocanon_Bofor_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_40mm_autocanon_Bofor_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_40mm_autocanon_Bofor_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_40mm_autocanon_Bofor_7.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_40mm_autocanon_Bofor_8.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_40mm_autocanon_Bofor_9.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Bofor_autocanon is TSoundHappening
(
    SoundDescList = Sound_FULDA_Bofor_autocanon
    Volume = 2
)


export Sound_FULDA_Bombe_Cluster_sol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Cluster_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Cluster_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Cluster_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Cluster_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Bombe_Cluster_sol is TSoundHappening
(
    SoundDescList = Sound_FULDA_Bombe_Cluster_sol
    Volume = 1
)


export Sound_FULDA_Bombe_Mk84_sol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Mk84_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Mk84_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Mk84_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Mk84_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Mk84_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Bombe_Mk84_sol is TSoundHappening
(
    SoundDescList = Sound_FULDA_Bombe_Mk84_sol
    Volume = 1
)


export Sound_FULDA_Bombe_Mk_sol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Mk_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Mk_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Mk_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Mk_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Mk_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Bombe_Mk_sol is TSoundHappening
(
    SoundDescList = Sound_FULDA_Bombe_Mk_sol
    Volume = 1
)


export Sound_FULDA_Bombe_Napalm_sol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Napalm_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Napalm_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Napalm_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Bombe_Napalm_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Bombe_Napalm_sol is TSoundHappening
(
    SoundDescList = Sound_FULDA_Bombe_Napalm_sol
    Volume = 1
)


export Sound_FULDA_Bren_L4A4_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L4A4_Bren_mitrailleuse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L4A4_Bren_mitrailleuse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L4A4_Bren_mitrailleuse_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L4A4_Bren_mitrailleuse_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L4A4_Bren_mitrailleuse_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L4A4_Bren_mitrailleuse_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L4A4_Bren_mitrailleuse_7.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L4A4_Bren_mitrailleuse_8.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Bren_L4A4_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_Bren_L4A4_Firing
    Volume = 1
)


export Sound_FULDA_CARL_GUSTAV_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_CARL_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_CARL_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_CARL_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CARL_GUSTAV_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_CARL_GUSTAV_Firing
    Volume = 2
)


export Sound_FULDA_Cal50 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_50_Firing_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_50_Firing_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_50_Firing_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_50_Firing_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_50_Firing_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_50_Firing_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_50_Firing_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_50_Firing_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_50_Firing_09.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Cal50 is TSoundHappening
(
    SoundDescList = Sound_FULDA_Cal50
    Volume = 1
)


export Sound_FULDA_CalM3P is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_M3P_Firing_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_M3P_Firing_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_M3P_Firing_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_M3P_Firing_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CalM3P is TSoundHappening
(
    SoundDescList = Sound_FULDA_CalM3P
    Volume = 1
)


export Sound_FULDA_CriticalFX_Alarm_Helico_Computer_Down is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Alarm_Helico_Computer.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Alarm_Helico_Computer_Down is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Alarm_Helico_Computer_Down
    Volume = 1
)


export Sound_FULDA_CriticalFX_Alarm_Helico_HUD is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Alarm_Helico_HUD.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Alarm_Helico_HUD is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Alarm_Helico_HUD
    Volume = 1
)


export Sound_FULDA_CriticalFX_Alarm_Helico_Leak is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Alarm_Helico_Leak.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Alarm_Helico_Leak is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Alarm_Helico_Leak
    Volume = 1
)


export Sound_FULDA_CriticalFX_Alarm_Helico_Structural_Damage is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Alarm_Helico_Structural_Damage.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Alarm_Helico_Structural_Damage is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Alarm_Helico_Structural_Damage
    Volume = 1
)


export Sound_FULDA_CriticalFX_Alarm_Plane is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Alarm_Plane.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Alarm_Plane is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Alarm_Plane
    Volume = 1
)


export Sound_FULDA_CriticalFX_Alarm_Plane_Computer_Down is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Alarm_Plane_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Alarm_Plane_Computer_Down is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Alarm_Plane_Computer_Down
    Volume = 1
)


export Sound_FULDA_CriticalFX_Alarm_Plane_Engine is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Alarm_Plane_2.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Alarm_Plane_Engine is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Alarm_Plane_Engine
    Volume = 1
)


export Sound_FULDA_CriticalFX_Alarm_Plane_HUD is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Alarm_Plane_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Alarm_Plane_HUD is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Alarm_Plane_HUD
    Volume = 1
)


export Sound_FULDA_CriticalFX_Alarm_Plane_Structural_Damage is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Alarm_Plane_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Alarm_Plane_Structural_Damage is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Alarm_Plane_Structural_Damage
    Volume = 1
)


export Sound_FULDA_CriticalFX_Ammo is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Ammo.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Ammo is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Ammo
    Volume = 1
)


export Sound_FULDA_CriticalFX_Comp_Reset is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Comp_Reset.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Comp_Reset is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Comp_Reset
    Volume = 1
)


export Sound_FULDA_CriticalFX_Crew is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Crew_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Crew_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Crew_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Crew_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Crew_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Crew_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Crew_7.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Crew_8.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Crew_9.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Crew is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Crew
    Volume = 1
)


export Sound_FULDA_CriticalFX_Crew_Dead is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Crew_Dead.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Crew_Dead is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Crew_Dead
    Volume = 1
)


export Sound_FULDA_CriticalFX_Fuel_Explosion is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Fuel_Explosion.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Fuel_Explosion is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Fuel_Explosion
    Volume = 1
)


export Sound_FULDA_CriticalFX_Gun_Turret_Stuck is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Gun_Turret_Stuck.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Gun_Turret_Stuck is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Gun_Turret_Stuck
    Volume = 1
)


export Sound_FULDA_CriticalFX_Hull_Break is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Hull_Break_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Hull_Break_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Hull_Break_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Hull_Break is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Hull_Break
    Volume = 1
)


export Sound_FULDA_CriticalFX_Moteur_Destroyed is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Moteur_Destroyed.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Moteur_Destroyed is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Moteur_Destroyed
    Volume = 1
)


export Sound_FULDA_CriticalFX_Spalling is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Spalling.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Spalling is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Spalling
    Volume = 1
)


export Sound_FULDA_CriticalFX_Track is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/CriticalFX_Track.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_CriticalFX_Track is TSoundHappening
(
    SoundDescList = Sound_FULDA_CriticalFX_Track
    Volume = 1
)


export Sound_FULDA_D20_152mm is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_D20_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_D20_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_D20_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_D20_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_D20_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_D20_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_D20_152mm is TSoundHappening
(
    SoundDescList = Sound_FULDA_D20_152mm
    Volume = 2
)


export Sound_FULDA_D30_122mm is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_D30_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_D30_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_D30_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_D30_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_D30_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_D30_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/122mm_canon_tir_D30_7.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_D30_122mm is TSoundHappening
(
    SoundDescList = Sound_FULDA_D30_122mm
    Volume = 2
)


export Sound_FULDA_D44_85mm is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/85mm_canon_tir_D44_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/85mm_canon_tir_D44_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/85mm_canon_tir_D44_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/85mm_canon_tir_D44_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/85mm_canon_tir_D44_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/85mm_canon_tir_D44_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_D44_85mm is TSoundHappening
(
    SoundDescList = Sound_FULDA_D44_85mm
    Volume = 2
)


export Sound_FULDA_DANA_SP_152mm is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_DANA_SP_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_DANA_SP_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/152mm_canon_tir_DANA_SP_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_DANA_SP_152mm is TSoundHappening
(
    SoundDescList = Sound_FULDA_DANA_SP_152mm
    Volume = 2
)


export Sound_FULDA_DP28_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/DP28_mitrailleuse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/DP28_mitrailleuse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/DP28_mitrailleuse_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/DP28_mitrailleuse_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_DP28_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_DP28_Firing
    Volume = 1
)


export Sound_FULDA_DRAGON_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_M47_Dragon.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_DRAGON_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_DRAGON_Firing
    Volume = 2
)


export Sound_FULDA_DShKM is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/DShK_Firing_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/DShK_Firing_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/DShK_Firing_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/DShK_Firing_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/DShK_Firing_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/DShK_Firing_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_DShKM is TSoundHappening
(
    SoundDescList = Sound_FULDA_DShKM
    Volume = 1
)


export Sound_FULDA_Drop_Bomb is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Drop_Bomb_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Drop_Bomb_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Drop_Bomb_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Drop_Bomb_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Drop_Bomb_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Drop_Bomb is TSoundHappening
(
    SoundDescList = Sound_FULDA_Drop_Bomb
    Volume = 2
)


export Sound_FULDA_Drop_Bomblets_MW1 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Drop_Bomblets_MW1_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Drop_Bomblets_MW1_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Bombes/Drop_Bomblets_MW1_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Drop_Bomblets_MW1 is TSoundHappening
(
    SoundDescList = Sound_FULDA_Drop_Bomblets_MW1
    Volume = 2
)


export Sound_FULDA_FALO is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FALO_Firing_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FALO_Firing_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FALO_Firing_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FALO_Firing_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FALO_Firing_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_FALO is TSoundHappening
(
    SoundDescList = Sound_FULDA_FALO
    Volume = 1
)


export Sound_FULDA_FAMAS_Burst_3_Rounds is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FAMAS_firing_3_rounds_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FAMAS_firing_3_rounds_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FAMAS_firing_3_rounds_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FAMAS_firing_3_rounds_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FAMAS_firing_3_rounds_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FAMAS_firing_3_rounds_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FAMAS_firing_3_rounds_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FAMAS_firing_3_rounds_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FAMAS_firing_3_rounds_09.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_FAMAS_Burst_3_Rounds is TSoundHappening
(
    SoundDescList = Sound_FULDA_FAMAS_Burst_3_Rounds
    Volume = 1
)


export Sound_FULDA_FH70_155mm is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_FH70_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_FH70_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_FH70_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_FH70_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/155mm_canon_tir_FH70_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_FH70_155mm is TSoundHappening
(
    SoundDescList = Sound_FULDA_FH70_155mm
    Volume = 2
)


export Sound_FULDA_FLASH66mm_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Flash_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Flash_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Flash_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_FLASH66mm_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_FLASH66mm_Firing
    Volume = 2
)


export Sound_FULDA_Fk20_Dual_chain_gun_Continuous is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_Fk20_Double_Salve_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_Fk20_Double_Salve_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_Fk20_Double_Salve_3.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_Fk20_Double_Salve_4.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_Fk20_Dual_chain_gun_Continuous is TSoundHappening
(
    SoundDescList = Sound_FULDA_Fk20_Dual_chain_gun_Continuous
    Volume = 2
)


export Sound_FULDA_Fk20_chain_gun_Continuous is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_Fk20_Salve_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_Fk20_Salve_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_Fk20_Salve_3.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_Fk20_Salve_4.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_Fk20_chain_gun_Continuous is TSoundHappening
(
    SoundDescList = Sound_FULDA_Fk20_chain_gun_Continuous
    Volume = 2
)


export Sound_FULDA_Flares_Launch is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Critiques/Flares.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Flares_Launch is TSoundHappening
(
    SoundDescList = Sound_FULDA_Flares_Launch
    Volume = 1
)


export Sound_FULDA_G3KA4_Burst_3_Rounds is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/G3KA4_firing_3_rounds_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/G3KA4_firing_3_rounds_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/G3KA4_firing_3_rounds_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/G3KA4_firing_3_rounds_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/G3KA4_firing_3_rounds_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/G3KA4_firing_3_rounds_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/G3KA4_firing_3_rounds_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/G3KA4_firing_3_rounds_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/G3KA4_firing_3_rounds_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/G3KA4_firing_3_rounds_10.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_G3KA4_Burst_3_Rounds is TSoundHappening
(
    SoundDescList = Sound_FULDA_G3KA4_Burst_3_Rounds
    Volume = 1
)


export Sound_FULDA_GUV is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/GUV_Gatling_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/GUV_Gatling_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/GUV_Gatling_3.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/GUV_Gatling_4.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_GUV is TSoundHappening
(
    SoundDescList = Sound_FULDA_GUV
    Volume = 2
)


export Sound_FULDA_Gsh_30_6 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/30mm_gatling_tir_Avion_Gsh30_6.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_Gsh_30_6 is TSoundHappening
(
    SoundDescList = Sound_FULDA_Gsh_30_6
    Volume = 2
)


export Sound_FULDA_HAWK_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_HAWK_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_HAWK_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_HAWK_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_HAWK_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_HAWK_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_HAWK_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_HAWK_Firing
    Volume = 2
)


export Sound_FULDA_HK21_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/HK21_mitrailleuse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/HK21_mitrailleuse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/HK21_mitrailleuse_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/HK21_mitrailleuse_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_HK21_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_HK21_Firing
    Volume = 1
)


export Sound_FULDA_HOT_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_HOT_1.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_HOT_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_HOT_Firing
    Volume = 2
)


export Sound_FULDA_HYDRA70_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Rocket_Hydra_70_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Rocket_Hydra_70_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Rocket_Hydra_70_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Rocket_Hydra_70_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_HYDRA70_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_HYDRA70_Firing
    Volume = 2
)


export Sound_FULDA_Impacts_DCA is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_DCA_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_DCA_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_DCA_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_DCA_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Impacts_DCA is TSoundHappening
(
    SoundDescList = Sound_FULDA_Impacts_DCA
    Volume = 1
)


export Sound_FULDA_KONKURS_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Konkurs_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Konkurs_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Konkurs_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_KONKURS_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_KONKURS_Firing
    Volume = 2
)


export Sound_FULDA_KPVT is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/KPVT_Firing_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/KPVT_Firing_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/KPVT_Firing_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/KPVT_Firing_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/KPVT_Firing_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/KPVT_Firing_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/KPVT_Firing_7.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/KPVT_Firing_8.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/KPVT_Firing_9.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_KPVT is TSoundHappening
(
    SoundDescList = Sound_FULDA_KPVT
    Volume = 1
)


export Sound_FULDA_KS19_100mm is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_KS19_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/100mm_canon_tir_KS19_2.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_KS19_100mm is TSoundHappening
(
    SoundDescList = Sound_FULDA_KS19_100mm
    Volume = 2
)


export Sound_FULDA_L37A2_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L37A2_mitrailleuse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L37A2_mitrailleuse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L37A2_mitrailleuse_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L37A2_mitrailleuse_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L37A2_mitrailleuse_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L37A2_mitrailleuse_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_L37A2_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_L37A2_Firing
    Volume = 1
)


export Sound_FULDA_L85A1_Burst_3_Rounds is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/L85A1_firing_3_rounds_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/L85A1_firing_3_rounds_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/L85A1_firing_3_rounds_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/L85A1_firing_3_rounds_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/L85A1_firing_3_rounds_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/L85A1_firing_3_rounds_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/L85A1_firing_3_rounds_07.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_L85A1_Burst_3_Rounds is TSoundHappening
(
    SoundDescList = Sound_FULDA_L85A1_Burst_3_Rounds
    Volume = 1
)


export Sound_FULDA_L86A1_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L86A1_mitrailleuse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L86A1_mitrailleuse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L86A1_mitrailleuse_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L86A1_mitrailleuse_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L86A1_mitrailleuse_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_L86A1_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_L86A1_Firing
    Volume = 1
)


export Sound_FULDA_L8A2_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L8A2_mitrailleuse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L8A2_mitrailleuse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L8A2_mitrailleuse_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_L8A2_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_L8A2_Firing
    Volume = 1
)


export Sound_FULDA_L94A1_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L94A1_mitrailleuse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L94A1_mitrailleuse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L94A1_mitrailleuse_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L94A1_mitrailleuse_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L94A1_mitrailleuse_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L94A1_mitrailleuse_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/L94A1_mitrailleuse_7.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_L94A1_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_L94A1_Firing
    Volume = 1
)


export Sound_FULDA_LAW_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_LAW_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_LAW_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_LAW_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_LAW_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_LAW_Firing
    Volume = 2
)


export Sound_FULDA_LML_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_LML_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_LML_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_LML_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_LML_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_LML_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_LML_Firing
    Volume = 2
)


export Sound_FULDA_M134_Gatling is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/762_Gatling_M134_tir_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/762_Gatling_M134_tir_2.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_M134_Gatling is TSoundHappening
(
    SoundDescList = Sound_FULDA_M134_Gatling
    Volume = 2
)


export Sound_FULDA_M16_Burst_3_Rounds is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_11.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_12.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_13.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_14.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_15.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_16.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_17.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_18.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_19.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M16_firing_3_rounds_20.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_M16_Burst_3_Rounds is TSoundHappening
(
    SoundDescList = Sound_FULDA_M16_Burst_3_Rounds
    Volume = 1
)


export Sound_FULDA_M197_Gatling is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/M197_Gatling.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_M197_Gatling is TSoundHappening
(
    SoundDescList = Sound_FULDA_M197_Gatling
    Volume = 2
)


export Sound_FULDA_M230_chain_gun is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_Apache_M230_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_Apache_M230_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_Apache_M230_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_Apache_M230_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_Apache_M230_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_Apache_M230_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_Apache_M230_7.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_Apache_M230_8.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_Apache_M230_9.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_M230_chain_gun is TSoundHappening
(
    SoundDescList = Sound_FULDA_M230_chain_gun
    Volume = 2
)


export Sound_FULDA_M242_chain_gun is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/25mm_autocanon_tir_Bushmaster_M242_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/25mm_autocanon_tir_Bushmaster_M242_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/25mm_autocanon_tir_Bushmaster_M242_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/25mm_autocanon_tir_Bushmaster_M242_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/25mm_autocanon_tir_Bushmaster_M242_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/25mm_autocanon_tir_Bushmaster_M242_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/25mm_autocanon_tir_Bushmaster_M242_7.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_M242_chain_gun is TSoundHappening
(
    SoundDescList = Sound_FULDA_M242_chain_gun
    Volume = 2
)


export Sound_FULDA_M249_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/M249_mitrailleuse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/M249_mitrailleuse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/M249_mitrailleuse_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/M249_mitrailleuse_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_M249_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_M249_Firing
    Volume = 1
)


export Sound_FULDA_M24_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/M240_mitrailleuse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/M240_mitrailleuse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/M240_mitrailleuse_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/M240_mitrailleuse_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/M240_mitrailleuse_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_M24_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_M24_Firing
    Volume = 1
)


export Sound_FULDA_M46_130mm is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/130mm_canon_tir_M46_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/130mm_canon_tir_M46_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/130mm_canon_tir_M46_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/130mm_canon_tir_M46_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/130mm_canon_tir_M46_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/130mm_canon_tir_M46_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/130mm_canon_tir_M46_7.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_M46_130mm is TSoundHappening
(
    SoundDescList = Sound_FULDA_M46_130mm
    Volume = 2
)


export Sound_FULDA_M60 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M60_Firing_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M60_Firing_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M60_Firing_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M60_Firing_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M60_Firing_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M60_Firing_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_M60 is TSoundHappening
(
    SoundDescList = Sound_FULDA_M60
    Volume = 1
)


export Sound_FULDA_M61 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/M61_Gatling_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/M61_Gatling_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/M61_Gatling_3.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/M61_Gatling_4.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_M61 is TSoundHappening
(
    SoundDescList = Sound_FULDA_M61
    Volume = 2
)


export Sound_FULDA_M61_VAD is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/M61_Gatling_VAD_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/M61_Gatling_VAD_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/M61_Gatling_VAD_3.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/M61_Gatling_VAD_4.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_M61_VAD is TSoundHappening
(
    SoundDescList = Sound_FULDA_M61_VAD
    Volume = 2
)


export Sound_FULDA_M621_GIAT is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/20mm_autocanon_tir_GIAT_M621_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/20mm_autocanon_tir_GIAT_M621_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/20mm_autocanon_tir_GIAT_M621_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/20mm_autocanon_tir_GIAT_M621_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/20mm_autocanon_tir_GIAT_M621_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/20mm_autocanon_tir_GIAT_M621_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/20mm_autocanon_tir_GIAT_M621_7.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_M621_GIAT is TSoundHappening
(
    SoundDescList = Sound_FULDA_M621_GIAT
    Volume = 2
)


export Sound_FULDA_M693_chain_gun is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_M693_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_M693_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_M693_3.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_M693_chain_gun is TSoundHappening
(
    SoundDescList = Sound_FULDA_M693_chain_gun
    Volume = 2
)


export Sound_FULDA_MAS_56_1_Round is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat56_firing_1_round_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat56_firing_1_round_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat56_firing_1_round_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat56_firing_1_round_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat56_firing_1_round_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat56_firing_1_round_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat56_firing_1_round_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat56_firing_1_round_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat56_firing_1_round_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat56_firing_1_round_10.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_MAS_56_1_Round is TSoundHappening
(
    SoundDescList = Sound_FULDA_MAS_56_1_Round
    Volume = 1
)


export Sound_FULDA_MAT49_Burst is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat49_firing_burst_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat49_firing_burst_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat49_firing_burst_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat49_firing_burst_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat49_firing_burst_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mat49_firing_burst_06.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_MAT49_Burst is TSoundHappening
(
    SoundDescList = Sound_FULDA_MAT49_Burst
    Volume = 1
)


export Sound_FULDA_MG3_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/MG3_mitrailleuse_rafale_longue_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/MG3_mitrailleuse_rafale_longue_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/MG3_mitrailleuse_rafale_longue_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/MG3_mitrailleuse_rafale_longue_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/MG3_mitrailleuse_rafale_longue_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/MG3_mitrailleuse_rafale_longue_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/MG3_mitrailleuse_rafale_longue_7.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/MG3_mitrailleuse_rafale_longue_8.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/MG3_mitrailleuse_rafale_longue_9.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_MG3_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_MG3_Firing
    Volume = 1
)


export Sound_FULDA_MILAN_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_MILAN_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_MILAN_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_MILAN_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_MILAN_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_MILAN_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_MILAN_Firing
    Volume = 2
)


export Sound_FULDA_MK_20_Rh_202_chain_gun is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/20mm_autocanon_tir_Rh_202_chain_gun_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/20mm_autocanon_tir_Rh_202_chain_gun_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/20mm_autocanon_tir_Rh_202_chain_gun_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/20mm_autocanon_tir_Rh_202_chain_gun_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/20mm_autocanon_tir_Rh_202_chain_gun_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_MK_20_Rh_202_chain_gun is TSoundHappening
(
    SoundDescList = Sound_FULDA_MK_20_Rh_202_chain_gun
    Volume = 2
)


export Sound_FULDA_MP5_Burst is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/MP5_firing_burst_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/MP5_firing_burst_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/MP5_firing_burst_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/MP5_firing_burst_04.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_MP5_Burst is TSoundHappening
(
    SoundDescList = Sound_FULDA_MP5_Burst
    Volume = 1
)


export Sound_FULDA_Malyutka_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Malyutka_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Malyutka_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Malyutka_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Malyutka_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Malyutka_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Malyutka_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_Malyutka_Firing
    Volume = 2
)


export Sound_FULDA_Mauser_BK27 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/27mm_autocanon_tir_Avion_MauserBK27_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/27mm_autocanon_tir_Avion_MauserBK27_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/27mm_autocanon_tir_Avion_MauserBK27_3.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_Mauser_BK27 is TSoundHappening
(
    SoundDescList = Sound_FULDA_Mauser_BK27
    Volume = 2
)


export Sound_FULDA_Maxim is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Maxim_Firing_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Maxim_Firing_2.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Maxim is TSoundHappening
(
    SoundDescList = Sound_FULDA_Maxim
    Volume = 1
)


export Sound_FULDA_Maxton_quad_gun is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_12_7mm_quadcanon_Maxton_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_12_7mm_quadcanon_Maxton_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_12_7mm_quadcanon_Maxton_3.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_12_7mm_quadcanon_Maxton_4.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_12_7mm_quadcanon_Maxton_5.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_Maxton_quad_gun is TSoundHappening
(
    SoundDescList = Sound_FULDA_Maxton_quad_gun
    Volume = 2
)


export Sound_FULDA_Mistral_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_MISTRAL_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_MISTRAL_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_MISTRAL_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_MISTRAL_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Mistral_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_Mistral_Firing
    Volume = 2
)


export Sound_FULDA_NSV is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_NSVT_Firing_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_NSVT_Firing_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_lourde
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/HMG/Cal_NSVT_Firing_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_NSV is TSoundHappening
(
    SoundDescList = Sound_FULDA_NSV
    Volume = 1
)


export Sound_FULDA_Nona_120mm is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Nona_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Nona_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Nona_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Canon_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/120mm_canon_tir_Nona_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Nona_120mm is TSoundHappening
(
    SoundDescList = Sound_FULDA_Nona_120mm
    Volume = 2
)


export Sound_FULDA_Oerlikon_chain_gun is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_35mm_autocanon_Oerlikon_GPF_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_35mm_autocanon_Oerlikon_GPF_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_35mm_autocanon_Oerlikon_GPF_3.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_Oerlikon_chain_gun is TSoundHappening
(
    SoundDescList = Sound_FULDA_Oerlikon_chain_gun
    Volume = 2
)


export Sound_FULDA_PKM is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/RPK_Firing_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/RPK_Firing_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/RPK_Firing_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/RPK_Firing_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/RPK_Firing_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_PKM is TSoundHappening
(
    SoundDescList = Sound_FULDA_PKM
    Volume = 1
)


export Sound_FULDA_PKT_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/PKT_mitrailleuse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/PKT_mitrailleuse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/PKT_mitrailleuse_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/PKT_mitrailleuse_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_PKT_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_PKT_Firing
    Volume = 1
)


export Sound_FULDA_PPS_43_Burst is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PPS_firing_burst_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PPS_firing_burst_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PPS_firing_burst_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PPS_firing_burst_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PPS_firing_burst_05.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_PPS_43_Burst is TSoundHappening
(
    SoundDescList = Sound_FULDA_PPS_43_Burst
    Volume = 1
)


export Sound_FULDA_PzF_44 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_PZ44_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_PZ44_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_PZ44_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_PZ44_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_PzF_44 is TSoundHappening
(
    SoundDescList = Sound_FULDA_PzF_44
    Volume = 2
)


export Sound_FULDA_R27_Vympel_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_R27_Vympel.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_R27_Vympel_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_R27_Vympel_Firing
    Volume = 2
)


export Sound_FULDA_RAPIER_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_RAPIER_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_RAPIER_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_RAPIER_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_RAPIER_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_RAPIER_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_RAPIER_Firing
    Volume = 2
)


export Sound_FULDA_RARDEN_L21A1_chain_gun is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_RARDEN_L21A1_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_RARDEN_L21A1_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_RARDEN_L21A1_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_RARDEN_L21A1_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_RARDEN_L21A1_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_RARDEN_L21A1_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_RARDEN_L21A1_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_RARDEN_L21A1_08.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_RARDEN_L21A1_chain_gun is TSoundHappening
(
    SoundDescList = Sound_FULDA_RARDEN_L21A1_chain_gun
    Volume = 2
)


export Sound_FULDA_RL83_Blindicide_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_RL83_Blindicide_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_RL83_Blindicide_2.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_RL83_Blindicide_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_RL83_Blindicide_Firing
    Volume = 2
)


export Sound_FULDA_ROLAND_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_9M311_Tunguska.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_ROLAND_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_ROLAND_Firing
    Volume = 2
)


export Sound_FULDA_RPG18_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_RPG18_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_RPG18_2.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_RPG18_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_RPG18_Firing
    Volume = 2
)


export Sound_FULDA_RPG7_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_RPG7_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_RPG7_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_RPG7_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_RPG7_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_RPG7_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_RPG7_Firing
    Volume = 2
)


export Sound_FULDA_RPK is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PKM_Firing_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PKM_Firing_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PKM_Firing_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PKM_Firing_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_RPK is TSoundHappening
(
    SoundDescList = Sound_FULDA_RPK
    Volume = 1
)


export Sound_FULDA_RPK_74 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/RPK_74_mitrailleuse_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/RPK_74_mitrailleuse_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/RPK_74_mitrailleuse_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Mitrailleuse_legere
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/MMG/RPK_74_mitrailleuse_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_RPK_74 is TSoundHappening
(
    SoundDescList = Sound_FULDA_RPK_74
    Volume = 1
)


export Sound_FULDA_Recoiless_Gun_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/106mm_canon_sans_recul_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/106mm_canon_sans_recul_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/106mm_canon_sans_recul_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Recoiless_Gun_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_Recoiless_Gun_Firing
    Volume = 2
)


export Sound_FULDA_Recoiless_LRAC_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/106mm_canon_sans_recul_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/106mm_canon_sans_recul_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Canon/106mm_canon_sans_recul_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Recoiless_LRAC_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_Recoiless_LRAC_Firing
    Volume = 2
)


export Sound_FULDA_S60_autocanon is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_57mm_autocanon_S60_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_57mm_autocanon_S60_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_57mm_autocanon_S60_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_57mm_autocanon_S60_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_57mm_autocanon_S60_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_57mm_autocanon_S60_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_57mm_autocanon_S60_7.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_S60_autocanon is TSoundHappening
(
    SoundDescList = Sound_FULDA_S60_autocanon
    Volume = 2
)


export Sound_FULDA_SHILLELAGH_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Shillelagh_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Shillelagh_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Shillelagh_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_SHILLELAGH_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_SHILLELAGH_Firing
    Volume = 2
)


export Sound_FULDA_SHTURM_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Shturm_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Shturm_2.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_SHTURM_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_SHTURM_Firing
    Volume = 2
)


export Sound_FULDA_SIDEWINDER_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Sidewinder_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Sidewinder_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Sidewinder_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_SIDEWINDER_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_SIDEWINDER_Firing
    Volume = 2
)


export Sound_FULDA_SIG_Burst_3_Rounds is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/SIG_firing_3_rounds_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/SIG_firing_3_rounds_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/SIG_firing_3_rounds_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/SIG_firing_3_rounds_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/SIG_firing_3_rounds_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/SIG_firing_3_rounds_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/SIG_firing_3_rounds_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/SIG_firing_3_rounds_08.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_SIG_Burst_3_Rounds is TSoundHappening
(
    SoundDescList = Sound_FULDA_SIG_Burst_3_Rounds
    Volume = 1
)


export Sound_FULDA_SMOKE_Discharge_Vehicle is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Smoke/Tank_Smoke_Grenade_Launcher.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_SMOKE_Discharge_Vehicle is TSoundHappening
(
    SoundDescList = Sound_FULDA_SMOKE_Discharge_Vehicle
    Volume = 2
)


export Sound_FULDA_SPARROW_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Sparrow_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Sparrow_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Sparrow_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Sparrow_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Sparrow_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_SPARROW_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_SPARROW_Firing
    Volume = 2
)


export Sound_FULDA_SWINGFIRE_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_SWINGFIRE_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_SWINGFIRE_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_SWINGFIRE_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_SWINGFIRE_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_SWINGFIRE_Firing
    Volume = 2
)


export Sound_FULDA_S_Rockets_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Rocket_S_Serie_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Rocket_S_Serie_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Rocket_S_Serie_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Rocket_S_Serie_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_S_Rockets_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_S_Rockets_Firing
    Volume = 2
)


export Sound_FULDA_Silenced_Burst is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Silenced_firing_burst_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Silenced_firing_burst_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Silenced_firing_burst_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Silenced_Burst is TSoundHappening
(
    SoundDescList = Sound_FULDA_Silenced_Burst
    Volume = 1
)


export Sound_FULDA_Sniper_Barret_M82 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Barret_M82_firing_1_round_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Barret_M82_firing_1_round_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Barret_M82_firing_1_round_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Barret_M82_firing_1_round_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Barret_M82_firing_1_round_05.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Sniper_Barret_M82 is TSoundHappening
(
    SoundDescList = Sound_FULDA_Sniper_Barret_M82
    Volume = 1
)


export Sound_FULDA_Sniper_Dragunov_1_Round is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Dragunov_firing_1_round_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Dragunov_firing_1_round_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Dragunov_firing_1_round_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Dragunov_firing_1_round_04.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Sniper_Dragunov_1_Round is TSoundHappening
(
    SoundDescList = Sound_FULDA_Sniper_Dragunov_1_Round
    Volume = 1
)


export Sound_FULDA_Sniper_FRF_1_Round is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FRF1_2_firing_1_round_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FRF1_2_firing_1_round_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/FRF1_2_firing_1_round_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Sniper_FRF_1_Round is TSoundHappening
(
    SoundDescList = Sound_FULDA_Sniper_FRF_1_Round
    Volume = 1
)


export Sound_FULDA_Sniper_HKG3_1_Round is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/HKG3_firing_1_round_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/HKG3_firing_1_round_02.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Sniper_HKG3_1_Round is TSoundHappening
(
    SoundDescList = Sound_FULDA_Sniper_HKG3_1_Round
    Volume = 1
)


export Sound_FULDA_Sniper_L1A1_FAL_1_Round is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/L1A1_FAL_firing_1_round_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/L1A1_FAL_firing_1_round_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/L1A1_FAL_firing_1_round_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Sniper_L1A1_FAL_1_Round is TSoundHappening
(
    SoundDescList = Sound_FULDA_Sniper_L1A1_FAL_1_Round
    Volume = 1
)


export Sound_FULDA_Sniper_L96A1_1_Round is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/L96_firing_1_round_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/L96_firing_1_round_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/L96_firing_1_round_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Sniper_L96A1_1_Round is TSoundHappening
(
    SoundDescList = Sound_FULDA_Sniper_L96A1_1_Round
    Volume = 1
)


export Sound_FULDA_Sniper_M14_1_Round is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M14_firing_1_round_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M14_firing_1_round_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M14_firing_1_round_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M14_firing_1_round_04.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Sniper_M14_1_Round is TSoundHappening
(
    SoundDescList = Sound_FULDA_Sniper_M14_1_Round
    Volume = 1
)


export Sound_FULDA_Sniper_M24_1_Round is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M24_firing_1_round_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M24_firing_1_round_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M24_firing_1_round_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/M24_firing_1_round_04.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Sniper_M24_1_Round is TSoundHappening
(
    SoundDescList = Sound_FULDA_Sniper_M24_1_Round
    Volume = 1
)


export Sound_FULDA_Sniper_Mosin_Nagant_1_Round is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mosin_Nagant_firing_1_round_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mosin_Nagant_firing_1_round_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mosin_Nagant_firing_1_round_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Mosin_Nagant_firing_1_round_04.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Sniper_Mosin_Nagant_1_Round is TSoundHappening
(
    SoundDescList = Sound_FULDA_Sniper_Mosin_Nagant_1_Round
    Volume = 1
)


export Sound_FULDA_Sniper_PSG1_1_Round is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PSG1_firing_1_round_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PSG1_firing_1_round_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PSG1_firing_1_round_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PSG1_firing_1_round_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/PSG1_firing_1_round_05.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Sniper_PSG1_1_Round is TSoundHappening
(
    SoundDescList = Sound_FULDA_Sniper_PSG1_1_Round
    Volume = 1
)


export Sound_FULDA_Sniper_VSS_Vintorez_1_Round is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Silenced_firing_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Silenced_firing_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Silenced_firing_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Silenced_firing_04.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Sniper_VSS_Vintorez_1_Round is TSoundHappening
(
    SoundDescList = Sound_FULDA_Sniper_VSS_Vintorez_1_Round
    Volume = 1
)


export Sound_FULDA_Sterling_Burst is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Sterling_firing_burst_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Sterling_firing_burst_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Sterling_firing_burst_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Sterling_firing_burst_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/Sterling_firing_burst_05.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Sterling_Burst is TSoundHappening
(
    SoundDescList = Sound_FULDA_Sterling_Burst
    Volume = 1
)


export Sound_FULDA_Stinger_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Stinger_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Stinger_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Stinger_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Stinger_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Stinger_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Stinger_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_Stinger_Firing
    Volume = 2
)


export Sound_FULDA_Strela_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Strela_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Strela_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Strela_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Strela_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_Strela_Firing
    Volume = 2
)


export Sound_FULDA_Strela_Manpad_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Strela_Manpad_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Strela_Manpad_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Strela_Manpad_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Strela_Manpad_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_Strela_Manpad_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Strela_Manpad_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_Strela_Manpad_Firing
    Volume = 1
)


export Sound_FULDA_TOR_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_TOR_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Gros
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_TOR_2.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_TOR_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_TOR_Firing
    Volume = 2
)


export Sound_FULDA_TOW_Firing is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_TOW_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_TOW_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_TOW_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_TOW_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_TOW_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_TOW_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_TOW_7.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_TOW_8.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Missile_Moyen
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Tir_TOW_9.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_TOW_Firing is TSoundHappening
(
    SoundDescList = Sound_FULDA_TOW_Firing
    Volume = 2
)


export Sound_FULDA_Tir_roquette_220_Buratino is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_220_TOS1_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_220_TOS1_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_220_TOS1_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_220_TOS1_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Tir_roquette_220_Buratino is TSoundHappening
(
    SoundDescList = Sound_FULDA_Tir_roquette_220_Buratino
    Volume = 2
)


export Sound_FULDA_Tir_roquette_BM21_122mm_MLRS is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_122_BM21_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_122_BM21_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_122_BM21_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Tir_roquette_BM21_122mm_MLRS is TSoundHappening
(
    SoundDescList = Sound_FULDA_Tir_roquette_BM21_122mm_MLRS
    Volume = 2
)


export Sound_FULDA_Tir_roquette_BM30 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_BM30_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_BM30_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_BM30_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Tir_roquette_BM30 is TSoundHappening
(
    SoundDescList = Sound_FULDA_Tir_roquette_BM30
    Volume = 2
)


export Sound_FULDA_Tir_roquette_M110_MLRS is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_110_LARS_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_110_LARS_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_110_LARS_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_110_LARS_4.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Tir_roquette_M110_MLRS is TSoundHappening
(
    SoundDescList = Sound_FULDA_Tir_roquette_M110_MLRS
    Volume = 2
)


export Sound_FULDA_Tir_roquette_M270_MLRS is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_M270_MLRS_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_M270_MLRS_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_M270_MLRS_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_M270_MLRS_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_M270_MLRS_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_M270_MLRS_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Tir_roquette_M270_MLRS is TSoundHappening
(
    SoundDescList = Sound_FULDA_Tir_roquette_M270_MLRS
    Volume = 2
)


export Sound_FULDA_Tir_roquette_MRF_Minenraumfahrzeug is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_MLRS
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Roquettes/Roquette_MRF_Minenraumfahrzeug_1.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_Tir_roquette_MRF_Minenraumfahrzeug is TSoundHappening
(
    SoundDescList = Sound_FULDA_Tir_roquette_MRF_Minenraumfahrzeug
    Volume = 2
)


export Sound_FULDA_UZI_Burst is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/UZI_firing_burst_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/UZI_firing_burst_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/UZI_firing_burst_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/UZI_firing_burst_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/UZI_firing_burst_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Fusil_Mitrailleur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Fusils/UZI_firing_burst_06.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FULDA_UZI_Burst is TSoundHappening
(
    SoundDescList = Sound_FULDA_UZI_Burst
    Volume = 1
)


export Sound_FULDA_VZ53_30mm_chain_gun is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_30mm_autocanon_VZ53_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_30mm_autocanon_VZ53_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_30mm_autocanon_VZ53_3.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_VZ53_30mm_chain_gun is TSoundHappening
(
    SoundDescList = Sound_FULDA_VZ53_30mm_chain_gun
    Volume = 2
)


export Sound_FULDA_YaKB is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/YakB_Gatling_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/YakB_Gatling_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/YakB_Gatling_3.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_YaKB is TSoundHappening
(
    SoundDescList = Sound_FULDA_YaKB
    Volume = 2
)


export Sound_FULDA_ZU23_Dual_chain_gun_Continuous is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_Zu23_Double_Salve_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_Zu23_Double_Salve_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_Zu23_Double_Salve_3.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tir_Autocanon
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/Tir_20mm_autocanon_Zu23_Double_Salve_4.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_FULDA_ZU23_Dual_chain_gun_Continuous is TSoundHappening
(
    SoundDescList = Sound_FULDA_ZU23_Dual_chain_gun_Continuous
    Volume = 2
)


export Sound_FermeAmbiance is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Ferme
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Ferme.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FermeAmbiance is TSoundHappening
(
    SoundDescList = Sound_FermeAmbiance
    Volume = 1
)


export Sound_FermeAmbiance_Evenements is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Ferme_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Ferme_Evenement_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Ferme_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Ferme_Evenement_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Ferme_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Ferme_Evenement_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Ferme_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Ferme_Evenement_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Ferme_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Ferme_Evenement_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Ferme_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Ferme_Evenement_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Ferme_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Ferme_Evenement_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Ferme_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Ferme_Evenement_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Ferme_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Ferme_Evenement_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Ferme_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Ferme_Evenement_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Ferme_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Ferme_Evenement_11.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_FermeAmbiance_Evenements is TSoundHappening
(
    SoundDescList = Sound_FermeAmbiance_Evenements
    Volume = 1
)


export Sound_Fulda_GAU8 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/GAU8_01.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/GAU8_02.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/GAU8_03.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/GAU8_04.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Gatling/GAU8_05.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_Fulda_GAU8 is TSoundHappening
(
    SoundDescList = Sound_Fulda_GAU8
    Volume = 2
)


export Sound_ImpactArtillerie_NukelSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Nuke
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Nuke/Tactical_Nuke_Impact.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactArtillerie_NukelSol is TSoundHappening
(
    SoundDescList = Sound_ImpactArtillerie_NukelSol
    Volume = 1
)


export Sound_ImpactArtillerie_lourdeSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Artillerie_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Artillerie_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Artillerie_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Artillerie_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Artillerie_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Artillerie_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactArtillerie_lourdeSol is TSoundHappening
(
    SoundDescList = Sound_ImpactArtillerie_lourdeSol
    Volume = 1
)


export Sound_ImpactAutocanon_20mmBatiment is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatPetitObus_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatPetitObus_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatPetitObus_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactAutocanon_20mmBatiment is TSoundHappening
(
    SoundDescList = Sound_ImpactAutocanon_20mmBatiment
    Volume = 1
)


export Sound_ImpactAutocanon_20mmSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactPetitObus_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactPetitObus_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactPetitObus_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactPetitObus_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactPetitObus_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactPetitObus_06.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactAutocanon_20mmSol is TSoundHappening
(
    SoundDescList = Sound_ImpactAutocanon_20mmSol
    Volume = 1
)


export Sound_ImpactAutocanon_20mmVehicule is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanon/Impact_Autocanon_Vehicule_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanon/Impact_Autocanon_Vehicule_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanon/Impact_Autocanon_Vehicule_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactAutocanon_20mmVehicule is TSoundHappening
(
    SoundDescList = Sound_ImpactAutocanon_20mmVehicule
    Volume = 1
)


export Sound_ImpactBallesBatiment is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_11.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_12.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_13.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_14.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_15.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBalle_16.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactBallesBatiment is TSoundHappening
(
    SoundDescList = Sound_ImpactBallesBatiment
    Volume = 1
)


export Sound_ImpactBallesSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_11.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_12.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_13.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactBallesSol is TSoundHappening
(
    SoundDescList = Sound_ImpactBallesSol
    Volume = 1
)


export Sound_ImpactBallesVehicule is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_Vehicule_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_Vehicule_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_Vehicule_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_Vehicule_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_Vehicule_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_Vehicule_06.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactBallesVehicule is TSoundHappening
(
    SoundDescList = Sound_ImpactBallesVehicule
    Volume = 1
)


export Sound_ImpactBalles_BalleSol_Sniper is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_11.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_12.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_13.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_14.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_15.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_16.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_17.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_18.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_19.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_20.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactBalles_BalleSol_Sniper is TSoundHappening
(
    SoundDescList = Sound_ImpactBalles_BalleSol_Sniper
    Volume = 1
)


export Sound_ImpactBalles_burstBatiment is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_11.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_12.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_13.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_14.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_15.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_16.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_17.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_18.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_19.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_20.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_21.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBatBallesBurst_22.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactBalles_burstBatiment is TSoundHappening
(
    SoundDescList = Sound_ImpactBalles_burstBatiment
    Volume = 1
)


export Sound_ImpactBalles_burstSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_11.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_12.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_13.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_14.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_15.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_16.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_17.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_18.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_19.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBalles_20.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactBalles_burstSol is TSoundHappening
(
    SoundDescList = Sound_ImpactBalles_burstSol
    Volume = 1
)


export Sound_ImpactBalles_burstSol_MG_3 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_MG3_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_MG3_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_MG3_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_MG3_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_MG3_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_MG3_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_MG3_07.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactBalles_burstSol_MG_3 is TSoundHappening
(
    SoundDescList = Sound_ImpactBalles_burstSol_MG_3
    Volume = 1
)


export Sound_ImpactBalles_burstSol_SAW is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_11.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_12.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_13.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactBalles_burstSol_SAW is TSoundHappening
(
    SoundDescList = Sound_ImpactBalles_burstSol_SAW
    Volume = 1
)


export Sound_ImpactBalles_burstSol_Short_MG_3 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_Short_MG3_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_Short_MG3_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_Short_MG3_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_Short_MG3_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_Short_MG3_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_Short_MG3_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_Short_MG3_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles_legeres
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactBallesBurst_Short_MG3_08.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactBalles_burstSol_Short_MG_3 is TSoundHappening
(
    SoundDescList = Sound_ImpactBalles_burstSol_Short_MG_3
    Volume = 1
)


export Sound_ImpactBalles_burstVehicule is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_burst_Vehicule_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_burst_Vehicule_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_burst_Vehicule_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_burst_Vehicule_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_burst_Vehicule_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_burst_Vehicule_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_burst_Vehicule_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Balles/Impact_Balle_burst_Vehicule_08.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactBalles_burstVehicule is TSoundHappening
(
    SoundDescList = Sound_ImpactBalles_burstVehicule
    Volume = 1
)


export Sound_ImpactCanon_grosBatiment is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_04.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactCanon_grosBatiment is TSoundHappening
(
    SoundDescList = Sound_ImpactCanon_grosBatiment
    Volume = 1
)


export Sound_ImpactCanon_grosSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moyen_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moyen_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moyen_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moyen_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_04.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactCanon_grosSol is TSoundHappening
(
    SoundDescList = Sound_ImpactCanon_grosSol
    Volume = 1
)


export Sound_ImpactCanon_moyenBatiment is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_04.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactCanon_moyenBatiment is TSoundHappening
(
    SoundDescList = Sound_ImpactCanon_moyenBatiment
    Volume = 1
)


export Sound_ImpactCanon_moyenSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Petits_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Petits_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Petits_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Petits_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_04.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactCanon_moyenSol is TSoundHappening
(
    SoundDescList = Sound_ImpactCanon_moyenSol
    Volume = 1
)


export Sound_ImpactCanon_petitBatiment is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impactGrosObus_04.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactCanon_petitBatiment is TSoundHappening
(
    SoundDescList = Sound_ImpactCanon_petitBatiment
    Volume = 1
)


export Sound_ImpactCanon_petitSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Petits_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Sol_Petit_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Petits_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Sol_Petit_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Tres_Petits_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Sol_Petit_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactCanon_petitSol is TSoundHappening
(
    SoundDescList = Sound_ImpactCanon_petitSol
    Volume = 1
)


export Sound_ImpactCanon_petitSol_GAU is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Sol_Petit_GAU_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Sol_Petit_GAU_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Sol_Petit_GAU_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactCanon_petitSol_GAU is TSoundHappening
(
    SoundDescList = Sound_ImpactCanon_petitSol_GAU
    Volume = 1
)


export Sound_ImpactGrenadeMainSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Sol_Moyen_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Sol_Moyen_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Sol_Moyen_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactGrenadeMainSol is TSoundHappening
(
    SoundDescList = Sound_ImpactGrenadeMainSol
    Volume = 1
)


export Sound_ImpactMissileAT_grosSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Sol_Gros_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Sol_Gros_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Sol_Gros_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactMissileAT_grosSol is TSoundHappening
(
    SoundDescList = Sound_ImpactMissileAT_grosSol
    Volume = 1
)


export Sound_ImpactMissileAT_grosVehicule is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Vehicule_Gros_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Vehicule_Gros_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Vehicule_Gros_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactMissileAT_grosVehicule is TSoundHappening
(
    SoundDescList = Sound_ImpactMissileAT_grosVehicule
    Volume = 1
)


export Sound_ImpactMissileAT_moyenVehicule is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Vehicule_Moyen_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Vehicule_Moyen_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Vehicule_Moyen_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactMissileAT_moyenVehicule is TSoundHappening
(
    SoundDescList = Sound_ImpactMissileAT_moyenVehicule
    Volume = 1
)


export Sound_ImpactMissileAT_petitSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Sol_Moyen_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Sol_Moyen_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Sol_Moyen_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactMissileAT_petitSol is TSoundHappening
(
    SoundDescList = Sound_ImpactMissileAT_petitSol
    Volume = 1
)


export Sound_ImpactMortier_moyenSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Mortier_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Mortier_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Mortier_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Mortier_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Mortier_5.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactMortier_moyenSol is TSoundHappening
(
    SoundDescList = Sound_ImpactMortier_moyenSol
    Volume = 1
)


export Sound_ImpactObus_grosVehicule is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Vehicule_Gros_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Vehicule_Gros_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Vehicule_Gros_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactObus_grosVehicule is TSoundHappening
(
    SoundDescList = Sound_ImpactObus_grosVehicule
    Volume = 1
)


export Sound_ImpactObus_moyenVehicule is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Vehicule_Moyen_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Vehicule_Moyen_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Vehicule_Moyen_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactObus_moyenVehicule is TSoundHappening
(
    SoundDescList = Sound_ImpactObus_moyenVehicule
    Volume = 1
)


export Sound_ImpactObus_petitVehicule is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Vehicule_Petit_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Vehicule_Petit_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusCanon/Impact_Obus_Vehicule_Petit_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactObus_petitVehicule is TSoundHappening
(
    SoundDescList = Sound_ImpactObus_petitVehicule
    Volume = 1
)


export Sound_ImpactRoquette_petitBatiment is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusArtillerie/Impact_Artillerie_Batiment_Moyen_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusArtillerie/Impact_Artillerie_Batiment_Moyen_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/ObusArtillerie/Impact_Artillerie_Batiment_Moyen_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactRoquette_petitBatiment is TSoundHappening
(
    SoundDescList = Sound_ImpactRoquette_petitBatiment
    Volume = 1
)


export Sound_ImpactRoquette_petitSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Sol_Moyen_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Sol_Moyen_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Sol_Moyen_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactRoquette_petitSol is TSoundHappening
(
    SoundDescList = Sound_ImpactRoquette_petitSol
    Volume = 1
)


export Sound_ImpactRoquette_petitVehicule is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Vehicule_Moyen_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Vehicule_Moyen_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Missiles/Impact_Missile_Vehicule_Moyen_03.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactRoquette_petitVehicule is TSoundHappening
(
    SoundDescList = Sound_ImpactRoquette_petitVehicule
    Volume = 1
)


export Sound_ImpactRoquettes_Buratino_lourdeSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Roquette_Buratino_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Roquette_Buratino_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Roquette_Buratino_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Roquette_Buratino_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Roquette_Buratino_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Roquette_Buratino_6.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactRoquettes_Buratino_lourdeSol is TSoundHappening
(
    SoundDescList = Sound_ImpactRoquettes_Buratino_lourdeSol
    Volume = 1
)


export Sound_ImpactRoquettes_MRF_lourdeSol is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Roquette_MRF_Minenraumfahrzeug_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Roquette_MRF_Minenraumfahrzeug_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_Roquette_MRF_Minenraumfahrzeug_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactRoquettes_MRF_lourdeSol is TSoundHappening
(
    SoundDescList = Sound_ImpactRoquettes_MRF_lourdeSol
    Volume = 2
)


export Sound_ImpactSAM_gros is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_SAM
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_SAM_Gros_1.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactSAM_gros is TSoundHappening
(
    SoundDescList = Sound_ImpactSAM_gros
    Volume = 1
)


export Sound_ImpactSAM_moyen is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_SAM
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_SAM_Moyen_1.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactSAM_moyen is TSoundHappening
(
    SoundDescList = Sound_ImpactSAM_moyen
    Volume = 1
)


export Sound_ImpactSAM_tres_gros is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_SAM
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impacts_SAM_Tres_Gros_1.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_ImpactSAM_tres_gros is TSoundHappening
(
    SoundDescList = Sound_ImpactSAM_tres_gros
    Volume = 4
)


export Sound_Impact_HE_Gros_Eau is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/Impact_Water_3.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_Impact_HE_Gros_Eau is TSoundHappening
(
    SoundDescList = Sound_Impact_HE_Gros_Eau
    Volume = 1
)


export Sound_Impact_HE_Petit_Eau is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Impacts/impact_Water.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_Impact_HE_Petit_Eau is TSoundHappening
(
    SoundDescList = Sound_Impact_HE_Petit_Eau
    Volume = 1
)


export Sound_Incendie is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Impact_Balles
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/fire_loop.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_Incendie is TSoundHappening
(
    SoundDescList = Sound_Incendie
    Volume = 0.7
)


export Sound_IndustrieAmbiance is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Indus
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Indus.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_IndustrieAmbiance is TSoundHappening
(
    SoundDescList = Sound_IndustrieAmbiance
    Volume = 1
)


export Sound_IndustrieAmbiance_Evenements is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Indus_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Indus_Evenement_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Indus_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Indus_Evenement_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Indus_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Indus_Evenement_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Indus_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Indus_Evenement_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Indus_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Indus_Evenement_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Indus_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Indus_Evenement_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Indus_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Indus_Evenement_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Indus_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Indus_Evenement_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Indus_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Indus_Evenement_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Ambiance_Indus_Evenements
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Indus_Evenement_10.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_IndustrieAmbiance_Evenements is TSoundHappening
(
    SoundDescList = Sound_IndustrieAmbiance_Evenements
    Volume = 1
)


export Sound_Mort_infanterie is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/mort/Agonie_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/mort/Agonie_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/mort/Agonie_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/mort/Agonie_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/mort/Agonie_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/mort/Agonie_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/mort/Agonie_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/mort/Agonie_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/mort/Agonie_10.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_Mort_infanterie is TSoundHappening
(
    SoundDescList = Sound_Mort_infanterie
    Volume = 1
)


export Sound_RadioChatter is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_11.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_12.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_13.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_14.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_15.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_16.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_17.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_18.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_19.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_20.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_21.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_22.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_23.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_24.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_25.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_26.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_27.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_28.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_29.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_30.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_31.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_32.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_33.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_34.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_35.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_36.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_37.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_38.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_39.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_40.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_41.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_42.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_43.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_44.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_45.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_46.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_47.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_HQ_Voice_OneShot_48.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_RadioChatter is TSoundHappening
(
    SoundDescList = Sound_RadioChatter
    Volume = 1
)


export Sound_RadioChatterArmyGeneral is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_01.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_02.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_03.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_04.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_05.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_06.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_07.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_08.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_09.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_10.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_11.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_12.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_13.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_14.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_15.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_16.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_17.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/Ambiance_2D_SoundSettings
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Env/Amb_Army_General_Radio_Chatter_OneShot_18.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_RadioChatterArmyGeneral is TSoundHappening
(
    SoundDescList = Sound_RadioChatterArmyGeneral
    Volume = 1
)


export Sound_SD_GSh_231 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/23mm_autocanon_tir_Avion_Gsh23_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/23mm_autocanon_tir_Avion_Gsh23_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/23mm_autocanon_tir_Avion_Gsh23_3.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/23mm_autocanon_tir_Avion_Gsh23_4.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/23mm_autocanon_tir_Avion_Gsh23_5.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/23mm_autocanon_tir_Avion_Gsh23_6.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/23mm_autocanon_tir_Avion_Gsh23_7.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_SD_GSh_231 is TSoundHappening
(
    SoundDescList = Sound_SD_GSh_231
    Volume = 2
)


export Sound_SD_GSh_301 is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_Avion_Gsh30_1.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_Avion_Gsh30_2.wav"
            )
            UseSpecialLoop = True
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Gros_Impact_HE
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Autocanons/30mm_autocanon_tir_Avion_Gsh30_3.wav"
            )
            UseSpecialLoop = True
        ),
    ]
)

export SoundHappening_SD_GSh_301 is TSoundHappening
(
    SoundDescList = Sound_SD_GSh_301
    Volume = 2
)


export Sound_Steelman_NATO_Combat is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Steelman_Combat
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Ground_Combat_1.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Steelman_Combat
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Ground_Combat_2.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Steelman_Combat
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Ground_Combat_3.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Steelman_Combat
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Ground_Combat_4.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Steelman_Combat
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Ground_Combat_5.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Steelman_Combat
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Ground_Combat_6.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Steelman_Combat
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Ground_Combat_7.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Steelman_Combat
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Ground_Combat_8.wav"
            )
            UseSpecialLoop = False
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Steelman_Combat
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Ground_Combat_9.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_Steelman_NATO_Combat is TSoundHappening
(
    SoundDescList = Sound_Steelman_NATO_Combat
    Volume = 1
)


export Sound_TirLanceFlamme is TSoundSet
(
    Items = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Lance_flammes/Tir_lanceFlammes.wav"
            )
            UseSpecialLoop = False
        ),
    ]
)

export SoundHappening_TirLanceFlamme is TSoundHappening
(
    SoundDescList = Sound_TirLanceFlamme
    Volume = 1
)



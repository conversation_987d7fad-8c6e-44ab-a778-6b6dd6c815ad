// Ne pas éditer, ce fichier est généré par AirplanePawnUnitDescriptorFileWriter_Specific


export Descriptor_Unit_pion_BEL_10Wing_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{45f25399-dff1-49c8-b782-8a044a90f23a}
    ClassNameForDebug  = 'Pawn_pion_BEL_10Wing_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_10Wing_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='ZZWRLLSPKK'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_10Wing_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_11Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{c802a0f1-f537-438d-be87-5be0aad8f251}
    ClassNameForDebug  = 'Pawn_pion_BEL_11Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_BEL_11Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_BEL_11Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_11Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='OXFSVPGOSO'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_11Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_1Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{98d36aba-60af-4fbc-b67b-6c77f6efade5}
    ClassNameForDebug  = 'Pawn_pion_BEL_1Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_BEL_1Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_BEL_1Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_1Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='MQZRQMCFXG'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_1Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_1Wing_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{7631bf73-dc97-4b69-b0a5-beb807d97f07}
    ClassNameForDebug  = 'Pawn_pion_BEL_1Wing_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_1Wing_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='GMSVVBVNTM'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_1Wing_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_23Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{1685b715-f2dc-4fb4-95df-13e31da2622c}
    ClassNameForDebug  = 'Pawn_pion_BEL_23Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_BEL_23Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_BEL_23Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_23Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='DOVCLRRXYO'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_23Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_2Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{fea19e15-f712-4d45-8ff5-5c67cdd8eafd}
    ClassNameForDebug  = 'Pawn_pion_BEL_2Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_BEL_2Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_BEL_2Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_2Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='CWHWNKWQNU'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_2Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_2Wing_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{ac31630a-c071-4ad6-a81e-db67adef5be0}
    ClassNameForDebug  = 'Pawn_pion_BEL_2Wing_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_2Wing_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='XQZMZXMMFR'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_2Wing_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_31Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{fb0cb261-4e97-4013-abba-efb62916e1ce}
    ClassNameForDebug  = 'Pawn_pion_BEL_31Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_BEL_31Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_BEL_31Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_31Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='FXOTADQGPW'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_31Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_349Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{95a46a2c-6fbd-4db2-881d-cdd646444de2}
    ClassNameForDebug  = 'Pawn_pion_BEL_349Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_BEL_349Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_BEL_349Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_349Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='YBHFKUEGOU'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_349Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_350Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{8b830a97-d0b0-47e7-830e-20ae57c81190}
    ClassNameForDebug  = 'Pawn_pion_BEL_350Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_BEL_350Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_BEL_350Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_350Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='AWQBHAVGCB'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_350Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_3Wing_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{b800c263-c629-4a04-8dc6-b3d5dd63c452}
    ClassNameForDebug  = 'Pawn_pion_BEL_3Wing_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_3Wing_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='FOKRPICVFM'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_3Wing_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_42Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{80b77725-c8de-4d64-bdb3-18f49d3ae1b0}
    ClassNameForDebug  = 'Pawn_pion_BEL_42Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_BEL_42Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_BEL_42Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_42Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='YQIWZBIDYO'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_42Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_7Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{1d1d2b8c-e976-4eaf-a9c0-83b5612c5483}
    ClassNameForDebug  = 'Pawn_pion_BEL_7Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_BEL_7Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_BEL_7Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_7Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='JHZKYLVLOE'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_7Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_8Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{af3f875b-1aab-45d0-a143-350fe44344d2}
    ClassNameForDebug  = 'Pawn_pion_BEL_8Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_BEL_8Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_BEL_8Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_8Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='HRAWIJWYKV'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_8Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_9Wing_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{333bc94f-2ee1-4325-91ee-674fa41cc5d5}
    ClassNameForDebug  = 'Pawn_pion_BEL_9Wing_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_9Wing_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='AWZLLXFHCI'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_9Wing_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_BEL_BAF_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{17a40a05-e191-44a0-8488-9260d88cfafc}
    ClassNameForDebug  = 'Pawn_pion_BEL_BAF_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_BEL_BAF_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='OJKHAGCWRN'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_BEL_BAF_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_NL_Grp_Eindhoven is TEntityDescriptor
(
    DescriptorId       = GUID:{f8a6403d-eecc-418d-982f-b68d99c83dfb}
    ClassNameForDebug  = 'Pawn_pion_NL_Grp_Eindhoven'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'NL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_NL_Grp_Eindhoven
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_NL_Grp_Eindhoven"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_NL_Grp_Eindhoven'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='MRVIDGIZSB'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_NL_Grp_Eindhoven'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_NL_Grp_Leeuwarden is TEntityDescriptor
(
    DescriptorId       = GUID:{6b47d9b9-bbd3-451f-82dc-4f0d04b897aa}
    ClassNameForDebug  = 'Pawn_pion_NL_Grp_Leeuwarden'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'NL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_NL_Grp_Leeuwarden
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_NL_Grp_Leeuwarden"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_NL_Grp_Leeuwarden'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='VOANMLMCUU'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_NL_Grp_Leeuwarden'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_NL_Grp_Twenthe is TEntityDescriptor
(
    DescriptorId       = GUID:{1d4bdf16-d0ae-4317-a816-11d5129319c9}
    ClassNameForDebug  = 'Pawn_pion_NL_Grp_Twenthe'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'NL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_NL_Grp_Twenthe
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_NL_Grp_Twenthe"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_NL_Grp_Twenthe'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='XMYNREYEHZ'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_NL_Grp_Twenthe'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_NL_Grp_Volkel is TEntityDescriptor
(
    DescriptorId       = GUID:{7262981e-60a8-4572-bb4e-9cd516282590}
    ClassNameForDebug  = 'Pawn_pion_NL_Grp_Volkel'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'NL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_NL_Grp_Volkel
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_NL_Grp_Volkel"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_NL_Grp_Volkel'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='KTHUSVYMGO'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_NL_Grp_Volkel'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_NL_KLu_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{8aa86caf-1f0b-4ff2-8ab9-0cf9e1974f70}
    ClassNameForDebug  = 'Pawn_pion_NL_KLu_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'NL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_division",
                    "Texture_STRATEGIC_division",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_NL_KLu_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='QSHVHATIRZ'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_NL_KLu_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_POL_3DLMB_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{98e82d07-64a0-42d0-9425-c0146624a37e}
    ClassNameForDebug  = 'Pawn_pion_POL_3DLMB_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_division",
                    "Texture_STRATEGIC_division",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_POL_3DLMB_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='OBPBHVSCVY'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_POL_3DLMB_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_POL_PLMB40 is TEntityDescriptor
(
    DescriptorId       = GUID:{76198002-a57a-4a6d-b5ca-53b4140309b3}
    ClassNameForDebug  = 'Pawn_pion_POL_PLMB40'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'POL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_POL_PLMB40
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_POL_PLMB40"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_POL_PLMB40'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='EMNQTTTYTY'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_POL_PLMB40'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_POL_PLMB8 is TEntityDescriptor
(
    DescriptorId       = GUID:{dc1a26c3-fd23-4cf4-a44d-48c25919c4eb}
    ClassNameForDebug  = 'Pawn_pion_POL_PLMB8'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'POL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_POL_PLMB8
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_POL_PLMB8"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_POL_PLMB8'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='DSWOIZJJLV'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_POL_PLMB8'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RDA_1LVD_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{85a9fa2f-fd22-48f8-a39f-9d459df4f8d7}
    ClassNameForDebug  = 'Pawn_pion_RDA_1LVD_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'DDR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_division",
                    "Texture_STRATEGIC_division",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RDA_1LVD_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='VOABZRHHEC'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RDA_1LVD_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RDA_3LVD_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{aef48120-072a-4a93-99c1-c20b9059f989}
    ClassNameForDebug  = 'Pawn_pion_RDA_3LVD_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'DDR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_division",
                    "Texture_STRATEGIC_division",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RDA_1LVD_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='YIARUBTJRC'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RDA_3LVD_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RDA_JBG28 is TEntityDescriptor
(
    DescriptorId       = GUID:{17c391fc-a51b-454f-81f4-b673066ac4e1}
    ClassNameForDebug  = 'Pawn_pion_RDA_JBG28'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'DDR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RDA_JBG28
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RDA_JBG28"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RDA_JBG28'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='OEDXTFNTIW'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RDA_JBG28'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RDA_JBG37 is TEntityDescriptor
(
    DescriptorId       = GUID:{bdb4d63d-1960-4ed7-91a7-566452748a03}
    ClassNameForDebug  = 'Pawn_pion_RDA_JBG37'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'DDR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RDA_JBG37
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RDA_JBG37"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RDA_JBG37'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='SJLQUEIPXU'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RDA_JBG37'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RDA_JBG77 is TEntityDescriptor
(
    DescriptorId       = GUID:{3bfd4f32-9ae6-49e9-9a81-7a26a0360903}
    ClassNameForDebug  = 'Pawn_pion_RDA_JBG77'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'DDR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RDA_JBG77
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RDA_JBG77"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RDA_JBG77'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='HSQWKIHCWI'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RDA_JBG77'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RDA_JG1 is TEntityDescriptor
(
    DescriptorId       = GUID:{26ca5070-1eb6-44e5-8580-53506ba50706}
    ClassNameForDebug  = 'Pawn_pion_RDA_JG1'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'DDR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RDA_JG1
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RDA_JG1"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RDA_JG1'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='UJSYLVNMMT'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RDA_JG1'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RDA_JG2 is TEntityDescriptor
(
    DescriptorId       = GUID:{86482238-d1d8-4b7d-bc3c-515abee4a58f}
    ClassNameForDebug  = 'Pawn_pion_RDA_JG2'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'DDR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RDA_JG2
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RDA_JG2"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RDA_JG2'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='CLABVOCPQS'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RDA_JG2'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RDA_JG3 is TEntityDescriptor
(
    DescriptorId       = GUID:{57c1900f-dd04-4dda-827d-8d9acd7cc726}
    ClassNameForDebug  = 'Pawn_pion_RDA_JG3'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'DDR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RDA_JG3
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RDA_JG3"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RDA_JG3'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='IPMOXKAYKN'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RDA_JG3'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RDA_JG7 is TEntityDescriptor
(
    DescriptorId       = GUID:{eae9c333-b1b2-4d9d-9d93-1db0138d1663}
    ClassNameForDebug  = 'Pawn_pion_RDA_JG7'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'DDR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RDA_JG7
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RDA_JG7"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RDA_JG7'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='SFCDICIELT'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RDA_JG7'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RDA_JG8 is TEntityDescriptor
(
    DescriptorId       = GUID:{e12a4a4c-f012-4e8b-b451-603eb618a5d5}
    ClassNameForDebug  = 'Pawn_pion_RDA_JG8'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'DDR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RDA_JG8
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RDA_JG8"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RDA_JG8'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='TZNVYASNET'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RDA_JG8'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RDA_JG9 is TEntityDescriptor
(
    DescriptorId       = GUID:{190e1500-5788-4b9d-b45f-297a2e7fa57f}
    ClassNameForDebug  = 'Pawn_pion_RDA_JG9'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'DDR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RDA_JG9
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RDA_JG9"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RDA_JG9'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='ETEFWHZWJZ'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RDA_JG9'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RFA_2LwDiv_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{fb315d8d-fae7-4c3f-a2ac-d1287dfa477f}
    ClassNameForDebug  = 'Pawn_pion_RFA_2LwDiv_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_division",
                    "Texture_STRATEGIC_division",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RFA_2LwDiv_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='PFLXHQPZPP'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RFA_2LwDiv_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RFA_JBG33 is TEntityDescriptor
(
    DescriptorId       = GUID:{831cb784-dec8-4004-aab3-f2ca6f46ab58}
    ClassNameForDebug  = 'Pawn_pion_RFA_JBG33'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'RFA'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RFA_JBG33
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RFA_JBG33"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RFA_JBG33'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='EBZXPUKMTQ'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RFA_JBG33'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RFA_JBG33_2 is TEntityDescriptor
(
    DescriptorId       = GUID:{f0e9fc06-c728-45d6-9fe6-e469f516c658}
    ClassNameForDebug  = 'Pawn_pion_RFA_JBG33_2'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'RFA'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RFA_JBG33_2
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RFA_JBG33_2"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RFA_JBG33_2'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='CRUMETCIRV'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RFA_JBG33_2'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RFA_JBG34 is TEntityDescriptor
(
    DescriptorId       = GUID:{ea2989d3-4b05-420e-b870-50ad917a45e2}
    ClassNameForDebug  = 'Pawn_pion_RFA_JBG34'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'RFA'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RFA_JBG34
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RFA_JBG34"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RFA_JBG34'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='NDTKLPZMDF'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RFA_JBG34'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RFA_JBG34_2 is TEntityDescriptor
(
    DescriptorId       = GUID:{e3a0b033-b213-4f39-bfce-71b8ff371f43}
    ClassNameForDebug  = 'Pawn_pion_RFA_JBG34_2'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'RFA'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RFA_JBG34_2
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RFA_JBG34_2"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RFA_JBG34_2'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='AUZQMXVNXD'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RFA_JBG34_2'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RFA_JBG35 is TEntityDescriptor
(
    DescriptorId       = GUID:{a55ec9e7-9157-446f-9d40-e70a3dd894a0}
    ClassNameForDebug  = 'Pawn_pion_RFA_JBG35'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'RFA'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RFA_JBG35
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RFA_JBG35"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RFA_JBG35'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='NOTAALARUG'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RFA_JBG35'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RFA_JBG35_2 is TEntityDescriptor
(
    DescriptorId       = GUID:{bddb9218-91f7-4944-9e70-14688c94de23}
    ClassNameForDebug  = 'Pawn_pion_RFA_JBG35_2'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'RFA'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RFA_JBG35_2
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RFA_JBG35_2"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RFA_JBG35_2'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='ZALUICYXNX'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RFA_JBG35_2'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RFA_JG74 is TEntityDescriptor
(
    DescriptorId       = GUID:{7ada4fd5-26d5-4002-aeb0-1ce1e68952d9}
    ClassNameForDebug  = 'Pawn_pion_RFA_JG74'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'RFA'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RFA_JG74
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RFA_JG74"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RFA_JG74'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='THLUJELDMC'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RFA_JG74'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_RFA_JG74_2 is TEntityDescriptor
(
    DescriptorId       = GUID:{0cf18235-568a-4395-be5e-fd0af3e95ea4}
    ClassNameForDebug  = 'Pawn_pion_RFA_JG74_2'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'RFA'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_RFA_JG74_2
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_RFA_JG74_2"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_RFA_JG74_2'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='MCLTGNPRGG'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_RFA_JG74_2'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_105ADIB_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{632d55af-1036-4f80-9013-5a63414daacd}
    ClassNameForDebug  = 'Pawn_pion_SOV_105ADIB_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_105ADIB_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='VFNPGPROFP'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_105ADIB_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_11ORAP is TEntityDescriptor
(
    DescriptorId       = GUID:{d6b142ce-3845-4c25-aaaa-b9cc844ecd65}
    ClassNameForDebug  = 'Pawn_pion_SOV_11ORAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_11ORAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_11ORAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_11ORAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='QHSSKTBWYF'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_11ORAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_125ADIB_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{03300f03-8801-4bfb-9ec5-0c1ed1dd3b9a}
    ClassNameForDebug  = 'Pawn_pion_SOV_125ADIB_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_125ADIB_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='ETWXUOGKHG'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_125ADIB_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_126IAD_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{ffc168e0-aa16-4538-a9fa-e1913491b3c3}
    ClassNameForDebug  = 'Pawn_pion_SOV_126IAD_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_126IAD_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='KOXIOKCLGB'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_126IAD_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_16GvIAD_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{2d1b2f72-5e4e-4629-87dd-de0b702e5398}
    ClassNameForDebug  = 'Pawn_pion_SOV_16GvIAD_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_division",
                    "Texture_STRATEGIC_division",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_16GvIAD_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='OJRDTXQHUA'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_16GvIAD_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_16VA_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{8868de80-d002-4eda-9866-6c439ae3e35f}
    ClassNameForDebug  = 'Pawn_pion_SOV_16VA_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_16VA_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='PKKXUYTQEP'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_16VA_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_19GAPIB is TEntityDescriptor
(
    DescriptorId       = GUID:{e86c736f-9429-4012-a29e-364a33d24e54}
    ClassNameForDebug  = 'Pawn_pion_SOV_19GAPIB'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'POL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_19GAPIB
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_19GAPIB"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_ATGM_air",
                    "Texture_STRATEGIC_ATGM_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_19GAPIB'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='UNOZVVHXPD'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_19GAPIB'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_20GAPIB is TEntityDescriptor
(
    DescriptorId       = GUID:{b862096a-7892-476f-afa9-34b2514fb437}
    ClassNameForDebug  = 'Pawn_pion_SOV_20GAPIB'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_20GAPIB
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_20GAPIB"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_20GAPIB'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='QJGKKAGRYT'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_20GAPIB'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_294ORAP is TEntityDescriptor
(
    DescriptorId       = GUID:{cbd55373-3e18-4720-969b-1c8f4b73958b}
    ClassNameForDebug  = 'Pawn_pion_SOV_294ORAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_294ORAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_294ORAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_294ORAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='HFQJIOKJHG'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_294ORAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_296APIB is TEntityDescriptor
(
    DescriptorId       = GUID:{46ebfc4f-3005-4aad-84ef-51632c0d1bd9}
    ClassNameForDebug  = 'Pawn_pion_SOV_296APIB'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'POL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_296APIB
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_296APIB"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_ATGM_air",
                    "Texture_STRATEGIC_ATGM_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_296APIB'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='WMRWUQRORS'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_296APIB'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_31IGAP is TEntityDescriptor
(
    DescriptorId       = GUID:{fa03d0aa-d497-4f89-92f5-c718333d9a5e}
    ClassNameForDebug  = 'Pawn_pion_SOV_31IGAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_31IGAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_31IGAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_31IGAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='QORHZYANUM'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_31IGAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_33IAP is TEntityDescriptor
(
    DescriptorId       = GUID:{ff41a9aa-d2d6-4933-9096-9ca76e09fa12}
    ClassNameForDebug  = 'Pawn_pion_SOV_33IAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_33IAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_33IAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_33IAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='XDOHPLLXEH'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_33IAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_357OCHAP is TEntityDescriptor
(
    DescriptorId       = GUID:{f115544e-3fa2-45b7-9427-8ef608540bf0}
    ClassNameForDebug  = 'Pawn_pion_SOV_357OCHAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'POL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_357OCHAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_357OCHAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_ATGM_air",
                    "Texture_STRATEGIC_ATGM_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_357OCHAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='EJZETSKQNG'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_357OCHAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_35IAP is TEntityDescriptor
(
    DescriptorId       = GUID:{4b5e0f3b-2cba-4f8e-a35b-57d2e79cdc45}
    ClassNameForDebug  = 'Pawn_pion_SOV_35IAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_35IAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_35IAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_35IAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='AFKFHFCXWT'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_35IAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_368OCHAP is TEntityDescriptor
(
    DescriptorId       = GUID:{d6454afe-0bf2-4662-9103-cd4d95bf04f7}
    ClassNameForDebug  = 'Pawn_pion_SOV_368OCHAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'POL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_368OCHAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_368OCHAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_ATGM_air",
                    "Texture_STRATEGIC_ATGM_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_368OCHAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='EQXDBXAADN'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_368OCHAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_559APIB is TEntityDescriptor
(
    DescriptorId       = GUID:{dd50e069-c1a4-4cbb-8967-9a6cb6bd59a2}
    ClassNameForDebug  = 'Pawn_pion_SOV_559APIB'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'NL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_559APIB
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_559APIB"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_SEAD_air",
                    "Texture_STRATEGIC_SEAD_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_559APIB'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='BZYPZCYIIU'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_559APIB'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_6GvIAD_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{d5401993-1b74-4a4b-8c65-a4707cb5ec39}
    ClassNameForDebug  = 'Pawn_pion_SOV_6GvIAD_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_6GvIAD_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='OJRDTXQHUA'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_6GvIAD_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_730APIB is TEntityDescriptor
(
    DescriptorId       = GUID:{18268e2a-c636-4960-ac9e-0eb6ee38cce4}
    ClassNameForDebug  = 'Pawn_pion_SOV_730APIB'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_730APIB
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_730APIB"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_730APIB'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='JKMPOHCBVL'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_730APIB'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_73IAP is TEntityDescriptor
(
    DescriptorId       = GUID:{51bbbec0-466a-46c4-9175-b261983fb504}
    ClassNameForDebug  = 'Pawn_pion_SOV_73IAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_73IAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_73IAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_73IAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='HUWJRVICMR'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_73IAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_773IAP is TEntityDescriptor
(
    DescriptorId       = GUID:{0dfbf0c9-3f10-4b80-9c03-ad0914f902b9}
    ClassNameForDebug  = 'Pawn_pion_SOV_773IAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_773IAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_773IAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_773IAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='AZPGHXQUQG'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_773IAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_787IAP is TEntityDescriptor
(
    DescriptorId       = GUID:{17ec8d50-d507-4a18-aa01-4329d1135599}
    ClassNameForDebug  = 'Pawn_pion_SOV_787IAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_787IAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_787IAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_787IAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='UCUMUYKNQB'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_787IAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_833IAP is TEntityDescriptor
(
    DescriptorId       = GUID:{22f1a879-956c-4b96-b032-eec739ec0493}
    ClassNameForDebug  = 'Pawn_pion_SOV_833IAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_833IAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_833IAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_833IAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='BYGCWMBGRX'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_833IAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_85IGAP is TEntityDescriptor
(
    DescriptorId       = GUID:{d287f546-9120-4591-a792-f910dace6be3}
    ClassNameForDebug  = 'Pawn_pion_SOV_85IGAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_85IGAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_85IGAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_85IGAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='NQSWNMCOMI'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_85IGAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_911APIB is TEntityDescriptor
(
    DescriptorId       = GUID:{0413731f-2f64-49e7-8193-456f566f8684}
    ClassNameForDebug  = 'Pawn_pion_SOV_911APIB'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'NL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_911APIB
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_911APIB"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_SEAD_air",
                    "Texture_STRATEGIC_SEAD_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_911APIB'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='EVGZYBGHCC'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_911APIB'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_931ORAP is TEntityDescriptor
(
    DescriptorId       = GUID:{2ab2c138-9ee5-4bda-a7bf-fb5392167bcc}
    ClassNameForDebug  = 'Pawn_pion_SOV_931ORAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'NL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_931ORAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_931ORAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_SEAD_air",
                    "Texture_STRATEGIC_SEAD_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_931ORAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='ADEDGBNKSU'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_931ORAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_SOV_968IAP is TEntityDescriptor
(
    DescriptorId       = GUID:{f13f0277-**************-8a62a0a708d7}
    ClassNameForDebug  = 'Pawn_pion_SOV_968IAP'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Axis
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_SOV_968IAP
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_SOV_968IAP"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_SOV_968IAP'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='FREDRXOMPL'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_SOV_968IAP'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_UK_12Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{a52feac2-aaf9-47ae-8b53-243ecb875020}
    ClassNameForDebug  = 'Pawn_pion_UK_12Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_UK_12Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_UK_12Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_UK_12Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='QBJJHVSKZX'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_UK_12Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_UK_16_20Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{190f8b79-1f7d-41a4-87e9-f8791dab19cc}
    ClassNameForDebug  = 'Pawn_pion_UK_16_20Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_UK_16_20Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_UK_16_20Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_UK_16_20Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='ZMGHDEOURW'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_UK_16_20Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_UK_17_31Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{caa6c061-2aa5-4755-9ef7-3aa84b2a4d14}
    ClassNameForDebug  = 'Pawn_pion_UK_17_31Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_UK_17_31Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_UK_17_31Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_UK_17_31Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='BHMSCBDQDA'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_UK_17_31Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_UK_19_92Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{88aa2f54-3348-4971-a8a2-d1e5b0caa0b4}
    ClassNameForDebug  = 'Pawn_pion_UK_19_92Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_UK_19_92Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_UK_19_92Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_UK_19_92Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='JEIATIESEP'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_UK_19_92Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_UK_2ATAF_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{ee6c11bf-da7c-4b1b-b8f1-f93d6b7134fc}
    ClassNameForDebug  = 'Pawn_pion_UK_2ATAF_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_UK_2ATAF_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='DGZWORDCXN'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_UK_2ATAF_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_UK_2_15Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{b5716c46-bde0-43bf-81a3-680ae7e9f5dc}
    ClassNameForDebug  = 'Pawn_pion_UK_2_15Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'NL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_UK_2_15Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_UK_2_15Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_SEAD_air",
                    "Texture_STRATEGIC_SEAD_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_UK_2_15Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='HKRBNLSHMT'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_UK_2_15Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_UK_3_4Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{4b93d9d9-1a26-4a36-94b0-cdc233234524}
    ClassNameForDebug  = 'Pawn_pion_UK_3_4Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_UK_3_4Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_UK_3_4Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_UK_3_4Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='SWTSBGQWBZ'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_UK_3_4Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_UK_9_14Sq is TEntityDescriptor
(
    DescriptorId       = GUID:{1ae60cc1-88a6-4804-bbde-20ef591ed0c9}
    ClassNameForDebug  = 'Pawn_pion_UK_9_14Sq'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_UK_9_14Sq
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_UK_9_14Sq"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_UK_9_14Sq'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='DDANKNMMJV'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_UK_9_14Sq'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_UK_RAFG_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{9997f122-7abb-4859-b79e-2df9ef3e6fb8}
    ClassNameForDebug  = 'Pawn_pion_UK_RAFG_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_UK_RAFG_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='KQKMMFWNQU'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_UK_RAFG_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_UK_RAF_Bruggen_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{569cf16d-37e6-4f98-bedc-482840b7f174}
    ClassNameForDebug  = 'Pawn_pion_UK_RAF_Bruggen_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_UK_RAF_Bruggen_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='CUBUGLAUBX'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_UK_RAF_Bruggen_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_UK_RAF_Gutersloh_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{ddb1762e-00bb-4a3d-9a88-9471ac764649}
    ClassNameForDebug  = 'Pawn_pion_UK_RAF_Gutersloh_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_UK_RAF_Gutersloh_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='KRLTBROZGB'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_UK_RAF_Gutersloh_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_UK_RAF_Laarbruch_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{c1d632d9-8007-4065-805d-54b087c569c2}
    ClassNameForDebug  = 'Pawn_pion_UK_RAF_Laarbruch_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_UK_RAF_Laarbruch_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='SJEUSVHJRF'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_UK_RAF_Laarbruch_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_UK_RAF_Wildenrath_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{a7db5575-4e3f-4e43-bab2-eb628d0b1d45}
    ClassNameForDebug  = 'Pawn_pion_UK_RAF_Wildenrath_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_UK_RAF_Wildenrath_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='KGODIAMQAG'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_UK_RAF_Wildenrath_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_10TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{d6265c8d-7f3c-48fe-adf4-7f09311e69aa}
    ClassNameForDebug  = 'Pawn_pion_US_10TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_10TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_10TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_10TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='NGWTVMSKQS'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_10TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_10TFW_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{ab84c1fb-d419-406e-85f9-373379491fb5}
    ClassNameForDebug  = 'Pawn_pion_US_10TFW_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_10TFW_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='VBIOESGPNX'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_10TFW_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_17AF_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{91800ca1-dbff-4203-aa6d-d484a9ab30b3}
    ClassNameForDebug  = 'Pawn_pion_US_17AF_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_17AF_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='VPVXVYXHHE'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_17AF_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_20TFW_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{6a3feaca-ebf8-42d4-b619-bb49cbe756eb}
    ClassNameForDebug  = 'Pawn_pion_US_20TFW_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_20TFW_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='IKAUTRYXQQ'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_20TFW_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_22TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{2d0b453e-e1f3-4c54-8433-238b274f9ec7}
    ClassNameForDebug  = 'Pawn_pion_US_22TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_22TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_22TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_22TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='CPYQOJKQKO'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_22TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_23TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{3ab1f335-8827-4ff7-948a-f4455dca698c}
    ClassNameForDebug  = 'Pawn_pion_US_23TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'NL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_23TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_23TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_SEAD_air",
                    "Texture_STRATEGIC_SEAD_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_23TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='RAXQRKPWFQ'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_23TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_313TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{9a618f31-28b1-4963-9de0-1b04da553721}
    ClassNameForDebug  = 'Pawn_pion_US_313TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_313TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_313TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_313TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='EZGUCUEKLE'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_313TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_32TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{5b4afa46-2cf6-4407-8eb3-6b5e4ca7ec29}
    ClassNameForDebug  = 'Pawn_pion_US_32TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_32TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_32TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_32TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='SOCPGGFBNE'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_32TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_36TFW_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{4af8c0f9-9562-4366-ae46-67f79cc3632c}
    ClassNameForDebug  = 'Pawn_pion_US_36TFW_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_36TFW_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='RUHLTGNORD'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_36TFW_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_3AF_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{b791aed0-24cc-4727-9484-b4da0f560204}
    ClassNameForDebug  = 'Pawn_pion_US_3AF_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_3AF_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='FTASMGHFYV'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_3AF_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_4450TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{3b6b396b-4f12-48e1-ba6c-ed82241f3185}
    ClassNameForDebug  = 'Pawn_pion_US_4450TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'NL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_4450TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_4450TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_4450TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='EDSEHFDWYU'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_4450TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_480TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{5a166921-faa1-4905-8cfc-e79cc9db9e8e}
    ClassNameForDebug  = 'Pawn_pion_US_480TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'NL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_480TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_480TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_SEAD_air",
                    "Texture_STRATEGIC_SEAD_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_480TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='WZYAHIUJKH'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_480TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_48TFW_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{ebb29b17-85f8-49bf-8012-f09960c2c362}
    ClassNameForDebug  = 'Pawn_pion_US_48TFW_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_48TFW_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='PUSKTKJWSB'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_48TFW_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_492TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{400d4f47-e03e-4d5c-baa0-a8100ac80189}
    ClassNameForDebug  = 'Pawn_pion_US_492TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_492TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_492TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_492TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='NXZNMJHTEX'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_492TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_493TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{325ade75-1e4b-4585-bd54-a9ceeea47bd1}
    ClassNameForDebug  = 'Pawn_pion_US_493TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_493TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_493TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_493TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='CXLHCJRDZJ'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_493TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_494TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{bd101782-f0a8-4960-acf0-2079db1eee20}
    ClassNameForDebug  = 'Pawn_pion_US_494TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_494TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_494TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_494TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='YRGNIUGSNF'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_494TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_495TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{dde32347-7c24-47d0-8731-788a633764ae}
    ClassNameForDebug  = 'Pawn_pion_US_495TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_495TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_495TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_495TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='MTIJVQOQZG'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_495TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_496TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{6b117b2d-b46c-45bc-9054-bfa0594dcd4f}
    ClassNameForDebug  = 'Pawn_pion_US_496TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_496TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_496TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_496TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='YWIOENPTLP'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_496TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_4ATAF_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{78495daa-9b75-43d5-94a5-f3091db9d5ec}
    ClassNameForDebug  = 'Pawn_pion_US_4ATAF_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_4ATAF_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='ESZCCWKILN'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_4ATAF_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_509TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{18d1f035-1c0d-46b2-82d8-b8a69ae9f8e6}
    ClassNameForDebug  = 'Pawn_pion_US_509TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'POL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_509TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_509TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_ATGM_air",
                    "Texture_STRATEGIC_ATGM_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_509TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='ODLTYORIBR'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_509TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_50TFW_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{38ccf3ea-a377-4484-8ce5-b5dd2315830d}
    ClassNameForDebug  = 'Pawn_pion_US_50TFW_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_50TFW_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='JVQHLFQFIM'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_50TFW_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_510TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{fe49b2b4-dbe5-46cc-9bb5-bec21050f364}
    ClassNameForDebug  = 'Pawn_pion_US_510TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'POL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_510TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_510TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_ATGM_air",
                    "Texture_STRATEGIC_ATGM_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_510TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='GZLGDVETLB'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_510TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_511TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{9fdafbc5-7d4a-491c-a328-394a9e385f7a}
    ClassNameForDebug  = 'Pawn_pion_US_511TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'POL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_511TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_511TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_ATGM_air",
                    "Texture_STRATEGIC_ATGM_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_511TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='LVJEMGXZHY'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_511TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_512TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{521e2355-4aba-4574-8aea-3443bdede642}
    ClassNameForDebug  = 'Pawn_pion_US_512TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_512TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_512TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_512TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='YSNVNECBJR'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_512TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_525TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{a0779461-e9ca-40e4-92c8-34c12f2abde9}
    ClassNameForDebug  = 'Pawn_pion_US_525TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_525TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_525TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_525TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='BHLOEJKNIW'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_525TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_526TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{9cf1aefc-7b89-4c6f-ad59-f5baa8f5ffcd}
    ClassNameForDebug  = 'Pawn_pion_US_526TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_526TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_526TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_526TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='VCDLVRCXJI'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_526TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_527Aggress is TEntityDescriptor
(
    DescriptorId       = GUID:{4c3a383b-c5b0-4f8b-918a-a79292486e3e}
    ClassNameForDebug  = 'Pawn_pion_US_527Aggress'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_527Aggress
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_527Aggress"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_527Aggress'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='ZCKIDIENSW'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_527Aggress'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_52TFW_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{a07f5864-1186-4569-959c-ff9b68d68456}
    ClassNameForDebug  = 'Pawn_pion_US_52TFW_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_52TFW_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='KWEWJULSTS'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_52TFW_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_53TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{ec74dc0f-c3f9-4d52-87d0-abb12b180a07}
    ClassNameForDebug  = 'Pawn_pion_US_53TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'FR'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_53TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_53TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_AA_air",
                    "Texture_STRATEGIC_AA_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_53TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='IFBXVBBNIX'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_53TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_55TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{49f5b1ce-b352-4cd1-aca1-2e29a3c7648e}
    ClassNameForDebug  = 'Pawn_pion_US_55TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_55TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_55TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_55TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='XVPONBIGUX'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_55TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_77TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{1a04b17e-1aa0-4404-9ebc-5ce24030370d}
    ClassNameForDebug  = 'Pawn_pion_US_77TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_77TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_77TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_77TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='OZDHPWPBLI'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_77TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_78TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{8a07104b-161f-4068-9083-cbff3ce6fccf}
    ClassNameForDebug  = 'Pawn_pion_US_78TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'POL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_78TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_78TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_ATGM_air",
                    "Texture_STRATEGIC_ATGM_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_78TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='TGWJCWRLFR'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_78TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_79TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{1662f27e-cdaf-43e8-9fd5-5430e6cf45c3}
    ClassNameForDebug  = 'Pawn_pion_US_79TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'US'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_79TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_79TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_Support_air",
                    "Texture_STRATEGIC_Support_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_79TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='ONEMPEDRAG'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_79TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_81TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{fc23a8dc-aa9d-490b-b893-18cd73fb1ecd}
    ClassNameForDebug  = 'Pawn_pion_US_81TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'NL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_81TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_81TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_SEAD_air",
                    "Texture_STRATEGIC_SEAD_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_81TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='BGTDNRYIPH'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_81TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_81TFW is TEntityDescriptor
(
    DescriptorId       = GUID:{91c403bf-ab82-4955-a4e1-cdc3b7c75143}
    ClassNameForDebug  = 'Pawn_pion_US_81TFW'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'POL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_81TFW
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_81TFW"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_ATGM_air",
                    "Texture_STRATEGIC_ATGM_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_81TFW'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='LPUMMSQVPE'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_81TFW'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_81TFW_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{f703cf7d-d8bf-4e1b-bd6b-84e77a25e51b}
    ClassNameForDebug  = 'Pawn_pion_US_81TFW_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_81TFW_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='XJMDESILIJ'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_81TFW_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_86TFW_GROUPE is TEntityDescriptor
(
    DescriptorId       = GUID:{fedab03c-4bbd-4c25-a3ea-80d235f735fa}
    ClassNameForDebug  = 'Pawn_pion_US_86TFW_GROUPE'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_86TFW_GROUPE'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='JLQTNYYRXU'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_86TFW_GROUPE'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_91TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{912814ad-6ef4-4b66-b093-a96cdfe41b7a}
    ClassNameForDebug  = 'Pawn_pion_US_91TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'POL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_91TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_91TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_ATGM_air",
                    "Texture_STRATEGIC_ATGM_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_91TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='COFZKIOBLM'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_91TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_pion_US_92TFS is TEntityDescriptor
(
    DescriptorId       = GUID:{9e1399a8-f895-4933-8f6b-245fef5ecb55}
    ClassNameForDebug  = 'Pawn_pion_US_92TFS'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            MotherCountry        = 'POL'
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_pion_US_92TFS
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "pion_US_92TFS"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_ATGM_air",
                    "Texture_STRATEGIC_ATGM_air",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'pion_US_92TFS'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='VBDTMOEDIP'
            ProdMenuTexture = 'Texture_Button_Pawn_pion_US_92TFS'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_test_avion_alex is TEntityDescriptor
(
    DescriptorId       = GUID:{d55eb404-721b-41d8-8421-f5a7828db665}
    ClassNameForDebug  = 'Pawn_test_avion_alex'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_test_avion_alex
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "test_avion_alex"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'test_deck_avion'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='ETCJQTKOED'
            ProdMenuTexture = 'Texture_Button_Pawn_test_avion_alex'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)
export Descriptor_Unit_test_avion_cas is TEntityDescriptor
(
    DescriptorId       = GUID:{d6a13c5e-2f7a-4736-ac7e-d7b60ad9cc74}
    ClassNameForDebug  = 'Pawn_test_avion_cas'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition            = ECoalition/Allied
            TypeUnitFormation    = 'Char'
        ),
        AirplaneFlagsModuleDescriptor,
        ~/PawnPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TApparenceModuleDescriptor
        (
            PickableObject      = True
            Depiction           = $/GFX/Depiction/Gfx_test_avion_cas
            ReferenceMesh       = $/GFX/DepictionResources/MeshModele_Socle_US
            BlackHoleIdentifier = "test_avion_cas"
        ),
        ~/StrategicStateEngineModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/StrategicSelectionModuleDescriptor,
        ~/EffectApplierModuleDescriptor,
        TStrategicLabelModuleDescriptor
        (
            BackgroundTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromStrategicIconType
                Alterator = $/GUIOption/StrategicIconType
                Values =
                [
                    "Texture_STRATEGIC_RTS_H_corps",
                    "Texture_STRATEGIC_corps",
                ]
            )
        ),
        ~/PawnAirplaneOrderConfigModuleDescriptor,
        ~/PackSignauxModuleDescriptor,
        TDeckModuleDescriptor
        (
            DeckIdentifier = 'test_deck_avion'
            RegimentName = 'None'
            Score = 1
            PictureForSelection = 'Texture_PawnPicture_Default'
            MaxPackCount = 12
        ),
        TStrategicBattleModuleDescriptor
        (
            CanBeInitialTarget = False
            HasZoneOfControl = False
            BattleSupportRadiusInAPCase = -1.0
            BattleRole = ~/EStrategicBattleRole/AirSupport
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 0.0
                TimeBetweenEachIdentifyRoll = 0.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TActionPointsModuleDescriptor
        (
            InitialActionPoint = 4
            ActionPointRecoveryPerTurn = 4
            NbInitialActionsPointsForProducedPawn = 4
        ),
        ~/StrategicMovementDescriptor_aerial,
        ~/StrategicAerialModuleDescriptor,
        TCubeActionModuleDescriptor
        (
            CubeActionDescriptor = $/GFX/UI/CubeAction_Menu_Ordres_Pawn
        ),
        StrategicUIModuleDescriptor
        (
            NameToken ='TVFYQTBVDC'
            ProdMenuTexture = 'Texture_Button_Pawn_test_avion_cas'
        ),
        TStrategicPositionModuleDescriptor( IsAirport = False ),
        ~/StrategicSequenceModuleDescriptor,
        StrategicFatigueModuleDescriptor( InitialFatigue  = 0 ),
        ~/IAStratZoneIndexModuleDescriptor,
    ]
)

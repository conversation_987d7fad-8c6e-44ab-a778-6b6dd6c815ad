// Ne pas éditer, ce fichier est généré par BuildingDescriptorFileWriter_Specific


export Descriptor_Unit_FOB_BEL is TEntityDescriptor
(
    DescriptorId       = GUID:{3f958f6c-ccf9-49d8-90e1-67efbcc746f1}
    ClassNameForDebug  = 'Building_FOB_BEL'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            MotherCountry                    = 'BEL'
            Coalition                        = ECoalition/Allied
        ),
        ~/BuildingDescriptorTagsModuleDescriptor,
        ~/BuildingFlagsModuleDescriptor,
        ~/BuildingPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TExperienceModuleDescriptor
        (
            ExperienceLevelsPackDescriptor = ~/ExperienceLevelsPackDescriptor_XP_pack_simple_v3
            ExperienceMultiplierBonusOnKill = ~/ExperienceMultiplierBonusOnKill
        ),
        ~/FOBBuildingModuleDescriptor,
        TSupplyModuleDescriptor
        (
            SupplyDescriptor   = $/GFX/Weapon/StandardSupply
            SupplyCapacity     = 16000.0
            SupplyPriority     = 0
        ),
        ~/CapturableModuleDescriptor,
        TBaseDamageModuleDescriptor
        (
            MaxPhysicalDamages = 10
            MaxSuppressionDamages = ~/Building_MaxSuppressionDamages
            MaxStunDamages = ~/Building_MaxStunDamages
            PhysicalDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_degats_phy
            SuppressDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_suppression
            StunDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_stun
        ),
        TDamageModuleDescriptor
        (
            SuppressDamagesRegenRatio = ~/Building_SuppressDamagesRegenRatioList
            StunDamagesRegen = ~/Building_StunDamagesRegen
            BlindageProperties = TBlindageProperties
            (
                ResistanceFront = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceSides = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceRear = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceTop = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ExplosiveReactiveArmor = False
            )
            KillWhenDamagesReachMax = True
            HitRollECM = 0.0
            AutoOrientation = False
            UseTopArmorAgainstFire = False
        ),
        ~/TargetCoordinatorModuleSelector,
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        ~/BuildingScannerConfigurationDescriptor,
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 1.0
                TimeBetweenEachIdentifyRoll = 1.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        ApparenceModel is BuildingApparenceModuleDescriptor
        (
            BlackHoleIdentifier = "FOB_BEL"
            SymbolName = 'FOB_Nato_03'
        ),
        TCadavreGeneratorModuleDescriptor( CadavreDescriptor = ~/Descriptor_UnitCadavre_FOB_BEL ),
        TIAStratModuleDescriptor
        (
            DatabaseId = 0
            GameplayBehavior = EGameplayBehavior/Nothing
        ),
        ~/IAStratZoneIndexModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/SelectionModuleDescriptor,
        ~/UnitStateEngineCompanyModuleDescriptor,
        ~/UnitStateEngineModuleDescriptor,
        TProductionModuleDescriptor
        (
            Factory            = EDefaultFactories/Logistic
            ProductionTime     = -1
            ProductionRessourcesNeeded = MAP [
                ($/GFX/Resources/Resource_CommandPoints, 175),
            ]
        ),
        ~/EffectApplierModuleDescriptor,
        TTacticalLabelModuleDescriptor
        (
            IdentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_fob",
                    "Texture_fob"
                ]
            )
            UnidentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_NATO_Unidentified",
                    "Texture_NATO_Unidentified"
                ]
            )
            PositionHeightOffset = 1000.0
        ),
        ~/BuildingOrderConfigModuleDescriptor,
        ~/OrderDisplayModuleDescriptor,
        ~/GroupableUnitModuleSelector,
        ~/PackSignauxModuleDescriptor,
        TUnitUpkeepModuleDescriptor( UpkeepPercentage = 0.0 ),
        TUnitUIModuleDescriptor
        (
            UnitRole = 'supply'
            PriceCategory = 'tank_A'
            NameToken = 'BFCFNEMWNZ'
            InfoPanelConfigurationToken = 'FOB'
            GenerateName = true
            MenuIconTexture = 'Texture_RTS_H_fob'
            ButtonTexture = 'Texture_Button_Unit_FOB_BEL'
            CountryTexture = 'CommonTexture_MotherCountryFlag_BEL'
            TypeStrategicCount = ETypeStrategicDetailedCount/NotCounted
        ),
    ]
)
export Descriptor_Unit_FOB_DDR is TEntityDescriptor
(
    DescriptorId       = GUID:{77799f4b-f37d-4fa2-87e7-b9d682ac2604}
    ClassNameForDebug  = 'Building_FOB_DDR'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            MotherCountry                    = 'DDR'
            Coalition                        = ECoalition/Axis
        ),
        ~/BuildingDescriptorTagsModuleDescriptor,
        ~/BuildingFlagsModuleDescriptor,
        ~/BuildingPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TExperienceModuleDescriptor
        (
            ExperienceLevelsPackDescriptor = ~/ExperienceLevelsPackDescriptor_XP_pack_simple_v3
            ExperienceMultiplierBonusOnKill = ~/ExperienceMultiplierBonusOnKill
        ),
        ~/FOBBuildingModuleDescriptor,
        TSupplyModuleDescriptor
        (
            SupplyDescriptor   = $/GFX/Weapon/StandardSupply
            SupplyCapacity     = 16000.0
            SupplyPriority     = 0
        ),
        ~/CapturableModuleDescriptor,
        TBaseDamageModuleDescriptor
        (
            MaxPhysicalDamages = 10
            MaxSuppressionDamages = ~/Building_MaxSuppressionDamages
            MaxStunDamages = ~/Building_MaxStunDamages
            PhysicalDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_degats_phy
            SuppressDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_suppression
            StunDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_stun
        ),
        TDamageModuleDescriptor
        (
            SuppressDamagesRegenRatio = ~/Building_SuppressDamagesRegenRatioList
            StunDamagesRegen = ~/Building_StunDamagesRegen
            BlindageProperties = TBlindageProperties
            (
                ResistanceFront = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceSides = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceRear = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceTop = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ExplosiveReactiveArmor = False
            )
            KillWhenDamagesReachMax = True
            HitRollECM = 0.0
            AutoOrientation = False
            UseTopArmorAgainstFire = False
        ),
        ~/TargetCoordinatorModuleSelector,
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        ~/BuildingScannerConfigurationDescriptor,
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 1.0
                TimeBetweenEachIdentifyRoll = 1.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        ApparenceModel is BuildingApparenceModuleDescriptor
        (
            BlackHoleIdentifier = "FOB_DDR"
            SymbolName = 'FOB_Pact_03'
        ),
        TCadavreGeneratorModuleDescriptor( CadavreDescriptor = ~/Descriptor_UnitCadavre_FOB_DDR ),
        TIAStratModuleDescriptor
        (
            DatabaseId = 0
            GameplayBehavior = EGameplayBehavior/Nothing
        ),
        ~/IAStratZoneIndexModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/SelectionModuleDescriptor,
        ~/UnitStateEngineCompanyModuleDescriptor,
        ~/UnitStateEngineModuleDescriptor,
        TProductionModuleDescriptor
        (
            Factory            = EDefaultFactories/Logistic
            ProductionTime     = -1
            ProductionRessourcesNeeded = MAP [
                ($/GFX/Resources/Resource_CommandPoints, 175),
            ]
        ),
        ~/EffectApplierModuleDescriptor,
        TTacticalLabelModuleDescriptor
        (
            IdentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_fob",
                    "Texture_fob"
                ]
            )
            UnidentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_NATO_Unidentified",
                    "Texture_NATO_Unidentified"
                ]
            )
            PositionHeightOffset = 1000.0
        ),
        ~/BuildingOrderConfigModuleDescriptor,
        ~/OrderDisplayModuleDescriptor,
        ~/GroupableUnitModuleSelector,
        ~/PackSignauxModuleDescriptor,
        TUnitUpkeepModuleDescriptor( UpkeepPercentage = 0.0 ),
        TUnitUIModuleDescriptor
        (
            UnitRole = 'supply'
            PriceCategory = 'tank_A'
            NameToken = 'FOBGER'
            InfoPanelConfigurationToken = 'FOB'
            GenerateName = true
            MenuIconTexture = 'Texture_RTS_H_fob'
            ButtonTexture = 'Texture_Button_Unit_FOB_DDR'
            CountryTexture = 'CommonTexture_MotherCountryFlag_DDR'
            TypeStrategicCount = ETypeStrategicDetailedCount/NotCounted
        ),
    ]
)
export Descriptor_Unit_FOB_FR is TEntityDescriptor
(
    DescriptorId       = GUID:{d2d3b308-e2dc-4f8a-bcfe-9a7681e52c97}
    ClassNameForDebug  = 'Building_FOB_FR'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            MotherCountry                    = 'FR'
            Coalition                        = ECoalition/Allied
        ),
        ~/BuildingDescriptorTagsModuleDescriptor,
        ~/BuildingFlagsModuleDescriptor,
        ~/BuildingPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TExperienceModuleDescriptor
        (
            ExperienceLevelsPackDescriptor = ~/ExperienceLevelsPackDescriptor_XP_pack_simple_v3
            ExperienceMultiplierBonusOnKill = ~/ExperienceMultiplierBonusOnKill
        ),
        ~/FOBBuildingModuleDescriptor,
        TSupplyModuleDescriptor
        (
            SupplyDescriptor   = $/GFX/Weapon/StandardSupply
            SupplyCapacity     = 16000.0
            SupplyPriority     = 0
        ),
        ~/CapturableModuleDescriptor,
        TBaseDamageModuleDescriptor
        (
            MaxPhysicalDamages = 10
            MaxSuppressionDamages = ~/Building_MaxSuppressionDamages
            MaxStunDamages = ~/Building_MaxStunDamages
            PhysicalDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_degats_phy
            SuppressDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_suppression
            StunDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_stun
        ),
        TDamageModuleDescriptor
        (
            SuppressDamagesRegenRatio = ~/Building_SuppressDamagesRegenRatioList
            StunDamagesRegen = ~/Building_StunDamagesRegen
            BlindageProperties = TBlindageProperties
            (
                ResistanceFront = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceSides = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceRear = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceTop = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ExplosiveReactiveArmor = False
            )
            KillWhenDamagesReachMax = True
            HitRollECM = 0.0
            AutoOrientation = False
            UseTopArmorAgainstFire = False
        ),
        ~/TargetCoordinatorModuleSelector,
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        ~/BuildingScannerConfigurationDescriptor,
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 1.0
                TimeBetweenEachIdentifyRoll = 1.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        ApparenceModel is BuildingApparenceModuleDescriptor
        (
            BlackHoleIdentifier = "FOB_FR"
            SymbolName = 'FOB_Nato_03'
        ),
        TCadavreGeneratorModuleDescriptor( CadavreDescriptor = ~/Descriptor_UnitCadavre_FOB_FR ),
        TIAStratModuleDescriptor
        (
            DatabaseId = 0
            GameplayBehavior = EGameplayBehavior/Nothing
        ),
        ~/IAStratZoneIndexModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/SelectionModuleDescriptor,
        ~/UnitStateEngineCompanyModuleDescriptor,
        ~/UnitStateEngineModuleDescriptor,
        TProductionModuleDescriptor
        (
            Factory            = EDefaultFactories/Logistic
            ProductionTime     = -1
            ProductionRessourcesNeeded = MAP [
                ($/GFX/Resources/Resource_CommandPoints, 175),
            ]
        ),
        ~/EffectApplierModuleDescriptor,
        TTacticalLabelModuleDescriptor
        (
            IdentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_fob",
                    "Texture_fob"
                ]
            )
            UnidentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_NATO_Unidentified",
                    "Texture_NATO_Unidentified"
                ]
            )
            PositionHeightOffset = 1000.0
        ),
        ~/BuildingOrderConfigModuleDescriptor,
        ~/OrderDisplayModuleDescriptor,
        ~/GroupableUnitModuleSelector,
        ~/PackSignauxModuleDescriptor,
        TUnitUpkeepModuleDescriptor( UpkeepPercentage = 0.0 ),
        TUnitUIModuleDescriptor
        (
            UnitRole = 'supply'
            PriceCategory = 'tank_A'
            NameToken = 'FOBFRA'
            InfoPanelConfigurationToken = 'FOB'
            GenerateName = true
            MenuIconTexture = 'Texture_RTS_H_fob'
            ButtonTexture = 'Texture_Button_Unit_FOB_FR'
            CountryTexture = 'CommonTexture_MotherCountryFlag_FR'
            TypeStrategicCount = ETypeStrategicDetailedCount/NotCounted
        ),
    ]
)
export Descriptor_Unit_FOB_NL is TEntityDescriptor
(
    DescriptorId       = GUID:{cedcaa41-8c25-4557-8bd0-c4c8b6796aa5}
    ClassNameForDebug  = 'Building_FOB_NL'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            MotherCountry                    = 'NL'
            Coalition                        = ECoalition/Allied
        ),
        ~/BuildingDescriptorTagsModuleDescriptor,
        ~/BuildingFlagsModuleDescriptor,
        ~/BuildingPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TExperienceModuleDescriptor
        (
            ExperienceLevelsPackDescriptor = ~/ExperienceLevelsPackDescriptor_XP_pack_simple_v3
            ExperienceMultiplierBonusOnKill = ~/ExperienceMultiplierBonusOnKill
        ),
        ~/FOBBuildingModuleDescriptor,
        TSupplyModuleDescriptor
        (
            SupplyDescriptor   = $/GFX/Weapon/StandardSupply
            SupplyCapacity     = 16000.0
            SupplyPriority     = 0
        ),
        ~/CapturableModuleDescriptor,
        TBaseDamageModuleDescriptor
        (
            MaxPhysicalDamages = 10
            MaxSuppressionDamages = ~/Building_MaxSuppressionDamages
            MaxStunDamages = ~/Building_MaxStunDamages
            PhysicalDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_degats_phy
            SuppressDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_suppression
            StunDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_stun
        ),
        TDamageModuleDescriptor
        (
            SuppressDamagesRegenRatio = ~/Building_SuppressDamagesRegenRatioList
            StunDamagesRegen = ~/Building_StunDamagesRegen
            BlindageProperties = TBlindageProperties
            (
                ResistanceFront = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceSides = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceRear = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceTop = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ExplosiveReactiveArmor = False
            )
            KillWhenDamagesReachMax = True
            HitRollECM = 0.0
            AutoOrientation = False
            UseTopArmorAgainstFire = False
        ),
        ~/TargetCoordinatorModuleSelector,
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        ~/BuildingScannerConfigurationDescriptor,
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 1.0
                TimeBetweenEachIdentifyRoll = 1.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        ApparenceModel is BuildingApparenceModuleDescriptor
        (
            BlackHoleIdentifier = "FOB_NL"
            SymbolName = 'FOB_Nato_03'
        ),
        TCadavreGeneratorModuleDescriptor( CadavreDescriptor = ~/Descriptor_UnitCadavre_FOB_NL ),
        TIAStratModuleDescriptor
        (
            DatabaseId = 0
            GameplayBehavior = EGameplayBehavior/Nothing
        ),
        ~/IAStratZoneIndexModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/SelectionModuleDescriptor,
        ~/UnitStateEngineCompanyModuleDescriptor,
        ~/UnitStateEngineModuleDescriptor,
        TProductionModuleDescriptor
        (
            Factory            = EDefaultFactories/Logistic
            ProductionTime     = -1
            ProductionRessourcesNeeded = MAP [
                ($/GFX/Resources/Resource_CommandPoints, 175),
            ]
        ),
        ~/EffectApplierModuleDescriptor,
        TTacticalLabelModuleDescriptor
        (
            IdentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_fob",
                    "Texture_fob"
                ]
            )
            UnidentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_NATO_Unidentified",
                    "Texture_NATO_Unidentified"
                ]
            )
            PositionHeightOffset = 1000.0
        ),
        ~/BuildingOrderConfigModuleDescriptor,
        ~/OrderDisplayModuleDescriptor,
        ~/GroupableUnitModuleSelector,
        ~/PackSignauxModuleDescriptor,
        TUnitUpkeepModuleDescriptor( UpkeepPercentage = 0.0 ),
        TUnitUIModuleDescriptor
        (
            UnitRole = 'supply'
            PriceCategory = 'tank_A'
            NameToken = 'FOBNL'
            InfoPanelConfigurationToken = 'FOB'
            GenerateName = true
            MenuIconTexture = 'Texture_RTS_H_fob'
            ButtonTexture = 'Texture_Button_Unit_FOB_NL'
            CountryTexture = 'CommonTexture_MotherCountryFlag_NL'
            TypeStrategicCount = ETypeStrategicDetailedCount/NotCounted
        ),
    ]
)
export Descriptor_Unit_FOB_POL is TEntityDescriptor
(
    DescriptorId       = GUID:{a4351253-3f1e-4def-b490-5ad25acaaf0a}
    ClassNameForDebug  = 'Building_FOB_POL'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            MotherCountry                    = 'POL'
            Coalition                        = ECoalition/Axis
        ),
        ~/BuildingDescriptorTagsModuleDescriptor,
        ~/BuildingFlagsModuleDescriptor,
        ~/BuildingPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TExperienceModuleDescriptor
        (
            ExperienceLevelsPackDescriptor = ~/ExperienceLevelsPackDescriptor_XP_pack_simple_v3
            ExperienceMultiplierBonusOnKill = ~/ExperienceMultiplierBonusOnKill
        ),
        ~/FOBBuildingModuleDescriptor,
        TSupplyModuleDescriptor
        (
            SupplyDescriptor   = $/GFX/Weapon/StandardSupply
            SupplyCapacity     = 16000.0
            SupplyPriority     = 0
        ),
        ~/CapturableModuleDescriptor,
        TBaseDamageModuleDescriptor
        (
            MaxPhysicalDamages = 10
            MaxSuppressionDamages = ~/Building_MaxSuppressionDamages
            MaxStunDamages = ~/Building_MaxStunDamages
            PhysicalDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_degats_phy
            SuppressDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_suppression
            StunDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_stun
        ),
        TDamageModuleDescriptor
        (
            SuppressDamagesRegenRatio = ~/Building_SuppressDamagesRegenRatioList
            StunDamagesRegen = ~/Building_StunDamagesRegen
            BlindageProperties = TBlindageProperties
            (
                ResistanceFront = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceSides = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceRear = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceTop = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ExplosiveReactiveArmor = False
            )
            KillWhenDamagesReachMax = True
            HitRollECM = 0.0
            AutoOrientation = False
            UseTopArmorAgainstFire = False
        ),
        ~/TargetCoordinatorModuleSelector,
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        ~/BuildingScannerConfigurationDescriptor,
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 1.0
                TimeBetweenEachIdentifyRoll = 1.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        ApparenceModel is BuildingApparenceModuleDescriptor
        (
            BlackHoleIdentifier = "FOB_POL"
            SymbolName = 'FOB_Pact_03'
        ),
        TCadavreGeneratorModuleDescriptor( CadavreDescriptor = ~/Descriptor_UnitCadavre_FOB_POL ),
        TIAStratModuleDescriptor
        (
            DatabaseId = 0
            GameplayBehavior = EGameplayBehavior/Nothing
        ),
        ~/IAStratZoneIndexModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/SelectionModuleDescriptor,
        ~/UnitStateEngineCompanyModuleDescriptor,
        ~/UnitStateEngineModuleDescriptor,
        TProductionModuleDescriptor
        (
            Factory            = EDefaultFactories/Logistic
            ProductionTime     = -1
            ProductionRessourcesNeeded = MAP [
                ($/GFX/Resources/Resource_CommandPoints, 175),
            ]
        ),
        ~/EffectApplierModuleDescriptor,
        TTacticalLabelModuleDescriptor
        (
            IdentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_fob",
                    "Texture_fob"
                ]
            )
            UnidentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_NATO_Unidentified",
                    "Texture_NATO_Unidentified"
                ]
            )
            PositionHeightOffset = 1000.0
        ),
        ~/BuildingOrderConfigModuleDescriptor,
        ~/OrderDisplayModuleDescriptor,
        ~/GroupableUnitModuleSelector,
        ~/PackSignauxModuleDescriptor,
        TUnitUpkeepModuleDescriptor( UpkeepPercentage = 0.0 ),
        TUnitUIModuleDescriptor
        (
            UnitRole = 'supply'
            PriceCategory = 'tank_A'
            NameToken = 'WJEPUEYMNL'
            InfoPanelConfigurationToken = 'FOB'
            GenerateName = true
            MenuIconTexture = 'Texture_RTS_H_fob'
            ButtonTexture = 'Texture_Button_Unit_FOB_POL'
            CountryTexture = 'CommonTexture_MotherCountryFlag_POL'
            TypeStrategicCount = ETypeStrategicDetailedCount/NotCounted
        ),
    ]
)
export Descriptor_Unit_FOB_RFA is TEntityDescriptor
(
    DescriptorId       = GUID:{82a2193f-c1f8-4a32-b1d8-8d724a5c08ad}
    ClassNameForDebug  = 'Building_FOB_RFA'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            MotherCountry                    = 'RFA'
            Coalition                        = ECoalition/Allied
        ),
        ~/BuildingDescriptorTagsModuleDescriptor,
        ~/BuildingFlagsModuleDescriptor,
        ~/BuildingPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TExperienceModuleDescriptor
        (
            ExperienceLevelsPackDescriptor = ~/ExperienceLevelsPackDescriptor_XP_pack_simple_v3
            ExperienceMultiplierBonusOnKill = ~/ExperienceMultiplierBonusOnKill
        ),
        ~/FOBBuildingModuleDescriptor,
        TSupplyModuleDescriptor
        (
            SupplyDescriptor   = $/GFX/Weapon/StandardSupply
            SupplyCapacity     = 16000.0
            SupplyPriority     = 0
        ),
        ~/CapturableModuleDescriptor,
        TBaseDamageModuleDescriptor
        (
            MaxPhysicalDamages = 10
            MaxSuppressionDamages = ~/Building_MaxSuppressionDamages
            MaxStunDamages = ~/Building_MaxStunDamages
            PhysicalDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_degats_phy
            SuppressDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_suppression
            StunDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_stun
        ),
        TDamageModuleDescriptor
        (
            SuppressDamagesRegenRatio = ~/Building_SuppressDamagesRegenRatioList
            StunDamagesRegen = ~/Building_StunDamagesRegen
            BlindageProperties = TBlindageProperties
            (
                ResistanceFront = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceSides = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceRear = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceTop = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ExplosiveReactiveArmor = False
            )
            KillWhenDamagesReachMax = True
            HitRollECM = 0.0
            AutoOrientation = False
            UseTopArmorAgainstFire = False
        ),
        ~/TargetCoordinatorModuleSelector,
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        ~/BuildingScannerConfigurationDescriptor,
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 1.0
                TimeBetweenEachIdentifyRoll = 1.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        ApparenceModel is BuildingApparenceModuleDescriptor
        (
            BlackHoleIdentifier = "FOB_RFA"
            SymbolName = 'FOB_Nato_03'
        ),
        TCadavreGeneratorModuleDescriptor( CadavreDescriptor = ~/Descriptor_UnitCadavre_FOB_RFA ),
        TIAStratModuleDescriptor
        (
            DatabaseId = 0
            GameplayBehavior = EGameplayBehavior/Nothing
        ),
        ~/IAStratZoneIndexModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/SelectionModuleDescriptor,
        ~/UnitStateEngineCompanyModuleDescriptor,
        ~/UnitStateEngineModuleDescriptor,
        TProductionModuleDescriptor
        (
            Factory            = EDefaultFactories/Logistic
            ProductionTime     = -1
            ProductionRessourcesNeeded = MAP [
                ($/GFX/Resources/Resource_CommandPoints, 175),
            ]
        ),
        ~/EffectApplierModuleDescriptor,
        TTacticalLabelModuleDescriptor
        (
            IdentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_fob",
                    "Texture_fob"
                ]
            )
            UnidentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_NATO_Unidentified",
                    "Texture_NATO_Unidentified"
                ]
            )
            PositionHeightOffset = 1000.0
        ),
        ~/BuildingOrderConfigModuleDescriptor,
        ~/OrderDisplayModuleDescriptor,
        ~/GroupableUnitModuleSelector,
        ~/PackSignauxModuleDescriptor,
        TUnitUpkeepModuleDescriptor( UpkeepPercentage = 0.0 ),
        TUnitUIModuleDescriptor
        (
            UnitRole = 'supply'
            PriceCategory = 'tank_A'
            NameToken = 'FOBGER'
            InfoPanelConfigurationToken = 'FOB'
            GenerateName = true
            MenuIconTexture = 'Texture_RTS_H_fob'
            ButtonTexture = 'Texture_Button_Unit_FOB_RFA'
            CountryTexture = 'CommonTexture_MotherCountryFlag_RFA'
            TypeStrategicCount = ETypeStrategicDetailedCount/NotCounted
        ),
    ]
)
export Descriptor_Unit_FOB_SOV is TEntityDescriptor
(
    DescriptorId       = GUID:{1a361ea8-4efc-46be-babd-150f15b5f386}
    ClassNameForDebug  = 'Building_FOB_SOV'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            MotherCountry                    = 'SOV'
            Coalition                        = ECoalition/Axis
        ),
        ~/BuildingDescriptorTagsModuleDescriptor,
        ~/BuildingFlagsModuleDescriptor,
        ~/BuildingPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TExperienceModuleDescriptor
        (
            ExperienceLevelsPackDescriptor = ~/ExperienceLevelsPackDescriptor_XP_pack_simple_v3
            ExperienceMultiplierBonusOnKill = ~/ExperienceMultiplierBonusOnKill
        ),
        ~/FOBBuildingModuleDescriptor,
        TSupplyModuleDescriptor
        (
            SupplyDescriptor   = $/GFX/Weapon/StandardSupply
            SupplyCapacity     = 16000.0
            SupplyPriority     = 0
        ),
        ~/CapturableModuleDescriptor,
        TBaseDamageModuleDescriptor
        (
            MaxPhysicalDamages = 10
            MaxSuppressionDamages = ~/Building_MaxSuppressionDamages
            MaxStunDamages = ~/Building_MaxStunDamages
            PhysicalDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_degats_phy
            SuppressDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_suppression
            StunDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_stun
        ),
        TDamageModuleDescriptor
        (
            SuppressDamagesRegenRatio = ~/Building_SuppressDamagesRegenRatioList
            StunDamagesRegen = ~/Building_StunDamagesRegen
            BlindageProperties = TBlindageProperties
            (
                ResistanceFront = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceSides = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceRear = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceTop = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ExplosiveReactiveArmor = False
            )
            KillWhenDamagesReachMax = True
            HitRollECM = 0.0
            AutoOrientation = False
            UseTopArmorAgainstFire = False
        ),
        ~/TargetCoordinatorModuleSelector,
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        ~/BuildingScannerConfigurationDescriptor,
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 1.0
                TimeBetweenEachIdentifyRoll = 1.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        ApparenceModel is BuildingApparenceModuleDescriptor
        (
            BlackHoleIdentifier = "FOB_SOV"
            SymbolName = 'FOB_Pact_03'
        ),
        TCadavreGeneratorModuleDescriptor( CadavreDescriptor = ~/Descriptor_UnitCadavre_FOB_SOV ),
        TIAStratModuleDescriptor
        (
            DatabaseId = 0
            GameplayBehavior = EGameplayBehavior/Nothing
        ),
        ~/IAStratZoneIndexModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/SelectionModuleDescriptor,
        ~/UnitStateEngineCompanyModuleDescriptor,
        ~/UnitStateEngineModuleDescriptor,
        TProductionModuleDescriptor
        (
            Factory            = EDefaultFactories/Logistic
            ProductionTime     = -1
            ProductionRessourcesNeeded = MAP [
                ($/GFX/Resources/Resource_CommandPoints, 175),
            ]
        ),
        ~/EffectApplierModuleDescriptor,
        TTacticalLabelModuleDescriptor
        (
            IdentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_fob",
                    "Texture_fob"
                ]
            )
            UnidentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_NATO_Unidentified",
                    "Texture_NATO_Unidentified"
                ]
            )
            PositionHeightOffset = 1000.0
        ),
        ~/BuildingOrderConfigModuleDescriptor,
        ~/OrderDisplayModuleDescriptor,
        ~/GroupableUnitModuleSelector,
        ~/PackSignauxModuleDescriptor,
        TUnitUpkeepModuleDescriptor( UpkeepPercentage = 0.0 ),
        TUnitUIModuleDescriptor
        (
            UnitRole = 'supply'
            PriceCategory = 'tank_A'
            NameToken = 'FOBSOV'
            InfoPanelConfigurationToken = 'FOB'
            GenerateName = true
            MenuIconTexture = 'Texture_RTS_H_fob'
            ButtonTexture = 'Texture_Button_Unit_FOB_SOV'
            CountryTexture = 'CommonTexture_MotherCountryFlag_SOV'
            TypeStrategicCount = ETypeStrategicDetailedCount/NotCounted
        ),
    ]
)
export Descriptor_Unit_FOB_UK is TEntityDescriptor
(
    DescriptorId       = GUID:{8b92f939-379b-45d6-b0be-140b5bb78a58}
    ClassNameForDebug  = 'Building_FOB_UK'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            MotherCountry                    = 'UK'
            Coalition                        = ECoalition/Allied
        ),
        ~/BuildingDescriptorTagsModuleDescriptor,
        ~/BuildingFlagsModuleDescriptor,
        ~/BuildingPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TExperienceModuleDescriptor
        (
            ExperienceLevelsPackDescriptor = ~/ExperienceLevelsPackDescriptor_XP_pack_simple_v3
            ExperienceMultiplierBonusOnKill = ~/ExperienceMultiplierBonusOnKill
        ),
        ~/FOBBuildingModuleDescriptor,
        TSupplyModuleDescriptor
        (
            SupplyDescriptor   = $/GFX/Weapon/StandardSupply
            SupplyCapacity     = 16000.0
            SupplyPriority     = 0
        ),
        ~/CapturableModuleDescriptor,
        TBaseDamageModuleDescriptor
        (
            MaxPhysicalDamages = 10
            MaxSuppressionDamages = ~/Building_MaxSuppressionDamages
            MaxStunDamages = ~/Building_MaxStunDamages
            PhysicalDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_degats_phy
            SuppressDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_suppression
            StunDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_stun
        ),
        TDamageModuleDescriptor
        (
            SuppressDamagesRegenRatio = ~/Building_SuppressDamagesRegenRatioList
            StunDamagesRegen = ~/Building_StunDamagesRegen
            BlindageProperties = TBlindageProperties
            (
                ResistanceFront = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceSides = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceRear = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceTop = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ExplosiveReactiveArmor = False
            )
            KillWhenDamagesReachMax = True
            HitRollECM = 0.0
            AutoOrientation = False
            UseTopArmorAgainstFire = False
        ),
        ~/TargetCoordinatorModuleSelector,
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        ~/BuildingScannerConfigurationDescriptor,
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 1.0
                TimeBetweenEachIdentifyRoll = 1.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        ApparenceModel is BuildingApparenceModuleDescriptor
        (
            BlackHoleIdentifier = "FOB_UK"
            SymbolName = 'FOB_Nato_03'
        ),
        TCadavreGeneratorModuleDescriptor( CadavreDescriptor = ~/Descriptor_UnitCadavre_FOB_UK ),
        TIAStratModuleDescriptor
        (
            DatabaseId = 0
            GameplayBehavior = EGameplayBehavior/Nothing
        ),
        ~/IAStratZoneIndexModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/SelectionModuleDescriptor,
        ~/UnitStateEngineCompanyModuleDescriptor,
        ~/UnitStateEngineModuleDescriptor,
        TProductionModuleDescriptor
        (
            Factory            = EDefaultFactories/Logistic
            ProductionTime     = -1
            ProductionRessourcesNeeded = MAP [
                ($/GFX/Resources/Resource_CommandPoints, 175),
            ]
        ),
        ~/EffectApplierModuleDescriptor,
        TTacticalLabelModuleDescriptor
        (
            IdentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_fob",
                    "Texture_fob"
                ]
            )
            UnidentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_NATO_Unidentified",
                    "Texture_NATO_Unidentified"
                ]
            )
            PositionHeightOffset = 1000.0
        ),
        ~/BuildingOrderConfigModuleDescriptor,
        ~/OrderDisplayModuleDescriptor,
        ~/GroupableUnitModuleSelector,
        ~/PackSignauxModuleDescriptor,
        TUnitUpkeepModuleDescriptor( UpkeepPercentage = 0.0 ),
        TUnitUIModuleDescriptor
        (
            UnitRole = 'supply'
            PriceCategory = 'tank_A'
            NameToken = 'FOBUSUK'
            InfoPanelConfigurationToken = 'FOB'
            GenerateName = true
            MenuIconTexture = 'Texture_RTS_H_fob'
            ButtonTexture = 'Texture_Button_Unit_FOB_UK'
            CountryTexture = 'CommonTexture_MotherCountryFlag_UK'
            TypeStrategicCount = ETypeStrategicDetailedCount/NotCounted
        ),
    ]
)
export Descriptor_Unit_FOB_US is TEntityDescriptor
(
    DescriptorId       = GUID:{a109415b-a688-405a-8dd8-df23d128a007}
    ClassNameForDebug  = 'Building_FOB_US'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            MotherCountry                    = 'US'
            Coalition                        = ECoalition/Allied
        ),
        ~/BuildingDescriptorTagsModuleDescriptor,
        ~/BuildingFlagsModuleDescriptor,
        ~/BuildingPositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        TExperienceModuleDescriptor
        (
            ExperienceLevelsPackDescriptor = ~/ExperienceLevelsPackDescriptor_XP_pack_simple_v3
            ExperienceMultiplierBonusOnKill = ~/ExperienceMultiplierBonusOnKill
        ),
        ~/FOBBuildingModuleDescriptor,
        TSupplyModuleDescriptor
        (
            SupplyDescriptor   = $/GFX/Weapon/StandardSupply
            SupplyCapacity     = 16000.0
            SupplyPriority     = 0
        ),
        ~/CapturableModuleDescriptor,
        TBaseDamageModuleDescriptor
        (
            MaxPhysicalDamages = 10
            MaxSuppressionDamages = ~/Building_MaxSuppressionDamages
            MaxStunDamages = ~/Building_MaxStunDamages
            PhysicalDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_degats_phy
            SuppressDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_suppression
            StunDamageLevelsPack = ~/DamageLevelsPackDescriptor_Default_pack_paliers_stun
        ),
        TDamageModuleDescriptor
        (
            SuppressDamagesRegenRatio = ~/Building_SuppressDamagesRegenRatioList
            StunDamagesRegen = ~/Building_StunDamagesRegen
            BlindageProperties = TBlindageProperties
            (
                ResistanceFront = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceSides = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceRear = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ResistanceTop = TResistanceTypeRTTI(Family=ResistanceFamily_batiment Index=1)
                ExplosiveReactiveArmor = False
            )
            KillWhenDamagesReachMax = True
            HitRollECM = 0.0
            AutoOrientation = False
            UseTopArmorAgainstFire = False
        ),
        ~/TargetCoordinatorModuleSelector,
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        ~/BuildingScannerConfigurationDescriptor,
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                IdentifyBaseProbability     = 1.0
                TimeBetweenEachIdentifyRoll = 1.0
                VisibilityRuleDescriptor    = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        ApparenceModel is BuildingApparenceModuleDescriptor
        (
            BlackHoleIdentifier = "FOB_US"
            SymbolName = 'FOB_Nato_03'
        ),
        TCadavreGeneratorModuleDescriptor( CadavreDescriptor = ~/Descriptor_UnitCadavre_FOB_US ),
        TIAStratModuleDescriptor
        (
            DatabaseId = 0
            GameplayBehavior = EGameplayBehavior/Nothing
        ),
        ~/IAStratZoneIndexModuleDescriptor,
        ~/DebugModuleDescriptor,
        ~/SelectionModuleDescriptor,
        ~/UnitStateEngineCompanyModuleDescriptor,
        ~/UnitStateEngineModuleDescriptor,
        TProductionModuleDescriptor
        (
            Factory            = EDefaultFactories/Logistic
            ProductionTime     = -1
            ProductionRessourcesNeeded = MAP [
                ($/GFX/Resources/Resource_CommandPoints, 175),
            ]
        ),
        ~/EffectApplierModuleDescriptor,
        TTacticalLabelModuleDescriptor
        (
            IdentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_fob",
                    "Texture_fob"
                ]
            )
            UnidentifiedTexture = TBUCKToolAlternativeValues_TUIValueTextureNameFromTEugBMutableInteger
            (
                CommandNameTrigger = ~/SpecificCommandName/UpdateGUIFromIconType
                Alterator = $/GUIOption/IconType
                Values =
                [
                    "Texture_RTS_H_NATO_Unidentified",
                    "Texture_NATO_Unidentified"
                ]
            )
            PositionHeightOffset = 1000.0
        ),
        ~/BuildingOrderConfigModuleDescriptor,
        ~/OrderDisplayModuleDescriptor,
        ~/GroupableUnitModuleSelector,
        ~/PackSignauxModuleDescriptor,
        TUnitUpkeepModuleDescriptor( UpkeepPercentage = 0.0 ),
        TUnitUIModuleDescriptor
        (
            UnitRole = 'supply'
            PriceCategory = 'tank_A'
            NameToken = 'FOBUSUK'
            InfoPanelConfigurationToken = 'FOB'
            GenerateName = true
            MenuIconTexture = 'Texture_RTS_H_fob'
            ButtonTexture = 'Texture_Button_Unit_FOB_US'
            CountryTexture = 'CommonTexture_MotherCountryFlag_US'
            TypeStrategicCount = ETypeStrategicDetailedCount/NotCounted
        ),
    ]
)

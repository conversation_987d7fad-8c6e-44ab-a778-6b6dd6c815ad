// Ne pas éditer, ce fichier est généré par MissileCarriageFileWriter

eAAM is 1
eAGM is 2
eMountingMissile is 0
eMountingPod     is 1
eMountingBomb    is 2


// 2K11_KRUG_DDR : ['SAM 9M8M3 x2']
export MissileCarriage_2K11_KRUG_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K11_KRUG_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_2K11_KRUG_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K11_KRUG_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// 2K11_KRUG_POL : ['SAM 9M8M3 x2']
export MissileCarriage_2K11_KRUG_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K11_KRUG_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_2K11_KRUG_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K11_KRUG_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// 2K11_KRUG_SOV : ['SAM 9M8M3 x2']
export MissileCarriage_2K11_KRUG_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K11_KRUG_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_2K11_KRUG_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K11_KRUG_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// 2K12_KUB_DDR : ['SAM 9M336 x3']
export MissileCarriage_2K12_KUB_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K12_KUB_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_2K12_KUB_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K12_KUB_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// 2K12_KUB_POL : ['SAM 9M336 x3']
export MissileCarriage_2K12_KUB_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K12_KUB_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_2K12_KUB_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K12_KUB_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// 2K12_KUB_SOV : ['SAM 9M336 x3']
export MissileCarriage_2K12_KUB_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K12_KUB_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_2K12_KUB_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K12_KUB_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// AIFV_B_MILAN_BEL : ['ATGM MILAN']
export MissileCarriage_AIFV_B_MILAN_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AIFV_B_MILAN_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_AIFV_B_MILAN_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AIFV_B_MILAN_BEL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// AIFV_B_TOW_NL : ['ATGM BGM71D TOW 2 x2']
export MissileCarriage_AIFV_B_TOW_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AIFV_B_TOW_NL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_AIFV_B_TOW_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AIFV_B_TOW_NL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// AMX_10_HOT_FR : ['ATGM HOT2 x4']
export MissileCarriage_AMX_10_HOT_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AMX_10_HOT_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_AMX_10_HOT_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AMX_10_HOT_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// AMX_10_P_MILAN_FR : ['ATGM MILAN']
export MissileCarriage_AMX_10_P_MILAN_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AMX_10_P_MILAN_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_AMX_10_P_MILAN_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AMX_10_P_MILAN_FR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// AMX_13_mod56_MILAN_BEL : ['ATGM MILAN']
export MissileCarriage_AMX_13_mod56_MILAN_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AMX_13_mod56_MILAN_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_AMX_13_mod56_MILAN_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AMX_13_mod56_MILAN_BEL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// AT_T12R_Ruta_SOV : ['ATGM 9M117 Bastion']
export MissileCarriage_AT_T12R_Ruta_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AT_T12R_Ruta_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_AT_T12R_Ruta_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AT_T12R_Ruta_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// AT_T12_Rapira_DDR : ['ATGM 9M117 Bastion']
export MissileCarriage_AT_T12_Rapira_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AT_T12_Rapira_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_AT_T12_Rapira_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AT_T12_Rapira_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// AT_T12_Rapira_SOV : ['ATGM 9M117 Bastion']
export MissileCarriage_AT_T12_Rapira_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AT_T12_Rapira_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_AT_T12_Rapira_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AT_T12_Rapira_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Fagot_DDR : ['ATGM 9K111M Fagot_M']
export MissileCarriage_ATteam_Fagot_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Fagot_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Fagot_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Fagot_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Fagot_FJ_DDR : ['ATGM 9K111M Fagot_M']
export MissileCarriage_ATteam_Fagot_FJ_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Fagot_FJ_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Fagot_FJ_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Fagot_FJ_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Fagot_SOV : ['ATGM 9K111M Fagot_M']
export MissileCarriage_ATteam_Fagot_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Fagot_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Fagot_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Fagot_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_ITOW_NG_US : ['ATGM BGM71C ITOW']
export MissileCarriage_ATteam_ITOW_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_ITOW_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_ITOW_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_ITOW_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_ITOW_NL : ['ATGM BGM71C ITOW']
export MissileCarriage_ATteam_ITOW_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_ITOW_NL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_ITOW_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_ITOW_NL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_ITOW_US : ['ATGM BGM71C ITOW']
export MissileCarriage_ATteam_ITOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_ITOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_ITOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_ITOW_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_KonkursM_TTsko_SOV : ['ATGM 9M113 KonkursM_late']
export MissileCarriage_ATteam_KonkursM_TTsko_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_KonkursM_TTsko_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_KonkursM_TTsko_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_KonkursM_TTsko_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Konkurs_DDR : ['ATGM 9M113 KonkursM']
export MissileCarriage_ATteam_Konkurs_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Konkurs_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Konkurs_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Konkurs_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Konkurs_SOV : ['ATGM 9M113 KonkursM']
export MissileCarriage_ATteam_Konkurs_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Konkurs_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Konkurs_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Konkurs_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Konkurs_TTsko_SOV : ['ATGM 9M113 KonkursM']
export MissileCarriage_ATteam_Konkurs_TTsko_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Konkurs_TTsko_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Konkurs_TTsko_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Konkurs_TTsko_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_1_BEL : ['ATGM MILAN']
export MissileCarriage_ATteam_Milan_1_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_1_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_BEL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_1_FR : ['ATGM MILAN']
export MissileCarriage_ATteam_Milan_1_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_1_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_FR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_1_RFA : ['ATGM MILAN']
export MissileCarriage_ATteam_Milan_1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_1_UK : ['ATGM MILAN']
export MissileCarriage_ATteam_Milan_1_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_1_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_UK
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_1_para_BEL : ['ATGM MILAN']
export MissileCarriage_ATteam_Milan_1_para_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_para_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_1_para_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_para_BEL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_1_para_FR : ['ATGM MILAN']
export MissileCarriage_ATteam_Milan_1_para_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_para_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_1_para_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_para_FR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_2_BEL : ['ATGM MILAN 2']
export MissileCarriage_ATteam_Milan_2_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_2_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_BEL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_2_FR : ['ATGM MILAN 2']
export MissileCarriage_ATteam_Milan_2_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_2_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_FR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_2_RFA : ['ATGM MILAN 2']
export MissileCarriage_ATteam_Milan_2_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_2_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_2_RIMa_FR : ['ATGM MILAN 2']
export MissileCarriage_ATteam_Milan_2_RIMa_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_RIMa_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_2_RIMa_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_RIMa_FR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_2_UK : ['ATGM MILAN 2']
export MissileCarriage_ATteam_Milan_2_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_2_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_UK
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_2_para_BEL : ['ATGM MILAN 2']
export MissileCarriage_ATteam_Milan_2_para_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_para_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_2_para_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_para_BEL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_2_para_FR : ['ATGM MILAN 2']
export MissileCarriage_ATteam_Milan_2_para_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_para_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_2_para_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_para_FR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_2_para_RFA : ['ATGM MILAN 2']
export MissileCarriage_ATteam_Milan_2_para_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_para_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_2_para_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_para_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_Milan_2_para_UK : ['ATGM MILAN 2']
export MissileCarriage_ATteam_Milan_2_para_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_para_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_Milan_2_para_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_para_UK
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_TOW2A_US : ['ATGM BGM71D TOW 2A']
export MissileCarriage_ATteam_TOW2A_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2A_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_TOW2A_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2A_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_TOW2_Aero_US : ['ATGM BGM71D TOW 2']
export MissileCarriage_ATteam_TOW2_Aero_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2_Aero_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_TOW2_Aero_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2_Aero_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_TOW2_NL : ['ATGM BGM71D TOW 2']
export MissileCarriage_ATteam_TOW2_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2_NL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_TOW2_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2_NL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_TOW2_US : ['ATGM BGM71D TOW 2']
export MissileCarriage_ATteam_TOW2_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_TOW2_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_TOW2_para_US : ['ATGM BGM71D TOW 2']
export MissileCarriage_ATteam_TOW2_para_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2_para_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_TOW2_para_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2_para_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_TOW_NL : ['ATGM BGM71 TOW']
export MissileCarriage_ATteam_TOW_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW_NL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_TOW_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW_NL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// ATteam_TOW_US : ['ATGM BGM71 TOW']
export MissileCarriage_ATteam_TOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_ATteam_TOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Atteam_Dragon_Marines_NL : ['M47_DRAGON Bipied']
export MissileCarriage_Atteam_Dragon_Marines_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Dragon_Marines_NL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Atteam_Dragon_Marines_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Dragon_Marines_NL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Atteam_Fagot_DShV_SOV : ['ATGM 9K111M Fagot_M']
export MissileCarriage_Atteam_Fagot_DShV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Fagot_DShV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Atteam_Fagot_DShV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Fagot_DShV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Atteam_Fagot_POL : ['ATGM 9K111M Fagot_M']
export MissileCarriage_Atteam_Fagot_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Fagot_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Atteam_Fagot_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Fagot_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Atteam_Fagot_Para_POL : ['ATGM 9K111M Fagot_M']
export MissileCarriage_Atteam_Fagot_Para_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Fagot_Para_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Atteam_Fagot_Para_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Fagot_Para_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Atteam_Fagot_VDV_SOV : ['ATGM 9K111M Fagot_M']
export MissileCarriage_Atteam_Fagot_VDV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Fagot_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Atteam_Fagot_VDV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Fagot_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Atteam_Konkurs_DShV_SOV : ['ATGM 9M113 KonkursM']
export MissileCarriage_Atteam_Konkurs_DShV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Konkurs_DShV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Atteam_Konkurs_DShV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Konkurs_DShV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Atteam_Konkurs_POL : ['ATGM 9M113 KonkursM']
export MissileCarriage_Atteam_Konkurs_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Konkurs_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Atteam_Konkurs_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Konkurs_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Atteam_Konkurs_VDV_SOV : ['ATGM 9M113 KonkursM']
export MissileCarriage_Atteam_Konkurs_VDV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Konkurs_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Atteam_Konkurs_VDV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Konkurs_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// BMD_1P_SOV : ['ATGM 9K111M Fagot_M']
export MissileCarriage_BMD_1P_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_1P_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMD_1P_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_1P_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMD_2_CMD_SOV : ['ATGM 9M113 Konkurs BMP2']
export MissileCarriage_BMD_2_CMD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_2_CMD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMD_2_CMD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_2_CMD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMD_2_SOV : ['ATGM 9M113 Konkurs BMP2']
export MissileCarriage_BMD_2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_2_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMD_2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_2_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMD_3_SOV : ['ATGM 9M113 KonkursM_late']
export MissileCarriage_BMD_3_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_3_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMD_3_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_3_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMD_3_reco_SOV : ['ATGM 9M113 KonkursM_late']
export MissileCarriage_BMD_3_reco_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_3_reco_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMD_3_reco_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_3_reco_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_1PG_SOV : ['ATGM 9K111M Fagot_M']
export MissileCarriage_BMP_1PG_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1PG_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMP_1PG_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1PG_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_1P_DDR : ['ATGM 9K111M Fagot_M']
export MissileCarriage_BMP_1P_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_BMP_1P_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
// BMP_1P_Konkurs_DDR : ['ATGM 9M113 Konkurs BMP2']
export MissileCarriage_BMP_1P_Konkurs_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_Konkurs_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMP_1P_Konkurs_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_Konkurs_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_1P_Konkurs_SOV : ['ATGM 9M113 Konkurs BMP2']
export MissileCarriage_BMP_1P_Konkurs_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_Konkurs_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMP_1P_Konkurs_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_Konkurs_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_1P_SOV : ['ATGM 9K111M Fagot_M']
export MissileCarriage_BMP_1P_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMP_1P_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_1P_reco_DDR : ['ATGM 9M14 MalyutkaP']
export MissileCarriage_BMP_1P_reco_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_reco_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_BMP_1P_reco_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_reco_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
// BMP_1P_reco_POL : ['ATGM 9K111M Fagot_M']
export MissileCarriage_BMP_1P_reco_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_reco_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_BMP_1P_reco_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_reco_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
// BMP_1P_reco_SOV : ['ATGM 9K111M Fagot_M']
export MissileCarriage_BMP_1P_reco_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_reco_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMP_1P_reco_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_reco_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_1_CMD_DDR : ['ATGM 9M14 MalyutkaP']
export MissileCarriage_BMP_1_CMD_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_CMD_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_BMP_1_CMD_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_CMD_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
// BMP_1_CMD_POL : ['ATGM 9M14 MalyutkaP']
export MissileCarriage_BMP_1_CMD_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_CMD_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_BMP_1_CMD_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_CMD_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
// BMP_1_CMD_SOV : ['ATGM 9M14 MalyutkaP']
export MissileCarriage_BMP_1_CMD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_CMD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_BMP_1_CMD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_CMD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
// BMP_1_SP2_DDR : ['ATGM 9M14 MalyutkaP']
export MissileCarriage_BMP_1_SP2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_SP2_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_BMP_1_SP2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_SP2_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
// BMP_1_SP2_POL : ['ATGM 9M14 MalyutkaP']
export MissileCarriage_BMP_1_SP2_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_SP2_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_BMP_1_SP2_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_SP2_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
// BMP_1_SP2_SOV : ['ATGM 9M14 MalyutkaP']
export MissileCarriage_BMP_1_SP2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_SP2_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_BMP_1_SP2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_SP2_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
// BMP_1_SP2_reco_POL : ['ATGM 9M14 MalyutkaP']
export MissileCarriage_BMP_1_SP2_reco_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_SP2_reco_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_BMP_1_SP2_reco_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_SP2_reco_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 4
        ),
    ]
)
// BMP_2AG_SOV : ['ATGM 9M113 Konkurs BMP2']
export MissileCarriage_BMP_2AG_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2AG_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMP_2AG_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2AG_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_2D_SOV : ['ATGM 9M113 Konkurs BMP2']
export MissileCarriage_BMP_2D_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2D_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMP_2D_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2D_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_2D_reco_SOV : ['ATGM 9M113 Konkurs BMP2']
export MissileCarriage_BMP_2D_reco_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2D_reco_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMP_2D_reco_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2D_reco_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_2_CMD_SOV : ['ATGM 9M113 Konkurs BMP2']
export MissileCarriage_BMP_2_CMD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_CMD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMP_2_CMD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_CMD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_2_DDR : ['ATGM 9M113 Konkurs BMP2']
export MissileCarriage_BMP_2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMP_2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_2_POL : ['ATGM 9M113 Konkurs BMP2']
export MissileCarriage_BMP_2_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMP_2_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_2_SOV : ['ATGM 9M113 Konkurs BMP2']
export MissileCarriage_BMP_2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMP_2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_2_reco_SOV : ['ATGM 9M113 Konkurs BMP2']
export MissileCarriage_BMP_2_reco_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_reco_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BMP_2_reco_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_reco_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// BMP_3_SOV : ['ATGM 9M117 Bastion']
export MissileCarriage_BMP_3_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_3_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_BMP_3_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_3_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// BRDM_2_Konkurs_M_SOV : ['ATGM 9M113 KonkursM_late x5']
export MissileCarriage_BRDM_2_Konkurs_M_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Konkurs_M_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_BRDM_2_Konkurs_M_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Konkurs_M_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// BRDM_2_Konkurs_POL : ['ATGM 9M113 Konkurs x5']
export MissileCarriage_BRDM_2_Konkurs_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Konkurs_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_BRDM_2_Konkurs_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Konkurs_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// BRDM_2_Konkurs_SOV : ['ATGM 9M113 Konkurs x5']
export MissileCarriage_BRDM_2_Konkurs_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Konkurs_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_BRDM_2_Konkurs_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Konkurs_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// BRDM_2_Malyu_P_POL : ['ATGM 9M14 MalyutkaP x6']
export MissileCarriage_BRDM_2_Malyu_P_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Malyu_P_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 18
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_BRDM_2_Malyu_P_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Malyu_P_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 18
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// BRDM_2_Malyu_P_SOV : ['ATGM 9M14 MalyutkaP x6']
export MissileCarriage_BRDM_2_Malyu_P_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Malyu_P_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 18
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_BRDM_2_Malyu_P_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Malyu_P_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 18
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// BRDM_Konkurs_DDR : ['ATGM 9M113 Konkurs x5']
export MissileCarriage_BRDM_Konkurs_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Konkurs_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_BRDM_Konkurs_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Konkurs_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// BRDM_Malyu_P_DDR : ['ATGM 9M14 MalyutkaP x6']
export MissileCarriage_BRDM_Malyu_P_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Malyu_P_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 18
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_BRDM_Malyu_P_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Malyu_P_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 18
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// BRDM_Strela_1_DDR : ['SAM Strela1 x4']
export MissileCarriage_BRDM_Strela_1_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Strela_1_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_BRDM_Strela_1_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Strela_1_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// BRDM_Strela_1_POL : ['SAM Strela1 x4']
export MissileCarriage_BRDM_Strela_1_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Strela_1_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_BRDM_Strela_1_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Strela_1_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// BRDM_Strela_1_SOV : ['SAM Strela1 x4']
export MissileCarriage_BRDM_Strela_1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Strela_1_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_BRDM_Strela_1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Strela_1_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// BTR_70_S5_SOV : ['RocketAir S5 57mm x16 BTR']
export MissileCarriage_BTR_70_S5_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BTR_70_S5_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BTR_70_S5_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BTR_70_S5_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// BTR_70_S8_SOV : ['RocketAir B8 80mm x20 BTR']
export MissileCarriage_BTR_70_S8_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BTR_70_S8_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_BTR_70_S8_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BTR_70_S8_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// BTR_D_Robot_SOV : ['ATGM 9M113 KonkursM']
export MissileCarriage_BTR_D_Robot_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BTR_D_Robot_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_BTR_D_Robot_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BTR_D_Robot_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Buk_9K37M_SOV : ['SAM 9M38M1 x4']
export MissileCarriage_Buk_9K37M_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buk_9K37M_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Buk_9K37M_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buk_9K37M_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// CUCV_Hellfire_US : ['AGM AGM114A x2 sol']
export MissileCarriage_CUCV_Hellfire_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_CUCV_Hellfire_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_CUCV_Hellfire_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_CUCV_Hellfire_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Crotale_FR : ['SAM R440 x4']
export MissileCarriage_Crotale_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Crotale_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Crotale_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Crotale_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// DCA_FASTA_4_DDR : ['SAM FASTA Strela2M x4 TOWED']
export MissileCarriage_DCA_FASTA_4_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_FASTA_4_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_DCA_FASTA_4_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_FASTA_4_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// DCA_I_Hawk_BEL : ['SAM I Hawk x3']
export MissileCarriage_DCA_I_Hawk_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_BEL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_DCA_I_Hawk_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_BEL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// DCA_I_Hawk_NL : ['SAM I Hawk x3']
export MissileCarriage_DCA_I_Hawk_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_NL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_DCA_I_Hawk_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_NL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// DCA_I_Hawk_RFA : ['SAM I Hawk x3']
export MissileCarriage_DCA_I_Hawk_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_RFA
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_DCA_I_Hawk_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_RFA
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// DCA_I_Hawk_US : ['SAM I Hawk x3']
export MissileCarriage_DCA_I_Hawk_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_DCA_I_Hawk_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// DCA_I_Hawk_capture_DDR : ['SAM I Hawk x3']
export MissileCarriage_DCA_I_Hawk_capture_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_capture_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_DCA_I_Hawk_capture_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_capture_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// DCA_Javelin_LML_UK : ['Javelin_LML']
export MissileCarriage_DCA_Javelin_LML_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_Javelin_LML_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 9
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_DCA_Javelin_LML_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_Javelin_LML_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 9
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// DCA_Rapier_Darkfire_UK : ['SAM RAPIER DARKFIRE x6']
export MissileCarriage_DCA_Rapier_Darkfire_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_Rapier_Darkfire_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_DCA_Rapier_Darkfire_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_Rapier_Darkfire_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// DCA_Rapier_FSA_UK : ['SAM RAPIER FSA x4']
export MissileCarriage_DCA_Rapier_FSA_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_Rapier_FSA_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_DCA_Rapier_FSA_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_Rapier_FSA_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// DCA_Rapier_UK : ['SAM RAPIER x4']
export MissileCarriage_DCA_Rapier_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_Rapier_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_DCA_Rapier_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_Rapier_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// DCA_XM85_Chaparral_US : ['SAM MIM72G']
export MissileCarriage_DCA_XM85_Chaparral_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_XM85_Chaparral_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_DCA_XM85_Chaparral_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_XM85_Chaparral_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// DCA_XMIM_115A_Roland_US : ['SAM ROLAND 3 x2']
export MissileCarriage_DCA_XMIM_115A_Roland_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_XMIM_115A_Roland_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_DCA_XMIM_115A_Roland_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_XMIM_115A_Roland_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// DCA_ZUR_23_2S_JOD_POL : ['SAM Strela2M x2']
export MissileCarriage_DCA_ZUR_23_2S_JOD_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_ZUR_23_2S_JOD_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_DCA_ZUR_23_2S_JOD_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_ZUR_23_2S_JOD_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// DCA_ZUR_23_2S_JOD_Para_POL : ['SAM Strela2M x2']
export MissileCarriage_DCA_ZUR_23_2S_JOD_Para_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_ZUR_23_2S_JOD_Para_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_DCA_ZUR_23_2S_JOD_Para_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_ZUR_23_2S_JOD_Para_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// FAV_TOW_US : ['ATGM BGM71D TOW 2']
export MissileCarriage_FAV_TOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FAV_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_FAV_TOW_US_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_FAV_TOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FAV_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_FAV_TOW_US_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// FV102_Striker_BEL : ['ATGM Swingfire x5']
export MissileCarriage_FV102_Striker_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV102_Striker_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_FV102_Striker_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV102_Striker_BEL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// FV102_Striker_UK : ['ATGM Swingfire x5']
export MissileCarriage_FV102_Striker_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV102_Striker_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_FV102_Striker_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV102_Striker_UK
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// FV102_Striker_para_UK : ['ATGM Swingfire x5']
export MissileCarriage_FV102_Striker_para_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV102_Striker_para_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_FV102_Striker_para_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV102_Striker_para_UK
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// FV120_Spartan_MCT_UK : ['ATGM MILAN 2 x2']
export MissileCarriage_FV120_Spartan_MCT_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV120_Spartan_MCT_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_FV120_Spartan_MCT_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV120_Spartan_MCT_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// FV432_MILAN_UK : ['ATGM MILAN']
export MissileCarriage_FV432_MILAN_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV432_MILAN_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_FV432_MILAN_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV432_MILAN_UK
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// FV438_Swingfire_UK : ['ATGM Swingfire x2']
export MissileCarriage_FV438_Swingfire_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV438_Swingfire_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_FV438_Swingfire_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV438_Swingfire_UK
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Faun_Kraka_TOW_RFA : ['ATGM BGM71C ITOW']
export MissileCarriage_Faun_Kraka_TOW_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Faun_Kraka_TOW_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MeshDescriptor = $/GFX/DepictionResources/Modele_Faun_Kraka_TOW_RFA_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Faun_Kraka_TOW_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Faun_Kraka_TOW_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MeshDescriptor = $/GFX/DepictionResources/Modele_Faun_Kraka_TOW_RFA_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Hibneryt_KG_POL : ['SAM Strela2M x2']
export MissileCarriage_Hibneryt_KG_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Hibneryt_KG_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Hibneryt_KG_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Hibneryt_KG_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// Iltis_MILAN_2_RFA : ['ATGM MILAN 2']
export MissileCarriage_Iltis_MILAN_2_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_RFA_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Iltis_MILAN_2_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_RFA_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Iltis_MILAN_BEL : ['ATGM MILAN 2']
export MissileCarriage_Iltis_MILAN_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_BEL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_BEL_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Iltis_MILAN_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_BEL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_BEL_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Iltis_MILAN_RFA : ['ATGM MILAN']
export MissileCarriage_Iltis_MILAN_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_RFA_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Iltis_MILAN_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_RFA_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Jaguar_1_RFA : ['ATGM HOT1']
export MissileCarriage_Jaguar_1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_1_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_1_RFA_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Jaguar_1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_1_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_1_RFA_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Jaguar_2_RFA : ['ATGM BGM71D TOW 2']
export MissileCarriage_Jaguar_2_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_2_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_2_RFA_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Jaguar_2_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_2_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_2_RFA_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// LO_1800_FASTA_4_DDR : ['SAM FASTA Strela2M x4']
export MissileCarriage_LO_1800_FASTA_4_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LO_1800_FASTA_4_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_LO_1800_FASTA_4_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LO_1800_FASTA_4_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// LSV_MILAN_UK : ['ATGM MILAN 2']
export MissileCarriage_LSV_MILAN_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LSV_MILAN_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_LSV_MILAN_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LSV_MILAN_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// LUAZ_967M_Fagot_SOV : ['ATGM 9K111M Fagot_M']
export MissileCarriage_LUAZ_967M_Fagot_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LUAZ_967M_Fagot_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_LUAZ_967M_Fagot_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LUAZ_967M_Fagot_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// LUAZ_967M_Fagot_VDV_SOV : ['ATGM 9K111M Fagot_M']
export MissileCarriage_LUAZ_967M_Fagot_VDV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LUAZ_967M_Fagot_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_LUAZ_967M_Fagot_VDV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LUAZ_967M_Fagot_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// LandRover_MILAN_Para_UK : ['ATGM MILAN 2']
export MissileCarriage_LandRover_MILAN_Para_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LandRover_MILAN_Para_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_LandRover_MILAN_Para_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LandRover_MILAN_Para_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// LandRover_MILAN_UK : ['ATGM MILAN 2']
export MissileCarriage_LandRover_MILAN_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LandRover_MILAN_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_LandRover_MILAN_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LandRover_MILAN_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// LuAZ_967M_AA_VDV_SOV : ['SAM Igla']
export MissileCarriage_LuAZ_967M_AA_VDV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LuAZ_967M_AA_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_LuAZ_967M_AA_VDV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LuAZ_967M_AA_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// M1025_Humvee_TOW_LUX : ['ATGM BGM71D TOW 2']
export MissileCarriage_M1025_Humvee_TOW_LUX is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_LUX
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_LUX_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M1025_Humvee_TOW_LUX_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_LUX
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_LUX_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// M1025_Humvee_TOW_US : ['ATGM BGM71D TOW 2']
export MissileCarriage_M1025_Humvee_TOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_US_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M1025_Humvee_TOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_US_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// M1025_Humvee_TOW_para_US : ['ATGM BGM71D TOW 2']
export MissileCarriage_M1025_Humvee_TOW_para_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_para_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_para_US_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M1025_Humvee_TOW_para_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_para_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_para_US_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// M113A1B_MILAN_BEL : ['ATGM MILAN']
export MissileCarriage_M113A1B_MILAN_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1B_MILAN_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_M113A1B_MILAN_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1B_MILAN_BEL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// M113A1G_MILAN_RFA : ['ATGM MILAN']
export MissileCarriage_M113A1G_MILAN_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1G_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1G_MILAN_RFA_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M113A1G_MILAN_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1G_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1G_MILAN_RFA_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// M113A1_Dragon_NG_US : ['M47_DRAGON']
export MissileCarriage_M113A1_Dragon_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1_Dragon_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1_Dragon_NG_US_Tourelle_02
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_M113A1_Dragon_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1_Dragon_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1_Dragon_NG_US_Tourelle_02
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// M113A1_TOW_US : ['ATGM BGM71 TOW']
export MissileCarriage_M113A1_TOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_M113A1_TOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// M113A2_TOW_US : ['ATGM BGM71D TOW 2']
export MissileCarriage_M113A2_TOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A2_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_M113A2_TOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A2_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// M113_Dragon_US : ['M47_DRAGON']
export MissileCarriage_M113_Dragon_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113_Dragon_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_M113_Dragon_US_Tourelle_02
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_M113_Dragon_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113_Dragon_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_M113_Dragon_US_Tourelle_02
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// M151A2_TOW_NG_US : ['ATGM BGM71C ITOW']
export MissileCarriage_M151A2_TOW_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M151A2_TOW_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_M151A2_TOW_NG_US_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M151A2_TOW_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M151A2_TOW_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_M151A2_TOW_NG_US_Tourelle_01
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// M201_MILAN_FR : ['ATGM MILAN']
export MissileCarriage_M201_MILAN_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M201_MILAN_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M201_MILAN_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M201_MILAN_FR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// M274_Mule_ITOW_US : ['ATGM BGM71C ITOW']
export MissileCarriage_M274_Mule_ITOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M274_Mule_ITOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M274_Mule_ITOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M274_Mule_ITOW_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// M2A1_Bradley_IFV_US : ['ATGM BGM71D TOW 2 x2']
export MissileCarriage_M2A1_Bradley_IFV_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2A1_Bradley_IFV_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_M2A1_Bradley_IFV_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2A1_Bradley_IFV_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// M2A1_Bradley_Leader_US : ['ATGM BGM71D TOW 2 x2']
export MissileCarriage_M2A1_Bradley_Leader_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2A1_Bradley_Leader_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_M2A1_Bradley_Leader_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2A1_Bradley_Leader_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// M2A2_Bradley_IFV_US : ['ATGM BGM71D TOW 2A x2']
export MissileCarriage_M2A2_Bradley_IFV_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2A2_Bradley_IFV_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_M2A2_Bradley_IFV_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2A2_Bradley_IFV_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// M2A2_Bradley_Leader_US : ['ATGM BGM71D TOW 2A x2']
export MissileCarriage_M2A2_Bradley_Leader_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2A2_Bradley_Leader_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_M2A2_Bradley_Leader_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2A2_Bradley_Leader_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// M2_Bradley_IFV_NG_US : ['ATGM BGM71C ITOW x2']
export MissileCarriage_M2_Bradley_IFV_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2_Bradley_IFV_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_M2_Bradley_IFV_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2_Bradley_IFV_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// M38A1_TOW_NL : ['ATGM BGM71 TOW']
export MissileCarriage_M38A1_TOW_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M38A1_TOW_NL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M38A1_TOW_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M38A1_TOW_NL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// M3A1_Bradley_CFV_US : ['ATGM BGM71D TOW 2 x2']
export MissileCarriage_M3A1_Bradley_CFV_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M3A1_Bradley_CFV_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_M3A1_Bradley_CFV_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M3A1_Bradley_CFV_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// M3A2_Bradley_CFV_US : ['ATGM BGM71D TOW 2A x2']
export MissileCarriage_M3A2_Bradley_CFV_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M3A2_Bradley_CFV_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_M3A2_Bradley_CFV_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M3A2_Bradley_CFV_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// M48_Chaparral_MIM72F_US : ['SAM MIM72G']
export MissileCarriage_M48_Chaparral_MIM72F_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M48_Chaparral_MIM72F_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M48_Chaparral_MIM72F_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M48_Chaparral_MIM72F_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// M551A1_TTS_Sheridan_CMD_US : ['ATGM MGM551C Shillelagh']
export MissileCarriage_M551A1_TTS_Sheridan_CMD_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M551A1_TTS_Sheridan_CMD_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M551A1_TTS_Sheridan_CMD_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M551A1_TTS_Sheridan_CMD_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// M551A1_TTS_Sheridan_US : ['ATGM MGM551C Shillelagh']
export MissileCarriage_M551A1_TTS_Sheridan_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M551A1_TTS_Sheridan_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M551A1_TTS_Sheridan_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M551A1_TTS_Sheridan_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// M901A1_ITW_US : ['ATGM BGM71D TOW 2 x2']
export MissileCarriage_M901A1_ITW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M901A1_ITW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M901A1_ITW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M901A1_ITW_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// M901_TOW_NG_US : ['ATGM BGM71C ITOW x2']
export MissileCarriage_M901_TOW_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M901_TOW_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M901_TOW_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M901_TOW_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// M901_TOW_US : ['ATGM BGM71C ITOW x2']
export MissileCarriage_M901_TOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M901_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M901_TOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M901_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// M998_Avenger_US : ['SAM FIM92 Stinger x8']
export MissileCarriage_M998_Avenger_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M998_Avenger_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M998_Avenger_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M998_Avenger_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// M998_Avenger_nonPara_US : ['SAM FIM92 Stinger x8']
export MissileCarriage_M998_Avenger_nonPara_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M998_Avenger_nonPara_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_M998_Avenger_nonPara_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M998_Avenger_nonPara_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// MCV_80_Warrior_MILAN_ERA_UK : ['ATGM MILAN 2']
export MissileCarriage_MCV_80_Warrior_MILAN_ERA_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_ERA_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_ERA_UK_Tourelle_02
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MCV_80_Warrior_MILAN_ERA_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_ERA_UK
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_ERA_UK_Tourelle_02
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// MCV_80_Warrior_MILAN_UK : ['ATGM MILAN']
export MissileCarriage_MCV_80_Warrior_MILAN_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_UK_Tourelle_02
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MCV_80_Warrior_MILAN_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_UK
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MeshDescriptor = $/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_UK_Tourelle_02
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// MTLB_Shturm_DDR : ['ATGM 9M114M KokonM']
export MissileCarriage_MTLB_Shturm_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Shturm_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_MTLB_Shturm_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Shturm_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// MTLB_Shturm_SOV : ['ATGM 9M114M KokonM']
export MissileCarriage_MTLB_Shturm_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Shturm_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_MTLB_Shturm_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Shturm_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// MTLB_Strela10M3_SOV : ['SAM Strela10M3 x4']
export MissileCarriage_MTLB_Strela10M3_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Strela10M3_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_MTLB_Strela10M3_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Strela10M3_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// MTLB_Strela10_DDR : ['SAM Strela10 x4']
export MissileCarriage_MTLB_Strela10_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Strela10_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_MTLB_Strela10_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Strela10_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// MTLB_Strela10_POL : ['SAM Strela10 x4']
export MissileCarriage_MTLB_Strela10_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Strela10_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_MTLB_Strela10_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Strela10_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// MTLB_Strela10_SOV : ['SAM Strela10 x4']
export MissileCarriage_MTLB_Strela10_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Strela10_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_MTLB_Strela10_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Strela10_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// Marder_1A1_MILAN_RFA : ['ATGM MILAN']
export MissileCarriage_Marder_1A1_MILAN_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_1A2_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Marder_1A1_MILAN_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_1A2_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Marder_1A2_MILAN_RFA : ['ATGM MILAN']
export MissileCarriage_Marder_1A2_MILAN_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_1A2_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Marder_1A2_MILAN_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_1A2_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Marder_1A3_MILAN_RFA : ['ATGM MILAN']
export MissileCarriage_Marder_1A3_MILAN_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_1A3_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Marder_1A3_MILAN_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_1A3_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Marder_Roland_2_RFA : ['SAM ROLAND 2 x2']
export MissileCarriage_Marder_Roland_2_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_Roland_2_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Marder_Roland_2_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_Roland_2_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// Marder_Roland_RFA : ['SAM ROLAND 3 x2']
export MissileCarriage_Marder_Roland_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_Roland_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Marder_Roland_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_Roland_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// OT_62_TOPAS_JOD_POL : ['SAM Strela2M x2']
export MissileCarriage_OT_62_TOPAS_JOD_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OT_62_TOPAS_JOD_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_OT_62_TOPAS_JOD_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OT_62_TOPAS_JOD_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// OT_64_SKOT_2AM_POL : ['ATGM 9M14 MalyutkaP x2']
export MissileCarriage_OT_64_SKOT_2AM_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OT_64_SKOT_2AM_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_OT_64_SKOT_2AM_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OT_64_SKOT_2AM_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Osa_9K33M3_DDR : ['SAM 9M33M2 x6']
export MissileCarriage_Osa_9K33M3_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Osa_9K33M3_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Osa_9K33M3_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Osa_9K33M3_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// Osa_9K33M3_POL : ['SAM 9M33M2 x6']
export MissileCarriage_Osa_9K33M3_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Osa_9K33M3_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Osa_9K33M3_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Osa_9K33M3_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// Osa_9K33M3_SOV : ['SAM 9M33M2 x6']
export MissileCarriage_Osa_9K33M3_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Osa_9K33M3_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Osa_9K33M3_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Osa_9K33M3_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// Roland_2_FR : ['SAM ROLAND 2 x2']
export MissileCarriage_Roland_2_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Roland_2_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Roland_2_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Roland_2_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// Roland_3_FR : ['SAM ROLAND 3 x2']
export MissileCarriage_Roland_3_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Roland_3_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Roland_3_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Roland_3_FR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// Supacat_ATMP_Javelin_LML_UK : ['Javelin_LML']
export MissileCarriage_Supacat_ATMP_Javelin_LML_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Supacat_ATMP_Javelin_LML_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 9
            MeshDescriptor = $/GFX/DepictionResources/Modele_Supacat_ATMP_Javelin_LML_UK_Tourelle_01
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Supacat_ATMP_Javelin_LML_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Supacat_ATMP_Javelin_LML_UK
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 9
            MeshDescriptor = $/GFX/DepictionResources/Modele_Supacat_ATMP_Javelin_LML_UK_Tourelle_01
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// Supacat_ATMP_MILAN_UK : ['ATGM MILAN 2']
export MissileCarriage_Supacat_ATMP_MILAN_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Supacat_ATMP_MILAN_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 9
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Supacat_ATMP_MILAN_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Supacat_ATMP_MILAN_UK
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 9
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T55AM2B_DDR : ['ATGM 9M117 Bastion']
export MissileCarriage_T55AM2B_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T55AM2B_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T55AM2B_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T55AM2B_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T55AM_1_SOV : ['ATGM 9M117 Bastion']
export MissileCarriage_T55AM_1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T55AM_1_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T55AM_1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T55AM_1_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T62MD_SOV : ['ATGM 9M117 Bastion']
export MissileCarriage_T62MD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T62MD_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T62MD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T62MD_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T62MV_SOV : ['ATGM 9M117 Bastion']
export MissileCarriage_T62MV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T62MV_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T62MV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T62MV_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T62M_SOV : ['ATGM 9M117 Bastion']
export MissileCarriage_T62M_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T62M_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T62M_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T62M_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T64BV_SOV : ['ATGM 9M128 Agona']
export MissileCarriage_T64BV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T64BV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T64BV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T64BV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T64B_SOV : ['ATGM 9M112 1 Kobra']
export MissileCarriage_T64B_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T64B_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T64B_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T64B_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T72S_DDR : ['ATGM 9M119M Svir']
export MissileCarriage_T72S_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T72S_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T72S_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T72S_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T80BV_Beast_SOV : ['ATGM 9M128 Agona']
export MissileCarriage_T80BV_Beast_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80BV_Beast_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T80BV_Beast_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80BV_Beast_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T80BV_SOV : ['ATGM 9M112 1 Kobra']
export MissileCarriage_T80BV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80BV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T80BV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80BV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T80B_SOV : ['ATGM 9M112 1 Kobra']
export MissileCarriage_T80B_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80B_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T80B_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80B_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T80UD_SOV : ['ATGM 9M119M Refleks']
export MissileCarriage_T80UD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80UD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T80UD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80UD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T80U_CMD_SOV : ['ATGM 9M119M Refleks']
export MissileCarriage_T80U_CMD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80U_CMD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T80U_CMD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80U_CMD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// T80U_SOV : ['ATGM 9M119M Refleks']
export MissileCarriage_T80U_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80U_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_T80U_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80U_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// TPZ_Fuchs_MILAN_RFA : ['ATGM MILAN']
export MissileCarriage_TPZ_Fuchs_MILAN_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_TPZ_Fuchs_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MeshDescriptor = $/GFX/DepictionResources/Modele_TPZ_Fuchs_MILAN_RFA_Tourelle_02
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_TPZ_Fuchs_MILAN_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_TPZ_Fuchs_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MeshDescriptor = $/GFX/DepictionResources/Modele_TPZ_Fuchs_MILAN_RFA_Tourelle_02
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Tor_SOV : ['SAM 9M330 Tor x8']
export MissileCarriage_Tor_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tor_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Tor_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tor_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// Tracked_Rapier_UK : ['SAM RAPIER x8']
export MissileCarriage_Tracked_Rapier_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tracked_Rapier_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Tracked_Rapier_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tracked_Rapier_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// Tunguska_2K22_SOV : ['SAM 9M311 Tunguska x8']
export MissileCarriage_Tunguska_2K22_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tunguska_2K22_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Tunguska_2K22_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tunguska_2K22_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// UAZ_469_Fagot_DDR : ['ATGM 9K111M Fagot_M']
export MissileCarriage_UAZ_469_Fagot_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Fagot_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_UAZ_469_Fagot_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Fagot_DDR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// UAZ_469_Fagot_POL : ['ATGM 9K111M Fagot_M']
export MissileCarriage_UAZ_469_Fagot_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Fagot_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_UAZ_469_Fagot_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Fagot_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// UAZ_469_Fagot_Para_POL : ['ATGM 9K111M Fagot_M']
export MissileCarriage_UAZ_469_Fagot_Para_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Fagot_Para_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_UAZ_469_Fagot_Para_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Fagot_Para_POL
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// UAZ_469_Konkurs_VDV_SOV : ['ATGM 9M113 Konkurs BMP2']
export MissileCarriage_UAZ_469_Konkurs_VDV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Konkurs_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_UAZ_469_Konkurs_VDV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Konkurs_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Unimog_U1350L_Para_BEL : ['ATGM MILAN']
export MissileCarriage_Unimog_U1350L_Para_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Unimog_U1350L_Para_BEL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Unimog_U1350L_Para_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Unimog_U1350L_Para_BEL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Ural_4320_Metla_SOV : ['RocketAir S5 57mm x32 BTR']
export MissileCarriage_Ural_4320_Metla_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Ural_4320_Metla_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Ural_4320_Metla_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Ural_4320_Metla_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// VAB_HOT_FR : ['ATGM HOT2 x4']
export MissileCarriage_VAB_HOT_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VAB_HOT_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_VAB_HOT_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VAB_HOT_FR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// VAB_MILAN_FR : ['ATGM MILAN']
export MissileCarriage_VAB_MILAN_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VAB_MILAN_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_VAB_MILAN_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VAB_MILAN_FR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// VBL_MILAN_FR : ['ATGM MILAN 2']
export MissileCarriage_VBL_MILAN_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VBL_MILAN_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_VBL_MILAN_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VBL_MILAN_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// VLRA_MILAN_FR : ['ATGM MILAN 2']
export MissileCarriage_VLRA_MILAN_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VLRA_MILAN_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_VLRA_MILAN_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VLRA_MILAN_FR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// VLRA_Mistral_FR : ['Mistral']
export MissileCarriage_VLRA_Mistral_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VLRA_Mistral_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_VLRA_Mistral_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VLRA_Mistral_FR
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// VLTT_P4_MILAN_FR : ['ATGM MILAN 2']
export MissileCarriage_VLTT_P4_MILAN_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VLTT_P4_MILAN_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_VLTT_P4_MILAN_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VLTT_P4_MILAN_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// VLTT_P4_MILAN_para_FR : ['ATGM MILAN 2']
export MissileCarriage_VLTT_P4_MILAN_para_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VLTT_P4_MILAN_para_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_VLTT_P4_MILAN_para_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VLTT_P4_MILAN_para_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Wiesel_TOW_RFA : ['ATGM BGM71D TOW 2']
export MissileCarriage_Wiesel_TOW_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Wiesel_TOW_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Wiesel_TOW_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Wiesel_TOW_RFA
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)

// A109BA_TOW_BEL : ['AGM BGM71D TOW 2 x4']
export MissileCarriage_A109BA_TOW_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A109BA_TOW_BEL
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_A109BA_TOW_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A109BA_TOW_BEL
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// A109BA_TOW_twin_BEL : ['AGM BGM71D TOW 2 x8']
export MissileCarriage_A109BA_TOW_twin_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A109BA_TOW_twin_BEL
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_A109BA_TOW_twin_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A109BA_TOW_twin_BEL
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// AH1E_Cobra_US : ['RocketAir Hydra 70mm x19'] ['RocketAir Hydra 70mm x19']
export MissileCarriage_AH1E_Cobra_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1E_Cobra_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_AH1E_Cobra_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1E_Cobra_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// AH1F_ATAS_US : ['RocketAir Hydra 70mm x14'] ['SAM FIM92 Stinger CS x4']
export MissileCarriage_AH1F_ATAS_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_ATAS_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_AH1F_ATAS_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_ATAS_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// AH1F_CNITE_US : ['RocketAir Hydra 70mm x19'] ['AGM BGM71D TOW 2 x4']
export MissileCarriage_AH1F_CNITE_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_CNITE_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_AH1F_CNITE_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_CNITE_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// AH1F_Cobra_NG_US : ['RocketAir Hydra 70mm x14'] ['AGM BGM71C ITOW x8']
export MissileCarriage_AH1F_Cobra_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_Cobra_NG_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_AH1F_Cobra_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_Cobra_NG_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// AH1F_Cobra_US : ['RocketAir Hydra 70mm x14'] ['AGM BGM71C ITOW x8']
export MissileCarriage_AH1F_Cobra_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_Cobra_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_AH1F_Cobra_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_Cobra_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// AH1F_HeavyHog_US : ['RocketAir Zuni 1272mm x8'] ['RocketAir Zuni 1272mm x8']
export MissileCarriage_AH1F_HeavyHog_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_HeavyHog_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_AH1F_HeavyHog_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_HeavyHog_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// AH1F_Hog_US : ['RocketAir Hydra 70mm x19'] ['RocketAir Hydra 70mm x19']
export MissileCarriage_AH1F_Hog_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_Hog_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_AH1F_Hog_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_Hog_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// AH1S_Cobra_US : ['RocketAir Hydra 70mm x19'] ['RocketAir Hydra 70mm x19']
export MissileCarriage_AH1S_Cobra_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1S_Cobra_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_AH1S_Cobra_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1S_Cobra_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// AH64_Apache_ATAS_US : ['SAM FIM92 Stinger CS x4'] ['AGM AGM114A x8']
export MissileCarriage_AH64_Apache_ATAS_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_ATAS_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_AH64_Apache_ATAS_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_ATAS_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// AH64_Apache_NG_US : ['RocketAir Hydra 70mm x19'] ['AGM AGM114A x8']
export MissileCarriage_AH64_Apache_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_NG_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_AH64_Apache_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_NG_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// AH64_Apache_US : ['RocketAir Hydra 70mm x19'] ['AGM AGM114A x8']
export MissileCarriage_AH64_Apache_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_AH64_Apache_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// AH64_Apache_emp1_US : ['AGM AGM114A x16']
export MissileCarriage_AH64_Apache_emp1_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_emp1_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_AH64_Apache_emp1_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_emp1_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// AH64_Apache_emp2_US : ['RocketAir Hydra 70mm x19'] ['RocketAir Hydra 70mm x19']
export MissileCarriage_AH64_Apache_emp2_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_emp2_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_AH64_Apache_emp2_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_emp2_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// AH6C_Little_Bird_US : ['RocketAir Hydra 70mm x14']
export MissileCarriage_AH6C_Little_Bird_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH6C_Little_Bird_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_AH6C_Little_Bird_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH6C_Little_Bird_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
    ]
)
// AH6G_Little_Bird_US : ['RocketAir Hydra 70mm x19']
export MissileCarriage_AH6G_Little_Bird_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH6G_Little_Bird_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 19
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_AH6G_Little_Bird_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH6G_Little_Bird_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 19
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// Alouette_III_SS11_FR : ['ATGM AS11 x4']
export MissileCarriage_Alouette_III_SS11_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alouette_III_SS11_FR
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Alouette_III_SS11_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alouette_III_SS11_FR
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Bo_105_PAH_1A1_RFA : ['AGM HOT2 x6']
export MissileCarriage_Bo_105_PAH_1A1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Bo_105_PAH_1A1_RFA
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Bo_105_PAH_1A1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Bo_105_PAH_1A1_RFA
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Bo_105_PAH_1_RFA : ['AGM HOT1 x6']
export MissileCarriage_Bo_105_PAH_1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Bo_105_PAH_1_RFA
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Bo_105_PAH_1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Bo_105_PAH_1_RFA
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Gazelle_HOT_2_FR : ['AGM HOT2 x4']
export MissileCarriage_Gazelle_HOT_2_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_HOT_2_FR
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Gazelle_HOT_2_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_HOT_2_FR
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Gazelle_HOT_FR : ['AGM HOT1 x4']
export MissileCarriage_Gazelle_HOT_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_HOT_FR
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Gazelle_HOT_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_HOT_FR
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Gazelle_Mistral_FR : ['Mistral Celtic x4']
export MissileCarriage_Gazelle_Mistral_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_Mistral_FR
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Gazelle_Mistral_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_Mistral_FR
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// Gazelle_SNEB_UK : ['RocketAir SNEB 68mm x12']
export MissileCarriage_Gazelle_SNEB_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_SNEB_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Gazelle_SNEB_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_SNEB_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
    ]
)
// Gazelle_SNEB_reco_UK : ['RocketAir SNEB 68mm x12']
export MissileCarriage_Gazelle_SNEB_reco_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_SNEB_reco_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Gazelle_SNEB_reco_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_SNEB_reco_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
    ]
)
// Ka_50_AA_SOV : ['SAM IglaV x4']
export MissileCarriage_Ka_50_AA_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Ka_50_AA_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Ka_50_AA_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Ka_50_AA_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// Ka_50_SOV : ['AGM 9K121 Vikhr x12'] ['RocketAir B8 80mm x10']
export MissileCarriage_Ka_50_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Ka_50_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Ka_50_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Ka_50_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Lynx_AH_Mk1_LBH_UK : ['AGM BGM71 TOW x8']
export MissileCarriage_Lynx_AH_Mk1_LBH_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk1_LBH_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Lynx_AH_Mk1_LBH_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk1_LBH_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Lynx_AH_Mk1_TOW_UK : ['AGM BGM71 TOW x8']
export MissileCarriage_Lynx_AH_Mk1_TOW_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk1_TOW_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Lynx_AH_Mk1_TOW_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk1_TOW_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Lynx_AH_Mk7_I_TOW2_UK : ['AGM BGM71C FITOW x8']
export MissileCarriage_Lynx_AH_Mk7_I_TOW2_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk7_I_TOW2_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Lynx_AH_Mk7_I_TOW2_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk7_I_TOW2_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Lynx_AH_Mk7_I_TOW_UK : ['AGM BGM71C ITOW x8']
export MissileCarriage_Lynx_AH_Mk7_I_TOW_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk7_I_TOW_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Lynx_AH_Mk7_I_TOW_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk7_I_TOW_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Lynx_AH_Mk7_SNEB_UK : ['RocketAir SNEB 68mm x18 helo']
export MissileCarriage_Lynx_AH_Mk7_SNEB_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk7_SNEB_UK
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Lynx_AH_Mk7_SNEB_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk7_SNEB_UK
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
    ]
)
// MH_60A_DAP_US : ['RocketAir Hydra 70mm x19']
export MissileCarriage_MH_60A_DAP_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MH_60A_DAP_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 19
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MH_60A_DAP_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MH_60A_DAP_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 19
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// Mi_14PL_AT_DDR : ['AGM Kh23M helo']
export MissileCarriage_Mi_14PL_AT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_14PL_AT_DDR
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Mi_14PL_AT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_14PL_AT_DDR
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// Mi_24D_AA_DDR : ['AA R60M Vympel_helo'] ['RocketAir B8 80mm x10']
export MissileCarriage_Mi_24D_AA_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_AA_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24D_AA_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_AA_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Mi_24D_Desant_SOV : ['RocketAir S5 57mm x8']
export MissileCarriage_Mi_24D_Desant_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_Desant_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Mi_24D_Desant_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_Desant_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// Mi_24D_POL : ['RocketAir S5 57mm x64'] ['AGM 9M17P FalangaP x4']
export MissileCarriage_Mi_24D_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 128
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24D_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 128
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24D_s5_AT_DDR : ['RocketAir S5 57mm x8'] ['AGM 9M17P FalangaP x4']
export MissileCarriage_Mi_24D_s5_AT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s5_AT_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24D_s5_AT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s5_AT_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24D_s5_AT_SOV : ['RocketAir S5 57mm x8'] ['AGM 9M17P FalangaP x4']
export MissileCarriage_Mi_24D_s5_AT_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s5_AT_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24D_s5_AT_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s5_AT_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24D_s8_AT_DDR : ['RocketAir B8 80mm x10'] ['AGM 9M17P FalangaP x4']
export MissileCarriage_Mi_24D_s8_AT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s8_AT_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24D_s8_AT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s8_AT_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24D_s8_AT_POL : ['RocketAir B8 80mm x10'] ['AGM 9M17P FalangaP x4']
export MissileCarriage_Mi_24D_s8_AT_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s8_AT_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24D_s8_AT_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s8_AT_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24D_s8_AT_SOV : ['RocketAir B8 80mm x10'] ['AGM 9M17P FalangaP x4']
export MissileCarriage_Mi_24D_s8_AT_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s8_AT_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24D_s8_AT_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s8_AT_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24K_reco_SOV : ['RocketAir B8 80mm x10']
export MissileCarriage_Mi_24K_reco_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24K_reco_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24K_reco_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24K_reco_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Mi_24P_AA_SOV : ['RocketAir B8 80mm x10'] ['AA R60M Vympel_helo']
export MissileCarriage_Mi_24P_AA_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24P_AA_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24P_AA_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24P_AA_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24P_SOV : ['RocketAir B8 80mm x10'] ['AGM 9M114M KokonM x4']
export MissileCarriage_Mi_24P_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24P_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24P_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24P_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24P_s8_AT2_DDR : ['RocketAir B8 80mm x10'] ['AGM 9M114M KokonM x8']
export MissileCarriage_Mi_24P_s8_AT2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24P_s8_AT2_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24P_s8_AT2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24P_s8_AT2_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24P_s8_AT_DDR : ['RocketAir B8 80mm x10'] ['AGM 9M114M KokonM x4']
export MissileCarriage_Mi_24P_s8_AT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24P_s8_AT_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24P_s8_AT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24P_s8_AT_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24VP_SOV : ['RocketAir B8 80mm x10'] ['AGM 9M114M KokonM x16']
export MissileCarriage_Mi_24VP_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24VP_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24VP_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24VP_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24V_AA_SOV : ['RocketAir B8 80mm x10'] ['SAM IglaV x4']
export MissileCarriage_Mi_24V_AA_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_AA_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24V_AA_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_AA_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24V_AT_SOV : ['RocketAir B8 80mm x10'] ['AGM 9M114M KokonM x8']
export MissileCarriage_Mi_24V_AT_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_AT_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24V_AT_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_AT_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24V_POL : ['RocketAir B8 80mm x10'] ['AGM 9M114M KokonM x8']
export MissileCarriage_Mi_24V_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24V_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24V_RKT2_SOV : ['RocketAir B8 80mm x10'] ['AGM 9M114M KokonM x4']
export MissileCarriage_Mi_24V_RKT2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_RKT2_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24V_RKT2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_RKT2_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24V_RKT_SOV : ['RocketAir S13 122mm x10'] ['AGM 9M114M KokonM x4']
export MissileCarriage_Mi_24V_RKT_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_RKT_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24V_RKT_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_RKT_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_24V_SOV : ['RocketAir S5 57mm x8'] ['SAM IglaV x4']
export MissileCarriage_Mi_24V_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 96
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_24V_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 96
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mi_2_AA_POL : ['SAM Strela2 x4'] ['AGM 9M14 MalyutkaP x4']
export MissileCarriage_Mi_2_AA_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_2_AA_POL
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_2_AA_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_2_AA_POL
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_2_ATGM_POL : ['AGM 9M14 MalyutkaP x4']
export MissileCarriage_Mi_2_ATGM_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_2_ATGM_POL
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Mi_2_ATGM_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_2_ATGM_POL
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Mi_2_rocket_DDR : ['RocketAir S5 57mm x32']
export MissileCarriage_Mi_2_rocket_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_2_rocket_DDR
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Mi_2_rocket_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_2_rocket_DDR
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// Mi_2_rocket_POL : ['RocketAir S5 57mm x32']
export MissileCarriage_Mi_2_rocket_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_2_rocket_POL
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Mi_2_rocket_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_2_rocket_POL
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// Mi_8MTV_SOV : ['RocketAir S5 57mm x8'] ['RocketAir S5 57mm x8']
export MissileCarriage_Mi_8MTV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8MTV_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Mi_8MTV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8MTV_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// Mi_8MT_POL : ['RocketAir S5 57mm x8'] ['RocketAir S5 57mm x8']
export MissileCarriage_Mi_8MT_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8MT_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Mi_8MT_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8MT_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// Mi_8TB_DDR : ['RocketAir S5 57mm x32'] ['AGM 9M14 MalyutkaP x6']
export MissileCarriage_Mi_8TB_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TB_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 128
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_8TB_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TB_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 128
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_8TB_SOV : ['RocketAir S5 57mm x32'] ['AGM 9M17P FalangaP x4']
export MissileCarriage_Mi_8TB_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TB_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 192
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_8TB_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TB_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 192
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_8TB_reco_Marine_DDR : ['RocketAir S5 57mm x32'] ['AGM 9M14 MalyutkaP x6']
export MissileCarriage_Mi_8TB_reco_Marine_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TB_reco_Marine_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 128
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_8TB_reco_Marine_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TB_reco_Marine_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 128
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// Mi_8TV_DDR : ['RocketAir S5 57mm x8'] ['RocketAir S5 57mm x8'] ['RocketAir S5 57mm x8']
export MissileCarriage_Mi_8TV_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_8TV_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Mi_8TV_Gunship_SOV : ['RocketAir B8 80mm x20'] ['AA R60M Vympel']
export MissileCarriage_Mi_8TV_Gunship_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_Gunship_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Mi_8TV_Gunship_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_Gunship_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// Mi_8TV_PodGatling_DDR : ['RocketAir S5 57mm x8']
export MissileCarriage_Mi_8TV_PodGatling_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_PodGatling_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_8TV_PodGatling_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_PodGatling_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Mi_8TV_PodGatling_PodAGL_SOV : ['RocketAir S5 57mm x8']
export MissileCarriage_Mi_8TV_PodGatling_PodAGL_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_PodGatling_PodAGL_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_8TV_PodGatling_PodAGL_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_PodGatling_PodAGL_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Mi_8TV_SOV : ['RocketAir S5 57mm x8'] ['RocketAir S5 57mm x8']
export MissileCarriage_Mi_8TV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Mi_8TV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// Mi_8TV_UPK_DDR : ['RocketAir B8 80mm x20']
export MissileCarriage_Mi_8TV_UPK_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_UPK_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_8TV_UPK_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_UPK_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Mi_8TV_s57_16_SOV : ['RocketAir S5 57mm x8'] ['RocketAir S5 57mm x8'] ['RocketAir S5 57mm x8']
export MissileCarriage_Mi_8TV_s57_16_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_s57_16_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_8TV_s57_16_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_s57_16_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Mi_8TV_s57_32_DDR : ['RocketAir S5 57mm x8'] ['RocketAir S5 57mm x8'] ['RocketAir S5 57mm x8']
export MissileCarriage_Mi_8TV_s57_32_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_s57_32_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_8TV_s57_32_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_s57_32_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Mi_8TV_s57_32_SOV : ['RocketAir S5 57mm x8'] ['RocketAir S5 57mm x8'] ['RocketAir S5 57mm x8']
export MissileCarriage_Mi_8TV_s57_32_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_s57_32_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_8TV_s57_32_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_s57_32_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 64
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Mi_8TV_s80_SOV : ['RocketAir B8 80mm x10'] ['RocketAir B8 80mm x10']
export MissileCarriage_Mi_8TV_s80_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_s80_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mi_8TV_s80_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_s80_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Mi_8T_DDR : ['RocketAir S5 57mm x8'] ['RocketAir S5 57mm x8']
export MissileCarriage_Mi_8T_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8T_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Mi_8T_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8T_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// Mi_8T_POL : ['RocketAir S5 57mm x8'] ['RocketAir S5 57mm x8']
export MissileCarriage_Mi_8T_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8T_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Mi_8T_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8T_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// OH58D_Combat_Scout_US : ['RocketAir Hydra 70mm x14']
export MissileCarriage_OH58D_Combat_Scout_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OH58D_Combat_Scout_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_OH58D_Combat_Scout_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OH58D_Combat_Scout_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 1
        ),
    ]
)
// OH58D_Kiowa_Warrior_US : ['AGM AGM114A x4']
export MissileCarriage_OH58D_Kiowa_Warrior_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OH58D_Kiowa_Warrior_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_OH58D_Kiowa_Warrior_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OH58D_Kiowa_Warrior_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
// OH58_CS_US : ['SAM FIM92 Stinger CS x4']
export MissileCarriage_OH58_CS_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OH58_CS_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_OH58_CS_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OH58_CS_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 1
        ),
    ]
)
// UH1M_gunship_US : ['RocketAir Hydra 70mm x19']
export MissileCarriage_UH1M_gunship_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UH1M_gunship_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_UH1M_gunship_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UH1M_gunship_US
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// W3W_Sokol_AA_POL : ['SAM Strela2 x4'] ['RocketAir B8 80mm x20']
export MissileCarriage_W3W_Sokol_AA_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_W3W_Sokol_AA_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_W3W_Sokol_AA_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_W3W_Sokol_AA_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// W3W_Sokol_RKT_POL : ['RocketAir B8 80mm x20'] ['RocketAir B8 80mm x20']
export MissileCarriage_W3W_Sokol_RKT_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_W3W_Sokol_RKT_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_W3W_Sokol_RKT_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_W3W_Sokol_RKT_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Westland_Scout_SS11_UK : ['ATGM AS11 x4']
export MissileCarriage_Westland_Scout_SS11_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Westland_Scout_SS11_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)
export MissileCarriage_Westland_Scout_SS11_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Westland_Scout_SS11_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 1
        ),
    ]
)

// A10_Thunderbolt_II_ATGM_US : ['AGM AGM65D Maverick'] ['AA AIM9M Sidewinder']
export MissileCarriage_A10_Thunderbolt_II_ATGM_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A10_Thunderbolt_II_ATGM_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_A10_Thunderbolt_II_ATGM_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A10_Thunderbolt_II_ATGM_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// A10_Thunderbolt_II_Rkt_US : ['RocketAir Hydra 70mm x76'] ['AA AIM9M Sidewinder']
export MissileCarriage_A10_Thunderbolt_II_Rkt_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A10_Thunderbolt_II_Rkt_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 76
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_A10_Thunderbolt_II_Rkt_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A10_Thunderbolt_II_Rkt_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 76
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// A10_Thunderbolt_II_US : ['Bomb Mk82 250kg x8'] ['AA AIM9M Sidewinder']
export MissileCarriage_A10_Thunderbolt_II_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A10_Thunderbolt_II_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_A10_Thunderbolt_II_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A10_Thunderbolt_II_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// A37B_Dragonfly_HE_US : ['Bomb Mk82 250kg x4'] ['Bomb Mk81 119kg x4']
export MissileCarriage_A37B_Dragonfly_HE_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A37B_Dragonfly_HE_US
    PylonSet = ~/DepictionPylonSet_Airplane_US
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_A37B_Dragonfly_HE_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A37B_Dragonfly_HE_US
    PylonSet = ~/DepictionPylonSet_Airplane_US_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
// A37B_Dragonfly_NPLM_US : ['Bomb Mk77 340kg Napalm x4']
export MissileCarriage_A37B_Dragonfly_NPLM_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A37B_Dragonfly_NPLM_US
    PylonSet = ~/DepictionPylonSet_Airplane_US
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_A37B_Dragonfly_NPLM_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A37B_Dragonfly_NPLM_US
    PylonSet = ~/DepictionPylonSet_Airplane_US_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// A37B_Dragonfly_US : ['RocketAir Hydra 70mm avion SMOKE x14'] ['RocketAir Hydra 70mm x38 avion']
export MissileCarriage_A37B_Dragonfly_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A37B_Dragonfly_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 28
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_A37B_Dragonfly_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A37B_Dragonfly_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 28
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// A6E_Intruder_SEAD_US : ['AGM AGM45 Shrike']
export MissileCarriage_A6E_Intruder_SEAD_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A6E_Intruder_SEAD_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_A6E_Intruder_SEAD_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A6E_Intruder_SEAD_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// A6E_Intruder_US : ['Bomb Mk77 340kg Napalm x8']
export MissileCarriage_A6E_Intruder_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A6E_Intruder_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_A6E_Intruder_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A6E_Intruder_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// A7D_Corsair_II_AT_US : ['AA AIM9L Sidewinder'] ['AGM AGM65B Maverick']
export MissileCarriage_A7D_Corsair_II_AT_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A7D_Corsair_II_AT_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_A7D_Corsair_II_AT_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A7D_Corsair_II_AT_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
    ]
)
// A7D_Corsair_II_CLU_US : ['AA AIM9L Sidewinder'] ['Bomb CBU Mk20 Rockeye II 250kg x12']
export MissileCarriage_A7D_Corsair_II_CLU_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A7D_Corsair_II_CLU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_A7D_Corsair_II_CLU_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A7D_Corsair_II_CLU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
// A7D_Corsair_II_RKT_US : ['AA AIM9L Sidewinder'] ['RocketAir Hydra 70mm x38 avion']
export MissileCarriage_A7D_Corsair_II_RKT_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A7D_Corsair_II_RKT_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 114
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_A7D_Corsair_II_RKT_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A7D_Corsair_II_RKT_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 114
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// A7D_Corsair_II_US : ['AA AIM9L Sidewinder'] ['Bomb Mk84 920kg x4']
export MissileCarriage_A7D_Corsair_II_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A7D_Corsair_II_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_A7D_Corsair_II_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A7D_Corsair_II_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
// Alpha_Jet_A_clu_RFA : ['Bomb CBU Mk20 Rockeye II 250kg x4']
export MissileCarriage_Alpha_Jet_A_clu_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_A_clu_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Alpha_Jet_A_clu_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_A_clu_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Alpha_Jet_A_he_RFA : ['Bomb Mk82 250kg x4']
export MissileCarriage_Alpha_Jet_A_he_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_A_he_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Alpha_Jet_A_he_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_A_he_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Alpha_Jet_A_nplm_RFA : ['Bomb Mk77 340kg Napalm x2'] ['Bomb Mk81 119kg x2']
export MissileCarriage_Alpha_Jet_A_nplm_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_A_nplm_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Alpha_Jet_A_nplm_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_A_nplm_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
// Alpha_Jet_A_rkt_RFA : ['RocketAir SNEB 68mm x18'] ['RocketAir SNEB 68mm x18']
export MissileCarriage_Alpha_Jet_A_rkt_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_A_rkt_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Alpha_Jet_A_rkt_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_A_rkt_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Alpha_Jet_BEL : ['Bomb Mk82 250kg x4']
export MissileCarriage_Alpha_Jet_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Alpha_Jet_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Alpha_Jet_E_FR : ['Bomb Matra 250kg x6']
export MissileCarriage_Alpha_Jet_E_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_E_FR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Alpha_Jet_E_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_E_FR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Alpha_Jet_E_NPLM_FR : ['Bomb Bidons Speciaux Napalm x4']
export MissileCarriage_Alpha_Jet_E_NPLM_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_E_NPLM_FR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Alpha_Jet_E_NPLM_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_E_NPLM_FR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Alpha_Jet_HE2_BEL : ['Bomb Mk83 450kg x4']
export MissileCarriage_Alpha_Jet_HE2_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_HE2_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Alpha_Jet_HE2_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_HE2_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Buccaneer_S2B_ATGM_UK : ['AGM AJ 168 x2']
export MissileCarriage_Buccaneer_S2B_ATGM_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buccaneer_S2B_ATGM_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Buccaneer_S2B_ATGM_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buccaneer_S2B_ATGM_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Buccaneer_S2B_GBU_UK : ['Bomb CPU 123 x1']
export MissileCarriage_Buccaneer_S2B_GBU_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buccaneer_S2B_GBU_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Buccaneer_S2B_GBU_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buccaneer_S2B_GBU_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Buccaneer_S2B_HE_UK : ['Bomb Mk18 RET 513kg x6'] ['AA AIM9L Sidewinder']
export MissileCarriage_Buccaneer_S2B_HE_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buccaneer_S2B_HE_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Buccaneer_S2B_HE_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buccaneer_S2B_HE_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Buccaneer_S2B_SEAD_UK : ['AGM AS37 Martel x2'] ['AA AIM9L Sidewinder']
export MissileCarriage_Buccaneer_S2B_SEAD_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buccaneer_S2B_SEAD_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Buccaneer_S2B_SEAD_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buccaneer_S2B_SEAD_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// CM170_Magister_FR : ['RocketAir SNEB 68mm x12']
export MissileCarriage_CM170_Magister_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_CM170_Magister_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_CM170_Magister_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_CM170_Magister_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
    ]
)
// CM170_Magister_SS11_FR : ['AGM AS11 x2 air']
export MissileCarriage_CM170_Magister_SS11_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_CM170_Magister_SS11_FR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_CM170_Magister_SS11_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_CM170_Magister_SS11_FR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// EA6B_Prowler_US : ['AGM AGM88 HARM']
export MissileCarriage_EA6B_Prowler_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_EA6B_Prowler_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_EA6B_Prowler_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_EA6B_Prowler_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// F104G_Starfighter_AT_RFA : ['AGM AS30']
export MissileCarriage_F104G_Starfighter_AT_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F104G_Starfighter_AT_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F104G_Starfighter_AT_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F104G_Starfighter_AT_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// F104G_Starfighter_HE_RFA : ['Bomb Mk83 450kg x2']
export MissileCarriage_F104G_Starfighter_HE_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F104G_Starfighter_HE_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F104G_Starfighter_HE_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F104G_Starfighter_HE_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// F104G_Starfighter_RFA : ['AA AIM9L Sidewinder']
export MissileCarriage_F104G_Starfighter_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F104G_Starfighter_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F104G_Starfighter_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F104G_Starfighter_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// F111E_Aardvark_CBU_US : ['Bomb CBU Mk20 Rockeye II 250kg x8']
export MissileCarriage_F111E_Aardvark_CBU_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111E_Aardvark_CBU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F111E_Aardvark_CBU_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111E_Aardvark_CBU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// F111E_Aardvark_US : ['Bomb Mk82 250kg x12']
export MissileCarriage_F111E_Aardvark_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111E_Aardvark_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F111E_Aardvark_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111E_Aardvark_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// F111E_Aardvark_napalm_US : ['Bomb Mk77 340kg Napalm x4']
export MissileCarriage_F111E_Aardvark_napalm_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111E_Aardvark_napalm_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F111E_Aardvark_napalm_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111E_Aardvark_napalm_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// F111F_Aardvark_CBU_US : ['Bomb CBU Mk20 Rockeye II 250kg x8']
export MissileCarriage_F111F_Aardvark_CBU_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_CBU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F111F_Aardvark_CBU_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_CBU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// F111F_Aardvark_LGB2_US : ['Bomb GBU 10 x2']
export MissileCarriage_F111F_Aardvark_LGB2_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_LGB2_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F111F_Aardvark_LGB2_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_LGB2_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// F111F_Aardvark_LGB_US : ['Bomb GBU 12 x2']
export MissileCarriage_F111F_Aardvark_LGB_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_LGB_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F111F_Aardvark_LGB_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_LGB_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// F111F_Aardvark_US : ['Bomb Mk82 250kg x12']
export MissileCarriage_F111F_Aardvark_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F111F_Aardvark_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// F111F_Aardvark_napalm_US : ['Bomb Mk77 340kg Napalm x4']
export MissileCarriage_F111F_Aardvark_napalm_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_napalm_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F111F_Aardvark_napalm_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_napalm_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// F117_Nighthawk_US : ['Bomb GBU 27 x1']
export MissileCarriage_F117_Nighthawk_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F117_Nighthawk_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F117_Nighthawk_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F117_Nighthawk_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// F15C_Eagle_AA2_US : ['AA AIM7M Sparrow'] ['AA AIM9M Sidewinder']
export MissileCarriage_F15C_Eagle_AA2_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F15C_Eagle_AA2_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F15C_Eagle_AA2_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F15C_Eagle_AA2_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F15C_Eagle_AA_US : ['AA AIM120A AMRAAM'] ['AA AIM9M Sidewinder']
export MissileCarriage_F15C_Eagle_AA_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F15C_Eagle_AA_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F15C_Eagle_AA_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F15C_Eagle_AA_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F15E_StrikeEagle_US : ['Bomb GBU 10 x2'] ['AA AIM9M Sidewinder']
export MissileCarriage_F15E_StrikeEagle_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F15E_StrikeEagle_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F15E_StrikeEagle_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F15E_StrikeEagle_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16A_AA2_NL : ['AA AIM9L Sidewinder'] ['AA AIM9L Sidewinder']
export MissileCarriage_F16A_AA2_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_AA2_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16A_AA2_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_AA2_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16A_AA_BEL : ['AA AIM9P Sidewinder'] ['AA AIM9P Sidewinder']
export MissileCarriage_F16A_AA_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_AA_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16A_AA_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_AA_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16A_AA_NL : ['AA AIM9L Sidewinder'] ['AA AIM9L Sidewinder']
export MissileCarriage_F16A_AA_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_AA_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16A_AA_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_AA_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16A_CBU_BEL : ['Bomb CBU Mk20 Rockeye II 250kg x2'] ['Bomb Mk82 250kg x6']
export MissileCarriage_F16A_CBU_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_CBU_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16A_CBU_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_CBU_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
// F16A_CLU_NL : ['Bomb BL755 cluster 264kg x4'] ['AA AIM9L Sidewinder']
export MissileCarriage_F16A_CLU_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_CLU_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16A_CLU_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_CLU_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16A_HE_NL : ['Bomb Mk83 450kg x4'] ['AA AIM9L Sidewinder']
export MissileCarriage_F16A_HE_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_HE_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16A_HE_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_HE_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16C_LGB_US : ['Bomb GBU 12 x1'] ['AA AIM9M Sidewinder']
export MissileCarriage_F16C_LGB_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16C_LGB_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16C_LGB_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16C_LGB_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16E_AA2_US : ['AA AIM9M Sidewinder'] ['AA AIM9M Sidewinder']
export MissileCarriage_F16E_AA2_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_AA2_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16E_AA2_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_AA2_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16E_AA_US : ['AA AIM9M Sidewinder'] ['AA AIM7M Sparrow']
export MissileCarriage_F16E_AA_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_AA_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16E_AA_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_AA_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16E_AGM_US : ['AGM AGM65D Maverick'] ['AA AIM9M Sidewinder']
export MissileCarriage_F16E_AGM_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_AGM_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16E_AGM_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_AGM_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16E_CBU_US : ['Bomb CBU Mk20 Rockeye II 250kg x2'] ['AA AIM9M Sidewinder']
export MissileCarriage_F16E_CBU_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_CBU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16E_CBU_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_CBU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16E_HE_US : ['Bomb Mk82 250kg x6'] ['AA AIM9M Sidewinder']
export MissileCarriage_F16E_HE_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_HE_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16E_HE_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_HE_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16E_SEAD_US : ['AGM AGM88 HARM'] ['AA AIM9M Sidewinder']
export MissileCarriage_F16E_SEAD_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_SEAD_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16E_SEAD_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_SEAD_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16E_TER_CLU_US : ['Bomb CBU Mk20 Rockeye II 250kg x8'] ['AA AIM9M Sidewinder']
export MissileCarriage_F16E_TER_CLU_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_TER_CLU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16E_TER_CLU_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_TER_CLU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16E_TER_HE_US : ['Bomb Mk82 250kg x12'] ['AA AIM9M Sidewinder']
export MissileCarriage_F16E_TER_HE_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_TER_HE_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16E_TER_HE_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_TER_HE_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F16E_napalm_US : ['Bomb Mk77 340kg Napalm x2'] ['AA AIM9M Sidewinder']
export MissileCarriage_F16E_napalm_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_napalm_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F16E_napalm_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_napalm_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F4E_Phantom_II_AA_US : ['AA AIM9J Sidewinder'] ['AA AIM7M Sparrow']
export MissileCarriage_F4E_Phantom_II_AA_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_AA_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F4E_Phantom_II_AA_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_AA_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F4E_Phantom_II_AT_US : ['AGM AGM65B Maverick']
export MissileCarriage_F4E_Phantom_II_AT_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_AT_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F4E_Phantom_II_AT_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_AT_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// F4E_Phantom_II_CBU_US : ['Bomb CBU Mk20 Rockeye II 250kg x2'] ['AA AIM9J Sidewinder']
export MissileCarriage_F4E_Phantom_II_CBU_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_CBU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F4E_Phantom_II_CBU_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_CBU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F4E_Phantom_II_HE_US : ['Bomb Mk82 250kg x4'] ['AA AIM9J Sidewinder']
export MissileCarriage_F4E_Phantom_II_HE_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_HE_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F4E_Phantom_II_HE_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_HE_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F4E_Phantom_II_napalm_US : ['Bomb Mk77 340kg Napalm x2'] ['AA AIM9J Sidewinder']
export MissileCarriage_F4E_Phantom_II_napalm_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_napalm_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F4E_Phantom_II_napalm_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_napalm_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F4F_Phantom_II_AA_RFA : ['AA AIM9L Sidewinder'] ['AA AIM9L Sidewinder']
export MissileCarriage_F4F_Phantom_II_AA_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_AA_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F4F_Phantom_II_AA_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_AA_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F4F_Phantom_II_AT_RFA : ['AGM AGM65B Maverick']
export MissileCarriage_F4F_Phantom_II_AT_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_AT_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F4F_Phantom_II_AT_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_AT_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// F4F_Phantom_II_HE1_RFA : ['Bomb Mk82 250kg x12'] ['AA AIM9L Sidewinder']
export MissileCarriage_F4F_Phantom_II_HE1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_HE1_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F4F_Phantom_II_HE1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_HE1_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 12
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F4F_Phantom_II_HE2_RFA : ['Bomb Mk83 450kg x5'] ['AA AIM9L Sidewinder']
export MissileCarriage_F4F_Phantom_II_HE2_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_HE2_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F4F_Phantom_II_HE2_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_HE2_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F4F_Phantom_II_RKT2_RFA : ['RocketAir Zuni 1272mm x8'] ['RocketAir Zuni 1272mm x8']
export MissileCarriage_F4F_Phantom_II_RKT2_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_RKT2_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F4F_Phantom_II_RKT2_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_RKT2_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// F4_Phantom_AA_F3_UK : ['AA Skyflash'] ['AA AIM9L Sidewinder']
export MissileCarriage_F4_Phantom_AA_F3_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Phantom_AA_F3_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F4_Phantom_AA_F3_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Phantom_AA_F3_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F4_Phantom_GR2_HE_UK : ['Bomb Mk18 RET 513kg x6'] ['AA Skyflash']
export MissileCarriage_F4_Phantom_GR2_HE_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Phantom_GR2_HE_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F4_Phantom_GR2_HE_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Phantom_GR2_HE_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F4_Phantom_GR2_NPLM_UK : ['Bomb Mk77 340kg Napalm x4'] ['AA Skyflash']
export MissileCarriage_F4_Phantom_GR2_NPLM_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Phantom_GR2_NPLM_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F4_Phantom_GR2_NPLM_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Phantom_GR2_NPLM_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F4_Phantom_GR2_UK : ['Bomb BL755 cluster 264kg x4'] ['Bomb Mk18 RET 513kg x4'] ['AA Skyflash']
export MissileCarriage_F4_Phantom_GR2_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Phantom_GR2_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_F4_Phantom_GR2_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Phantom_GR2_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 4
        ),
    ]
)
// F4_Wild_Weasel_US : ['AGM AGM45 Shrike'] ['AA AIM9J Sidewinder']
export MissileCarriage_F4_Wild_Weasel_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Wild_Weasel_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F4_Wild_Weasel_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Wild_Weasel_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F5A_FreedomFighter_AA_NL : ['AA AIM9P Sidewinder']
export MissileCarriage_F5A_FreedomFighter_AA_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_AA_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F5A_FreedomFighter_AA_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_AA_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// F5A_FreedomFighter_CLU_NL : ['Bomb CBU Mk20 Rockeye II 250kg x2'] ['AA AIM9P Sidewinder']
export MissileCarriage_F5A_FreedomFighter_CLU_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_CLU_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F5A_FreedomFighter_CLU_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_CLU_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F5A_FreedomFighter_NL : ['Bomb Mk83 450kg x2'] ['AA AIM9P Sidewinder']
export MissileCarriage_F5A_FreedomFighter_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F5A_FreedomFighter_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F5A_FreedomFighter_NPLM_NL : ['Bomb Mk77 340kg Napalm x2'] ['AA AIM9P Sidewinder']
export MissileCarriage_F5A_FreedomFighter_NPLM_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_NPLM_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F5A_FreedomFighter_NPLM_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_NPLM_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// F5A_FreedomFighter_RKT_NL : ['RocketAir CRV7 70mm x38'] ['RocketAir CRV7 70mm x38']
export MissileCarriage_F5A_FreedomFighter_RKT_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_RKT_NL
    PylonSet = ~/DepictionPylonSet_Airplane_UK
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_F5A_FreedomFighter_RKT_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_RKT_NL
    PylonSet = ~/DepictionPylonSet_Airplane_UK_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// F8P_Crusader_AA2_FR : ['AA Matra R530']
export MissileCarriage_F8P_Crusader_AA2_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F8P_Crusader_AA2_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F8P_Crusader_AA2_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F8P_Crusader_AA2_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// F8P_Crusader_FR : ['AA R550 Magic II']
export MissileCarriage_F8P_Crusader_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F8P_Crusader_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_F8P_Crusader_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F8P_Crusader_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// FA16_CAS_US : ['AGM AGM65D Maverick'] ['AA AIM9L Sidewinder']
export MissileCarriage_FA16_CAS_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FA16_CAS_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_FA16_CAS_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FA16_CAS_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 3
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 4
        ),
    ]
)
// G91_R3_Gina_HE_RFA : ['Bomb Mk81 119kg x4']
export MissileCarriage_G91_R3_Gina_HE_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_G91_R3_Gina_HE_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_G91_R3_Gina_HE_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_G91_R3_Gina_HE_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// G91_R3_Gina_NPL_RFA : ['Bomb Mk77 340kg Napalm x2'] ['AA AIM9L Sidewinder']
export MissileCarriage_G91_R3_Gina_NPL_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_G91_R3_Gina_NPL_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_G91_R3_Gina_NPL_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_G91_R3_Gina_NPL_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// G91_R3_Gina_RKT_RFA : ['RocketAir SNEB 68mm x18'] ['RocketAir SNEB 68mm x18']
export MissileCarriage_G91_R3_Gina_RKT_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_G91_R3_Gina_RKT_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_G91_R3_Gina_RKT_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_G91_R3_Gina_RKT_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Harrier_CLU_UK : ['Bomb BL755 cluster 264kg x2'] ['AA AIM9L Sidewinder']
export MissileCarriage_Harrier_CLU_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_CLU_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Harrier_CLU_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_CLU_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Harrier_GR5_UK : ['Bomb Mk18 RET 513kg x6'] ['AA AIM9L Sidewinder']
export MissileCarriage_Harrier_GR5_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_GR5_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Harrier_GR5_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_GR5_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Harrier_HE1_UK : ['Bomb Mk83 450kg x2'] ['AA AIM9L Sidewinder']
export MissileCarriage_Harrier_HE1_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_HE1_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Harrier_HE1_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_HE1_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Harrier_HE2_UK : ['Bomb Mk18 RET 513kg x4']
export MissileCarriage_Harrier_HE2_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_HE2_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Harrier_HE2_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_HE2_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Harrier_RKT1_UK : ['RocketAir SNEB 68mm x36'] ['AA AIM9L Sidewinder']
export MissileCarriage_Harrier_RKT1_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_RKT1_UK
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Harrier_RKT1_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_RKT1_UK
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Harrier_RKT2_UK : ['RocketAir SNEB 68mm x36'] ['RocketAir SNEB 68mm x18']
export MissileCarriage_Harrier_RKT2_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_RKT2_UK
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Harrier_RKT2_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_RKT2_UK
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Harrier_UK : ['AA AIM9L Sidewinder']
export MissileCarriage_Harrier_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Harrier_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// Jaguar_ATGM_FR : ['AGM AS30L']
export MissileCarriage_Jaguar_ATGM_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_ATGM_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Jaguar_ATGM_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_ATGM_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Jaguar_CLU_UK : ['Bomb BL755 cluster 264kg x4'] ['AA AIM9L Sidewinder']
export MissileCarriage_Jaguar_CLU_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_CLU_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Jaguar_CLU_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_CLU_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Jaguar_HE1_UK : ['Bomb Mk82 250kg x8'] ['AA AIM9L Sidewinder']
export MissileCarriage_Jaguar_HE1_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_HE1_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Jaguar_HE1_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_HE1_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Jaguar_HE2_UK : ['Bomb Mk18 RET 513kg x4'] ['AA AIM9L Sidewinder']
export MissileCarriage_Jaguar_HE2_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_HE2_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Jaguar_HE2_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_HE2_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Jaguar_HE_FR : ['Bomb Matra 400kg x4'] ['AA R550 Magic II']
export MissileCarriage_Jaguar_HE_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_HE_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Jaguar_HE_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_HE_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Jaguar_RKT_FR : ['RocketAir SNEB 68mm x36'] ['RocketAir SNEB 68mm x18']
export MissileCarriage_Jaguar_RKT_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_RKT_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Jaguar_RKT_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_RKT_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Jaguar_RKT_UK : ['RocketAir Hydra 70mm x38 avion'] ['AA AIM9L Sidewinder']
export MissileCarriage_Jaguar_RKT_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_RKT_UK
    PylonSet = ~/DepictionPylonSet_Airplane_UK
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Jaguar_RKT_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_RKT_UK
    PylonSet = ~/DepictionPylonSet_Airplane_UK_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Jaguar_SEAD2_FR : ['AGM ARMAT'] ['AA R550 Magic II']
export MissileCarriage_Jaguar_SEAD2_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_SEAD2_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Jaguar_SEAD2_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_SEAD2_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Jaguar_SEAD_FR : ['AGM AS37 Martel']
export MissileCarriage_Jaguar_SEAD_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_SEAD_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Jaguar_SEAD_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_SEAD_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Jaguar_clu_FR : ['Bomb BLG66 Belouga cluster 305kg x4'] ['AA R550 Magic II']
export MissileCarriage_Jaguar_clu_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_clu_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Jaguar_clu_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_clu_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Jaguar_nplm_FR : ['Bomb Bidons Speciaux Napalm x4'] ['AA R550 Magic II']
export MissileCarriage_Jaguar_nplm_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_nplm_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Jaguar_nplm_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_nplm_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Jaguar_overwing_UK : ['Bomb Mk18 RET 513kg x6'] ['AA AIM9L Sidewinder']
export MissileCarriage_Jaguar_overwing_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_overwing_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Jaguar_overwing_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_overwing_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// L39ZO_CLU_DDR : ['Bomb CLU RBK 250kg x2'] ['Bomb FAB 250kg x2']
export MissileCarriage_L39ZO_CLU_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_CLU_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_L39ZO_CLU_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_CLU_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
// L39ZO_DDR : ['RocketAir S5 57mm x32'] ['RocketAir S5 57mm x32']
export MissileCarriage_L39ZO_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_L39ZO_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// L39ZO_HE1_DDR : ['Bomb FAB 500kg x2']
export MissileCarriage_L39ZO_HE1_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_HE1_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_L39ZO_HE1_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_HE1_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// L39ZO_HE1_SOV : ['Bomb FAB 500kg x2']
export MissileCarriage_L39ZO_HE1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_HE1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_L39ZO_HE1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_HE1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// L39ZO_NPLM_SOV : ['Bomb ZB500 500kg Napalm x2']
export MissileCarriage_L39ZO_NPLM_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_NPLM_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_L39ZO_NPLM_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_NPLM_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// MiG_17PF_POL : ['RocketAir Grom 57mm x16'] ['RocketAir Grom 57mm x16']
export MissileCarriage_MiG_17PF_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_17PF_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_17PF_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_17PF_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// MiG_21PFM_AA_DDR : ['AA R60M Vympel']
export MissileCarriage_MiG_21PFM_AA_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21PFM_AA_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_21PFM_AA_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21PFM_AA_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// MiG_21PFM_DDR : ['RocketAir S13 122mm x10 avion'] ['RocketAir S5 57mm x32']
export MissileCarriage_MiG_21PFM_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21PFM_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_21PFM_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21PFM_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 32
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// MiG_21bis_AA2_DDR : ['AA R60M Vympel'] ['AA R13M']
export MissileCarriage_MiG_21bis_AA2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_AA2_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_21bis_AA2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_AA2_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_21bis_AA3_DDR : ['AA R60M Vympel'] ['AA R60M Vympel']
export MissileCarriage_MiG_21bis_AA3_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_AA3_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_21bis_AA3_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_AA3_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_21bis_AA_POL : ['AA R60M Vympel'] ['AA R3R']
export MissileCarriage_MiG_21bis_AA_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_AA_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_21bis_AA_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_AA_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_21bis_CLU_DDR : ['Bomb CLU RBK 500kg x2'] ['Bomb CLU RBK 250kg x2']
export MissileCarriage_MiG_21bis_CLU_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_CLU_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_21bis_CLU_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_CLU_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
// MiG_21bis_HE_DDR : ['Bomb FAB 250kg x4']
export MissileCarriage_MiG_21bis_HE_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_HE_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_21bis_HE_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_HE_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// MiG_21bis_HE_POL : ['Bomb FAB 250kg x4']
export MissileCarriage_MiG_21bis_HE_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_HE_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_21bis_HE_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_HE_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// MiG_21bis_NPLM_DDR : ['Bomb ZB500 500kg Napalm x2'] ['Bomb FAB 250kg x2']
export MissileCarriage_MiG_21bis_NPLM_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_NPLM_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_21bis_NPLM_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_NPLM_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
// MiG_21bis_POL : ['AA R60M Vympel'] ['AA R13M']
export MissileCarriage_MiG_21bis_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_21bis_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_23BN_AT2_DDR : ['AGM Kh23M'] ['RocketAir B8 80mm x40']
export MissileCarriage_MiG_23BN_AT2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_AT2_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_23BN_AT2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_AT2_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// MiG_23BN_AT_DDR : ['AGM Kh23M']
export MissileCarriage_MiG_23BN_AT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_AT_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_23BN_AT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_AT_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// MiG_23BN_CLU_DDR : ['Bomb CLU RBK 250kg x6']
export MissileCarriage_MiG_23BN_CLU_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_CLU_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_23BN_CLU_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_CLU_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// MiG_23BN_DDR : ['Bomb FAB 500kg x4']
export MissileCarriage_MiG_23BN_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_23BN_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// MiG_23BN_KMGU_DDR : ['Bomb FAB 250kg x4']
export MissileCarriage_MiG_23BN_KMGU_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_KMGU_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_23BN_KMGU_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_KMGU_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
// MiG_23BN_RKT_DDR : ['RocketAir B8 80mm x40']
export MissileCarriage_MiG_23BN_RKT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_RKT_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_23BN_RKT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_RKT_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// MiG_23BN_nplm_DDR : ['Bomb ZB500 500kg Napalm x6']
export MissileCarriage_MiG_23BN_nplm_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_nplm_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_23BN_nplm_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_nplm_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// MiG_23MF_AA2_POL : ['AA R3R'] ['AA R13M']
export MissileCarriage_MiG_23MF_AA2_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MF_AA2_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_23MF_AA2_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MF_AA2_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_23MF_AA_DDR : ['AA R23R Vympel'] ['AA R60M Vympel']
export MissileCarriage_MiG_23MF_AA_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MF_AA_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_23MF_AA_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MF_AA_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_23MF_AA_POL : ['AA R23R Vympel'] ['AA R60M Vympel']
export MissileCarriage_MiG_23MF_AA_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MF_AA_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_23MF_AA_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MF_AA_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_23MF_DDR : ['Bomb FAB 500kg x6']
export MissileCarriage_MiG_23MF_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MF_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_23MF_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MF_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// MiG_23MLD_AA1_SOV : ['AA R24R Vympel'] ['AA R60M Vympel']
export MissileCarriage_MiG_23MLD_AA1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MLD_AA1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_23MLD_AA1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MLD_AA1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_23MLD_SOV : ['AA R24MR Vympel'] ['AA R73 Vympel']
export MissileCarriage_MiG_23MLD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MLD_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_23MLD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MLD_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_23ML_DDR : ['AA R24R Vympel'] ['AA R60M Vympel']
export MissileCarriage_MiG_23ML_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23ML_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_23ML_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23ML_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_23ML_SOV : ['AA R60M Vympel'] ['AA R60M Vympel']
export MissileCarriage_MiG_23ML_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23ML_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_23ML_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23ML_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_23P_SOV : ['AA R24R Vympel'] ['AA R60M Vympel']
export MissileCarriage_MiG_23P_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23P_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_23P_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23P_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_25BM_SOV : ['AGM Kh58U']
export MissileCarriage_MiG_25BM_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_25BM_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_25BM_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_25BM_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// MiG_25RBF_SOV : ['Bomb FAB 500kg x8']
export MissileCarriage_MiG_25RBF_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_25RBF_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_25RBF_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_25RBF_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// MiG_27K_AT1_SOV : ['AGM Kh29L'] ['RocketAir B8 80mm x40']
export MissileCarriage_MiG_27K_AT1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27K_AT1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_27K_AT1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27K_AT1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// MiG_27K_AT2_SOV : ['AGM Kh29T'] ['AA R60M Vympel']
export MissileCarriage_MiG_27K_AT2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27K_AT2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_27K_AT2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27K_AT2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_27K_LGB_SOV : ['Bomb KAB 500Kr x1'] ['AA R60M Vympel']
export MissileCarriage_MiG_27K_LGB_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27K_LGB_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_27K_LGB_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27K_LGB_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_27K_SEAD_SOV : ['AGM Kh25MP']
export MissileCarriage_MiG_27K_SEAD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27K_SEAD_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_27K_SEAD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27K_SEAD_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// MiG_27M_CLU_SOV : ['Bomb CLU RBK 500kg x4']
export MissileCarriage_MiG_27M_CLU_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_CLU_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_27M_CLU_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_CLU_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// MiG_27M_SOV : ['AGM Kh29T']
export MissileCarriage_MiG_27M_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_27M_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// MiG_27M_bombe_SOV : ['Bomb FAB 500kg x4']
export MissileCarriage_MiG_27M_bombe_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_bombe_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_27M_bombe_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_bombe_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// MiG_27M_napalm_SOV : ['Bomb ZB500 500kg Napalm x4']
export MissileCarriage_MiG_27M_napalm_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_napalm_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_27M_napalm_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_napalm_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// MiG_27M_rkt_SOV : ['RocketAir B8 80mm x10 Avion'] ['RocketAir B8 80mm x10 Avion']
export MissileCarriage_MiG_27M_rkt_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_rkt_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_27M_rkt_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_rkt_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// MiG_27M_sead_SOV : ['AGM Kh28 X28']
export MissileCarriage_MiG_27M_sead_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_sead_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_27M_sead_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_sead_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// MiG_29_AA2_POL : ['AA R73 Vympel'] ['AA R73 Vympel']
export MissileCarriage_MiG_29_AA2_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA2_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_29_AA2_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA2_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_29_AA2_SOV : ['AA R60M Vympel'] ['AA R27R Vympel']
export MissileCarriage_MiG_29_AA2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_29_AA2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_29_AA3_SOV : ['AA R73 Vympel'] ['AA R27T Vympel']
export MissileCarriage_MiG_29_AA3_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA3_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_29_AA3_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA3_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_29_AA_DDR : ['AA R73 Vympel'] ['AA R27R Vympel']
export MissileCarriage_MiG_29_AA_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_29_AA_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_29_AA_POL : ['AA R73 Vympel'] ['AA R27R Vympel']
export MissileCarriage_MiG_29_AA_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_29_AA_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_29_AA_SOV : ['AA R73 Vympel'] ['AA R27R Vympel']
export MissileCarriage_MiG_29_AA_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_29_AA_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_31M_SOV : ['AA R37 Vympel']
export MissileCarriage_MiG_31M_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_31M_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_MiG_31M_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_31M_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// MiG_31_AA1_SOV : ['AA R33 Vympel'] ['AA R40TD1']
export MissileCarriage_MiG_31_AA1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_31_AA1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_31_AA1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_31_AA1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// MiG_31_AA2_SOV : ['AA R33 Vympel'] ['AA R60M Vympel']
export MissileCarriage_MiG_31_AA2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_31_AA2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_MiG_31_AA2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_31_AA2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mirage_2000_C_FR : ['AA R550 Magic II'] ['AA Matra Super 530D']
export MissileCarriage_Mirage_2000_C_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_2000_C_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mirage_2000_C_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_2000_C_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mirage_5_BA_BEL : ['Bomb Matra 400kg x4'] ['AA AIM9P Sidewinder']
export MissileCarriage_Mirage_5_BA_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mirage_5_BA_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mirage_5_BA_CLU_BEL : ['Bomb BL755 cluster 264kg x4'] ['AA AIM9P Sidewinder']
export MissileCarriage_Mirage_5_BA_CLU_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_CLU_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mirage_5_BA_CLU_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_CLU_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mirage_5_BA_MIRSIP_BEL : ['Bomb Mk83 450kg x4'] ['AA AIM9M Sidewinder']
export MissileCarriage_Mirage_5_BA_MIRSIP_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_MIRSIP_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mirage_5_BA_MIRSIP_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_MIRSIP_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mirage_5_BA_NPLM_BEL : ['Bomb Bidons Speciaux Napalm x4'] ['AA AIM9P Sidewinder']
export MissileCarriage_Mirage_5_BA_NPLM_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_NPLM_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mirage_5_BA_NPLM_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_NPLM_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mirage_5_BA_RKT_BEL : ['RocketAir Hydra 70mm x14'] ['RocketAir Hydra 70mm x14']
export MissileCarriage_Mirage_5_BA_RKT_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_RKT_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_US
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mirage_5_BA_RKT_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_RKT_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_US_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 14
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Mirage_5_F_FR : ['Bomb Matra 250kg x10'] ['AA R550 Magic II']
export MissileCarriage_Mirage_5_F_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_F_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mirage_5_F_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_F_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mirage_5_F_clu_FR : ['Bomb BLG66 Belouga cluster 305kg x4']
export MissileCarriage_Mirage_5_F_clu_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_F_clu_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Mirage_5_F_clu_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_F_clu_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Mirage_5_F_nplm_FR : ['Bomb Bidons Speciaux Napalm x4'] ['AA R550 Magic II']
export MissileCarriage_Mirage_5_F_nplm_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_F_nplm_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mirage_5_F_nplm_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_F_nplm_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mirage_F1_CT_FR : ['Bomb BGL 400 x1'] ['AA R550 Magic II']
export MissileCarriage_Mirage_F1_CT_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_F1_CT_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mirage_F1_CT_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_F1_CT_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mirage_F1_C_FR : ['AA R550 Magic II'] ['AA Matra Super 530F']
export MissileCarriage_Mirage_F1_C_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_F1_C_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mirage_F1_C_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_F1_C_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mirage_III_E_FR : ['AA R550 Magic'] ['AA Matra R530']
export MissileCarriage_Mirage_III_E_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_III_E_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mirage_III_E_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_III_E_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Mirage_IV_FR : ['Bomb Matra 400kg x16']
export MissileCarriage_Mirage_IV_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_IV_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Mirage_IV_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_IV_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Mirage_IV_SEAD_FR : ['AGM AS37 Martel'] ['Bomb Matra 400kg x6']
export MissileCarriage_Mirage_IV_SEAD_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_IV_SEAD_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Mirage_IV_SEAD_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_IV_SEAD_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 3
        ),
    ]
)
// OA10A_US : ['RocketAir Hydra 70mm x38 avion'] ['RocketAir Hydra 70mm avion SMOKE x38']
export MissileCarriage_OA10A_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OA10A_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_OA10A_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OA10A_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 38
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Su_15TM_AA2_SOV : ['AA R98MT'] ['AA R60M Vympel']
export MissileCarriage_Su_15TM_AA2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_15TM_AA2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Su_15TM_AA2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_15TM_AA2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 2
        ),
    ]
)
// Su_15TM_AA_SOV : ['AA R98MT'] ['AA R98MR'] ['AA R60M Vympel']
export MissileCarriage_Su_15TM_AA_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_15TM_AA_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAAM
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_15TM_AA_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_15TM_AA_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAAM
            WeaponIndex = 1
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 1
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_17M4_SOV : ['RocketAir S13 122mm x20'] ['AA R60M Vympel']
export MissileCarriage_Su_17M4_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_17M4_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_17M4_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_17M4_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 20
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_17_cluster_POL : ['Bomb CLU RBK 500kg x6']
export MissileCarriage_Su_17_cluster_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_17_cluster_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Su_17_cluster_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_17_cluster_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Su_22_AT2_DDR : ['AGM Kh25ML'] ['AA R60M Vympel']
export MissileCarriage_Su_22_AT2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_AT2_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_AT2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_AT2_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_AT_DDR : ['AGM Kh29T'] ['AA R60M Vympel']
export MissileCarriage_Su_22_AT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_AT_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_AT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_AT_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_AT_POL : ['AGM Kh29T'] ['AA R60M Vympel']
export MissileCarriage_Su_22_AT_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_AT_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_AT_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_AT_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_AT_SOV : ['AGM Kh29T'] ['AA R60M Vympel']
export MissileCarriage_Su_22_AT_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_AT_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_AT_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_AT_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_DDR : ['Bomb FAB 500kg x4'] ['AA R60M Vympel']
export MissileCarriage_Su_22_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_HE2_DDR : ['Bomb FAB 500kg x6'] ['AA R60M Vympel']
export MissileCarriage_Su_22_HE2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_HE2_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_HE2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_HE2_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_POL : ['Bomb FAB 500kg x4'] ['AA R60M Vympel']
export MissileCarriage_Su_22_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_RKT2_POL : ['RocketAir B8 80mm x80'] ['AA R60M Vympel']
export MissileCarriage_Su_22_RKT2_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_RKT2_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_RKT2_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_RKT2_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_RKT_POL : ['AA R60M Vympel']
export MissileCarriage_Su_22_RKT_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_RKT_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_RKT_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_RKT_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_SEAD_DDR : ['AGM Kh28 X28'] ['AA R60M Vympel']
export MissileCarriage_Su_22_SEAD_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_SEAD_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_SEAD_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_SEAD_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_SEAD_POL : ['AGM Kh25MP'] ['AA R60M Vympel']
export MissileCarriage_Su_22_SEAD_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_SEAD_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_SEAD_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_SEAD_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_UPK_DDR : ['RocketAir B8 80mm x40']
export MissileCarriage_Su_22_UPK_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_UPK_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_UPK_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_UPK_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 80
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Su_22_clu_DDR : ['Bomb CLU RBK 500kg x2'] ['AA R60M Vympel']
export MissileCarriage_Su_22_clu_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_clu_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_clu_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_clu_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_clu_POL : ['Bomb CLU RBK 250kg x4'] ['AA R60M Vympel']
export MissileCarriage_Su_22_clu_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_clu_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_clu_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_clu_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_nplm_DDR : ['Bomb ZB500 500kg Napalm x4'] ['AA R60M Vympel']
export MissileCarriage_Su_22_nplm_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_nplm_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_nplm_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_nplm_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_22_nplm_POL : ['Bomb ZB500 500kg Napalm x4'] ['AA R60M Vympel']
export MissileCarriage_Su_22_nplm_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_nplm_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_22_nplm_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_nplm_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_24MP_SEAD2_SOV : ['AGM Kh58U']
export MissileCarriage_Su_24MP_SEAD2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24MP_SEAD2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Su_24MP_SEAD2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24MP_SEAD2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Su_24MP_SOV : ['AGM Kh28 X28']
export MissileCarriage_Su_24MP_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24MP_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Su_24MP_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24MP_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Su_24M_AT1_SOV : ['AGM Kh29T']
export MissileCarriage_Su_24M_AT1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_AT1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Su_24M_AT1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_AT1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Su_24M_AT2_SOV : ['AGM Kh29L']
export MissileCarriage_Su_24M_AT2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_AT2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Su_24M_AT2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_AT2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Su_24M_LGB2_SOV : ['Bomb KAB 1500L x1']
export MissileCarriage_Su_24M_LGB2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_LGB2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Su_24M_LGB2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_LGB2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Su_24M_LGB_SOV : ['Bomb KAB 1500Kr x1']
export MissileCarriage_Su_24M_LGB_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_LGB_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Su_24M_LGB_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_LGB_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
    ]
)
// Su_24M_SOV : ['Bomb FAB 500kg x8']
export MissileCarriage_Su_24M_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Su_24M_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Su_24M_clu2_SOV : ['Bomb CLU RBK 500kg x8']
export MissileCarriage_Su_24M_clu2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_clu2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Su_24M_clu2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_clu2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Su_24M_clu_SOV : ['Bomb CLU RBK 250kg x8']
export MissileCarriage_Su_24M_clu_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_clu_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Su_24M_clu_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_clu_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Su_24M_nplm_SOV : ['Bomb ZB500 500kg Napalm x6']
export MissileCarriage_Su_24M_nplm_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_nplm_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Su_24M_nplm_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_nplm_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Su_24M_thermo_SOV : ['Bomb ODAB 500PM 500kg Thermobaric x6']
export MissileCarriage_Su_24M_thermo_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_thermo_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Su_24M_thermo_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_thermo_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Su_25T_SOV : ['AGM 9K121 Vikhr x16 avion'] ['AA R73 Vympel']
export MissileCarriage_Su_25T_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25T_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_25T_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25T_SOV
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 16
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_25_SOV : ['AGM Kh25ML'] ['AA R60M Vympel']
export MissileCarriage_Su_25_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_25_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_25_clu_SOV : ['Bomb CLU RBK 250kg x6'] ['AA R60M Vympel']
export MissileCarriage_Su_25_clu_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_clu_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_25_clu_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_clu_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_25_he_SOV : ['Bomb FAB 500kg x6'] ['AA R60M Vympel']
export MissileCarriage_Su_25_he_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_he_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_25_he_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_he_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_25_nplm_SOV : ['Bomb ZB500 500kg Napalm x4'] ['AA R60M Vympel']
export MissileCarriage_Su_25_nplm_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_nplm_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_25_nplm_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_nplm_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Su_25_rkt2_SOV : ['RocketAir S13 122mm x10 avion'] ['RocketAir B8 80mm x40'] ['RocketAir B8 80mm x40']
export MissileCarriage_Su_25_rkt2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_rkt2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_Su_25_rkt2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_rkt2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 10
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 4
        ),
    ]
)
// Su_25_rkt_SOV : ['RocketAir B8 80mm x40'] ['RocketAir B8 80mm x40']
export MissileCarriage_Su_25_rkt_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_rkt_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_25_rkt_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_rkt_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 40
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 3
        ),
    ]
)
// Su_27K_SOV : ['AA R27R Vympel'] ['AA R27T Vympel'] ['AA R73 Vympel']
export MissileCarriage_Su_27K_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_27K_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 4
        ),
    ]
)
export MissileCarriage_Su_27K_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_27K_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 4
        ),
    ]
)
// Su_27S_SOV : ['AA R73 Vympel'] ['AA R27R Vympel']
export MissileCarriage_Su_27S_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_27S_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Su_27S_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_27S_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Super_Etendard_AT_FR : ['AGM AS30L'] ['AA R550 Magic II']
export MissileCarriage_Super_Etendard_AT_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Super_Etendard_AT_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Super_Etendard_AT_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Super_Etendard_AT_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Super_Etendard_CLU_FR : ['Bomb BLG66 Belouga cluster 305kg x4'] ['AA R550 Magic II']
export MissileCarriage_Super_Etendard_CLU_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Super_Etendard_CLU_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Super_Etendard_CLU_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Super_Etendard_CLU_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Super_Etendard_FR : ['RocketAir SNEB 68mm x36'] ['AA R550 Magic II']
export MissileCarriage_Super_Etendard_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Super_Etendard_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Super_Etendard_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Super_Etendard_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 36
            MissileType = eAGM
            MountingType = eMountingPod
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Super_Etendard_HE_FR : ['Bomb Matra 400kg x6']
export MissileCarriage_Super_Etendard_HE_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Super_Etendard_HE_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
export MissileCarriage_Super_Etendard_HE_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Super_Etendard_HE_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
    ]
)
// Tornado_ADV_HE_UK : ['Bomb Mk18 RET 513kg x6'] ['AA AIM9L Sidewinder']
export MissileCarriage_Tornado_ADV_HE_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_ADV_HE_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Tornado_ADV_HE_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_ADV_HE_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 6
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Tornado_ADV_SEAD_UK : ['AGM ALARM'] ['AA AIM9L Sidewinder']
export MissileCarriage_Tornado_ADV_SEAD_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_ADV_SEAD_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Tornado_ADV_SEAD_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_ADV_SEAD_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Tornado_ADV_UK : ['AA AIM9L Sidewinder'] ['AA Skyflash_SuperTEMP']
export MissileCarriage_Tornado_ADV_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_ADV_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Tornado_ADV_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_ADV_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Tornado_ADV_clu_UK : ['Bomb BL755 cluster 264kg x8'] ['AA AIM9L Sidewinder']
export MissileCarriage_Tornado_ADV_clu_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_ADV_clu_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Tornado_ADV_clu_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_ADV_clu_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 8
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Tornado_IDS_AT1_RFA : ['AGM AGM65B Maverick'] ['AA AIM9L Sidewinder']
export MissileCarriage_Tornado_IDS_AT1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_IDS_AT1_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Tornado_IDS_AT1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_IDS_AT1_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 4
            MissileType = eAGM
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Tornado_IDS_CLUS_RFA : ['Bomb CBU Mk20 Rockeye II 250kg x5'] ['AA AIM9L Sidewinder']
export MissileCarriage_Tornado_IDS_CLUS_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_IDS_CLUS_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Tornado_IDS_CLUS_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_IDS_CLUS_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 5
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Tornado_IDS_HE1_RFA : ['Bomb Mk83 450kg x3'] ['AA AIM9L Sidewinder']
export MissileCarriage_Tornado_IDS_HE1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_IDS_HE1_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Tornado_IDS_HE1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_IDS_HE1_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 3
            MissileType = eAGM
            MountingType = eMountingBomb
            WeaponIndex = 2
        ),
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
// Tornado_IDS_MW1_RFA : ['AA AIM9L Sidewinder']
export MissileCarriage_Tornado_IDS_MW1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_IDS_MW1_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)
export MissileCarriage_Tornado_IDS_MW1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_IDS_MW1_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo
        (
            Count = 2
            MissileType = eAAM
            WeaponIndex = 3
        ),
    ]
)


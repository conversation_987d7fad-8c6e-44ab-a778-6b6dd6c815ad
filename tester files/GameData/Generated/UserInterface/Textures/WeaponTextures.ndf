// Ne pas éditer, ce fichier est généré par WeaponTextureFileWriter



Texture_Interface_Weapon_GIAT_M621 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/GIAT_M621.png"
)
Texture_Interface_Weapon_mk20_rh202 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/mk20_rh202.png"
)
Texture_Interface_Weapon_Gsh2323 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Gsh2323.png"
)
Texture_Interface_Weapon_bushmaster is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/bushmaster.png"
)
Texture_Interface_Weapon_24A2 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/24A2.png"
)
Texture_Interface_Weapon_24A2_bmp2 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/24A2_bmp2.png"
)
Texture_Interface_Weapon_2A72 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A72.png"
)
Texture_Interface_Weapon_Gsh30k is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Gsh30k.png"
)
Texture_Interface_Weapon_rarden is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/rarden.png"
)
Texture_Interface_Weapon_M230 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M230.png"
)
Texture_Interface_Weapon_M693_F1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M693_F1.png"
)
Texture_Interface_Weapon_T20_13 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/T20_13.png"
)
Texture_Interface_Weapon_Mk_77 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Mk_77.png"
)
Texture_Interface_Weapon_Bomb_CLU_BL755 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Bomb_CLU_BL755.png"
)
Texture_Interface_Weapon_BLG66_Belouga is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/BLG66_Belouga.png"
)
Texture_Interface_Weapon_mk20 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/mk20.png"
)
Texture_Interface_Weapon_FAB_500 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/FAB_500.png"
)
Texture_Interface_Weapon_mk82 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/mk82.png"
)
Texture_Interface_Weapon_FAB_250 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/FAB_250.png"
)
Texture_Interface_Weapon_SAMP_T25 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/SAMP_T25.png"
)
Texture_Interface_Weapon_SAMP_T200 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/SAMP_T200.png"
)
Texture_Interface_Weapon_Mk18_RET is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Mk18_RET.png"
)
Texture_Interface_Weapon_mk81 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/mk81.png"
)
Texture_Interface_Weapon_Mk83 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Mk83.png"
)
Texture_Interface_Weapon_zb500 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/zb500.png"
)
Texture_Interface_Weapon_d10t is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/d10t.png"
)
Texture_Interface_Weapon_2a26 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2a26.png"
)
Texture_Interface_Weapon_Canon_AP_105mm_M68 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Canon_AP_105mm_M68.png"
)
Texture_Interface_Weapon_m68_105 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/m68_105.png"
)
Texture_Interface_Weapon_2A46_64BV is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A46_64BV.png"
)
Texture_Interface_Weapon_L11A5 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/L11A5.png"
)
Texture_Interface_Weapon_l44 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/l44.png"
)
Texture_Interface_Weapon_M68_120 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M68_120.png"
)
Texture_Interface_Weapon_Canon_AP_122mm_D25T is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Canon_AP_122mm_D25T.png"
)
Texture_Interface_Weapon_2A46_T80 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A46_T80.png"
)
Texture_Interface_Weapon_Canon_AP_125mm_2A45_Sprut is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Canon_AP_125mm_2A45_Sprut.png"
)
Texture_Interface_Weapon_2A18_AP is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A18_AP.png"
)
Texture_Interface_Weapon_2A60_AP is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A60_AP.png"
)
Texture_Interface_Weapon_2A60_K120 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A60_K120.png"
)
Texture_Interface_Weapon_zis_2_57 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/zis_2_57.png"
)
Texture_Interface_Weapon_2A28_grom is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A28_grom.png"
)
Texture_Interface_Weapon_D56T is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/D56T.png"
)
Texture_Interface_Weapon_Scorpion_Canon is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Scorpion_Canon.png"
)
Texture_Interface_Weapon_Canon_AP_90mm_KanJPz is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Canon_AP_90mm_KanJPz.png"
)
Texture_Interface_Weapon_d44 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/d44.png"
)
Texture_Interface_Weapon_Canon_AP_85mm_D48 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Canon_AP_85mm_D48.png"
)
Texture_Interface_Weapon_Canon_AP_85mm_K52 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Canon_AP_85mm_K52.png"
)
Texture_Interface_Weapon_F3 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/F3.png"
)
Texture_Interface_Weapon_Canon_AP_90mm_CN90_F4 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Canon_AP_90mm_CN90_F4.png"
)
Texture_Interface_Weapon_m48 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/m48.png"
)
Texture_Interface_Weapon_D22_AP is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/D22_AP.png"
)
Texture_Interface_Weapon_D30_AP is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/D30_AP.png"
)
Texture_Interface_Weapon_Canon_AP_KS19_100mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Canon_AP_KS19_100mm.png"
)
Texture_Interface_Weapon_ATO_54 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/ATO_54.png"
)
Texture_Interface_Weapon_2A70 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A70.png"
)
Texture_Interface_Weapon_avre is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/avre.png"
)
Texture_Interface_Weapon_sheridan is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/sheridan.png"
)
Texture_Interface_Weapon_CM60A1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/CM60A1.png"
)
Texture_Interface_Weapon_SPG9 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/SPG9.png"
)
Texture_Interface_Weapon_Canon_HE_KS30_130mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Canon_HE_KS30_130mm.png"
)
Texture_Interface_Weapon_fk20 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/fk20.png"
)
Texture_Interface_Weapon_Bofors_L70 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Bofors_L70.png"
)
Texture_Interface_Weapon_KPVT is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/KPVT.png"
)
Texture_Interface_Weapon_DCA_1_canon_KS19_100mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/DCA_1_canon_KS19_100mm.png"
)
Texture_Interface_Weapon_DCA_1_canon_KS30_130mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/DCA_1_canon_KS30_130mm.png"
)
Texture_Interface_Weapon_gdf_35 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/gdf_35.png"
)
Texture_Interface_Weapon_DCA_2_canon_2M3_25mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/DCA_2_canon_2M3_25mm.png"
)
Texture_Interface_Weapon_DCA_2_canon_76T2_20mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/DCA_2_canon_76T2_20mm.png"
)
Texture_Interface_Weapon_zu23 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/zu23.png"
)
Texture_Interface_Weapon_mag is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/mag.png"
)
Texture_Interface_Weapon_DCA_4_canon_ZPU4_towed_14_5mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/DCA_4_canon_ZPU4_towed_14_5mm.png"
)
Texture_Interface_Weapon_2A38M is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A38M.png"
)
Texture_Interface_Weapon_HS_831A is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/HS_831A.png"
)
Texture_Interface_Weapon_APZ23 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/APZ23.png"
)
Texture_Interface_Weapon_DCA_4_canons_Maxson is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/DCA_4_canons_Maxson.png"
)
Texture_Interface_Weapon_AK74 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/AK74.png"
)
Texture_Interface_Weapon_AKs74 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/AKs74.png"
)
Texture_Interface_Weapon_L1A1_SLR is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/L1A1_SLR.png"
)
Texture_Interface_Weapon_FAMAS is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/FAMAS.png"
)
Texture_Interface_Weapon_FM_FNC is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/FM_FNC.png"
)
Texture_Interface_Weapon_g3a4 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/g3a4.png"
)
Texture_Interface_Weapon_FM_Garand is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/FM_Garand.png"
)
Texture_Interface_Weapon_L85A1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/L85A1.png"
)
Texture_Interface_Weapon_M14 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M14.png"
)
Texture_Interface_Weapon_M16 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M16.png"
)
Texture_Interface_Weapon_M16A1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M16A1.png"
)
Texture_Interface_Weapon_MAS_49_56 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/MAS_49_56.png"
)
Texture_Interface_Weapon_FM_SIG_540 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/FM_SIG_540.png"
)
Texture_Interface_Weapon_SIG_543 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/SIG_543.png"
)
Texture_Interface_Weapon_FM_Tantal is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/FM_Tantal.png"
)
Texture_Interface_Weapon_FM_kbk_AKM is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/FM_kbk_AKM.png"
)
Texture_Interface_Weapon_FM_kbk_AKMS is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/FM_kbk_AKMS.png"
)
Texture_Interface_Weapon_m197_vulcan is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/m197_vulcan.png"
)
Texture_Interface_Weapon_Gatling_GAU19_12_7mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Gatling_GAU19_12_7mm.png"
)
Texture_Interface_Weapon_JakB is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/JakB.png"
)
Texture_Interface_Weapon_m168 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/m168.png"
)
Texture_Interface_Weapon_m134 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/m134.png"
)
Texture_Interface_Weapon_AANF1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/AANF1.png"
)
Texture_Interface_Weapon_arden is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/arden.png"
)
Texture_Interface_Weapon_defa30mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/defa30mm.png"
)
Texture_Interface_Weapon_gau8 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/gau8.png"
)
Texture_Interface_Weapon_Gsh3030 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Gsh3030.png"
)
Texture_Interface_Weapon_Gsh630 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Gsh630.png"
)
Texture_Interface_Weapon_GatlingAir_Colt_Mk12_20mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/GatlingAir_Colt_Mk12_20mm.png"
)
Texture_Interface_Weapon_M61_vulcan is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M61_vulcan.png"
)
Texture_Interface_Weapon_CanonAir_Mauser_BK_27mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/CanonAir_Mauser_BK_27mm.png"
)
Texture_Interface_Weapon_nr30 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/nr30.png"
)
Texture_Interface_Weapon_Smoke_Grenade is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Smoke_Grenade.png"
)
Texture_Interface_Weapon_satchel is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/satchel.png"
)
Texture_Interface_Weapon_NSVT is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/NSVT.png"
)
Texture_Interface_Weapon_DShKM is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/DShKM.png"
)
Texture_Interface_Weapon_12_7_mm_M2HB is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/12_7_mm_M2HB.png"
)
Texture_Interface_Weapon_m3p is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/m3p.png"
)
Texture_Interface_Weapon_2A18 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A18.png"
)
Texture_Interface_Weapon_2A36 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A36.png"
)
Texture_Interface_Weapon_2A36_AP is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A36_AP.png"
)
Texture_Interface_Weapon_2A44 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A44.png"
)
Texture_Interface_Weapon_2A60 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A60.png"
)
Texture_Interface_Weapon_D22 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/D22.png"
)
Texture_Interface_Weapon_2A65_AP is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A65_AP.png"
)
Texture_Interface_Weapon_2A65 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2A65.png"
)
Texture_Interface_Weapon_fh_70 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/fh_70.png"
)
Texture_Interface_Weapon_A222_Howitzer_130mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/A222_Howitzer_130mm.png"
)
Texture_Interface_Weapon_B4M_203mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/B4M_203mm.png"
)
Texture_Interface_Weapon_BS3_towed_100mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/BS3_towed_100mm.png"
)
Texture_Interface_Weapon_BS3_Direct_AP is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/BS3_Direct_AP.png"
)
Texture_Interface_Weapon_Br5M_towed_280mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Br5M_towed_280mm.png"
)
Texture_Interface_Weapon_D1_towed_152mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/D1_towed_152mm.png"
)
Texture_Interface_Weapon_D20 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/D20.png"
)
Texture_Interface_Weapon_ML20 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/ML20.png"
)
Texture_Interface_Weapon_ML20_AP is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/ML20_AP.png"
)
Texture_Interface_Weapon_D30 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/D30.png"
)
Texture_Interface_Weapon_GCT_F1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/GCT_F1.png"
)
Texture_Interface_Weapon_L13A1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/L13A1.png"
)
Texture_Interface_Weapon_L13A1_Direct is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/L13A1_Direct.png"
)
Texture_Interface_Weapon_M101_105mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M101_105mm.png"
)
Texture_Interface_Weapon_m102 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/m102.png"
)
Texture_Interface_Weapon_M113A1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M113A1.png"
)
Texture_Interface_Weapon_M114_39 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M114_39.png"
)
Texture_Interface_Weapon_M114_39_AP is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M114_39_AP.png"
)
Texture_Interface_Weapon_M115 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M115.png"
)
Texture_Interface_Weapon_M115_AP is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M115_AP.png"
)
Texture_Interface_Weapon_M118_Direct is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M118_Direct.png"
)
Texture_Interface_Weapon_M118 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M118.png"
)
Texture_Interface_Weapon_m185 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/m185.png"
)
Texture_Interface_Weapon_M201A is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M201A.png"
)
Texture_Interface_Weapon_M46 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M46.png"
)
Texture_Interface_Weapon_M50_63 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M50_63.png"
)
Texture_Interface_Weapon_TRF1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/TRF1.png"
)
Texture_Interface_Weapon_KMGU_dispenser is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/KMGU_dispenser.png"
)
Texture_Interface_Weapon_AGS_17 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/AGS_17.png"
)
Texture_Interface_Weapon_mk19 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/mk19.png"
)
Texture_Interface_Weapon_Lance_grenade_M129_40mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Lance_grenade_M129_40mm.png"
)
Texture_Interface_Weapon_PalladD_wz83 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/PalladD_wz83.png"
)
Texture_Interface_Weapon_Hydra_70 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Hydra_70.png"
)
Texture_Interface_Weapon_m21of is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/m21of.png"
)
Texture_Interface_Weapon_pkt is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/pkt.png"
)
Texture_Interface_Weapon_hk21 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/hk21.png"
)
Texture_Interface_Weapon_mag_coax is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/mag_coax.png"
)
Texture_Interface_Weapon_M240_Vehicule is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M240_Vehicule.png"
)
Texture_Interface_Weapon_M1919 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M1919.png"
)
Texture_Interface_Weapon_M240_abrams is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M240_abrams.png"
)
Texture_Interface_Weapon_M240d is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M240d.png"
)
Texture_Interface_Weapon_M60 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M60.png"
)
Texture_Interface_Weapon_M60E3 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M60E3.png"
)
Texture_Interface_Weapon_mg3 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/mg3.png"
)
Texture_Interface_Weapon_PKM is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/PKM.png"
)
Texture_Interface_Weapon_M240B is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M240B.png"
)
Texture_Interface_Weapon_Maxim is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Maxim.png"
)
Texture_Interface_Weapon_MW1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/MW1.png"
)
Texture_Interface_Weapon_Mortier_2B14_82mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Mortier_2B14_82mm.png"
)
Texture_Interface_Weapon_tampella_120 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/tampella_120.png"
)
Texture_Interface_Weapon_81mm_mortar is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/81mm_mortar.png"
)
Texture_Interface_Weapon_Mortier_240mm_M240 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Mortier_240mm_M240.png"
)
Texture_Interface_Weapon_m29 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/m29.png"
)
Texture_Interface_Weapon_M30 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M30.png"
)
Texture_Interface_Weapon_pm43 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/pm43.png"
)
Texture_Interface_Weapon_2b9 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/2b9.png"
)
Texture_Interface_Weapon_AKs74u is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/AKs74u.png"
)
Texture_Interface_Weapon_AS_Val is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/AS_Val.png"
)
Texture_Interface_Weapon_M4_carbine is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M4_carbine.png"
)
Texture_Interface_Weapon_M3_Grease_Gun is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M3_Grease_Gun.png"
)
Texture_Interface_Weapon_MAT_49 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/MAT_49.png"
)
Texture_Interface_Weapon_mp5a3 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/mp5a3.png"
)
Texture_Interface_Weapon_mp55s is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/mp55s.png"
)
Texture_Interface_Weapon_PM63_Rak is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/PM63_Rak.png"
)
Texture_Interface_Weapon_PPS43 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/PPS43.png"
)
Texture_Interface_Weapon_skorpion is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/skorpion.png"
)
Texture_Interface_Weapon_L2A3 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/L2A3.png"
)
Texture_Interface_Weapon_Vintorez is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Vintorez.png"
)
Texture_Interface_Weapon_PM_vigneron_m2 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/PM_vigneron_m2.png"
)
Texture_Interface_Weapon_uzi is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/uzi.png"
)
Texture_Interface_Weapon_guv_agl is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/guv_agl.png"
)
Texture_Interface_Weapon_guv is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/guv.png"
)
Texture_Interface_Weapon_s13 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/s13.png"
)
Texture_Interface_Weapon_s80 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/s80.png"
)
Texture_Interface_Weapon_s5m is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/s5m.png"
)
Texture_Interface_Weapon_zuni is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/zuni.png"
)
Texture_Interface_Weapon_s24 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/s24.png"
)
Texture_Interface_Weapon_9m55k is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/9m55k.png"
)
Texture_Interface_Weapon_sf2_110 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/sf2_110.png"
)
Texture_Interface_Weapon_atacms is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/atacms.png"
)
Texture_Interface_Weapon_m26 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/m26.png"
)
Texture_Interface_Weapon_PW_LWD is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/PW_LWD.png"
)
Texture_Interface_Weapon_mo10104 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/mo10104.png"
)
Texture_Interface_Weapon_AGI_3x40 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/AGI_3x40.png"
)
Texture_Interface_Weapon_APILAS is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/APILAS.png"
)
Texture_Interface_Weapon_at4_cs is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/at4_cs.png"
)
Texture_Interface_Weapon_armbrust is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/armbrust.png"
)
Texture_Interface_Weapon_B11_RCL_107mm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/B11_RCL_107mm.png"
)
Texture_Interface_Weapon_Blindicide_RL100 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Blindicide_RL100.png"
)
Texture_Interface_Weapon_Blindicide_RL83 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Blindicide_RL83.png"
)
Texture_Interface_Weapon_carl_gustave is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/carl_gustave.png"
)
Texture_Interface_Weapon_handflammpatrone is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/handflammpatrone.png"
)
Texture_Interface_Weapon_LAW_80 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/LAW_80.png"
)
Texture_Interface_Weapon_LRAC_73 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/LRAC_73.png"
)
Texture_Interface_Weapon_LRAC_f1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/LRAC_f1.png"
)
Texture_Interface_Weapon_RocketInf_M20_SuperBaz is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RocketInf_M20_SuperBaz.png"
)
Texture_Interface_Weapon_M202_flash is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M202_flash.png"
)
Texture_Interface_Weapon_M40A1_RCL is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M40A1_RCL.png"
)
Texture_Interface_Weapon_m68_rcl is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/m68_rcl.png"
)
Texture_Interface_Weapon_M72_law is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M72_law.png"
)
Texture_Interface_Weapon_pzfaust3 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/pzfaust3.png"
)
Texture_Interface_Weapon_panzerfaust_44 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/panzerfaust_44.png"
)
Texture_Interface_Weapon_RPG16 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RPG16.png"
)
Texture_Interface_Weapon_RPG22 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RPG22.png"
)
Texture_Interface_Weapon_RPG2 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RPG2.png"
)
Texture_Interface_Weapon_RPG26 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RPG26.png"
)
Texture_Interface_Weapon_RPG27 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RPG27.png"
)
Texture_Interface_Weapon_RPG29 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RPG29.png"
)
Texture_Interface_Weapon_RPG7 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RPG7.png"
)
Texture_Interface_Weapon_RocketInf_RPG76_Komar is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RocketInf_RPG76_Komar.png"
)
Texture_Interface_Weapon_RPO_RYS is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RPO_RYS.png"
)
Texture_Interface_Weapon_RPO_RYS_x4 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RPO_RYS_x4.png"
)
Texture_Interface_Weapon_RPO is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RPO.png"
)
Texture_Interface_Weapon_Viper is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Viper.png"
)
Texture_Interface_Weapon_WASP is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/WASP.png"
)
Texture_Interface_Weapon_L4A4 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/L4A4.png"
)
Texture_Interface_Weapon_DP28 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/DP28.png"
)
Texture_Interface_Weapon_Saw_FALO is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Saw_FALO.png"
)
Texture_Interface_Weapon_L86A1_LSW is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/L86A1_LSW.png"
)
Texture_Interface_Weapon_M249 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M249.png"
)
Texture_Interface_Weapon_RPK is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RPK.png"
)
Texture_Interface_Weapon_RPK74 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/RPK74.png"
)
Texture_Interface_Weapon_FAL_sniper is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/FAL_sniper.png"
)
Texture_Interface_Weapon_FN_Model_30_11 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/FN_Model_30_11.png"
)
Texture_Interface_Weapon_FR_F1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/FR_F1.png"
)
Texture_Interface_Weapon_FR_F2 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/FR_F2.png"
)
Texture_Interface_Weapon_G3A3ZF is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/G3A3ZF.png"
)
Texture_Interface_Weapon_L96A1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/L96A1.png"
)
Texture_Interface_Weapon_M14_Sniper is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M14_Sniper.png"
)
Texture_Interface_Weapon_M24_Sniper is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M24_Sniper.png"
)
Texture_Interface_Weapon_Mosin_Nagant is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Mosin_Nagant.png"
)
Texture_Interface_Weapon_PSG1 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/PSG1.png"
)
Texture_Interface_Weapon_SVD_Dragunov is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/SVD_Dragunov.png"
)
Texture_Interface_Weapon_M82_Barrett is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M82_Barrett.png"
)
Texture_Interface_Weapon_LFP_54 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/LFP_54.png"
)
Texture_Interface_Weapon_lpo is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/lpo.png"
)
Texture_Interface_Weapon_M2_Flamethrower is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M2_Flamethrower.png"
)
Texture_Interface_Weapon_aim120_amraam is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/aim120_amraam.png"
)
Texture_Interface_Weapon_sparrow is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/sparrow.png"
)
Texture_Interface_Weapon_aim9_sidewinder is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/aim9_sidewinder.png"
)
Texture_Interface_Weapon_Super_530D is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Super_530D.png"
)
Texture_Interface_Weapon_R60_Vympel is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/R60_Vympel.png"
)
Texture_Interface_Weapon_R27_Vympel is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/R27_Vympel.png"
)
Texture_Interface_Weapon_R27T_Vympel is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/R27T_Vympel.png"
)
Texture_Interface_Weapon_R37_Vympel is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/R37_Vympel.png"
)
Texture_Interface_Weapon_R550_Magic is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/R550_Magic.png"
)
Texture_Interface_Weapon_R550_Magic_II is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/R550_Magic_II.png"
)
Texture_Interface_Weapon_R73_Vympel is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/R73_Vympel.png"
)
Texture_Interface_Weapon_Kh_58 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Kh_58.png"
)
Texture_Interface_Weapon_R98MR is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/R98MR.png"
)
Texture_Interface_Weapon_R98MT is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/R98MT.png"
)
Texture_Interface_Weapon_skyflash is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/skyflash.png"
)
Texture_Interface_Weapon_kokon is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/kokon.png"
)
Texture_Interface_Weapon_Malyutka is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Malyutka.png"
)
Texture_Interface_Weapon_Konkurs is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Konkurs.png"
)
Texture_Interface_Weapon_agm114 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/agm114.png"
)
Texture_Interface_Weapon_agm45_shrike is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/agm45_shrike.png"
)
Texture_Interface_Weapon_agm65_maverick is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/agm65_maverick.png"
)
Texture_Interface_Weapon_agm88_harm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/agm88_harm.png"
)
Texture_Interface_Weapon_AS37_Martel is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/AS37_Martel.png"
)
Texture_Interface_Weapon_agm_alarm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/agm_alarm.png"
)
Texture_Interface_Weapon_ARMAT is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/ARMAT.png"
)
Texture_Interface_Weapon_AGM_SS11_x2 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/AGM_SS11_x2.png"
)
Texture_Interface_Weapon_as30 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/as30.png"
)
Texture_Interface_Weapon_TOW is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/TOW.png"
)
Texture_Interface_Weapon_ITOW is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/ITOW.png"
)
Texture_Interface_Weapon_TOW2 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/TOW2.png"
)
Texture_Interface_Weapon_hot is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/hot.png"
)
Texture_Interface_Weapon_Kh_23M is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Kh_23M.png"
)
Texture_Interface_Weapon_Kh_25ML is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Kh_25ML.png"
)
Texture_Interface_Weapon_AGM_Kh25MP is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/AGM_Kh25MP.png"
)
Texture_Interface_Weapon_Kh_28 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Kh_28.png"
)
Texture_Interface_Weapon_Kh_29 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Kh_29.png"
)
Texture_Interface_Weapon_AGM_Kh29L is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/AGM_Kh29L.png"
)
Texture_Interface_Weapon_R33_Vympel is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/R33_Vympel.png"
)
Texture_Interface_Weapon_9k111 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/9k111.png"
)
Texture_Interface_Weapon_Kobra is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Kobra.png"
)
Texture_Interface_Weapon_KonkursM_late is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/KonkursM_late.png"
)
Texture_Interface_Weapon_sturm is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/sturm.png"
)
Texture_Interface_Weapon_Arkan is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Arkan.png"
)
Texture_Interface_Weapon_refleks is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/refleks.png"
)
Texture_Interface_Weapon_shilelagh is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/shilelagh.png"
)
Texture_Interface_Weapon_Milan is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Milan.png"
)
Texture_Interface_Weapon_swingfire is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/swingfire.png"
)
Texture_Interface_Weapon_Metis is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Metis.png"
)
Texture_Interface_Weapon_Eryx is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Eryx.png"
)
Texture_Interface_Weapon_BGL_400 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/BGL_400.png"
)
Texture_Interface_Weapon_CPU123 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/CPU123.png"
)
Texture_Interface_Weapon_GBU_10 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/GBU_10.png"
)
Texture_Interface_Weapon_GBU_27 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/GBU_27.png"
)
Texture_Interface_Weapon_KAB_1500Kr is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/KAB_1500Kr.png"
)
Texture_Interface_Weapon_KAB_500Kr is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/KAB_500Kr.png"
)
Texture_Interface_Weapon_s_24 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/s_24.png"
)
Texture_Interface_Weapon_Javelin is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Javelin.png"
)
Texture_Interface_Weapon_M47_dragon is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/M47_dragon.png"
)
Texture_Interface_Weapon_fim92 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/fim92.png"
)
Texture_Interface_Weapon_grail is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/grail.png"
)
Texture_Interface_Weapon_Mistral is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Mistral.png"
)
Texture_Interface_Weapon_9M311_Tunguska is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/9M311_Tunguska.png"
)
Texture_Interface_Weapon_9M33_Osa is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/9M33_Osa.png"
)
Texture_Interface_Weapon_9M38M1_Kub is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/9M38M1_Kub.png"
)
Texture_Interface_Weapon_SAM_9M8M3_x2 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/SAM_9M8M3_x2.png"
)
Texture_Interface_Weapon_SAM_FASTA_Strela2M_x4 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/SAM_FASTA_Strela2M_x4.png"
)
Texture_Interface_Weapon_aim92 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/aim92.png"
)
Texture_Interface_Weapon_hawk is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/hawk.png"
)
Texture_Interface_Weapon_Igla is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Igla.png"
)
Texture_Interface_Weapon_mim72 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/mim72.png"
)
Texture_Interface_Weapon_R440 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/R440.png"
)
Texture_Interface_Weapon_Rapier is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Rapier.png"
)
Texture_Interface_Weapon_roland is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/roland.png"
)
Texture_Interface_Weapon_9K31_Strela is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/9K31_Strela.png"
)
Texture_Interface_Weapon_Strela_10 is TUIResourceTexture_Common
(
    FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/Armes/Panel_Info/Strela_10.png"
)

WeaponAdditionalTextureBank is TBUCKToolAdditionalTextureBank
(
    Textures = MAP
    [
        ("Texture_Interface_Weapon_GIAT_M621", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_GIAT_M621)]),
        ("Texture_Interface_Weapon_mk20_rh202", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_mk20_rh202)]),
        ("Texture_Interface_Weapon_Gsh2323", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Gsh2323)]),
        ("Texture_Interface_Weapon_bushmaster", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_bushmaster)]),
        ("Texture_Interface_Weapon_24A2", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_24A2)]),
        ("Texture_Interface_Weapon_24A2_bmp2", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_24A2_bmp2)]),
        ("Texture_Interface_Weapon_2A72", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A72)]),
        ("Texture_Interface_Weapon_Gsh30k", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Gsh30k)]),
        ("Texture_Interface_Weapon_rarden", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_rarden)]),
        ("Texture_Interface_Weapon_M230", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M230)]),
        ("Texture_Interface_Weapon_M693_F1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M693_F1)]),
        ("Texture_Interface_Weapon_T20_13", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_T20_13)]),
        ("Texture_Interface_Weapon_Mk_77", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Mk_77)]),
        ("Texture_Interface_Weapon_Bomb_CLU_BL755", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Bomb_CLU_BL755)]),
        ("Texture_Interface_Weapon_BLG66_Belouga", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_BLG66_Belouga)]),
        ("Texture_Interface_Weapon_mk20", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_mk20)]),
        ("Texture_Interface_Weapon_FAB_500", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_FAB_500)]),
        ("Texture_Interface_Weapon_mk82", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_mk82)]),
        ("Texture_Interface_Weapon_FAB_250", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_FAB_250)]),
        ("Texture_Interface_Weapon_SAMP_T25", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_SAMP_T25)]),
        ("Texture_Interface_Weapon_SAMP_T200", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_SAMP_T200)]),
        ("Texture_Interface_Weapon_Mk18_RET", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Mk18_RET)]),
        ("Texture_Interface_Weapon_mk81", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_mk81)]),
        ("Texture_Interface_Weapon_Mk83", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Mk83)]),
        ("Texture_Interface_Weapon_zb500", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_zb500)]),
        ("Texture_Interface_Weapon_d10t", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_d10t)]),
        ("Texture_Interface_Weapon_2a26", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2a26)]),
        ("Texture_Interface_Weapon_Canon_AP_105mm_M68", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Canon_AP_105mm_M68)]),
        ("Texture_Interface_Weapon_m68_105", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_m68_105)]),
        ("Texture_Interface_Weapon_2A46_64BV", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A46_64BV)]),
        ("Texture_Interface_Weapon_L11A5", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_L11A5)]),
        ("Texture_Interface_Weapon_l44", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_l44)]),
        ("Texture_Interface_Weapon_M68_120", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M68_120)]),
        ("Texture_Interface_Weapon_Canon_AP_122mm_D25T", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Canon_AP_122mm_D25T)]),
        ("Texture_Interface_Weapon_2A46_T80", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A46_T80)]),
        ("Texture_Interface_Weapon_Canon_AP_125mm_2A45_Sprut", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Canon_AP_125mm_2A45_Sprut)]),
        ("Texture_Interface_Weapon_2A18_AP", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A18_AP)]),
        ("Texture_Interface_Weapon_2A60_AP", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A60_AP)]),
        ("Texture_Interface_Weapon_2A60_K120", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A60_K120)]),
        ("Texture_Interface_Weapon_zis_2_57", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_zis_2_57)]),
        ("Texture_Interface_Weapon_2A28_grom", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A28_grom)]),
        ("Texture_Interface_Weapon_D56T", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_D56T)]),
        ("Texture_Interface_Weapon_Scorpion_Canon", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Scorpion_Canon)]),
        ("Texture_Interface_Weapon_Canon_AP_90mm_KanJPz", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Canon_AP_90mm_KanJPz)]),
        ("Texture_Interface_Weapon_d44", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_d44)]),
        ("Texture_Interface_Weapon_Canon_AP_85mm_D48", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Canon_AP_85mm_D48)]),
        ("Texture_Interface_Weapon_Canon_AP_85mm_K52", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Canon_AP_85mm_K52)]),
        ("Texture_Interface_Weapon_F3", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_F3)]),
        ("Texture_Interface_Weapon_Canon_AP_90mm_CN90_F4", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Canon_AP_90mm_CN90_F4)]),
        ("Texture_Interface_Weapon_m48", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_m48)]),
        ("Texture_Interface_Weapon_D22_AP", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_D22_AP)]),
        ("Texture_Interface_Weapon_D30_AP", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_D30_AP)]),
        ("Texture_Interface_Weapon_Canon_AP_KS19_100mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Canon_AP_KS19_100mm)]),
        ("Texture_Interface_Weapon_ATO_54", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_ATO_54)]),
        ("Texture_Interface_Weapon_2A70", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A70)]),
        ("Texture_Interface_Weapon_avre", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_avre)]),
        ("Texture_Interface_Weapon_sheridan", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_sheridan)]),
        ("Texture_Interface_Weapon_CM60A1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_CM60A1)]),
        ("Texture_Interface_Weapon_SPG9", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_SPG9)]),
        ("Texture_Interface_Weapon_Canon_HE_KS30_130mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Canon_HE_KS30_130mm)]),
        ("Texture_Interface_Weapon_fk20", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_fk20)]),
        ("Texture_Interface_Weapon_Bofors_L70", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Bofors_L70)]),
        ("Texture_Interface_Weapon_KPVT", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_KPVT)]),
        ("Texture_Interface_Weapon_DCA_1_canon_KS19_100mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_DCA_1_canon_KS19_100mm)]),
        ("Texture_Interface_Weapon_DCA_1_canon_KS30_130mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_DCA_1_canon_KS30_130mm)]),
        ("Texture_Interface_Weapon_gdf_35", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_gdf_35)]),
        ("Texture_Interface_Weapon_DCA_2_canon_2M3_25mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_DCA_2_canon_2M3_25mm)]),
        ("Texture_Interface_Weapon_DCA_2_canon_76T2_20mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_DCA_2_canon_76T2_20mm)]),
        ("Texture_Interface_Weapon_zu23", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_zu23)]),
        ("Texture_Interface_Weapon_mag", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_mag)]),
        ("Texture_Interface_Weapon_DCA_4_canon_ZPU4_towed_14_5mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_DCA_4_canon_ZPU4_towed_14_5mm)]),
        ("Texture_Interface_Weapon_2A38M", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A38M)]),
        ("Texture_Interface_Weapon_HS_831A", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_HS_831A)]),
        ("Texture_Interface_Weapon_APZ23", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_APZ23)]),
        ("Texture_Interface_Weapon_DCA_4_canons_Maxson", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_DCA_4_canons_Maxson)]),
        ("Texture_Interface_Weapon_AK74", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_AK74)]),
        ("Texture_Interface_Weapon_AKs74", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_AKs74)]),
        ("Texture_Interface_Weapon_L1A1_SLR", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_L1A1_SLR)]),
        ("Texture_Interface_Weapon_FAMAS", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_FAMAS)]),
        ("Texture_Interface_Weapon_FM_FNC", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_FM_FNC)]),
        ("Texture_Interface_Weapon_g3a4", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_g3a4)]),
        ("Texture_Interface_Weapon_FM_Garand", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_FM_Garand)]),
        ("Texture_Interface_Weapon_L85A1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_L85A1)]),
        ("Texture_Interface_Weapon_M14", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M14)]),
        ("Texture_Interface_Weapon_M16", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M16)]),
        ("Texture_Interface_Weapon_M16A1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M16A1)]),
        ("Texture_Interface_Weapon_MAS_49_56", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_MAS_49_56)]),
        ("Texture_Interface_Weapon_FM_SIG_540", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_FM_SIG_540)]),
        ("Texture_Interface_Weapon_SIG_543", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_SIG_543)]),
        ("Texture_Interface_Weapon_FM_Tantal", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_FM_Tantal)]),
        ("Texture_Interface_Weapon_FM_kbk_AKM", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_FM_kbk_AKM)]),
        ("Texture_Interface_Weapon_FM_kbk_AKMS", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_FM_kbk_AKMS)]),
        ("Texture_Interface_Weapon_m197_vulcan", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_m197_vulcan)]),
        ("Texture_Interface_Weapon_Gatling_GAU19_12_7mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Gatling_GAU19_12_7mm)]),
        ("Texture_Interface_Weapon_JakB", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_JakB)]),
        ("Texture_Interface_Weapon_m168", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_m168)]),
        ("Texture_Interface_Weapon_m134", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_m134)]),
        ("Texture_Interface_Weapon_AANF1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_AANF1)]),
        ("Texture_Interface_Weapon_arden", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_arden)]),
        ("Texture_Interface_Weapon_defa30mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_defa30mm)]),
        ("Texture_Interface_Weapon_gau8", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_gau8)]),
        ("Texture_Interface_Weapon_Gsh3030", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Gsh3030)]),
        ("Texture_Interface_Weapon_Gsh630", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Gsh630)]),
        ("Texture_Interface_Weapon_GatlingAir_Colt_Mk12_20mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_GatlingAir_Colt_Mk12_20mm)]),
        ("Texture_Interface_Weapon_M61_vulcan", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M61_vulcan)]),
        ("Texture_Interface_Weapon_CanonAir_Mauser_BK_27mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_CanonAir_Mauser_BK_27mm)]),
        ("Texture_Interface_Weapon_nr30", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_nr30)]),
        ("Texture_Interface_Weapon_Smoke_Grenade", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Smoke_Grenade)]),
        ("Texture_Interface_Weapon_satchel", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_satchel)]),
        ("Texture_Interface_Weapon_NSVT", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_NSVT)]),
        ("Texture_Interface_Weapon_DShKM", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_DShKM)]),
        ("Texture_Interface_Weapon_12_7_mm_M2HB", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_12_7_mm_M2HB)]),
        ("Texture_Interface_Weapon_m3p", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_m3p)]),
        ("Texture_Interface_Weapon_2A18", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A18)]),
        ("Texture_Interface_Weapon_2A36", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A36)]),
        ("Texture_Interface_Weapon_2A36_AP", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A36_AP)]),
        ("Texture_Interface_Weapon_2A44", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A44)]),
        ("Texture_Interface_Weapon_2A60", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A60)]),
        ("Texture_Interface_Weapon_D22", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_D22)]),
        ("Texture_Interface_Weapon_2A65_AP", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A65_AP)]),
        ("Texture_Interface_Weapon_2A65", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2A65)]),
        ("Texture_Interface_Weapon_fh_70", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_fh_70)]),
        ("Texture_Interface_Weapon_A222_Howitzer_130mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_A222_Howitzer_130mm)]),
        ("Texture_Interface_Weapon_B4M_203mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_B4M_203mm)]),
        ("Texture_Interface_Weapon_BS3_towed_100mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_BS3_towed_100mm)]),
        ("Texture_Interface_Weapon_BS3_Direct_AP", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_BS3_Direct_AP)]),
        ("Texture_Interface_Weapon_Br5M_towed_280mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Br5M_towed_280mm)]),
        ("Texture_Interface_Weapon_D1_towed_152mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_D1_towed_152mm)]),
        ("Texture_Interface_Weapon_D20", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_D20)]),
        ("Texture_Interface_Weapon_ML20", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_ML20)]),
        ("Texture_Interface_Weapon_ML20_AP", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_ML20_AP)]),
        ("Texture_Interface_Weapon_D30", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_D30)]),
        ("Texture_Interface_Weapon_GCT_F1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_GCT_F1)]),
        ("Texture_Interface_Weapon_L13A1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_L13A1)]),
        ("Texture_Interface_Weapon_L13A1_Direct", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_L13A1_Direct)]),
        ("Texture_Interface_Weapon_M101_105mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M101_105mm)]),
        ("Texture_Interface_Weapon_m102", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_m102)]),
        ("Texture_Interface_Weapon_M113A1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M113A1)]),
        ("Texture_Interface_Weapon_M114_39", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M114_39)]),
        ("Texture_Interface_Weapon_M114_39_AP", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M114_39_AP)]),
        ("Texture_Interface_Weapon_M115", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M115)]),
        ("Texture_Interface_Weapon_M115_AP", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M115_AP)]),
        ("Texture_Interface_Weapon_M118_Direct", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M118_Direct)]),
        ("Texture_Interface_Weapon_M118", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M118)]),
        ("Texture_Interface_Weapon_m185", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_m185)]),
        ("Texture_Interface_Weapon_M201A", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M201A)]),
        ("Texture_Interface_Weapon_M46", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M46)]),
        ("Texture_Interface_Weapon_M50_63", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M50_63)]),
        ("Texture_Interface_Weapon_TRF1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_TRF1)]),
        ("Texture_Interface_Weapon_KMGU_dispenser", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_KMGU_dispenser)]),
        ("Texture_Interface_Weapon_AGS_17", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_AGS_17)]),
        ("Texture_Interface_Weapon_mk19", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_mk19)]),
        ("Texture_Interface_Weapon_Lance_grenade_M129_40mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Lance_grenade_M129_40mm)]),
        ("Texture_Interface_Weapon_PalladD_wz83", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_PalladD_wz83)]),
        ("Texture_Interface_Weapon_Hydra_70", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Hydra_70)]),
        ("Texture_Interface_Weapon_m21of", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_m21of)]),
        ("Texture_Interface_Weapon_pkt", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_pkt)]),
        ("Texture_Interface_Weapon_hk21", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_hk21)]),
        ("Texture_Interface_Weapon_mag_coax", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_mag_coax)]),
        ("Texture_Interface_Weapon_M240_Vehicule", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M240_Vehicule)]),
        ("Texture_Interface_Weapon_M1919", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M1919)]),
        ("Texture_Interface_Weapon_M240_abrams", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M240_abrams)]),
        ("Texture_Interface_Weapon_M240d", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M240d)]),
        ("Texture_Interface_Weapon_M60", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M60)]),
        ("Texture_Interface_Weapon_M60E3", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M60E3)]),
        ("Texture_Interface_Weapon_mg3", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_mg3)]),
        ("Texture_Interface_Weapon_PKM", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_PKM)]),
        ("Texture_Interface_Weapon_M240B", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M240B)]),
        ("Texture_Interface_Weapon_Maxim", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Maxim)]),
        ("Texture_Interface_Weapon_MW1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_MW1)]),
        ("Texture_Interface_Weapon_Mortier_2B14_82mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Mortier_2B14_82mm)]),
        ("Texture_Interface_Weapon_tampella_120", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_tampella_120)]),
        ("Texture_Interface_Weapon_81mm_mortar", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_81mm_mortar)]),
        ("Texture_Interface_Weapon_Mortier_240mm_M240", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Mortier_240mm_M240)]),
        ("Texture_Interface_Weapon_m29", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_m29)]),
        ("Texture_Interface_Weapon_M30", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M30)]),
        ("Texture_Interface_Weapon_pm43", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_pm43)]),
        ("Texture_Interface_Weapon_2b9", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_2b9)]),
        ("Texture_Interface_Weapon_AKs74u", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_AKs74u)]),
        ("Texture_Interface_Weapon_AS_Val", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_AS_Val)]),
        ("Texture_Interface_Weapon_M4_carbine", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M4_carbine)]),
        ("Texture_Interface_Weapon_M3_Grease_Gun", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M3_Grease_Gun)]),
        ("Texture_Interface_Weapon_MAT_49", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_MAT_49)]),
        ("Texture_Interface_Weapon_mp5a3", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_mp5a3)]),
        ("Texture_Interface_Weapon_mp55s", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_mp55s)]),
        ("Texture_Interface_Weapon_PM63_Rak", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_PM63_Rak)]),
        ("Texture_Interface_Weapon_PPS43", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_PPS43)]),
        ("Texture_Interface_Weapon_skorpion", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_skorpion)]),
        ("Texture_Interface_Weapon_L2A3", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_L2A3)]),
        ("Texture_Interface_Weapon_Vintorez", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Vintorez)]),
        ("Texture_Interface_Weapon_PM_vigneron_m2", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_PM_vigneron_m2)]),
        ("Texture_Interface_Weapon_uzi", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_uzi)]),
        ("Texture_Interface_Weapon_guv_agl", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_guv_agl)]),
        ("Texture_Interface_Weapon_guv", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_guv)]),
        ("Texture_Interface_Weapon_s13", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_s13)]),
        ("Texture_Interface_Weapon_s80", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_s80)]),
        ("Texture_Interface_Weapon_s5m", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_s5m)]),
        ("Texture_Interface_Weapon_zuni", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_zuni)]),
        ("Texture_Interface_Weapon_s24", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_s24)]),
        ("Texture_Interface_Weapon_9m55k", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_9m55k)]),
        ("Texture_Interface_Weapon_sf2_110", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_sf2_110)]),
        ("Texture_Interface_Weapon_atacms", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_atacms)]),
        ("Texture_Interface_Weapon_m26", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_m26)]),
        ("Texture_Interface_Weapon_PW_LWD", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_PW_LWD)]),
        ("Texture_Interface_Weapon_mo10104", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_mo10104)]),
        ("Texture_Interface_Weapon_AGI_3x40", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_AGI_3x40)]),
        ("Texture_Interface_Weapon_APILAS", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_APILAS)]),
        ("Texture_Interface_Weapon_at4_cs", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_at4_cs)]),
        ("Texture_Interface_Weapon_armbrust", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_armbrust)]),
        ("Texture_Interface_Weapon_B11_RCL_107mm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_B11_RCL_107mm)]),
        ("Texture_Interface_Weapon_Blindicide_RL100", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Blindicide_RL100)]),
        ("Texture_Interface_Weapon_Blindicide_RL83", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Blindicide_RL83)]),
        ("Texture_Interface_Weapon_carl_gustave", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_carl_gustave)]),
        ("Texture_Interface_Weapon_handflammpatrone", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_handflammpatrone)]),
        ("Texture_Interface_Weapon_LAW_80", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_LAW_80)]),
        ("Texture_Interface_Weapon_LRAC_73", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_LRAC_73)]),
        ("Texture_Interface_Weapon_LRAC_f1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_LRAC_f1)]),
        ("Texture_Interface_Weapon_RocketInf_M20_SuperBaz", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RocketInf_M20_SuperBaz)]),
        ("Texture_Interface_Weapon_M202_flash", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M202_flash)]),
        ("Texture_Interface_Weapon_M40A1_RCL", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M40A1_RCL)]),
        ("Texture_Interface_Weapon_m68_rcl", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_m68_rcl)]),
        ("Texture_Interface_Weapon_M72_law", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M72_law)]),
        ("Texture_Interface_Weapon_pzfaust3", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_pzfaust3)]),
        ("Texture_Interface_Weapon_panzerfaust_44", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_panzerfaust_44)]),
        ("Texture_Interface_Weapon_RPG16", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RPG16)]),
        ("Texture_Interface_Weapon_RPG22", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RPG22)]),
        ("Texture_Interface_Weapon_RPG2", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RPG2)]),
        ("Texture_Interface_Weapon_RPG26", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RPG26)]),
        ("Texture_Interface_Weapon_RPG27", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RPG27)]),
        ("Texture_Interface_Weapon_RPG29", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RPG29)]),
        ("Texture_Interface_Weapon_RPG7", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RPG7)]),
        ("Texture_Interface_Weapon_RocketInf_RPG76_Komar", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RocketInf_RPG76_Komar)]),
        ("Texture_Interface_Weapon_RPO_RYS", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RPO_RYS)]),
        ("Texture_Interface_Weapon_RPO_RYS_x4", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RPO_RYS_x4)]),
        ("Texture_Interface_Weapon_RPO", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RPO)]),
        ("Texture_Interface_Weapon_Viper", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Viper)]),
        ("Texture_Interface_Weapon_WASP", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_WASP)]),
        ("Texture_Interface_Weapon_L4A4", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_L4A4)]),
        ("Texture_Interface_Weapon_DP28", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_DP28)]),
        ("Texture_Interface_Weapon_Saw_FALO", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Saw_FALO)]),
        ("Texture_Interface_Weapon_L86A1_LSW", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_L86A1_LSW)]),
        ("Texture_Interface_Weapon_M249", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M249)]),
        ("Texture_Interface_Weapon_RPK", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RPK)]),
        ("Texture_Interface_Weapon_RPK74", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_RPK74)]),
        ("Texture_Interface_Weapon_FAL_sniper", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_FAL_sniper)]),
        ("Texture_Interface_Weapon_FN_Model_30_11", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_FN_Model_30_11)]),
        ("Texture_Interface_Weapon_FR_F1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_FR_F1)]),
        ("Texture_Interface_Weapon_FR_F2", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_FR_F2)]),
        ("Texture_Interface_Weapon_G3A3ZF", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_G3A3ZF)]),
        ("Texture_Interface_Weapon_L96A1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_L96A1)]),
        ("Texture_Interface_Weapon_M14_Sniper", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M14_Sniper)]),
        ("Texture_Interface_Weapon_M24_Sniper", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M24_Sniper)]),
        ("Texture_Interface_Weapon_Mosin_Nagant", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Mosin_Nagant)]),
        ("Texture_Interface_Weapon_PSG1", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_PSG1)]),
        ("Texture_Interface_Weapon_SVD_Dragunov", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_SVD_Dragunov)]),
        ("Texture_Interface_Weapon_M82_Barrett", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M82_Barrett)]),
        ("Texture_Interface_Weapon_LFP_54", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_LFP_54)]),
        ("Texture_Interface_Weapon_lpo", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_lpo)]),
        ("Texture_Interface_Weapon_M2_Flamethrower", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M2_Flamethrower)]),
        ("Texture_Interface_Weapon_aim120_amraam", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_aim120_amraam)]),
        ("Texture_Interface_Weapon_sparrow", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_sparrow)]),
        ("Texture_Interface_Weapon_aim9_sidewinder", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_aim9_sidewinder)]),
        ("Texture_Interface_Weapon_Super_530D", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Super_530D)]),
        ("Texture_Interface_Weapon_R60_Vympel", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_R60_Vympel)]),
        ("Texture_Interface_Weapon_R27_Vympel", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_R27_Vympel)]),
        ("Texture_Interface_Weapon_R27T_Vympel", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_R27T_Vympel)]),
        ("Texture_Interface_Weapon_R37_Vympel", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_R37_Vympel)]),
        ("Texture_Interface_Weapon_R550_Magic", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_R550_Magic)]),
        ("Texture_Interface_Weapon_R550_Magic_II", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_R550_Magic_II)]),
        ("Texture_Interface_Weapon_R73_Vympel", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_R73_Vympel)]),
        ("Texture_Interface_Weapon_Kh_58", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Kh_58)]),
        ("Texture_Interface_Weapon_R98MR", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_R98MR)]),
        ("Texture_Interface_Weapon_R98MT", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_R98MT)]),
        ("Texture_Interface_Weapon_skyflash", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_skyflash)]),
        ("Texture_Interface_Weapon_kokon", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_kokon)]),
        ("Texture_Interface_Weapon_Malyutka", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Malyutka)]),
        ("Texture_Interface_Weapon_Konkurs", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Konkurs)]),
        ("Texture_Interface_Weapon_agm114", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_agm114)]),
        ("Texture_Interface_Weapon_agm45_shrike", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_agm45_shrike)]),
        ("Texture_Interface_Weapon_agm65_maverick", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_agm65_maverick)]),
        ("Texture_Interface_Weapon_agm88_harm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_agm88_harm)]),
        ("Texture_Interface_Weapon_AS37_Martel", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_AS37_Martel)]),
        ("Texture_Interface_Weapon_agm_alarm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_agm_alarm)]),
        ("Texture_Interface_Weapon_ARMAT", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_ARMAT)]),
        ("Texture_Interface_Weapon_AGM_SS11_x2", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_AGM_SS11_x2)]),
        ("Texture_Interface_Weapon_as30", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_as30)]),
        ("Texture_Interface_Weapon_TOW", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_TOW)]),
        ("Texture_Interface_Weapon_ITOW", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_ITOW)]),
        ("Texture_Interface_Weapon_TOW2", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_TOW2)]),
        ("Texture_Interface_Weapon_hot", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_hot)]),
        ("Texture_Interface_Weapon_Kh_23M", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Kh_23M)]),
        ("Texture_Interface_Weapon_Kh_25ML", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Kh_25ML)]),
        ("Texture_Interface_Weapon_AGM_Kh25MP", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_AGM_Kh25MP)]),
        ("Texture_Interface_Weapon_Kh_28", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Kh_28)]),
        ("Texture_Interface_Weapon_Kh_29", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Kh_29)]),
        ("Texture_Interface_Weapon_AGM_Kh29L", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_AGM_Kh29L)]),
        ("Texture_Interface_Weapon_R33_Vympel", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_R33_Vympel)]),
        ("Texture_Interface_Weapon_9k111", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_9k111)]),
        ("Texture_Interface_Weapon_Kobra", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Kobra)]),
        ("Texture_Interface_Weapon_KonkursM_late", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_KonkursM_late)]),
        ("Texture_Interface_Weapon_sturm", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_sturm)]),
        ("Texture_Interface_Weapon_Arkan", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Arkan)]),
        ("Texture_Interface_Weapon_refleks", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_refleks)]),
        ("Texture_Interface_Weapon_shilelagh", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_shilelagh)]),
        ("Texture_Interface_Weapon_Milan", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Milan)]),
        ("Texture_Interface_Weapon_swingfire", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_swingfire)]),
        ("Texture_Interface_Weapon_Metis", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Metis)]),
        ("Texture_Interface_Weapon_Eryx", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Eryx)]),
        ("Texture_Interface_Weapon_BGL_400", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_BGL_400)]),
        ("Texture_Interface_Weapon_CPU123", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_CPU123)]),
        ("Texture_Interface_Weapon_GBU_10", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_GBU_10)]),
        ("Texture_Interface_Weapon_GBU_27", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_GBU_27)]),
        ("Texture_Interface_Weapon_KAB_1500Kr", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_KAB_1500Kr)]),
        ("Texture_Interface_Weapon_KAB_500Kr", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_KAB_500Kr)]),
        ("Texture_Interface_Weapon_s_24", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_s_24)]),
        ("Texture_Interface_Weapon_Javelin", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Javelin)]),
        ("Texture_Interface_Weapon_M47_dragon", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_M47_dragon)]),
        ("Texture_Interface_Weapon_fim92", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_fim92)]),
        ("Texture_Interface_Weapon_grail", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_grail)]),
        ("Texture_Interface_Weapon_Mistral", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Mistral)]),
        ("Texture_Interface_Weapon_9M311_Tunguska", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_9M311_Tunguska)]),
        ("Texture_Interface_Weapon_9M33_Osa", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_9M33_Osa)]),
        ("Texture_Interface_Weapon_9M38M1_Kub", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_9M38M1_Kub)]),
        ("Texture_Interface_Weapon_SAM_9M8M3_x2", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_SAM_9M8M3_x2)]),
        ("Texture_Interface_Weapon_SAM_FASTA_Strela2M_x4", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_SAM_FASTA_Strela2M_x4)]),
        ("Texture_Interface_Weapon_aim92", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_aim92)]),
        ("Texture_Interface_Weapon_hawk", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_hawk)]),
        ("Texture_Interface_Weapon_Igla", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Igla)]),
        ("Texture_Interface_Weapon_mim72", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_mim72)]),
        ("Texture_Interface_Weapon_R440", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_R440)]),
        ("Texture_Interface_Weapon_Rapier", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Rapier)]),
        ("Texture_Interface_Weapon_roland", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_roland)]),
        ("Texture_Interface_Weapon_9K31_Strela", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_9K31_Strela)]),
        ("Texture_Interface_Weapon_Strela_10", MAP [(~/ComponentState/Normal, ~/Texture_Interface_Weapon_Strela_10)]),
    ]
)
